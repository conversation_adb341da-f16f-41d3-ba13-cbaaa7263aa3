/**
 * 速率限制中间件
 * 用于防止API滥用和爆破攻击
 */

// 存储用户请求记录的内存映射
// 格式: { userId: { actionType: { lastRequestTime: timestamp, count: number } } }
const userRequestMap = {};

// 定期清理过期的请求记录，避免内存泄漏（每小时执行一次）
setInterval(() => {
    const now = Date.now();
    for (const userId in userRequestMap) {
        for (const actionType in userRequestMap[userId]) {
            // 清理超过2小时的记录
            if (now - userRequestMap[userId][actionType].lastRequestTime > 2 * 60 * 60 * 1000) {
                delete userRequestMap[userId][actionType];
            }
        }
        // 如果用户没有任何活动记录，删除整个用户条目
        if (Object.keys(userRequestMap[userId]).length === 0) {
            delete userRequestMap[userId];
        }
    }
    console.log(`[Rate Limit] 已清理过期请求记录，当前记录数: ${Object.keys(userRequestMap).length}`);
}, 60 * 60 * 1000);

/**
 * 创建速率限制中间件
 * @param {string} actionType - 操作类型标识(如 'comment', 'example', 'avatar')
 * @param {number} windowMs - 时间窗口(毫秒)
 * @param {number} maxRequests - 在时间窗口内允许的最大请求数
 * @returns {Function} Express中间件函数
 */
function createRateLimiter(actionType, windowMs, maxRequests = 1) {
    return (req, res, next) => {
        // 需要用户ID (通过认证中间件提供)
        const userId = req.user?.id;
        console.log(`[Rate Limit] 检查限制: 操作=${actionType}, 用户ID=${userId || '未知'}, 路径=${req.originalUrl}, 方法=${req.method}`);
        
        if (!userId) {
            console.log('[Rate Limit] 无用户ID，跳过限制检查');
            return next(); // 如果没有用户ID，跳过限制(认证中间件会处理)
        }

        // 初始化用户的请求记录
        if (!userRequestMap[userId]) {
            console.log(`[Rate Limit] 为用户 ${userId} 创建请求记录`);
            userRequestMap[userId] = {};
        }
        if (!userRequestMap[userId][actionType]) {
            console.log(`[Rate Limit] 为用户 ${userId} 创建 ${actionType} 操作记录`);
            userRequestMap[userId][actionType] = {
                lastRequestTime: 0,
                count: 0
            };
        }

        const userActionRecord = userRequestMap[userId][actionType];
        const now = Date.now();
        const timeSinceLastRequest = now - userActionRecord.lastRequestTime;

        console.log(`[Rate Limit] 用户 ${userId} 的 ${actionType} 操作: 当前计数=${userActionRecord.count}, 距上次请求=${timeSinceLastRequest}ms, 限制窗口=${windowMs}ms, 路径=${req.originalUrl}`);

        // 如果时间窗口已过，重置计数
        if (timeSinceLastRequest > windowMs) {
            console.log(`[Rate Limit] 用户 ${userId} 的 ${actionType} 操作: 时间窗口已过，重置计数`);
            userActionRecord.count = 0;
            userActionRecord.lastRequestTime = now;
        }

        // 检查是否超过请求限制
        if (userActionRecord.count >= maxRequests) {
            const retryAfterSeconds = Math.ceil((windowMs - timeSinceLastRequest) / 1000);
            console.log(`[Rate Limit] 用户 ${userId} 的 ${actionType} 操作: 超过限制! 需等待 ${retryAfterSeconds} 秒, 路径=${req.originalUrl}`);
            res.setHeader('Retry-After', String(retryAfterSeconds));
            return res.status(429).json({
                error: `请求过于频繁，请${retryAfterSeconds}秒后重试`,
                retryAfter: retryAfterSeconds
            });
        }

        // 更新请求记录
        userActionRecord.count++;
        userActionRecord.lastRequestTime = now;
        console.log(`[Rate Limit] 用户 ${userId} 的 ${actionType} 操作: 允许通过，计数增加到 ${userActionRecord.count}`);

        // 继续处理请求
        next();
    };
}

// 输出当前限制器状态的辅助函数
function logRateLimitStatus() {
    console.log('[Rate Limit] 当前状态:');
    console.log(JSON.stringify(userRequestMap, null, 2));
}

// 每5分钟输出一次限制器状态
setInterval(logRateLimitStatus, 5 * 60 * 1000);

// 预定义的限制器 - 减小时间窗口使其更容易触发
const commentRateLimiter = createRateLimiter('comment', 10 * 1000, 1); // 10秒内只允许发1条评论
const exampleRateLimiter = createRateLimiter('example', 10 * 1000, 1); // 10秒内只允许上传1个案例
const avatarRateLimiter = createRateLimiter('avatar', 10 * 1000, 1); // 10秒内只允许更换1次头像
const patternExtractionRateLimiter = createRateLimiter('pattern_extraction', 30 * 1000, 1); // 30秒内只允许提交1次印花提取请求

// 如果需要，可以为API调用创建其他速率限制器
const apiRateLimiter = createRateLimiter('general_api', 5 * 1000, 5); // 5秒内最多5个一般API请求

module.exports = {
    commentRateLimiter,
    exampleRateLimiter,
    avatarRateLimiter,
    apiRateLimiter,
    patternExtractionRateLimiter, // 导出印花提取限制器
    createRateLimiter, // 导出工厂函数，以便于创建自定义限制器
    logRateLimitStatus // 导出日志函数，用于手动检查状态
}; 