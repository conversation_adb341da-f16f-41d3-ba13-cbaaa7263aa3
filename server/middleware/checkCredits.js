const pool = require('../db/pool'); // 导入数据库连接池

// const FREE_GENERATIONS_PER_DAY = 15; // 不再使用次数
// const DAILY_FREE_CREDITS_AMOUNT = 20; // 每日发放的免费积分额度 -- REMOVE THIS
// const GENERATION_COST = 2; // 不再使用硬编码的常量

// 修改为工厂函数，返回接受 featureKey 参数的中间件
function checkAndDeductCredits(featureKey) {
    if (!featureKey) {
        console.error('[checkCredits F] Error: Missing featureKey provided to middleware factory.');
        return (req, res, next) => {
            res.status(500).json({ error: '服务器中间件配置错误 (缺少功能标识)' });
        };
    }
    console.log(`[checkCredits F] Creating middleware instance for feature: ${featureKey}`);

    // 返回实际的中间件函数
    return async (req, res, next) => {
        console.log(`[checkCredits M] Middleware entered for feature: ${featureKey}.`);
        const userId = req.user.id;

        if (!userId) {
            console.error('[checkCredits M] Error: User ID not found in req.user.');
            return res.status(401).json({ error: '用户未认证' });
        }

        let connectionForCost = null; // New variable specifically for fetching cost
        let connectionForTransaction = null; // Variable for the transaction
        let cost = null;
        let useDailyFree = true; // Default to true, will be overwritten by DB value

        try {
            // --- 第一步: 获取功能成本和免费额度使用设置 --- START ---
            connectionForCost = await pool.getConnection(); // Acquire connection for cost fetching
            console.log(`[checkCredits M] User ${userId}, Feature ${featureKey}: Fetching cost and free credit policy from DB using connection ID ${connectionForCost.threadId}.`);
            const [[featureConfigData]] = await connectionForCost.query(
                'SELECT cost, can_use_free_credits FROM feature_costs WHERE feature_key = ?',
                [featureKey]
            );
            
            // --- Fetch DAILY_FREE_CREDIT_ALLOWANCE --- START ---
            const [[dailyAllowanceData]] = await connectionForCost.query(
                'SELECT cost FROM feature_costs WHERE feature_key = ?',
                ['DAILY_FREE_CREDIT_ALLOWANCE'] // The key for daily free allowance
            );
            // --- Fetch DAILY_FREE_CREDIT_ALLOWANCE --- END ---
            
            // --- 查询用户激活状态 --- START ---
            const [[userData]] = await connectionForCost.query(
                'SELECT is_activated FROM users WHERE id = ?',
                [userId]
            );
            const isUserActivated = userData && userData.is_activated === 1;
            console.log(`[checkCredits M] User ${userId}: Activation status: ${isUserActivated ? 'Activated' : 'Not Activated'}`);
            // --- 查询用户激活状态 --- END ---
            
            // --- Release connection used for fetching cost --- START ---
            if (connectionForCost) {
                connectionForCost.release();
                console.log(`[checkCredits M] User ${userId}: Connection ID ${connectionForCost.threadId} released after fetching cost.`);
                connectionForCost = null; // Set to null after release
            }
            // --- Release connection used for fetching cost --- END ---

            if (!featureConfigData || featureConfigData.cost === undefined || featureConfigData.cost === null) {
                console.error(`[checkCredits M] Error: Cost not found or invalid for featureKey: ${featureKey}`);
                return res.status(500).json({ error: `服务器错误：未找到功能 ${featureKey} 的成本配置。` });
            }
            cost = parseInt(featureConfigData.cost, 10);
            // Set useDailyFree based on the database value. If undefined (e.g., column not yet added for all rows), default to false for safety.
            useDailyFree = featureConfigData.can_use_free_credits === 1;
            
            // 未激活用户不能使用每日免费积分
            if (!isUserActivated) {
                useDailyFree = false;
                console.log(`[checkCredits M] User ${userId}: Not activated, disabling daily free credits usage.`);
            }

            if (isNaN(cost) || cost < 0) {
                 console.error(`[checkCredits M] Error: Invalid cost (${cost}) retrieved for featureKey: ${featureKey}`);
                 return res.status(500).json({ error: `服务器错误：功能 ${featureKey} 的成本配置无效。` });
            }
            console.log(`[checkCredits M] User ${userId}, Feature ${featureKey}: Cost retrieved: ${cost}`);

            if (!dailyAllowanceData || dailyAllowanceData.cost === undefined || dailyAllowanceData.cost === null) {
                console.error(`[checkCredits M] Error: Daily free credit allowance not found or invalid in feature_costs.`);
                return res.status(500).json({ error: '服务器错误：每日免费额度配置缺失或无效。' });
            }
            const DAILY_FREE_CREDITS_AMOUNT = parseInt(dailyAllowanceData.cost, 10);
            if (isNaN(DAILY_FREE_CREDITS_AMOUNT) || DAILY_FREE_CREDITS_AMOUNT < 0) {
                console.error(`[checkCredits M] Error: Invalid daily free credit allowance (${DAILY_FREE_CREDITS_AMOUNT}) retrieved.`);
                return res.status(500).json({ error: '服务器错误：每日免费额度配置无效。' });
            }
            console.log(`[checkCredits M] User ${userId}: Daily Free Credit Allowance retrieved: ${DAILY_FREE_CREDITS_AMOUNT}`);
            console.log(`[checkCredits M] User ${userId}, Feature ${featureKey}: Policy fetched - Cost: ${cost}, Can Use Free Credits: ${useDailyFree}`);
            // --- 第一步: 获取功能成本和免费额度使用设置 --- END ---

            // --- 第二步: 检查和扣除积分 (事务处理) --- START ---
            connectionForTransaction = await pool.getConnection(); // Get a NEW connection for the transaction
            await connectionForTransaction.beginTransaction();
            console.log(`[checkCredits M] User ${userId}: DB connection ID ${connectionForTransaction.threadId} acquired and transaction started for credit check.`);

            // 1. 获取用户信息 (加锁)
            const [users] = await connectionForTransaction.query(
                'SELECT id, credits, daily_generations_used, last_generation_date FROM users WHERE id = ? FOR UPDATE',
                [userId]
            );

            if (users.length === 0) {
                await connectionForTransaction.rollback();
                connectionForTransaction.release();
                console.warn(`[checkCredits M] User ${userId}: Not found.`);
                return res.status(404).json({ error: '找不到用户' });
            }

            let user = users[0];
            const today = new Date().toISOString().slice(0, 10);
            let cumulativeCredits = user.credits || 0;
            let dailyCreditsUsed = user.daily_generations_used || 0;
            const lastResetDateStr = user.last_generation_date ? user.last_generation_date.toISOString().slice(0, 10) : null;
            let remainingDailyFreeCredits = 0;

            console.log(`[checkCredits M] User ${userId}: Data fetched - Credits=${cumulativeCredits}, UsedToday=${dailyCreditsUsed}, LastReset=${lastResetDateStr}`);

            // 2. 检查并执行每日重置 (逻辑不变)
            if (lastResetDateStr !== today) {
                console.log(`[checkCredits M] User ${userId}: Performing daily reset. Last reset: ${lastResetDateStr}`);
                dailyCreditsUsed = 0;
                await connectionForTransaction.query(
                    'UPDATE users SET daily_generations_used = 0, last_generation_date = ? WHERE id = ?',
                    [today, userId]
                );
                console.log(`[checkCredits M] User ${userId}: Daily reset fields updated in DB.`);
                remainingDailyFreeCredits = DAILY_FREE_CREDITS_AMOUNT;
            } else {
                remainingDailyFreeCredits = Math.max(0, DAILY_FREE_CREDITS_AMOUNT - dailyCreditsUsed);
            }
            console.log(`[checkCredits M] User ${userId}: Remaining daily free credits: ${remainingDailyFreeCredits}`);

            // 3. 判断积分是否足够并执行扣除 (使用从 DB 获取的 cost 和 useDailyFree)
            if (useDailyFree) {
            if (remainingDailyFreeCredits >= cost) {
                const newDailyUsed = dailyCreditsUsed + cost;
                console.log(`[checkCredits M] User ${userId}: Sufficient daily credits. Deducting ${cost} from daily quota. New daily used: ${newDailyUsed}`);
                await connectionForTransaction.query(
                    'UPDATE users SET daily_generations_used = ? WHERE id = ?',
                    [newDailyUsed, userId]
                );
                await connectionForTransaction.query(
                    'INSERT INTO credit_transactions (user_id, change_amount, balance_after, reason, notes) VALUES (?, ?, ?, ?, ?)',
                        [userId, 0, cumulativeCredits, 'usage_free', `(${featureKey}) 消耗每日免费额度 ${cost}`]
                );
                console.log(`[checkCredits M] User ${userId}: Recorded free usage transaction.`);

            } else if (remainingDailyFreeCredits + cumulativeCredits >= cost) {
                const neededFromCumulative = cost - remainingDailyFreeCredits;
                const newCumulativeCredits = cumulativeCredits - neededFromCumulative;
                const newDailyUsed = DAILY_FREE_CREDITS_AMOUNT;
                console.log(`[checkCredits M] User ${userId}: Daily credits insufficient (${remainingDailyFreeCredits}). Using ${neededFromCumulative} cumulative credits. New balance: ${newCumulativeCredits}. Daily quota maxed out.`);
                await connectionForTransaction.query(
                    'UPDATE users SET credits = ?, daily_generations_used = ? WHERE id = ?',
                    [newCumulativeCredits, newDailyUsed, userId]
                );
                await connectionForTransaction.query(
                    'INSERT INTO credit_transactions (user_id, change_amount, balance_after, reason, notes) VALUES (?, ?, ?, ?, ?)',
                        [userId, -neededFromCumulative, newCumulativeCredits, 'usage_paid', `(${featureKey}) 每日额度用尽，消耗累积积分 ${neededFromCumulative}`]
                );
                console.log(`[checkCredits M] User ${userId}: Recorded paid usage transaction.`);
                cumulativeCredits = newCumulativeCredits;

            } else {
                    console.warn(`[checkCredits M] User ${userId}: Insufficient credits (Daily Free Allowed). Daily=${remainingDailyFreeCredits}, Cumulative=${cumulativeCredits}, Cost=${cost} for ${featureKey}`);
                await connectionForTransaction.rollback();
                return res.status(402).json({ error: '积分不足，请充值后重试。', required: cost, current_daily: remainingDailyFreeCredits, current_cumulative: cumulativeCredits });
                }
            } else {
                if (cumulativeCredits >= cost) {
                    const newCumulativeCredits = cumulativeCredits - cost;
                    console.log(`[checkCredits M] User ${userId}: Feature ${featureKey} (No Daily Free). Deducting ${cost} from cumulative credits. New balance: ${newCumulativeCredits}`);
                    await connectionForTransaction.query(
                        'UPDATE users SET credits = ? WHERE id = ?',
                        [newCumulativeCredits, userId]
                    );
                    await connectionForTransaction.query(
                        'INSERT INTO credit_transactions (user_id, change_amount, balance_after, reason, notes) VALUES (?, ?, ?, ?, ?)',
                        [userId, -cost, newCumulativeCredits, 'usage_paid_direct', `(${featureKey}) 直接消耗累积积分 ${cost} (禁用每日额度)`]
                    );
                    console.log(`[checkCredits M] User ${userId}: Recorded direct paid usage transaction.`);
                    cumulativeCredits = newCumulativeCredits;
                } else {
                    console.warn(`[checkCredits M] User ${userId}: Insufficient cumulative credits (No Daily Free). Cumulative=${cumulativeCredits}, Cost=${cost} for ${featureKey}`);
                    await connectionForTransaction.rollback();
                    return res.status(402).json({ error: '账户积分不足，请充值后重试。', required: cost, current_cumulative: cumulativeCredits });
                }
            }

            // 4. 提交事务
            await connectionForTransaction.commit();
            console.log(`[checkCredits M] User ${userId}: Transaction committed successfully on connection ID ${connectionForTransaction.threadId}.`);

            // 附加更新后的信息 (逻辑不变)
            const [[updatedUserData]] = await connectionForTransaction.query('SELECT daily_generations_used FROM users WHERE id = ?', [userId]);
            req.updatedCreditsInfo = {
                cumulativeCredits: cumulativeCredits,
                remainingDailyFreeCredits: Math.max(0, DAILY_FREE_CREDITS_AMOUNT - (updatedUserData ? updatedUserData.daily_generations_used : 0))
            };

            next(); // 继续处理请求
            // --- 第二步: 检查和扣除积分 (事务处理) --- END ---

        } catch (error) {
            console.error(`[checkCredits M] User ${userId}: Error during credit check/deduction for feature ${featureKey} (cost=${cost}):`, error);
            // Rollback the transaction if it exists and is active
            if (connectionForTransaction) {
                try { await connectionForTransaction.rollback(); console.error(`[checkCredits M] User ${userId}: Transaction rolled back.`); } catch (rbError) { console.error(`[checkCredits M] User ${userId}: Rollback failed:`, rbError); }
            }
            res.status(500).json({ error: '检查积分时发生内部错误' });
        } finally {
            // Ensure both potential connections are released
            if (connectionForCost) {
                connectionForCost.release();
                 console.log(`[checkCredits M] User ${userId}: Cost connection (ID ${connectionForCost.threadId}) released in finally block.`);
            }
             if (connectionForTransaction) {
                connectionForTransaction.release();
                console.log(`[checkCredits M] User ${userId}: Transaction connection (ID ${connectionForTransaction.threadId}) released in finally block.`);
            }
        }
    };
}

module.exports = {
    checkAndDeductCredits, // 导出工厂函数
    // DAILY_FREE_CREDITS_AMOUNT, // 不再导出这个常量
    // FREE_GENERATIONS_PER_DAY, // 不再导出
    // GENERATION_COST 
}; 