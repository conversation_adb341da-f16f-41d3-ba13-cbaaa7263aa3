const express = require('express');
const { authenticateToken } = require('../utils/auth');
const pool = require('../db/pool');

const router = express.Router();

/**
 * 扣除用户积分的函数
 * @param {Object} req - Express请求对象
 * @param {string} featureKey - 功能标识符
 * @param {number} [amount] - 可选的积分数量，如果未提供则从数据库获取
 * @returns {Promise<Object>} - 包含扣除结果的对象
 */
async function deductCredits(req, featureKey, amount = null) {
    const userId = req.user.id;

    console.log(`[Credits Deduct] User ${userId} requested to deduct credits for ${featureKey}.`);

    if (!userId) {
        console.warn('[Credits Deduct] Unauthorized access attempt.');
        return { success: false, error: '用户未认证' };
    }

    if (!featureKey) {
        console.warn('[Credits Deduct] Missing feature key.');
        return { success: false, error: '缺少功能标识符' };
    }

    let connectionForCost = null;
    let connectionForTransaction = null;
    let cost = null;
    let useDailyFree = true;

    try {
        // 获取功能成本和免费额度使用设置
        connectionForCost = await pool.getConnection();
        console.log(`[Credits Deduct] User ${userId}, Feature ${featureKey}: Fetching cost and free credit policy.`);
        
        const [[featureConfigData]] = await connectionForCost.query(
            'SELECT cost, can_use_free_credits FROM feature_costs WHERE feature_key = ?',
            [featureKey]
        );
        
        const [[dailyAllowanceData]] = await connectionForCost.query(
            'SELECT cost FROM feature_costs WHERE feature_key = ?',
            ['DAILY_FREE_CREDIT_ALLOWANCE']
        );
        
        connectionForCost.release();
        connectionForCost = null;

        if (!featureConfigData || featureConfigData.cost === undefined || featureConfigData.cost === null) {
            console.error(`[Credits Deduct] Error: Cost not found for featureKey: ${featureKey}`);
            return { success: false, error: `未找到功能 ${featureKey} 的成本配置` };
        }

        cost = amount || parseInt(featureConfigData.cost, 10);
        useDailyFree = featureConfigData.can_use_free_credits === 1;

        if (isNaN(cost) || cost < 0) {
            console.error(`[Credits Deduct] Error: Invalid cost (${cost}) for featureKey: ${featureKey}`);
            return { success: false, error: `功能 ${featureKey} 的成本配置无效` };
        }

        if (!dailyAllowanceData || dailyAllowanceData.cost === undefined || dailyAllowanceData.cost === null) {
            console.error(`[Credits Deduct] Error: Daily free credit allowance not found.`);
            return { success: false, error: '每日免费额度配置缺失' };
        }

        const DAILY_FREE_CREDITS_AMOUNT = parseInt(dailyAllowanceData.cost, 10);
        if (isNaN(DAILY_FREE_CREDITS_AMOUNT) || DAILY_FREE_CREDITS_AMOUNT < 0) {
            console.error(`[Credits Deduct] Error: Invalid daily free credit allowance (${DAILY_FREE_CREDITS_AMOUNT}).`);
            return { success: false, error: '每日免费额度配置无效' };
        }

        // 检查和扣除积分
        connectionForTransaction = await pool.getConnection();
        await connectionForTransaction.beginTransaction();

        // 获取用户信息
        const [users] = await connectionForTransaction.query(
            'SELECT id, credits, daily_generations_used, last_generation_date FROM users WHERE id = ? FOR UPDATE',
            [userId]
        );

        if (users.length === 0) {
            await connectionForTransaction.rollback();
            connectionForTransaction.release();
            return { success: false, error: '找不到用户' };
        }

        let user = users[0];
        const today = new Date().toISOString().slice(0, 10);
        let cumulativeCredits = user.credits || 0;
        let dailyCreditsUsed = user.daily_generations_used || 0;
        const lastResetDateStr = user.last_generation_date ? user.last_generation_date.toISOString().slice(0, 10) : null;
        let remainingDailyFreeCredits = 0;

        // 检查并执行每日重置
        if (lastResetDateStr !== today) {
            dailyCreditsUsed = 0;
            await connectionForTransaction.query(
                'UPDATE users SET daily_generations_used = 0, last_generation_date = ? WHERE id = ?',
                [today, userId]
            );
            remainingDailyFreeCredits = DAILY_FREE_CREDITS_AMOUNT;
        } else {
            remainingDailyFreeCredits = Math.max(0, DAILY_FREE_CREDITS_AMOUNT - dailyCreditsUsed);
        }

        // 判断积分是否足够并执行扣除
        if (useDailyFree) {
            if (remainingDailyFreeCredits >= cost) {
                const newDailyUsed = dailyCreditsUsed + cost;
                await connectionForTransaction.query(
                    'UPDATE users SET daily_generations_used = ? WHERE id = ?',
                    [newDailyUsed, userId]
                );
                await connectionForTransaction.query(
                    'INSERT INTO credit_transactions (user_id, change_amount, balance_after, reason, notes) VALUES (?, ?, ?, ?, ?)',
                    [userId, 0, cumulativeCredits, 'usage_free', `(${featureKey}) 消耗每日免费额度 ${cost}`]
                );
            } else if (remainingDailyFreeCredits + cumulativeCredits >= cost) {
                const neededFromCumulative = cost - remainingDailyFreeCredits;
                const newCumulativeCredits = cumulativeCredits - neededFromCumulative;
                const newDailyUsed = DAILY_FREE_CREDITS_AMOUNT;
                await connectionForTransaction.query(
                    'UPDATE users SET credits = ?, daily_generations_used = ? WHERE id = ?',
                    [newCumulativeCredits, newDailyUsed, userId]
                );
                await connectionForTransaction.query(
                    'INSERT INTO credit_transactions (user_id, change_amount, balance_after, reason, notes) VALUES (?, ?, ?, ?, ?)',
                    [userId, -neededFromCumulative, newCumulativeCredits, 'usage_paid', `(${featureKey}) 每日额度用尽，消耗累积积分 ${neededFromCumulative}`]
                );
                cumulativeCredits = newCumulativeCredits;
            } else {
                await connectionForTransaction.rollback();
                return { 
                    success: false, 
                    error: '积分不足，请充值后重试。', 
                    required: cost, 
                    current_daily: remainingDailyFreeCredits, 
                    current_cumulative: cumulativeCredits 
                };
            }
        } else {
            if (cumulativeCredits >= cost) {
                const newCumulativeCredits = cumulativeCredits - cost;
                await connectionForTransaction.query(
                    'UPDATE users SET credits = ? WHERE id = ?',
                    [newCumulativeCredits, userId]
                );
                await connectionForTransaction.query(
                    'INSERT INTO credit_transactions (user_id, change_amount, balance_after, reason, notes) VALUES (?, ?, ?, ?, ?)',
                    [userId, -cost, newCumulativeCredits, 'usage_paid_direct', `(${featureKey}) 直接消耗累积积分 ${cost} (禁用每日额度)`]
                );
                cumulativeCredits = newCumulativeCredits;
            } else {
                await connectionForTransaction.rollback();
                return { 
                    success: false, 
                    error: '账户积分不足，请充值后重试。', 
                    required: cost, 
                    current_cumulative: cumulativeCredits 
                };
            }
        }

        // 提交事务
        await connectionForTransaction.commit();

        // 获取更新后的信息
        const [[updatedUserData]] = await connectionForTransaction.query('SELECT daily_generations_used FROM users WHERE id = ?', [userId]);
        
        return {
            success: true,
            message: `成功扣除${cost}积分`,
            cumulativeCredits: cumulativeCredits,
            remainingDailyFreeCredits: Math.max(0, DAILY_FREE_CREDITS_AMOUNT - (updatedUserData ? updatedUserData.daily_generations_used : 0))
        };

    } catch (error) {
        console.error(`[Credits Deduct] User ${userId}: Error during credit deduction for feature ${featureKey}:`, error);
        if (connectionForTransaction) {
            try { await connectionForTransaction.rollback(); } catch (rbError) { console.error(`[Credits Deduct] Rollback failed:`, rbError); }
        }
        return { success: false, error: '扣除积分时发生内部错误' };
    } finally {
        if (connectionForCost) {
            connectionForCost.release();
        }
        if (connectionForTransaction) {
            connectionForTransaction.release();
        }
    }
}

// POST /api/credits/deduct - 扣除用户积分
router.post('/deduct', authenticateToken, async (req, res) => {
    const userId = req.user.id;
    const { featureKey, amount } = req.body;

    if (!userId || !featureKey) {
        return res.status(400).json({ 
            success: false, 
            error: !userId ? '用户未认证' : '缺少功能标识符' 
        });
    }

    // 调用deductCredits函数
    const result = await deductCredits(req, featureKey, amount);
    
    // 根据结果返回响应
    if (!result.success) {
        return res.status(result.error.includes('积分不足') ? 402 : 500).json(result);
    }
    
    return res.json(result);
});

// 同时导出路由和函数
module.exports = router;
module.exports.deductCredits = deductCredits;
