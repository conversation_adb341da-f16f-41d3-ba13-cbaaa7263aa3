const jwt = require('jsonwebtoken');
const pool = require('../db/pool');

/**
 * SSO认证工具类
 * 提供跨系统的用户认证和积分管理
 */
class SSOAuth {
    
    /**
     * 生成SSO Token
     * @param {Object} user 用户信息
     * @param {String} targetSystem 目标系统 (admin|jaaz)
     * @returns {String} SSO Token
     */
    static generateSSOToken(user, targetSystem = 'jaaz') {
        const payload = {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            nickname: user.nickname,
            avatar_url: user.avatar_url,
            target_system: targetSystem,
            sso: true,
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24小时有效期
        };
        
        return jwt.sign(payload, process.env.JWT_SECRET);
    }
    
    /**
     * 验证SSO Token
     * @param {String} token SSO Token
     * @returns {Object|null} 用户信息或null
     */
    static async verifySSOToken(token) {
        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            
            // 验证是否为SSO token
            if (!decoded.sso) {
                throw new Error('不是有效的SSO Token');
            }
            
            // 从数据库获取最新用户信息
            const connection = await pool.getConnection();
            try {
                const [users] = await connection.query(
                    'SELECT id, username, email, role, nickname, avatar_url, is_activated FROM users WHERE id = ?',
                    [decoded.id]
                );
                
                if (users.length === 0) {
                    throw new Error('用户不存在');
                }
                
                const user = users[0];
                if (user.is_activated === 0) {
                    throw new Error('用户未激活');
                }
                
                return user;
            } finally {
                connection.release();
            }
        } catch (error) {
            console.error('SSO Token验证失败:', error);
            return null;
        }
    }
    
    /**
     * 获取用户积分信息
     * @param {Number} userId 用户ID
     * @returns {Object} 积分信息
     */
    static async getUserCredits(userId) {
        const connection = await pool.getConnection();
        try {
            const [rows] = await connection.query(
                'SELECT credits FROM users WHERE id = ?',
                [userId]
            );
            
            if (rows.length === 0) {
                throw new Error('用户不存在');
            }
            
            return {
                credits: parseInt(rows[0].credits || 0, 10)
            };
        } finally {
            connection.release();
        }
    }
    
    /**
     * 扣除用户积分 (采用乐观锁和重试机制)
     * @param {Number} userId 用户ID
     * @param {Number} amount 扣除数量
     * @param {String} reason 扣除原因
     * @param {Number} retries 重试次数
     * @returns {Boolean} 是否成功
     */
    static async deductCredits(userId, amount, reason = '', retries = 3) {
        for (let i = 0; i < retries; i++) {
        const connection = await pool.getConnection();
        try {
                // 将检查和更新合并为一条原子操作
                const [result] = await connection.query(
                    'UPDATE users SET credits = credits - ? WHERE id = ? AND credits >= ?',
                    [amount, userId, amount]
                );
            
                // 如果更新影响的行数为0，说明积分不足或用户不存在
                if (result.affectedRows === 0) {
                    // 再次查询以确定具体原因
                    const [users] = await connection.query('SELECT credits FROM users WHERE id = ?', [userId]);
                    if (users.length === 0) {
                throw new Error('用户不存在');
            }
                    if (users[0].credits < amount) {
                throw new Error('积分余额不足');
            }
                    // 如果代码能执行到这里，可能是其他并发问题，进行重试
                    throw new Error('更新积分为0行，但检查显示积分足够，可能存在并发冲突，准备重试...');
                }

                // 记录积分变动日志（如果需要且表存在）
            try {
                await connection.query(
                    'INSERT INTO credit_logs (user_id, amount, type, reason, created_at) VALUES (?, ?, ?, ?, NOW())',
                    [userId, -amount, 'deduct', reason]
                );
            } catch (logError) {
                console.log('积分日志记录失败（可能表不存在）:', logError.message);
            }
            
                console.log(`用户 ${userId} 成功扣除 ${amount} 积分。`);
                return true; // 成功，跳出循环

        } catch (error) {
                console.error(`第 ${i + 1} 次扣除积分失败:`, error.message);
                // 只对特定的"锁等待超时"错误进行重试
                if (error.code === 'ER_LOCK_WAIT_TIMEOUT' && i < retries - 1) {
                    console.log('等待100ms后重试...');
                    await new Promise(res => setTimeout(res, 100)); // 等待100毫秒
                    continue; // 继续下一次循环
                }
                // 对于其他错误或最后一次重试失败，则直接抛出
            throw error;
        } finally {
            connection.release();
        }
        }
        // 如果循环结束仍未成功（理论上不应发生，因为成功会return，失败会throw）
        throw new Error('积分扣除失败，已达最大重试次数。');
    }
    
    /**
     * 增加用户积分
     * @param {Number} userId 用户ID
     * @param {Number} amount 增加数量
     * @param {String} reason 增加原因
     * @returns {Boolean} 是否成功
     */
    static async addCredits(userId, amount, reason = '') {
        const connection = await pool.getConnection();
        try {
            await connection.beginTransaction();
            
            // 增加积分
            await connection.query(
                'UPDATE users SET credits = credits + ? WHERE id = ?',
                [amount, userId]
            );
            
            // 记录积分变动日志
            try {
                await connection.query(
                    'INSERT INTO credit_logs (user_id, amount, type, reason, created_at) VALUES (?, ?, ?, ?, NOW())',
                    [userId, amount, 'add', reason]
                );
            } catch (logError) {
                console.log('积分日志记录失败（可能表不存在）:', logError.message);
            }
            
            await connection.commit();
            return true;
        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    }
}

module.exports = SSOAuth; 