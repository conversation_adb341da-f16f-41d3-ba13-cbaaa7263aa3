    // prompt-examples/server/utils/mailer.js
    const nodemailer = require('nodemailer');

    let transporter;

    // 检查邮件配置环境变量
    const requiredMailEnv = ['MAIL_HOST', 'MAIL_PORT', 'MAIL_USER', 'MAIL_PASS', 'MAIL_FROM'];
    const missingMailEnv = requiredMailEnv.filter(key => !process.env[key]);

    if (missingMailEnv.length > 0) {
        console.warn(`警告: 缺少以下邮件配置环境变量: ${missingMailEnv.join(', ')}。邮件功能可能无法正常工作。`);
        // 可以选择创建一个空的 transporter 或在需要时再创建，或者直接禁用邮件功能
        transporter = null; // 或者一个模拟对象
    } else {
        try {
            const mailConfig = {
                host: process.env.MAIL_HOST,
                port: parseInt(process.env.MAIL_PORT), // 确保是数字
                // secure 通常基于端口判断，但可以显式设置
                secure: parseInt(process.env.MAIL_PORT) === 465, // true for 465, false for other ports like 587
                auth: {
                    user: process.env.MAIL_USER,
                    pass: process.env.MAIL_PASS,
                },
                // 可选：添加 TLS 配置 (根据需要调整)
                tls: {
                    // do not fail on invalid certs (在某些情况下需要, 生产环境谨慎)
                    rejectUnauthorized: process.env.MAIL_TLS_REJECT_UNAUTHORIZED === 'true', // 默认 false
                    // ciphers:'SSLv3' // 通常不需要强制指定
                },
                // 可选：设置超时
                connectionTimeout: 10000, // 10秒连接超时
                greetingTimeout: 5000, // 5秒问候超时
                socketTimeout: 15000, // 15秒socket超时
            };

            transporter = nodemailer.createTransport(mailConfig);

            // 验证配置 (异步)
            transporter.verify((error, success) => {
                if (error) {
                    console.error('邮件发送器配置验证失败 (来自 utils/mailer.js):', error);
                    // 可以选择将 transporter 设为 null 或保留以便稍后重试
                    // transporter = null;
                } else {
                    console.log('邮件发送器配置验证成功，准备发送邮件 (来自 utils/mailer.js)');
                }
            });

            console.log('邮件发送器已创建 (来自 utils/mailer.js)');

        } catch(configError) {
             console.error('创建邮件发送器时发生错误 (来自 utils/mailer.js):', configError);
             transporter = null; // 创建失败则设为 null
        }
    }


    module.exports = transporter; // 导出 transporter 实例或 null