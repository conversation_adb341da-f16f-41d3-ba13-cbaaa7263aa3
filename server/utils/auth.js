    // prompt-examples/server/utils/auth.js
    const jwt = require('jsonwebtoken');
    const bcrypt = require('bcrypt');
    const pool = require('../db/pool'); // 从新的 db 模块导入 pool
    const crypto = require('crypto'); // 添加crypto库，用于生成随机令牌

    const JWT_SECRET = process.env.JWT_SECRET;
    const SALT_ROUNDS = 10;

    if (!JWT_SECRET) {
        console.error('错误: JWT_SECRET 环境变量未设置! 请在 .env 文件中添加 JWT_SECRET (来自 utils/auth.js).');
        // 在开发环境中生成一个临时的，但在生产中必须设置
        if (process.env.NODE_ENV !== 'production') {
            console.warn('警告: 正在使用临时的 JWT_SECRET。请务必在 .env 文件中设置一个安全的密钥! (来自 utils/auth.js)');
            // JWT_SECRET = require('crypto').randomBytes(32).toString('hex'); // 取消注释以自动生成
        } else {
             process.exit(1); // 生产环境中强制退出
        }
    } else {
        console.log('JWT_SECRET 已加载 (来自 utils/auth.js)');
    }


    const authenticateToken = async (req, res, next) => {
      try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (!token) {
          console.log('[Auth] 拒绝访问: 未提供 Token');
          return res.status(401).json({ error: '未提供认证令牌' });
        }

        console.log('[Auth] 收到 Token (前缀):', token.substring(0, 10) + '...');
        
        try {
          // 首先尝试作为普通JWT Token解码
          const decoded = jwt.verify(token, JWT_SECRET);
          console.log('[Auth] Token 解码成功, 用户 ID:', decoded.userId || decoded.id);

          // 检查是否为SSO Token
          if (decoded.sso && decoded.target_system) {
            console.log('[Auth] 检测到SSO Token，验证SSO用户');
            // 导入SSO模块
            const SSOAuth = require('./sso');
            const user = await SSOAuth.verifySSOToken(token);
            
            if (!user) {
              console.log('[Auth] SSO Token验证失败');
              return res.status(401).json({ error: 'SSO令牌无效或已过期' });
            }
            
            req.user = user;
            console.log(`[Auth] SSO用户 ${req.user.username} (ID: ${req.user.id}) 认证通过`);
            return next();
          }

          // 普通Admin系统Token，继续原有逻辑
          const userId = decoded.userId;
          
          // 从数据库获取最新的用户信息
          const connection = await pool.getConnection();
          const [users] = await connection.query(
              'SELECT id, username, role, nickname, avatar_url, email, is_activated FROM users WHERE id = ?',
              [userId]
          );
          connection.release();

          if (users.length === 0) {
            console.log(`[Auth] 拒绝访问: 用户 ID ${userId} 在数据库中未找到`);
            return res.status(401).json({ error: '用户不存在或已被删除' });
          }

          req.user = users[0];
          console.log(`[Auth] 用户 ${req.user.username} (ID: ${req.user.id}) 认证通过`);
          next();
          
        } catch (jwtError) {
          console.error('[Auth] Token 验证错误:', jwtError.name, jwtError.message);
          if (jwtError.name === 'TokenExpiredError') {
               return res.status(401).json({ error: '认证令牌已过期', code: 'TOKEN_EXPIRED' });
          }
          if (jwtError.name === 'JsonWebTokenError') {
               return res.status(403).json({ error: '无效的认证令牌', code: 'INVALID_TOKEN' });
          }
           // 其他未知错误
          return res.status(500).json({ error: '认证时发生内部错误', code: 'AUTH_INTERNAL_ERROR' });
        }
      } catch (error) {
        console.error('[Auth] 认证中间件错误:', error);
        return res.status(500).json({ error: '认证时发生内部错误', code: 'AUTH_INTERNAL_ERROR' });
      }
    };

    const requireAdmin = (req, res, next) => {
        if (!req.user) {
             // 理论上 authenticateToken 应该先运行
            console.warn('[Auth] requireAdmin 在 authenticateToken 之前被调用或失败');
             return res.status(401).json({ error: '需要先进行认证' });
        }
        if (req.user.role !== 'admin') {
            console.log(`[Auth] 权限拒绝: 用户 ${req.user.username} (ID: ${req.user.id}, Role: ${req.user.role}) 尝试访问管理员资源 ${req.originalUrl}`);
            return res.status(403).json({ error: '需要管理员权限' });
        }
        console.log(`[Auth] 管理员 ${req.user.username} (ID: ${req.user.id}) 权限验证通过`);
        next();
    };
    
    // 新增：检查账户激活状态
    const checkAccountActivation = (req, res, next) => {
        if (!req.user) {
            console.warn('[Auth] checkAccountActivation 在 authenticateToken 之前被调用或失败');
            return res.status(401).json({ error: '需要先进行认证' });
        }
        
        // 如果用户未激活，返回错误
        if (req.user.is_activated === 0) {
            console.log(`[Auth] 账户未激活: 用户 ${req.user.username} (ID: ${req.user.id}) 尝试访问需要激活的资源`);
            return res.status(403).json({ error: '账户尚未激活，请先激活您的账户', code: 'ACCOUNT_NOT_ACTIVATED' });
        }
        
        console.log(`[Auth] 账户已激活: 用户 ${req.user.username} (ID: ${req.user.id})`);
        next();
    };

    // 检查是否是第一个用户 (现在需要传入 pool)
    async function isFirstUser(dbPool) {
      let connection;
      try {
        connection = await dbPool.getConnection();
        const [users] = await connection.query('SELECT COUNT(*) as count FROM users');
        const isFirst = users[0].count === 0;
        console.log(`[Auth] 检查是否为第一个用户: ${isFirst}`);
        return isFirst;
      } catch(error) {
          console.error("[Auth] 检查第一个用户时出错:", error);
          throw error; // 将错误向上抛出
      }
      finally {
        if (connection) connection.release();
      }
    }

    // 生成 JWT token
    function generateToken(user) {
        // Payload 仅包含必要且不经常变动的信息
        const payload = {
          userId: user.id,
          username: user.username, // 或 email，取决于你的登录标识
          role: user.role
        };
        const token = jwt.sign(
            payload,
            JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRES_IN || '30d' } // 从环境变量读取过期时间
        );
        console.log(`[Auth] 为用户 ${user.username || user.id} 生成 Token (有效期: ${process.env.JWT_EXPIRES_IN || '30d'})`);
        return token;
    }

    // 密码 Hashing 函数 (也可以放在这里)
    async function hashPassword(password) {
        const hashedPassword = await bcrypt.hash(password, SALT_ROUNDS);
        console.log('[Auth] 密码已 Hashed (来自 utils/auth.js)');
        return hashedPassword;
    }

    // 密码比较函数 (也可以放在这里)
    async function comparePassword(plainPassword, hashedPassword) {
        const isMatch = await bcrypt.compare(plainPassword, hashedPassword);
        console.log('[Auth] 密码比较结果:', isMatch, '(来自 utils/auth.js)');
        return isMatch;
    }
    
    // 新增：生成激活令牌
    function generateActivationToken() {
        return crypto.randomBytes(32).toString('hex');
    }
    
    // 新增：创建用户激活记录并发送激活邮件
    async function createActivationToken(userId, email, mailer) {
        const token = generateActivationToken();
        const hashedToken = crypto.createHash('sha256').update(token).digest('hex');
        
        // 设置过期时间为24小时后
        const expires = new Date();
        expires.setHours(expires.getHours() + 24);
        
        let connection;
        try {
            connection = await pool.getConnection();
            
            // 删除该用户之前的激活记录（如果有）
            await connection.query('DELETE FROM account_activations WHERE user_id = ?', [userId]);
            
            // 插入新的激活记录
            await connection.query(
                'INSERT INTO account_activations (user_id, token, expires_at) VALUES (?, ?, ?)',
                [userId, hashedToken, expires]
            );
            
            connection.release();
            
            // 如果提供了邮件发送器，发送激活邮件
            if (mailer) {
                // 使用正确的完整URL，包含prompt-examples路径前缀
                const activationLink = `https://www.yzycolour.top/prompt-examples/admin/login.html?activation_token=${token}`;
                
                await mailer.sendMail({
                    from: process.env.MAIL_FROM,
                    to: email,
                    subject: '激活您的账户',
                    html: `
                        <h1>欢迎加入提示词案例管理系统</h1>
                        <p>请点击下方链接激活您的账户：</p>
                        <a href="${activationLink}" style="display: inline-block; padding: 10px 20px; background-color: #4A90E2; color: white; text-decoration: none; border-radius: 5px;">激活账户</a>
                        <p>或复制以下链接到浏览器地址栏：</p>
                        <p>${activationLink}</p>
                        <p>此链接将在24小时后失效。</p>
                        <p>如果您没有注册账户，请忽略此邮件。</p>
                    `
                });
                
                console.log(`[Auth] 已向 ${email} 发送激活邮件，链接: ${activationLink}`);
            } else {
                console.warn(`[Auth] 邮件发送器未配置，无法发送激活邮件到 ${email}`);
            }
            
            return token;
        } catch (error) {
            if (connection) connection.release();
            console.error('[Auth] 创建激活令牌错误:', error);
            throw error;
        }
    }
    
    // 新增：验证激活令牌
    async function verifyActivationToken(token) {
        const hashedToken = crypto.createHash('sha256').update(token).digest('hex');
        let connection;
        
        try {
            connection = await pool.getConnection();
            
            // 查询激活记录，确保未过期
            const [records] = await connection.query(
                'SELECT * FROM account_activations WHERE token = ? AND expires_at > NOW()',
                [hashedToken]
            );
            
            if (records.length === 0) {
                console.log('[Auth] 激活令牌无效或已过期');
                return { success: false, message: '激活链接无效或已过期，请重新注册或联系管理员' };
            }
            
            const activationRecord = records[0];
            
            // 查询用户记录
            const [users] = await connection.query(
                'SELECT * FROM users WHERE id = ?',
                [activationRecord.user_id]
            );
            
            if (users.length === 0) {
                console.log(`[Auth] 用户ID ${activationRecord.user_id} 不存在`);
                return { success: false, message: '用户不存在，请重新注册' };
            }
            
            const user = users[0];
            
            // 如果用户已经激活，直接返回成功
            if (user.is_activated) {
                console.log(`[Auth] 用户ID ${user.id} 已经激活`);
                return { success: true, message: '账户已经激活', email: user.email };
            }
            
            // 激活用户
            await connection.query(
                'UPDATE users SET is_activated = 1 WHERE id = ?',
                [user.id]
            );
            
            // 删除激活记录
            await connection.query(
                'DELETE FROM account_activations WHERE user_id = ?',
                [user.id]
            );
            
            connection.release();
            
            console.log(`[Auth] 用户ID ${user.id} 账户已成功激活`);
            return { success: true, message: '账户已成功激活', email: user.email };
        } catch (error) {
            if (connection) connection.release();
            console.error('[Auth] 验证激活令牌错误:', error);
            throw error;
        }
    }
    
    // 新增：重新发送激活邮件
    async function resendActivationEmail(email, mailer) {
        let connection;
        
        try {
            connection = await pool.getConnection();
            
            // 查询用户记录
            const [users] = await connection.query(
                'SELECT * FROM users WHERE email = ?',
                [email]
            );
            
            if (users.length === 0) {
                console.log(`[Auth] 邮箱 ${email} 对应的用户不存在`);
                return { success: false, message: '用户不存在' };
            }
            
            const user = users[0];
            
            // 如果用户已经激活，直接返回成功
            if (user.is_activated) {
                console.log(`[Auth] 用户ID ${user.id} 已经激活`);
                return { success: true, message: '账户已经激活' };
            }
            
            // 创建新的激活令牌并发送邮件
            await createActivationToken(user.id, user.email, mailer);
            
            connection.release();
            
            console.log(`[Auth] 已重新发送激活邮件到 ${email}`);
            return { success: true, message: '激活邮件已重新发送' };
        } catch (error) {
            if (connection) connection.release();
            console.error('[Auth] 重发激活邮件错误:', error);
            throw error;
        }
    }

    module.exports = {
        authenticateToken,
        requireAdmin,
        checkAccountActivation,
        isFirstUser,
        generateToken,
        hashPassword,
        comparePassword,
        generateActivationToken,
        createActivationToken,
        verifyActivationToken,
        resendActivationEmail
    };