/**
 * Flux Kontext Pro 图片处理服务
 * 负责与Flux API交互并存储处理结果
 */

const fetch = require('node-fetch');
const axios = require('axios'); // 引入axios用于与ComfyUI交互
const { v4: uuidv4 } = require('uuid');
const db = require('./db/pool'); // 修正数据库连接模块路径
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// Flux API配置
const FLUX_API_URL = 'https://api.us1.bfl.ai/v1/flux-kontext-pro';
const FLUX_EXPAND_API_URL = 'https://api.us1.bfl.ai/v1/flux-pro-1.0-expand';
const FLUX_API_KEY = process.env.FLUX_API_KEY; // 从环境变量获取API密钥

// Replicate API配置
const REPLICATE_API_URL = 'https://api.replicate.com/v1/predictions';
const REPLICATE_API_KEY = process.env.REPLICATE_API_KEY || '****************************************'; // 从环境变量获取API密钥
const REPLICATE_FLUX_KONTEXT_PRO_MODEL = 'black-forest-labs/flux-kontext-pro'; // Replicate上的Flux Kontext Pro模型ID
const REPLICATE_FLUX_KONTEXT_MAX_MODEL = 'black-forest-labs/flux-kontext-max'; // Replicate上的Flux Kontext Max模型ID

// ComfyUI配置
const COMFYUI_BASE_URL = process.env.COMFYUI_GPU_URL;

const COMFYUI_LOAD_IMAGE_NODE_ID_FLUX = "189"; // 对应 'LoadImage'
const COMFYUI_PROMPT_NODE_ID_FLUX = "197"; // 对应 'DeepTranslatorTextNode'
const COMFYUI_SAVE_IMAGE_NODE_ID_FLUX = "136"; // 对应 'SaveImage'

// 添加日志函数
const logFlux = (message, data = null) => {
    const logMsg = `[Flux Service] ${message}`;
    if (data) {
        console.log(logMsg, typeof data === 'object' ? JSON.stringify(data) : data);
    } else {
        console.log(logMsg);
    }
}

// 启动时检查API密钥
logFlux(`初始化服务，Flux API密钥状态: ${FLUX_API_KEY ? '已配置' : '未配置'}`);
if (!FLUX_API_KEY) {
    logFlux('警告: 未配置Flux API密钥，部分功能将无法正常工作');
}

logFlux(`Replicate API密钥状态: ${REPLICATE_API_KEY ? '已配置' : '未配置'}`);
if (!REPLICATE_API_KEY) {
    logFlux('警告: 未配置Replicate API密钥，Replicate功能将无法正常工作');
}

/**
 * 恢复用户积分
 * @param {Number} userId - 用户ID
 * @param {Number} credits - 要恢复的积分数量
 * @param {String} reason - 恢复原因
 * @returns {Promise<void>}
 */
async function restoreUserCredits(userId, credits, reason = 'flux_api_error_refund') {
    logFlux(`尝试恢复用户 ${userId} 的 ${credits} 积分，原因: ${reason}`);
    try {
        // 导入积分服务
        const creditService = require('./services/credit-service');
        
        // 恢复积分
        await creditService.addCredits(userId, credits, reason);
        logFlux(`成功恢复用户 ${userId} 的 ${credits} 积分`);
    } catch (error) {
        logFlux(`恢复用户积分失败: ${error.message}`, error.stack);
    }
}

/**
 * 启动Flux图片处理任务
 * @param {Object} data - 处理参数
 * @param {String} data.mode - 处理模式 (normal 或 expand)
 * @param {String} data.prompt - 提示词
 * @param {String} [data.input_image] - 可选的Base64编码图片 (普通模式)
 * @param {String} [data.image] - 必选的Base64编码图片 (扩图模式)
 * @param {String} [data.aspect_ratio] - 可选的宽高比 (普通模式)
 * @param {String} [data.output_format] - 可选的输出格式
 * @param {Number} [data.safety_tolerance] - 可选的安全级别
 * @param {Boolean} [data.prompt_upsampling] - 是否启用提示词优化
 * @param {Number} [data.seed] - 可选的随机种子
 * @param {Number} [data.top] - 顶部扩展像素数 (扩图模式)
 * @param {Number} [data.bottom] - 底部扩展像素数 (扩图模式)
 * @param {Number} [data.left] - 左侧扩展像素数 (扩图模式)
 * @param {Number} [data.right] - 右侧扩展像素数 (扩图模式)
 * @param {Number} [data.steps] - 扩图步数 (扩图模式)
 * @param {Number} [data.guidance] - 引导强度 (扩图模式)
 * @param {Number} userId - 用户ID
 * @returns {Promise<Object>} 任务信息
 */
async function startFluxProcessing(data, userId) {
    const { workflow } = data;

    if (workflow === 'comfy_flux_kontext') {
        return await startComfyFluxKontextProcessing(data, userId);
    } else if (workflow === 'flux_kontext_pro') {
        return await startFluxNormalProcessing(data, userId);
    } else if (data.mode === 'expand') {
        return await startFluxExpandProcessing(data, userId);
    } else {
        // Fallback or default behavior if workflow is not specified or unknown
        return await startFluxNormalProcessing(data, userId);
    }
}

/**
 * 启动普通Flux图片处理任务
 * @param {Object} data - 处理参数
 * @param {Number} userId - 用户ID
 * @returns {Promise<Object>} 任务信息
 */
async function startFluxNormalProcessing(data, userId) {
    logFlux(`开始普通处理任务，用户ID: ${userId}`);
    
    // 记录积分成本，用于可能的退款
    const creditCost = 2; // 普通模式消耗2积分
    
    // 验证必要参数
    if (!data.prompt) {
        logFlux('错误: 提示词不能为空');
        throw new Error('提示词不能为空');
    }

    // 准备请求数据
    const requestData = {
        prompt: data.prompt,
        aspect_ratio: data.aspect_ratio || '1:1',
        output_format: data.output_format || 'png',
        safety_tolerance: data.safety_tolerance || 2,
        prompt_upsampling: !!data.prompt_upsampling
    };

    // 添加可选参数
    if (data.seed) {
        requestData.seed = parseInt(data.seed);
    }

    // 添加图片（如果有）
    if (data.input_image) {
        requestData.input_image = data.input_image;
        logFlux('请求包含图片数据');
    } else {
        logFlux('请求不包含图片数据，使用纯文本生成');
    }

    logFlux(`准备发送请求到Flux API，提示词: "${data.prompt.substring(0, 30)}${data.prompt.length > 30 ? '...' : ''}"`);
    logFlux('请求参数:', { 
        aspect_ratio: requestData.aspect_ratio,
        output_format: requestData.output_format,
        safety_tolerance: requestData.safety_tolerance,
        prompt_upsampling: requestData.prompt_upsampling,
        has_seed: !!requestData.seed,
        has_image: !!requestData.input_image
    });

    try {
    // 发送请求到Flux API
    logFlux(`发送请求到: ${FLUX_API_URL}`);
    const response = await fetch(FLUX_API_URL, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'x-key': FLUX_API_KEY
        },
        body: JSON.stringify(requestData)
    });

    logFlux(`收到响应，状态码: ${response.status}`);
    
    if (!response.ok) {
        const errorData = await response.json();
        logFlux(`API错误响应:`, errorData);
            
            // 特别处理402错误（积分不足）
            if (response.status === 402) {
                // 尝试恢复用户积分
                await restoreUserCredits(userId, creditCost, 'flux_api_credits_insufficient');
                throw new Error('Flux API积分不足，请联系管理员充值API账户');
            }
            
        throw new Error(errorData.detail || `HTTP错误: ${response.status}`);
    }

    // 解析响应
    const responseData = await response.json();
    logFlux(`API成功响应:`, responseData);
    
    // 检查响应格式
    if (!responseData.id) {
        logFlux('错误: API响应格式无效，缺少任务ID', responseData);
        throw new Error('无效的API响应');
    }

    // 生成内部任务ID
    const internalTaskId = uuidv4();
    logFlux(`生成内部任务ID: ${internalTaskId}, Flux任务ID: ${responseData.id}`);
    
    // 将任务信息存入数据库
    logFlux(`将任务信息存入数据库, 用户ID: ${userId}, 任务ID: ${internalTaskId}`);
    const [insertResult] = await db.query(
        `INSERT INTO flux_tasks 
        (flux_task_id, user_id, prompt, polling_url, status, created_at, params, task_type) 
        VALUES (?, ?, ?, ?, ?, NOW(), ?, ?)`,
        [
            responseData.id,
            userId,
            data.prompt,
            `https://api.us1.bfl.ai/v1/get_result?id=${responseData.id}`, // 存储正确的轮询URL
            'pending',
            JSON.stringify(requestData),
            'normal'
        ]
    );
    
    // 获取数据库自动生成的ID
    const dbTaskId = insertResult.insertId;
    logFlux(`任务信息已存入数据库，数据库生成的ID: ${dbTaskId}`);

    // 启动后台轮询任务，使用数据库生成的ID
    logFlux(`启动后台轮询任务, 任务ID: ${responseData.id}`);
    startPollingTask(dbTaskId, responseData.id);
    
    // 返回数据库生成的任务ID和状态
    return {
        taskId: dbTaskId,
        status: 'pending'
    };
    } catch (error) {
        logFlux(`启动Flux处理失败: ${error.message}`, error.stack);
        throw error;
    }
}

/**
 * 启动Flux扩图处理任务
 * @param {Object} data - 处理参数
 * @param {Number} userId - 用户ID
 * @returns {Promise<Object>} 任务信息
 */
async function startFluxExpandProcessing(data, userId) {
    logFlux(`开始扩图处理任务，用户ID: ${userId}`);
    
    // 记录积分成本，用于可能的退款
    const creditCost = 3; // 扩图模式消耗3积分
    
    // 验证必要参数
    if (!data.image) {
        logFlux('错误: 扩图模式下图片不能为空');
        throw new Error('扩图模式下图片不能为空');
    }
    
    // 验证至少有一个方向的扩展
    const top = parseInt(data.top) || 0;
    const bottom = parseInt(data.bottom) || 0;
    const left = parseInt(data.left) || 0;
    const right = parseInt(data.right) || 0;
    
    if (top === 0 && bottom === 0 && left === 0 && right === 0) {
        logFlux('错误: 请至少设置一个方向的扩展像素数');
        throw new Error('请至少设置一个方向的扩展像素数');
    }

    // 准备请求数据
    const requestData = {
        image: data.image,
        top: top,
        bottom: bottom,
        left: left,
        right: right,
        prompt: data.prompt || '',
        steps: parseInt(data.steps) || 50,
        output_format: data.output_format || 'png',
        safety_tolerance: data.safety_tolerance || 2,
        prompt_upsampling: !!data.prompt_upsampling
    };
    
    // 添加引导强度参数
    if (data.guidance) {
        requestData.guidance = parseFloat(data.guidance);
    }

    // 添加可选参数
    if (data.seed) {
        requestData.seed = parseInt(data.seed);
    }

    logFlux(`准备发送扩图请求到Flux API，扩展像素: 上${top}下${bottom}左${left}右${right}`);
    logFlux('扩图请求参数:', { 
        has_prompt: !!requestData.prompt,
        steps: requestData.steps,
        output_format: requestData.output_format,
        safety_tolerance: requestData.safety_tolerance,
        prompt_upsampling: requestData.prompt_upsampling,
        has_guidance: !!requestData.guidance,
        has_seed: !!requestData.seed
    });

    try {
    // 发送请求到Flux API
    logFlux(`发送请求到: ${FLUX_EXPAND_API_URL}`);
    const response = await fetch(FLUX_EXPAND_API_URL, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'x-key': FLUX_API_KEY
        },
        body: JSON.stringify(requestData)
    });

    logFlux(`收到响应，状态码: ${response.status}`);
    
    if (!response.ok) {
        const errorData = await response.json();
        logFlux(`API错误响应:`, errorData);
            
            // 特别处理402错误（积分不足）
            if (response.status === 402) {
                // 尝试恢复用户积分
                await restoreUserCredits(userId, creditCost, 'flux_api_expand_credits_insufficient');
                throw new Error('Flux API扩图积分不足，请联系管理员充值API账户');
            }
            
        throw new Error(errorData.detail || `HTTP错误: ${response.status}`);
    }

    // 解析响应
    const responseData = await response.json();
    logFlux(`API成功响应:`, responseData);
    
    // 检查响应格式
    if (!responseData.id) {
        logFlux('错误: API响应格式无效，缺少任务ID', responseData);
        throw new Error('无效的API响应');
    }

    // 生成内部任务ID
    const internalTaskId = uuidv4();
    logFlux(`生成内部任务ID: ${internalTaskId}, Flux任务ID: ${responseData.id}`);
    
    // 将任务信息存入数据库
    logFlux(`将任务信息存入数据库, 用户ID: ${userId}, 任务ID: ${internalTaskId}`);
    const [insertResult] = await db.query(
        `INSERT INTO flux_tasks 
        (flux_task_id, user_id, prompt, polling_url, status, created_at, params, task_type) 
        VALUES (?, ?, ?, ?, ?, NOW(), ?, ?)`,
        [
            responseData.id,
            userId,
            data.prompt || '扩图任务',
            `https://api.us1.bfl.ai/v1/get_result?id=${responseData.id}`, // 存储正确的轮询URL
            'pending',
            JSON.stringify(requestData),
            'expand'
        ]
    );
    
    // 获取数据库自动生成的ID
    const dbTaskId = insertResult.insertId;
    logFlux(`任务信息已存入数据库，数据库生成的ID: ${dbTaskId}`);

    // 启动后台轮询任务，使用数据库生成的ID
    logFlux(`启动后台轮询任务, 任务ID: ${responseData.id}`);
    startPollingTask(dbTaskId, responseData.id);
    
    // 返回数据库生成的任务ID和状态
    return {
        taskId: dbTaskId,
        status: 'pending'
    };
    } catch (error) {
        logFlux(`启动Flux扩图处理失败: ${error.message}`, error.stack);
        throw error;
    }
}

/**
 * 启动后台轮询任务
 * @param {String} taskId - 内部任务ID
 * @param {String} fluxTaskId - Flux API任务ID
 */
function startPollingTask(taskId, fluxTaskId) {
    logFlux(`开始轮询任务状态, 内部任务ID: ${taskId}, Flux任务ID: ${fluxTaskId}`);
    
    // 构建正确的轮询URL
    const pollingUrl = `https://api.us1.bfl.ai/v1/get_result?id=${fluxTaskId}`;
    logFlux(`轮询URL: ${pollingUrl}`);
    
    // 使用setTimeout实现轮询，避免阻塞主线程
    const poll = async () => {
        try {
            // 检查任务是否已完成或失败
            logFlux(`检查任务 ${taskId} 的状态`);
            const taskStatus = await getTaskStatus(taskId);
            logFlux(`任务 ${taskId} 当前状态: ${taskStatus}`);
            
            if (taskStatus === 'succeeded' || taskStatus === 'failed') {
                logFlux(`任务 ${taskId} 已完成或失败，停止轮询`);
                return; // 任务已完成，停止轮询
            }

            // 发送请求到Flux API
            logFlux(`发送轮询请求到: ${pollingUrl}`);
            const response = await fetch(pollingUrl, {
                method: 'GET',
                headers: {
                    'x-key': FLUX_API_KEY
                }
            });

            logFlux(`轮询响应状态码: ${response.status}`);
            if (!response.ok) {
                logFlux(`轮询请求失败: HTTP ${response.status}`);
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const data = await response.json();
            logFlux(`轮询响应数据:`, data);
            
            // 更新任务状态 - 使用正确的状态判断
            if (data.status === 'Ready') {
                // 处理成功
                logFlux(`任务 ${taskId} 处理成功`);
                await updateTaskSuccess(taskId, data);
            } else if (data.status === 'Error' || data.status === 'Request Moderated' || data.status === 'Content Moderated' || data.status === 'Task not found') {
                // 处理失败
                logFlux(`任务 ${taskId} 处理失败: ${data.status}`);
                await updateTaskFailure(taskId, data.status || '未知错误');
            } else if (data.status === 'Pending') {
                // 继续轮询
                logFlux(`任务 ${taskId} 仍在处理中，2秒后再次轮询`);
                setTimeout(poll, 2000);
            } else {
                // 未知状态，视为失败
                logFlux(`任务 ${taskId} 状态未知: ${data.status}`);
                await updateTaskFailure(taskId, `未知状态: ${data.status}`);
            }
        } catch (error) {
            logFlux(`轮询任务 ${taskId} 出错: ${error.message}`, error.stack);
            await updateTaskFailure(taskId, error.message || '轮询出错');
        }
    };

    // 启动首次轮询
    logFlux(`2秒后开始首次轮询任务 ${taskId}`);
    setTimeout(poll, 2000);
}

/**
 * 获取任务状态
 * @param {String} taskId - 内部任务ID
 * @returns {Promise<String>} 任务状态
 */
async function getTaskStatus(taskId) {
    logFlux(`获取任务 ${taskId} 状态`);
    try {
        const [rows] = await db.query(
            'SELECT status FROM flux_tasks WHERE id = ?',
            [taskId]
        );

        if (rows.length === 0) {
            logFlux(`错误: 任务 ${taskId} 不存在`);
            throw new Error('任务不存在');
        }

        logFlux(`任务 ${taskId} 状态: ${rows[0].status}`);
        return rows[0].status;
    } catch (error) {
        logFlux(`获取任务 ${taskId} 状态失败: ${error.message}`, error.stack);
        throw error;
    }
}

/**
 * 更新任务成功状态
 * @param {String} taskId - 内部任务ID
 * @param {Object} data - Flux API返回的数据
 */
async function updateTaskSuccess(taskId, data) {
    logFlux(`更新任务 ${taskId} 为成功状态`);
    try {
        // 从result.sample获取图片URL
        const fluxImageUrl = data.result && data.result.sample ? data.result.sample : null;
        
        if (!fluxImageUrl) {
            logFlux(`警告: 任务 ${taskId} 成功但没有图片URL`);
            await db.query(
                `UPDATE flux_tasks 
                SET status = 'succeeded', 
                    completed_at = NOW(), 
                    result_data = ? 
                WHERE id = ?`,
                [
                    JSON.stringify(data),
                    taskId
                ]
            );
            return;
        }
        
        logFlux(`任务 ${taskId} 成功，Flux图片URL: ${fluxImageUrl}`);
        
        // 获取用户ID，用于构建文件名前缀
        const [userRows] = await db.query(
            'SELECT user_id FROM flux_tasks WHERE id = ?',
            [taskId]
        );
        
        let finalImageUrl = fluxImageUrl; // 默认使用Flux API返回的URL
        
        if (userRows.length > 0) {
            const userId = userRows[0].user_id;
            const COMFYUI_STORAGE_URL = process.env.COMFYUI_CPU_STORAGE_URL;
            if (COMFYUI_STORAGE_URL) {
                try {
                    logFlux(`尝试将任务 ${taskId} 的图片下载到ComfyUI存储: ${COMFYUI_STORAGE_URL}`);
                    const comfyPayload = {
                        url: fluxImageUrl,
                        filename_prefix: `flux_${userId}_${taskId}`,
                        subdir: 'flux/outputs'
                    };
                    
                    const comfyResponse = await axios.post(`${COMFYUI_STORAGE_URL}/yzy/download-url`, comfyPayload, {
                        headers: { 'Content-Type': 'application/json' }
                    });
                    
                    if (comfyResponse.data && comfyResponse.data.success) {
                        const { filename, subfolder, type } = comfyResponse.data;
                        finalImageUrl = `${COMFYUI_STORAGE_URL}/view?filename=${encodeURIComponent(filename)}&subfolder=${encodeURIComponent(subfolder || 'flux/outputs')}&type=${type || 'output'}`;
                        logFlux(`图片成功转移到ComfyUI存储: ${finalImageUrl}`);
                    } else {
                        logFlux(`ComfyUI存储转移失败:`, comfyResponse.data);
                    }
                } catch (comfyError) {
                    logFlux(`ComfyUI存储转移错误: ${comfyError.message}`);
                }
            } else {
                logFlux('未配置COMFYUI_CPU_STORAGE_URL，使用原始Flux URL');
            }
        } else {
            logFlux(`警告: 无法获取任务 ${taskId} 的用户ID`);
        }

        // 更新数据库
        await db.query(
            `UPDATE flux_tasks 
            SET status = 'succeeded', 
                output_image_url = ?, 
                completed_at = NOW(), 
                result_data = ? 
            WHERE id = ?`,
            [
                finalImageUrl,
                JSON.stringify(data),
                taskId
            ]
        );
        logFlux(`任务 ${taskId} 已更新为成功状态，图片URL: ${finalImageUrl}`);
    } catch (error) {
        logFlux(`更新任务 ${taskId} 成功状态失败: ${error.message}`, error.stack);
        throw error;
    }
}

/**
 * 更新任务失败状态
 * @param {String} taskId - 内部任务ID
 * @param {String} error - 错误信息
 */
async function updateTaskFailure(taskId, error) {
    logFlux(`更新任务 ${taskId} 为失败状态，错误: ${error}`);
    try {
        await db.query(
            `UPDATE flux_tasks 
            SET status = 'failed', 
                error = ?, 
                completed_at = NOW() 
            WHERE id = ?`,
            [
                error,
                taskId
            ]
        );
        logFlux(`任务 ${taskId} 已更新为失败状态`);
    } catch (err) {
        logFlux(`更新任务 ${taskId} 失败状态时出错: ${err.message}`, err.stack);
        throw err;
    }
}

/**
 * 获取任务详情
 * @param {String} taskId - 内部任务ID
 * @param {Number} userId - 用户ID
 * @returns {Promise<Object>} 任务详情
 */
async function getTaskDetails(taskId, userId) {
    logFlux(`获取任务 ${taskId} 详情，用户ID: ${userId}`);
    try {
        const [rows] = await db.query(
            `SELECT id, flux_task_id, prompt, status, output_image_url, 
                    error, created_at, completed_at, params 
            FROM flux_tasks 
            WHERE id = ? AND user_id = ?`,
            [taskId, userId]
        );

        if (rows.length === 0) {
            logFlux(`错误: 任务 ${taskId} 不存在或用户 ${userId} 无权访问`);
            throw new Error('任务不存在或无权访问');
        }

        logFlux(`成功获取任务 ${taskId} 详情`);
        return rows[0];
    } catch (error) {
        logFlux(`获取任务 ${taskId} 详情失败: ${error.message}`, error.stack);
        throw error;
    }
}

/**
 * 获取用户的历史任务
 * @param {Number} userId - 用户ID
 * @param {Number} page - 页码
 * @param {Number} pageSize - 每页数量
 * @returns {Promise<Object>} 分页的历史任务
 */
async function getUserTasks(userId, page = 1, pageSize = 10) {
    logFlux(`获取用户 ${userId} 的历史任务，页码: ${page}，每页数量: ${pageSize}`);
    try {
        // 计算偏移量
        const offset = (page - 1) * pageSize;

        // 获取任务总数
        const [countRows] = await db.query(
            'SELECT COUNT(*) as total FROM flux_tasks WHERE user_id = ?',
            [userId]
        );
        const total = countRows[0].total;
        logFlux(`用户 ${userId} 共有 ${total} 个历史任务`);

        // 获取分页数据
        const [rows] = await db.query(
            `SELECT id, prompt, status, output_image_url, created_at, completed_at 
            FROM flux_tasks 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?`,
            [userId, pageSize, offset]
        );
        logFlux(`获取到用户 ${userId} 的 ${rows.length} 个任务记录`);

        return {
            items: rows,
            total,
            page,
            pageSize,
            hasMore: offset + rows.length < total
        };
    } catch (error) {
        logFlux(`获取用户 ${userId} 任务列表失败: ${error.message}`, error.stack);
        throw error;
    }
}

/**
 * 删除任务
 * @param {String} taskId - 内部任务ID
 * @param {Number} userId - 用户ID
 * @returns {Promise<Boolean>} 是否删除成功
 */
async function deleteTask(taskId, userId) {
    logFlux(`删除任务 ${taskId}，用户ID: ${userId}`);
    try {
        const [result] = await db.query(
            'DELETE FROM flux_tasks WHERE id = ? AND user_id = ?',
            [taskId, userId]
        );

        const success = result.affectedRows > 0;
        if (success) {
            logFlux(`任务 ${taskId} 已成功删除`);
        } else {
            logFlux(`任务 ${taskId} 删除失败，可能不存在或用户 ${userId} 无权删除`);
        }
        
        return success;
    } catch (error) {
        logFlux(`删除任务 ${taskId} 失败: ${error.message}`, error.stack);
        throw error;
    }
}

/**
 * 启动Replicate图片处理任务
 * @param {Object} data - 处理参数
 * @param {Number} userId - 用户ID
 * @returns {Promise<Object>} 任务信息
 */
async function startReplicateProcessing(data, userId) {
    logFlux(`开始Replicate处理任务，用户ID: ${userId}，模型: ${data.model || 'pro'}`);
    
    // 记录积分成本，用于可能的退款
    const creditCost = 3; // Replicate模式消耗3积分
    
    // 验证必要参数
    if (!data.prompt) {
        logFlux('错误: 提示词不能为空');
        throw new Error('提示词不能为空');
    }

    if (!data.input_image) {
        logFlux('错误: 输入图片不能为空');
        throw new Error('输入图片不能为空');
    }

    // 选择要使用的模型
    const modelVersion = data.model && data.model.toLowerCase() === 'max' ? 
        REPLICATE_FLUX_KONTEXT_MAX_MODEL : 
        REPLICATE_FLUX_KONTEXT_PRO_MODEL;
    
    // 准备请求数据
    const requestData = {
        version: modelVersion,
        input: {
            prompt: data.prompt,
            input_image: data.input_image
        }
    };
    
    // 添加可选参数
    if (data.negative_prompt) {
        requestData.input.negative_prompt = data.negative_prompt;
        logFlux('添加负面提示词');
    }
    
    if (data.seed !== undefined) {
        requestData.input.seed = parseInt(data.seed);
        logFlux(`添加随机种子: ${requestData.input.seed}`);
    }
    
    logFlux(`准备发送请求到Replicate API，提示词: "${data.prompt.substring(0, 30)}${data.prompt.length > 30 ? '...' : ''}"`);
    logFlux('请求参数:', { 
        model: modelVersion,
        has_negative_prompt: !!requestData.input.negative_prompt,
        has_seed: requestData.input.seed !== undefined
    });

    try {
        // 发送请求到Replicate API
        logFlux(`发送请求到: ${REPLICATE_API_URL}`);
        const response = await fetch(REPLICATE_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Token ${REPLICATE_API_KEY}`
            },
            body: JSON.stringify(requestData)
        });

        logFlux(`收到响应，状态码: ${response.status}`);
        
        if (!response.ok) {
            let errorMsg = '请求失败';
            try {
                const errorData = await response.json();
                errorMsg = errorData.detail || `HTTP错误: ${response.status}`;
                logFlux(`API错误响应:`, errorData);
            } catch (jsonError) {
                errorMsg = `HTTP错误: ${response.status}`;
                logFlux(`解析API错误响应失败:`, jsonError);
            }
                
            // 恢复用户积分
            await restoreUserCredits(userId, creditCost, 'replicate_api_error_refund');
            throw new Error(errorMsg);
        }

        // 解析响应
        const responseData = await response.json();
        logFlux(`API成功响应:`, responseData);
        
        // 检查响应格式
        if (!responseData.id) {
            logFlux('错误: API响应格式无效，缺少任务ID', responseData);
            throw new Error('无效的API响应');
        }

        // 生成内部任务ID
        const internalTaskId = uuidv4();
        logFlux(`生成内部任务ID: ${internalTaskId}, Replicate任务ID: ${responseData.id}`);
        
        // 将任务信息存入数据库
        logFlux(`将任务信息存入数据库, 用户ID: ${userId}, 任务ID: ${internalTaskId}`);
        const [insertResult] = await db.query(
            `INSERT INTO flux_tasks 
            (flux_task_id, user_id, prompt, polling_url, status, created_at, params, task_type, api_provider) 
            VALUES (?, ?, ?, ?, ?, NOW(), ?, ?, ?)`,
            [
                responseData.id,
                userId,
                data.prompt,
                `https://api.replicate.com/v1/predictions/${responseData.id}`, // 存储正确的轮询URL
                'pending',
                JSON.stringify(requestData),
                'replicate',
                'replicate'
            ]
        );
        
        // 获取数据库自动生成的ID
        const dbTaskId = insertResult.insertId;
        logFlux(`任务信息已存入数据库，数据库生成的ID: ${dbTaskId}`);

        // 启动后台轮询任务，使用数据库生成的ID
        logFlux(`启动后台轮询任务, 任务ID: ${responseData.id}`);
        startReplicatePollingTask(dbTaskId, responseData.id);
        
        // 返回数据库生成的任务ID和状态
        return {
            taskId: dbTaskId,
            status: 'pending'
        };
    } catch (error) {
        logFlux(`启动Replicate处理失败: ${error.message}`, error.stack);
        throw error;
    }
}

/**
 * 启动Replicate后台轮询任务
 * @param {String} taskId - 内部任务ID
 * @param {String} replicateTaskId - Replicate API任务ID
 */
function startReplicatePollingTask(taskId, replicateTaskId) {
    logFlux(`开始轮询Replicate任务状态, 内部任务ID: ${taskId}, Replicate任务ID: ${replicateTaskId}`);
    
    // 构建轮询URL
    const pollingUrl = `https://api.replicate.com/v1/predictions/${replicateTaskId}`;
    logFlux(`轮询URL: ${pollingUrl}`);
    
    // 使用setTimeout实现轮询，避免阻塞主线程
    const poll = async () => {
        try {
            // 检查任务是否已完成或失败
            logFlux(`检查任务 ${taskId} 的状态`);
            const taskStatus = await getTaskStatus(taskId);
            logFlux(`任务 ${taskId} 当前状态: ${taskStatus}`);
            
            if (taskStatus === 'succeeded' || taskStatus === 'failed') {
                logFlux(`任务 ${taskId} 已完成或失败，停止轮询`);
                return; // 任务已完成，停止轮询
            }

            // 发送请求到Replicate API
            logFlux(`发送轮询请求到: ${pollingUrl}`);
            const response = await fetch(pollingUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `Token ${REPLICATE_API_KEY}`
                }
            });

            logFlux(`轮询响应状态码: ${response.status}`);
            if (!response.ok) {
                logFlux(`轮询请求失败: HTTP ${response.status}`);
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const data = await response.json();
            logFlux(`轮询响应数据:`, data);
            
            // 根据Replicate API的状态更新任务
            switch (data.status) {
                case 'succeeded':
                    // 处理成功
                    logFlux(`任务 ${taskId} 处理成功`);
                    await updateReplicateTaskSuccess(taskId, data);
                    break;
                    
                case 'failed':
                    // 处理失败
                    logFlux(`任务 ${taskId} 处理失败: ${data.error || '未知错误'}`);
                    await updateTaskFailure(taskId, data.error || '未知错误');
                    break;
                    
                case 'canceled':
                    // 任务被取消
                    logFlux(`任务 ${taskId} 被取消`);
                    await updateTaskFailure(taskId, '任务被取消');
                    break;
                    
                case 'processing':
                case 'starting':
                    // 设置任务进度 - Replicate API提供了progress百分比
                    const progress = data.progress ? Math.round(data.progress * 100) : null;
                    if (progress !== null) {
                        logFlux(`任务 ${taskId} 处理中，进度: ${progress}%`);
                        await updateTaskProgress(taskId, progress);
                    }
                    
                    // 继续轮询
                    logFlux(`任务 ${taskId} 仍在处理中，2秒后再次轮询`);
                    setTimeout(poll, 2000);
                    break;
                    
                default:
                    // 未知状态，继续轮询
                    logFlux(`任务 ${taskId} 状态未知: ${data.status}，继续轮询`);
                    setTimeout(poll, 2000);
                    break;
            }
        } catch (error) {
            logFlux(`轮询任务 ${taskId} 出错: ${error.message}`, error.stack);
            await updateTaskFailure(taskId, error.message || '轮询出错');
        }
    };

    // 启动首次轮询
    logFlux(`2秒后开始首次轮询任务 ${taskId}`);
    setTimeout(poll, 2000);
}

/**
 * 更新任务进度
 * @param {String} taskId - 内部任务ID
 * @param {Number} progress - 进度百分比
 */
async function updateTaskProgress(taskId, progress) {
    logFlux(`更新任务 ${taskId} 的进度为 ${progress}%`);
    try {
        await db.query(
            `UPDATE flux_tasks 
            SET progress = ? 
            WHERE id = ?`,
            [
                progress,
                taskId
            ]
        );
        logFlux(`任务 ${taskId} 进度已更新`);
    } catch (err) {
        logFlux(`更新任务 ${taskId} 进度时出错: ${err.message}`, err.stack);
        // 不抛出异常，让轮询继续
    }
}

/**
 * 更新Replicate任务成功状态
 * @param {String} taskId - 内部任务ID
 * @param {Object} data - Replicate API返回的数据
 */
async function updateReplicateTaskSuccess(taskId, data) {
    logFlux(`更新任务 ${taskId} 为成功状态 (Replicate)`);
    try {
        // Replicate API成功时，output包含一个或多个生成的图片URL
        const replicateImageUrl = data.output && Array.isArray(data.output) && data.output.length > 0 ? 
            data.output[0] : null;
        
        if (!replicateImageUrl) {
            logFlux(`警告: 任务 ${taskId} 成功但没有图片URL`);
            await db.query(
                `UPDATE flux_tasks 
                SET status = 'succeeded', 
                    completed_at = NOW(), 
                    result_data = ? 
                WHERE id = ?`,
                [
                    JSON.stringify(data),
                    taskId
                ]
            );
            return;
        }
        
        logFlux(`任务 ${taskId} 成功，Replicate图片URL: ${replicateImageUrl}`);
        
        // 获取用户ID，用于构建文件名前缀
        const [userRows] = await db.query(
            'SELECT user_id FROM flux_tasks WHERE id = ?',
            [taskId]
        );
        
        let finalImageUrl = replicateImageUrl; // 默认使用Replicate API返回的URL

        if (userRows.length > 0) {
            const userId = userRows[0].user_id;
            const COMFYUI_STORAGE_URL = process.env.COMFYUI_CPU_STORAGE_URL;
            if (COMFYUI_STORAGE_URL) {
                try {
                    logFlux(`尝试将任务 ${taskId} 的图片下载到ComfyUI存储 (Replicate): ${COMFYUI_STORAGE_URL}`);
                    const comfyPayload = {
                        url: replicateImageUrl,
                        filename_prefix: `replicate_${userId}_${taskId}`,
                        subdir: 'replicate/outputs'
                    };

                    const comfyResponse = await axios.post(`${COMFYUI_STORAGE_URL}/yzy/download-url`, comfyPayload, {
                        headers: { 'Content-Type': 'application/json' }
                    });

                    if (comfyResponse.data && comfyResponse.data.success) {
                        const { filename, subfolder, type } = comfyResponse.data;
                        finalImageUrl = `${COMFYUI_STORAGE_URL}/view?filename=${encodeURIComponent(filename)}&subfolder=${encodeURIComponent(subfolder || 'replicate/outputs')}&type=${type || 'output'}`;
                        logFlux(`图片成功转移到ComfyUI存储 (Replicate): ${finalImageUrl}`);
                    } else {
                        logFlux(`ComfyUI存储转移失败 (Replicate):`, comfyResponse.data);
                    }
                } catch (comfyError) {
                    logFlux(`ComfyUI存储转移错误 (Replicate): ${comfyError.message}`);
                }
            } else {
                logFlux('未配置COMFYUI_CPU_STORAGE_URL，使用原始Replicate URL');
            }
        } else {
            logFlux(`警告: 无法获取任务 ${taskId} 的用户ID (Replicate)`);
        }
        
        // 更新数据库
        await db.query(
            `UPDATE flux_tasks 
            SET status = 'succeeded', 
                output_image_url = ?, 
                completed_at = NOW(), 
                result_data = ? 
            WHERE id = ?`,
            [
                finalImageUrl,
                JSON.stringify(data),
                taskId
            ]
        );
        logFlux(`任务 ${taskId} 已更新为成功状态，图片URL: ${finalImageUrl}`);
    } catch (error) {
        logFlux(`更新任务 ${taskId} 成功状态失败: ${error.message}`, error.stack);
        throw error;
    }
}

// --- 辅助函数：上传图片到 ComfyUI ---
async function uploadImageToComfyUI(fileBuffer, originalFilename, comfyBaseUrl) {
    const formData = new FormData();
    formData.append('image', fileBuffer, originalFilename);
    formData.append('overwrite', 'true');

    console.log(`[Flux Service Helper] Uploading to ComfyUI: ${originalFilename} to ${comfyBaseUrl}`);
    const response = await axios.post(`${comfyBaseUrl}/upload/image`, formData, {
        headers: formData.getHeaders(),
    });

    if (response.status !== 200 || !response.data || !response.data.name) {
        console.error('[Flux Service Helper] ComfyUI upload failed:', response.data);
        throw new Error('上传图片到 ComfyUI 失败');
    }
    console.log('[Flux Service Helper] ComfyUI upload successful:', response.data);
    return response.data; // { name, subfolder, type }
}

// --- 辅助函数：从数据库获取 ComfyUI 工作流 ---
async function getComfyUIWorkflow(workflowName) {
    let connection;
    try {
        connection = await db.getConnection();
        const [rows] = await connection.query('SELECT workflow_json FROM workflows WHERE name = ?', [workflowName]);
        if (rows.length === 0) {
            throw new Error(`未在数据库中找到名为 "${workflowName}" 的工作流`);
        }
        return rows[0].workflow_json; // 驱动程序会自动解析JSON
    } finally {
        if (connection) connection.release();
    }
}

// --- 辅助函数：触发 ComfyUI 工作流 ---
async function triggerComfyUIWorkflow(workflowJson, clientId, comfyBaseUrl) {
    console.log(`[Flux Service Helper] Triggering ComfyUI workflow with clientId: ${clientId} on ${comfyBaseUrl}`);
    const response = await axios.post(`${comfyBaseUrl}/prompt`, {
        prompt: workflowJson,
        client_id: clientId,
    });
    if (response.status !== 200 || !response.data || !response.data.prompt_id) {
        console.error('[Flux Service Helper] ComfyUI trigger failed:', response.data);
        throw new Error('触发 ComfyUI 工作流失败');
    }
    console.log('[Flux Service Helper] ComfyUI workflow triggered:', response.data);
    return response.data.prompt_id;
}

// --- 辅助函数：轮询 ComfyUI 历史记录 ---
async function pollComfyUIHistory(promptId, comfyBaseUrl, maxAttempts = 40, interval = 5000) {
    for (let i = 0; i < maxAttempts; i++) {
        try {
            const response = await axios.get(`${comfyBaseUrl}/history/${promptId}`);
            if (response.data && response.data[promptId]) {
                const historyEntry = response.data[promptId];
                if (historyEntry.outputs && historyEntry.outputs[COMFYUI_SAVE_IMAGE_NODE_ID_FLUX]) {
                    console.log(`[Flux Service Helper] Polling success for ${promptId}`);
                    return historyEntry;
                }
            }
        } catch (error) {
            // 404 is normal if task is not yet in history, so we don't log it as a big error
            if (error.response && error.response.status !== 404) {
                 console.warn(`[Flux Service Helper] Polling for ${promptId} attempt ${i+1} failed with status ${error.response.status}`);
            }
        }
        await new Promise(resolve => setTimeout(resolve, interval));
    }
    throw new Error(`轮询 ComfyUI 任务超时: ${promptId}`);
}

/**
 * 启动 ComfyUI Flux 上下文处理任务
 * @param {Object} data - 从路由传递的数据
 * @param {Object} file - 上传的文件对象 (来自 multer)
 * @param {Number} userId - 用户ID
 * @returns {Promise<Object>} 任务信息
 */
async function startComfyFluxKontextProcessing(data, file, userId) {
    const { prompt, workflow } = data;
    const clientId = uuidv4();
    let taskId; // Renamed from historyId for clarity

    const COMFYUI_BASE_URL = process.env.COMFYUI_GPU_URL;
    if (!COMFYUI_BASE_URL) {
        throw new Error('ComfyUI 服务地址 (COMFYUI_GPU_URL) 未配置。');
    }

    let connection;
    try {
        // 1. 上传图片
        const uploadedImage = await uploadImageToComfyUI(file.buffer, file.originalname, COMFYUI_BASE_URL);

        // 2. 获取工作流
        const workflowJson = await getComfyUIWorkflow(workflow);
        
        // 3. 修改工作流
        workflowJson[COMFYUI_PROMPT_NODE_ID_FLUX].inputs.String = prompt;
        workflowJson[COMFYUI_LOAD_IMAGE_NODE_ID_FLUX].inputs.image = uploadedImage.name;

        // 在触发前获取当前队列位置
        const queueData = await getComfyUIQueue();
        const queueRemaining = queueData?.exec_info?.queue_remaining ?? 0;
        const queuePosition = queueRemaining + 1;
        console.log(`[Comfy Flux Service] Current queue remaining: ${queueRemaining}. New task position: ${queuePosition}`);

        // 4. 触发工作流
        const promptId = await triggerComfyUIWorkflow(workflowJson, clientId, COMFYUI_BASE_URL);

        // 5. 在 flux_tasks 表中创建初始记录
        connection = await db.getConnection();
        const params = JSON.stringify({ workflow, original_filename: file.originalname });
        const [insertResult] = await connection.query(
            `INSERT INTO flux_tasks (user_id, prompt, api_provider, flux_task_id, status, params, created_at) 
             VALUES (?, ?, ?, ?, ?, ?, NOW())`,
            [userId, prompt, 'comfyui', promptId, 'processing', params]
        );
        taskId = insertResult.insertId;
        console.log(`[Comfy Flux Service] Created task record in flux_tasks ID: ${taskId} for prompt ID: ${promptId}`);

        // 6. 开始异步轮询，但不等待它完成
        pollAndFinalizeTask(taskId, promptId, COMFYUI_BASE_URL);

        // 7. 立即返回任务ID和队列位置给前端
        return { taskId: taskId, queuePosition: queuePosition };

    } catch (error) {
        console.error('Comfy Flux processing error:', error.message, error.stack);
        // 如果已经创建了任务记录，则更新为失败状态
        if (taskId) {
            await updateTaskToFailed(taskId, error.message);
        }
        throw error;
    } finally {
        if (connection) connection.release();
    }
}

// 异步轮询和更新任务的函数
async function pollAndFinalizeTask(taskId, promptId, comfyBaseUrl) {
    try {
        const historyData = await pollComfyUIHistory(promptId, comfyBaseUrl);
        
        const outputNode = historyData.outputs[COMFYUI_SAVE_IMAGE_NODE_ID_FLUX];
        if (outputNode && outputNode.images && outputNode.images.length > 0) {
            const finalImage = outputNode.images[0];
            const imageUrl = `${comfyBaseUrl}/view?filename=${finalImage.filename}&subfolder=${finalImage.subfolder}&type=${finalImage.type}`;
            
            const resultData = JSON.stringify(historyData); // Store the full ComfyUI result

            // 更新 flux_tasks 记录为成功
            let connection;
            try {
                connection = await db.getConnection();
                await connection.query(
                    'UPDATE flux_tasks SET status = ?, output_image_url = ?, result_data = ?, completed_at = NOW() WHERE id = ?',
                    ['succeeded', imageUrl, resultData, taskId]
                );
                console.log(`[Comfy Flux Poll] Task ${taskId} in flux_tasks completed successfully.`);
            } finally {
                if (connection) connection.release();
            }
        } else {
            throw new Error('任务执行完成，但未找到预期的输出图像。');
        }
    } catch (error) {
        console.error(`Error polling and finalizing task ${taskId}:`, error.message);
        await updateTaskToFailed(taskId, error.message);
    }
}

// 更新任务为失败状态的辅助函数
async function updateTaskToFailed(taskId, errorMessage) {
    let connection;
    try {
        connection = await db.getConnection();
        await connection.query(
            'UPDATE flux_tasks SET status = ?, error = ?, completed_at = NOW() WHERE id = ?',
            ['failed', errorMessage, taskId]
        );
        console.log(`[Comfy Flux Helper] Marked task ${taskId} in flux_tasks as FAILED.`);
    } catch (dbError) {
        console.error(`[Comfy Flux Helper] CRITICAL: Failed to mark task ${taskId} as FAILED in DB:`, dbError.message);
    } finally {
        if (connection) connection.release();
    }
}

// --- 辅助函数：获取ComfyUI队列信息 ---
async function getComfyUIQueue() {
    const COMFYUI_BASE_URL = process.env.COMFYUI_GPU_URL;
    if (!COMFYUI_BASE_URL) {
        // 如果没有配置ComfyUI地址，直接返回空信息，避免主流程中断
        console.warn('[Flux Service Helper] COMFYUI_GPU_URL not configured, cannot get queue info.');
        return null;
    }
    try {
        const response = await axios.get(`${COMFYUI_BASE_URL}/prompt`);
        return response.data;
    } catch (error) {
        console.error(`[Flux Service Helper] Failed to get ComfyUI queue: ${error.message}`);
        // 返回null，允许主流程继续
        return null;
    }
}

/**
 * 数据库表结构 (如果需要创建):
 * 
 * CREATE TABLE IF NOT EXISTS flux_tasks (
 *   id VARCHAR(36) NOT NULL PRIMARY KEY,
 *   flux_task_id VARCHAR(100) NOT NULL,
 *   user_id INT NOT NULL,
 *   prompt TEXT NOT NULL,
 *   polling_url VARCHAR(255) NOT NULL,
 *   status ENUM('pending', 'processing', 'succeeded', 'failed') NOT NULL DEFAULT 'pending',
 *   output_image_url TEXT NULL,
 *   error TEXT NULL,
 *   progress INT NULL,
 *   created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
 *   completed_at TIMESTAMP NULL,
 *   result_data JSON NULL,
 *   params JSON NULL,
 *   task_type VARCHAR(50) NOT NULL DEFAULT 'normal',
 *   api_provider VARCHAR(50) NOT NULL DEFAULT 'flux',
 *   INDEX (user_id),
 *   INDEX (status)
 * );
 */

module.exports = {
    startFluxProcessing,
    startComfyFluxKontextProcessing,
    getComfyUIQueue,
    getTaskStatus,
    getTaskDetails,
    getUserTasks,
    deleteTask,
    restoreUserCredits
}; 