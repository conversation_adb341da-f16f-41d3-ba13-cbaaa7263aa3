-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-05-23 13:23:51
-- 服务器版本： 5.7.40-log
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `promptdb`
--

-- --------------------------------------------------------

--
-- 表的结构 `feature_costs`
--

CREATE TABLE `feature_costs` (
  `feature_key` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '功能唯一标识符',
  `cost` int(11) NOT NULL DEFAULT '2' COMMENT '消耗的积分数',
  `display_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '功能描述',
  `can_use_free_credits` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='功能积分成本配置表';

--
-- 转存表中的数据 `feature_costs`
--

INSERT INTO `feature_costs` (`feature_key`, `cost`, `display_name`, `description`, `can_use_free_credits`) VALUES
('ai_generate', 2, 'AI 图片生成', 'AI 图片生成', 1),
('ai_generate_midjourney', 8, 'Midjourney生成', 'Midjourney文生图能力分消耗', 0),
('ai_mj_action', 1, NULL, 'Midjourney 操作 (Upscale, Variant, Reroll)', 1),
('comfy_remove_background', 2, '使用 ComfyUI 进行图片背景移除', '使用 ComfyUI 进行图片背景移除', 1),
('creative_upscale', 2, '清晰放大', '清晰放大', 1),
('DAILY_FREE_CREDIT_ALLOWANCE', 10, '每日免费赠送积分额度', '每日免费赠送积分额度', 0),
('gpt4o_image_edit', 10, 'GPT-4o 图片编辑单次消耗', 'GPT-4o 图片编辑单次消耗', 0),
('image_to_3d', 25, '图片生成 3D 模型', '图片生成 3D 模型', 0),
('image_to_3d_animation', 25, '图片转3D模型动画制作', '图片转3D模型动画制作', 0),
('image_to_video_plus', 22, '图生视频 (Plus模型)', '图生视频 (Plus模型)', 0),
('image_to_video_turbo', 10, '图生视频 (Turbo模型)', '图生视频 (Turbo模型)', 0),
('kf2v_plus', 22, '图生视频(首尾帧KF2V模型)', '图生视频(首尾帧KF2V模型)', 0),
('mj_fast_imagine', 2, 'Midjourney Fast模式', 'Midjourney Fast模式文生图能力分消耗', 0),
('mj_relax_imagine', 1, 'Midjourney Relax模式', 'Midjourney Relax模式文生图能力分消耗', 0),
('model_animation', 10, '3D模型添加动画', '3D模型添加动画', 0),
('remove_anything', 2, '移除图片中的任何物体或区域', '移除图片中的任何物体或区域', 1),
('remove_background', 2, '背景移除', '背景移除', 1),
('vectorize_image', 4, '位图转 SVG', '位图转 SVG', 1),
('voice_clone', 5, 'AI声音克隆功能', 'AI声音克隆功能', 0);

--
-- 转储表的索引
--

--
-- 表的索引 `feature_costs`
--
ALTER TABLE `feature_costs`
  ADD PRIMARY KEY (`feature_key`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
