    // prompt-examples/server/db/pool.js
    const mysql = require('mysql2/promise');

    const dbConfig = {
        host: process.env.DB_HOST || '127.0.0.1',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'promptdb',
        waitForConnections: true,
        connectionLimit: 10,
        queueLimit: 0,
        // 添加 timezone 以确保日期时间处理的一致性
        timezone: process.env.DB_TIMEZONE || '+00:00' // Allow overriding timezone via .env
    };

    let pool;
    try {
        pool = mysql.createPool(dbConfig);

        // 添加详细的数据库连接日志
        pool.on('connection', (connection) => {
            console.log(`数据库新连接建立 (ID: ${connection.threadId}) (来自 db/pool.js)`);
        });
        pool.on('release', (connection) => {
             console.log(`数据库连接释放 (ID: ${connection.threadId}) (来自 db/pool.js)`);
        });
        pool.on('error', (err) => {
            console.error('数据库连接池错误 (来自 db/pool.js):', err);
        });

        console.log('数据库连接池已成功创建 (来自 db/pool.js)');

        // 尝试获取一个连接以立即测试
        pool.getConnection()
          .then(conn => {
            console.log('数据库连接池初始连接测试成功 (来自 db/pool.js)');
            conn.release();
          })
          .catch(err => {
            console.error('数据库连接池初始连接测试失败 (来自 db/pool.js):', err);
             console.error('请检查数据库配置:', {
                host: dbConfig.host,
                user: dbConfig.user,
                database: dbConfig.database,
                // 不打印密码
             });
          });

    } catch (error) {
        console.error('创建数据库连接池失败 (来自 db/pool.js):', error);
        // 在创建失败时抛出错误或退出进程，以避免后续错误
        process.exit(1); // 或者根据你的错误处理策略决定
    }


    module.exports = pool;