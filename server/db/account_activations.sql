-- 创建account_activations表，用于存储账户激活的令牌
CREATE TABLE IF NOT EXISTS `account_activations` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `user_id` INT NOT NULL,
  `token` VARCHAR(255) NOT NULL,
  `expires_at` DATETIME NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id_unique` (`user_id`),
  INDEX `token_index` (`token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 添加外键约束（如果需要）
ALTER TABLE `account_activations` 
ADD CONSTRAINT `fk_account_activations_users`
FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
ON DELETE CASCADE;

-- 为users表添加is_activated字段（如果尚未添加）
ALTER TABLE `users` 
ADD COLUMN IF NOT EXISTS `is_activated` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '账户是否已激活' AFTER `email`;

-- 设置已有用户的激活状态为已激活
UPDATE `users` SET `is_activated` = 1 WHERE `is_activated` = 0; 