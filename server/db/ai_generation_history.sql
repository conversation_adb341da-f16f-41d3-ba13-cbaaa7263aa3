-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-05-24 16:58:49
-- 服务器版本： 5.7.40-log
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `promptdb`
--

-- --------------------------------------------------------

--
-- 表的结构 `ai_generation_history`
--

CREATE TABLE `ai_generation_history` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(11) NOT NULL,
  `prompt` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `image_urls` json NOT NULL,
  `model` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `width` int(11) DEFAULT NULL,
  `height` int(11) DEFAULT NULL,
  `generation_time_ms` bigint(20) UNSIGNED DEFAULT NULL COMMENT '生成耗时（毫秒）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `jimeng_history_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'UNKNOWN' COMMENT 'Task status: PROCESSING, COMPLETED, FAILED, UNKNOWN',
  `task_provider` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'AI provider: comfyui, jimeng, etc.',
  `task_provider_id` text COLLATE utf8mb4_unicode_ci COMMENT 'ID from the AI provider (e.g., ComfyUI prompt_id)',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '任务描述，可包含队列信息',
  `progress` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务进度，例如 0%, 100%',
  `comfy_details` json DEFAULT NULL COMMENT 'Specific details for ComfyUI tasks',
  `error_message` text COLLATE utf8mb4_unicode_ci COMMENT 'Error message if the task failed',
  `comfy_prompt_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `polling_attempts` int(11) DEFAULT '0',
  `mj_task_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mj_buttons` text COLLATE utf8mb4_unicode_ci,
  `finish_time` datetime DEFAULT NULL COMMENT '任务完成时间',
  `fail_reason` text COLLATE utf8mb4_unicode_ci COMMENT '任务失败原因'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转储表的索引
--

--
-- 表的索引 `ai_generation_history`
--
ALTER TABLE `ai_generation_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_status_comfy` (`status`,`comfy_prompt_id`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `ai_generation_history`
--
ALTER TABLE `ai_generation_history`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 限制导出的表
--

--
-- 限制表 `ai_generation_history`
--
ALTER TABLE `ai_generation_history`
  ADD CONSTRAINT `fk_ai_history_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
