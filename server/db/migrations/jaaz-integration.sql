-- Jaaz AI设计代理数据库集成脚本
-- 执行时间：请在维护窗口执行
-- 作用：扩展现有用户系统，支持AI设计功能
-- 注意：复用现有积分系统，不创建新的积分字段

-- 1. 不修改用户表（复用现有的credits字段）
-- 原有积分系统已存在，无需添加新字段

-- 2. 创建设计作品表
CREATE TABLE designs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  title VARCHAR(255) NOT NULL COMMENT '设计作品标题',
  description TEXT COMMENT '设计作品描述',
  canvas_data LONGTEXT COMMENT 'Excalidraw画布数据（JSON格式）',
  preview_image VARCHAR(500) COMMENT '预览图URL',
  thumbnail_image VARCHAR(500) COMMENT '缩略图URL',
  tags JSON COMMENT '设计标签',
  is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
  is_template BOOLEAN DEFAULT FALSE COMMENT '是否为模板',
  view_count INT DEFAULT 0 COMMENT '查看次数',
  like_count INT DEFAULT 0 COMMENT '点赞次数',
  fork_count INT DEFAULT 0 COMMENT '派生次数',
  original_design_id INT COMMENT '原始设计ID（如果是派生作品）',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (original_design_id) REFERENCES designs(id) ON DELETE SET NULL,
  INDEX idx_user_created (user_id, created_at),
  INDEX idx_public_created (is_public, created_at),
  INDEX idx_template (is_template),
  FULLTEXT KEY ft_title_desc (title, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设计作品表';

-- 3. 创建AI生成记录表
CREATE TABLE ai_generations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  design_id INT COMMENT '关联的设计作品ID',
  prompt TEXT NOT NULL COMMENT 'AI生成提示词',
  model_used VARCHAR(50) COMMENT '使用的AI模型',
  model_version VARCHAR(50) COMMENT '模型版本',
  generation_type ENUM('image', 'text', 'layout', 'color_scheme') DEFAULT 'image' COMMENT '生成类型',
  input_data JSON COMMENT '输入数据（如参考图片URL等）',
  result_data JSON COMMENT '生成结果数据',
  result_url VARCHAR(500) COMMENT '生成结果文件URL',
  cost_credits INT DEFAULT 5 COMMENT '消耗的积分数量（默认5积分，成本较高）',
  generation_time_ms INT COMMENT '生成耗时（毫秒）',
  status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
  error_message TEXT COMMENT '错误信息（如果失败）',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL COMMENT '完成时间',
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (design_id) REFERENCES designs(id) ON DELETE SET NULL,
  INDEX idx_user_created (user_id, created_at),
  INDEX idx_status (status),
  INDEX idx_model (model_used)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI生成记录表';

-- 4. 创建设计模板分类表
CREATE TABLE design_categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL COMMENT '分类名称',
  slug VARCHAR(100) NOT NULL UNIQUE COMMENT '分类标识符',
  description TEXT COMMENT '分类描述',
  icon VARCHAR(100) COMMENT '图标名称',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_slug (slug),
  INDEX idx_sort (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设计模板分类表';

-- 5. 创建设计作品分类关联表
CREATE TABLE design_category_relations (
  design_id INT NOT NULL,
  category_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (design_id, category_id),
  FOREIGN KEY (design_id) REFERENCES designs(id) ON DELETE CASCADE,
  FOREIGN KEY (category_id) REFERENCES design_categories(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设计作品分类关联表';

-- 6. 创建设计点赞表
CREATE TABLE design_likes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  design_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (design_id) REFERENCES designs(id) ON DELETE CASCADE,
  UNIQUE KEY uk_user_design (user_id, design_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设计点赞表';

-- 7. 插入默认设计分类
INSERT INTO design_categories (name, slug, description, icon, sort_order) VALUES
('海报设计', 'poster', '各类宣传海报、活动海报设计模板', 'image', 1),
('Logo设计', 'logo', '品牌标志、图标设计模板', 'star', 2),
('名片设计', 'business-card', '商务名片、个人名片设计模板', 'credit-card', 3),
('社交媒体', 'social-media', '微信朋友圈、微博、Instagram等社交媒体设计', 'share', 4),
('演示文稿', 'presentation', 'PPT模板、演示文稿设计', 'file-text', 5),
('网页设计', 'web-design', '网站页面、移动端界面设计', 'monitor', 6),
('插画设计', 'illustration', '卡通插画、矢量插图设计', 'palette', 7),
('分镜图', 'storyboard', '视频分镜、故事板设计模板', 'film', 8);

-- 8. 创建用户偏好设置表（存储Jaaz相关配置）
CREATE TABLE user_design_preferences (
  user_id INT PRIMARY KEY,
  preferred_canvas_theme ENUM('light', 'dark', 'auto') DEFAULT 'auto',
  default_export_format ENUM('png', 'jpg', 'svg', 'pdf') DEFAULT 'png',
  auto_save_enabled BOOLEAN DEFAULT TRUE,
  ai_suggestions_enabled BOOLEAN DEFAULT TRUE,
  grid_snap_enabled BOOLEAN DEFAULT TRUE,
  canvas_settings JSON COMMENT '画布个人设置',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户设计偏好设置表';

-- 9. 创建系统配置表（存储Jaaz服务配置）
CREATE TABLE jaaz_system_config (
  config_key VARCHAR(100) PRIMARY KEY,
  config_value TEXT NOT NULL,
  config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
  description TEXT COMMENT '配置项描述',
  is_public BOOLEAN DEFAULT FALSE COMMENT '是否为公开配置（前端可访问）',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by INT COMMENT '更新者用户ID',
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Jaaz系统配置表';

-- 10. 插入默认系统配置
INSERT INTO jaaz_system_config (config_key, config_value, config_type, description, is_public) VALUES
('jaaz_service_url', 'http://localhost:57988', 'string', 'Jaaz后端服务地址', FALSE),
('max_canvas_elements', '1000', 'number', '画布最大元素数量', TRUE),
('max_design_file_size', '10485760', 'number', '设计文件最大尺寸（字节）', TRUE),
('ai_generation_timeout', '300', 'number', 'AI生成超时时间（秒）', FALSE),
('default_credits_per_generation', '5', 'number', '每次AI生成默认消耗积分（高成本）', TRUE),
('enable_public_gallery', 'true', 'boolean', '是否启用公共设计画廊', TRUE),
('maintenance_mode', 'false', 'boolean', '维护模式开关', TRUE);

-- 11. 在feature_costs表中添加Jaaz相关功能费用
INSERT INTO feature_costs (feature_key, cost, description) VALUES
('jaaz_ai_design_generation', 5, 'AI设计生成 - 消耗5积分'),
('jaaz_template_use', 1, '使用设计模板 - 消耗1积分'),
('jaaz_export_hd', 2, '高清导出设计 - 消耗2积分'),
('jaaz_ai_optimize', 3, 'AI优化设计 - 消耗3积分')
ON DUPLICATE KEY UPDATE 
  cost = VALUES(cost),
  description = VALUES(description);

-- 12. 添加触发器：自动更新设计作品的统计数据
DELIMITER $$

CREATE TRIGGER update_design_like_count 
AFTER INSERT ON design_likes
FOR EACH ROW
BEGIN
    UPDATE designs 
    SET like_count = (
        SELECT COUNT(*) FROM design_likes WHERE design_id = NEW.design_id
    )
    WHERE id = NEW.design_id;
END$$

CREATE TRIGGER update_design_like_count_delete
AFTER DELETE ON design_likes
FOR EACH ROW
BEGIN
    UPDATE designs 
    SET like_count = (
        SELECT COUNT(*) FROM design_likes WHERE design_id = OLD.design_id
    )
    WHERE id = OLD.design_id;
END$$

DELIMITER ;

-- 13. 验证脚本执行
SELECT 'Jaaz数据库集成完成！使用现有积分系统，无默认积分' as status;

-- 显示新增的表
SHOW TABLES LIKE '%design%';
SHOW TABLES LIKE '%jaaz%';

-- 显示feature_costs表中的Jaaz功能
SELECT * FROM feature_costs WHERE feature_key LIKE 'jaaz_%'; 