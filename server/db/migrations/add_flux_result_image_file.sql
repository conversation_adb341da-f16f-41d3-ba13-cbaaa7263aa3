-- 添加新列 flux_result_image_file
ALTER TABLE `prompt_examples` ADD COLUMN `flux_result_image_file` varchar(255) DEFAULT NULL;

-- 如果存在 flux_result_image 列且与新增列不同，则将数据转移到新列
UPDATE `prompt_examples` SET `flux_result_image_file` = `flux_result_image` WHERE `flux_result_image` IS NOT NULL;

-- 如果存在 flux_second_image 列且与代码中使用的 flux_second_image_file 不同，则重命名
-- 首先检查是否存在 flux_second_image 但不存在 flux_second_image_file
SET @columnExists = 0;
SELECT COUNT(*) INTO @columnExists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'prompt_examples' 
AND COLUMN_NAME = 'flux_second_image' 
AND NOT EXISTS (
    SELECT 1 FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'prompt_examples' 
    AND COLUMN_NAME = 'flux_second_image_file'
);

-- 如果需要进行重命名
SET @alterStatement = IF(@columnExists > 0, 
    'ALTER TABLE `prompt_examples` CHANGE COLUMN `flux_second_image` `flux_second_image_file` varchar(255) DEFAULT NULL',
    'SELECT "Column flux_second_image_file already exists or flux_second_image does not exist" AS message');

PREPARE stmt FROM @alterStatement;
EXECUTE stmt;
DEALLOCATE PREPARE stmt; 