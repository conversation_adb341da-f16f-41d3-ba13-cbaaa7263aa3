-- 创建印花提取历史记录表
CREATE TABLE IF NOT EXISTS `pattern_extraction_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `task_id` varchar(255) NOT NULL COMMENT 'ComfyUI任务ID',
  `image_name` varchar(255) NOT NULL COMMENT '原始图片文件名',
  `enhance_detail` tinyint(1) DEFAULT '0' COMMENT '是否增强细节',
  `remove_background` tinyint(1) DEFAULT '0' COMMENT '是否移除背景',
  `vectorize` tinyint(1) DEFAULT '0' COMMENT '是否矢量化',
  `status` enum('pending','processing','completed','error','timeout') NOT NULL DEFAULT 'pending' COMMENT '任务状态',
  `result_image` varchar(255) DEFAULT NULL COMMENT '结果图片文件名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `task_id` (`task_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='印花提取历史记录'; 