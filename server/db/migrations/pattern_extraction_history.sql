-- 创建印花提取历史记录表
CREATE TABLE IF NOT EXISTS `pattern_extraction_history` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `task_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'ComfyUI任务ID',
  `image_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始图像文件名',
  `enhance_detail` tinyint(1) DEFAULT '0' COMMENT '是否增强细节',
  `remove_background` tinyint(1) DEFAULT '1' COMMENT '是否移除背景',
  `vectorize` tinyint(1) DEFAULT '0' COMMENT '是否矢量化',
  `status` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'processing' COMMENT '任务状态: processing, completed, error, timeout',
  `result_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '结果图像文件名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id_index` (`user_id`),
  KEY `task_id_index` (`task_id`),
  KEY `status_index` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 