-- 创建Flux任务表
CREATE TABLE IF NOT EXISTS flux_tasks (
    id VARCHAR(36) PRIMARY KEY,
    flux_task_id VARCHAR(255) NOT NULL,
    user_id INT NOT NULL,
    prompt TEXT NOT NULL,
    polling_url TEXT NOT NULL,
    status ENUM('pending', 'succeeded', 'failed') NOT NULL DEFAULT 'pending',
    output_image_url TEXT,
    error TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    params JSON,
    result_data JSON,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
); 