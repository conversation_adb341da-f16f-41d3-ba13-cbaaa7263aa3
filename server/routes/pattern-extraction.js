/**
 * 印花提取功能路由
 * 基于FluxKontext ComfyUI工作流，实现从图片中提取印花图案并去除背景
 */

const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const pool = require('../db/pool');
const { authenticateToken } = require('../utils/auth');
const { checkAndDeductCredits: checkCredits } = require('../middleware/checkCredits');
const { deductCredits } = require('../middleware/deductCredits');
const rateLimit = require('../middleware/rateLimit').patternExtractionRateLimiter;
const axios = require('axios');

// 配置文件上传
const uploadDir = process.env.UPLOAD_DIR || 'uploads';
const uploadPath = path.join(__dirname, '..', uploadDir);

// 确保上传目录存在
if (!fs.existsSync(uploadPath)) {
  fs.mkdirSync(uploadPath, { recursive: true });
}

// 配置文件存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const uniqueName = `pattern_extraction_${Date.now()}_${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

// 创建上传中间件
const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB 限制
  fileFilter: function (req, file, cb) {
    const allowedTypes = /jpeg|jpg|png|webp/;
    const mimetype = allowedTypes.test(file.mimetype);
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    
    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('仅支持JPEG, JPG, PNG和WEBP格式的图片!'));
  }
}).single('image');

// 常量定义
const FEATURE_KEY = 'pattern_extraction';
const COMFYUI_WORKFLOW_NAME = 'yinhua';  // 数据库中的印花提取工作流名称
const COMFYUI_REMBG_WORKFLOW_NAME = 'comfy_抠图标准流';  // 数据库中的去背景工作流名称，ID为3
const COMFYUI_LOAD_IMAGE_NODE_ID = "189"; // 从该节点加载图像
const COMFYUI_SAVE_IMAGE_NODE_ID = "136"; // 从该节点获取结果图像
const COMFYUI_REMBG_LOAD_IMAGE_NODE_ID = "47"; // 去背景工作流中加载图像的节点ID
const COMFYUI_REMBG_SAVE_IMAGE_NODE_ID = "49"; // 去背景工作流中保存图像的节点ID

// 添加矢量化URL常量，与imageUtils.js保持一致
const COMFYUI_BASE_URL_FOR_VECTORIZE = process.env.COMFYUI_BASE_URL_FOR_VECTORIZE || 'https://u63642-a2b4-fccb0b0d.westx.seetacloud.com:8443';

/**
 * 从数据库获取印花提取工作流
 */
async function getPatternExtractionWorkflow() {
  const connection = await pool.getConnection();
  try {
    const [rows] = await connection.query(
      'SELECT workflow_json FROM workflows WHERE name = ?', 
      [COMFYUI_WORKFLOW_NAME]
    );
    
    if (rows.length === 0) {
      throw new Error(`找不到印花提取工作流: ${COMFYUI_WORKFLOW_NAME}`);
    }
    
    // 检查workflow_json是否已经是对象
    const workflowJson = rows[0].workflow_json;
    if (typeof workflowJson === 'string') {
      return JSON.parse(workflowJson);
    } else {
      return workflowJson; // 已经是对象，直接返回
    }
  } finally {
    connection.release();
  }
}

/**
 * 从数据库获取去背景工作流
 */
async function getRemoveBackgroundWorkflow() {
  const connection = await pool.getConnection();
  try {
    const [rows] = await connection.query(
      'SELECT workflow_json FROM workflows WHERE name = ?', 
      [COMFYUI_REMBG_WORKFLOW_NAME]
    );
    
    if (rows.length === 0) {
      throw new Error(`找不到去背景工作流: ${COMFYUI_REMBG_WORKFLOW_NAME}`);
    }
    
    // 检查workflow_json是否已经是对象
    const workflowJson = rows[0].workflow_json;
    if (typeof workflowJson === 'string') {
      return JSON.parse(workflowJson);
    } else {
      return workflowJson; // 已经是对象，直接返回
    }
  } finally {
    connection.release();
  }
}

/**
 * 准备工作流，应用用户设置
 * @param {Object} workflowJson - 原始工作流JSON
 * @param {Object} options - 用户选项
 * @returns {Object} 修改后的工作流JSON
 */
async function prepareWorkflow(workflowJson, options = {}) {
  try {
    // 1. 设置图像尺寸
    const width = options.width || 1024;
    const height = options.height || 1024;
    
    // 确保宽高在合理范围内
    const validWidth = Math.min(Math.max(width, 512), 2048);
    const validHeight = Math.min(Math.max(height, 512), 2048);
    
    // 修改EmptySD3LatentImage节点的宽高
    if (workflowJson["203"] && workflowJson["203"].inputs) {
      workflowJson["203"].inputs.width = validWidth;
      workflowJson["203"].inputs.height = validHeight;
    }
    
    // 2. 生成随机种子
    // 生成0到916673751187820之间的随机数
    const maxSeed = 916673751187820;
    const randomSeed = Math.floor(Math.random() * (maxSeed + 1));
    
    // 修改KSampler节点的种子值
    if (workflowJson["31"] && workflowJson["31"].inputs) {
      workflowJson["31"].inputs.seed = randomSeed;
    }
    
    return workflowJson;
  } catch (error) {
    console.error('准备工作流时出错:', error);
    return workflowJson; // 出错时返回原始工作流
  }
}

/**
 * 向ComfyUI提交任务
 */
async function submitComfyUITask(workflowJson, imageName, options = {}) {
  try {
    // 获取ComfyUI的基础URL
    const COMFYUI_BASE_URL = process.env.COMFYUI_GPU_URL;
    if (!COMFYUI_BASE_URL) {
      throw new Error('ComfyUI GPU URL未配置');
    }
    
    // 准备工作流，应用用户设置
    workflowJson = await prepareWorkflow(workflowJson, options);
    
    // 1. 首先上传图片到ComfyUI
    const imagePath = path.join(uploadPath, imageName);
    if (!fs.existsSync(imagePath)) {
      throw new Error(`图片文件不存在: ${imagePath}`);
    }
    
    // 读取图片文件
    const imageBuffer = fs.readFileSync(imagePath);
    
    // 上传图片到ComfyUI - 使用form-data包
    const FormData = require('form-data');
    const formData = new FormData();
    formData.append('image', imageBuffer, {
      filename: imageName,
      contentType: 'image/jpeg' // 根据实际文件类型调整
    });
    formData.append('overwrite', 'true');
    
    console.log(`[Pattern Extraction] 上传图片到ComfyUI: ${imageName}`);
    const uploadResponse = await axios.post(`${COMFYUI_BASE_URL}/upload/image`, formData, {
      headers: formData.getHeaders()
    });
    
    if (uploadResponse.status !== 200 || !uploadResponse.data || !uploadResponse.data.name) {
      console.error('[Pattern Extraction] ComfyUI上传失败:', uploadResponse.data);
      throw new Error('上传图片到ComfyUI失败');
    }
    
    console.log('[Pattern Extraction] ComfyUI上传成功:', uploadResponse.data);
    const uploadedImageName = uploadResponse.data.name;
    
    // 2. 修改工作流，设置输入图像为上传后的图像名称
    workflowJson[COMFYUI_LOAD_IMAGE_NODE_ID].inputs.image = uploadedImageName;
    
    // 3. 准备提交数据
    const promptData = {
      prompt: workflowJson,
      client_id: uuidv4()
    };
    
    // 4. 提交任务到ComfyUI
    const response = await axios.post(`${COMFYUI_BASE_URL}/prompt`, promptData);
    
    if (!response.data || !response.data.prompt_id) {
      throw new Error('提交任务到ComfyUI失败: 未获取到任务ID');
    }
    
    return {
      taskId: response.data.prompt_id,
      clientId: promptData.client_id
    };
  } catch (error) {
    console.error('向ComfyUI提交任务失败:', error);
    throw new Error(`提交任务失败: ${error.message}`);
  }
}

/**
 * 保存任务到数据库
 */
async function saveTaskToDatabase(userId, taskId, imageName, options) {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();
    
    // 1. 插入任务记录
    const [result] = await connection.query(
      `INSERT INTO pattern_extraction_history 
       (user_id, task_id, image_name, enhance_detail, remove_background, vectorize, status) 
       VALUES (?, ?, ?, ?, ?, ?, 'processing')`,
      [userId, taskId, imageName, options.enhanceDetail, options.removeBackground, options.vectorize]
    );
    
    await connection.commit();
    return result.insertId;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}

/**
 * 获取任务状态
 */
async function getTaskStatus(taskId) {
  try {
    // 获取ComfyUI的基础URL
    const COMFYUI_BASE_URL = process.env.COMFYUI_GPU_URL;
    if (!COMFYUI_BASE_URL) {
      throw new Error('ComfyUI GPU URL未配置');
    }
    
    // 1. 获取任务历史
    const historyResponse = await axios.get(`${COMFYUI_BASE_URL}/history`);
    
    if (!historyResponse.data || !historyResponse.data[taskId]) {
      // 任务可能还在队列中
      return { 
        status: 'pending',
        message: '任务正在等待中' 
      };
    }
    
    const taskHistory = historyResponse.data[taskId];
    
    // 2. 检查是否有错误
    if (taskHistory.error) {
      return {
        status: 'error',
        message: '处理失败: ' + taskHistory.error
      };
    }
    
    // 3. 检查是否有输出图像
    if (taskHistory.outputs && taskHistory.outputs[COMFYUI_SAVE_IMAGE_NODE_ID] &&
        taskHistory.outputs[COMFYUI_SAVE_IMAGE_NODE_ID].images) {
      // 找到第一张输出图片
      const firstImage = taskHistory.outputs[COMFYUI_SAVE_IMAGE_NODE_ID].images[0];
      return {
        status: 'completed',
        message: '印花提取成功',
        result: {
          filename: firstImage.filename,
          subfolder: firstImage.subfolder || '',
          type: firstImage.type || 'output'
        }
      };
    }
    
    // 4. 任务正在处理中
    return {
      status: 'processing',
      message: '印花正在提取中',
      progress: taskHistory.progress || 0
    };
  } catch (error) {
    console.error('获取任务状态失败:', error);
    throw new Error(`获取任务状态失败: ${error.message}`);
  }
}

/**
 * 更新数据库中的任务状态
 */
async function updateTaskInDatabase(recordId, status, resultData = null) {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();
    
    if (status === 'completed' && resultData) {
      // 使用完整的文件名，确保能够通过ComfyUI的view接口访问
      console.log(`[Pattern Extraction] 更新任务状态为完成，结果图片: ${resultData.filename}`);
      await connection.query(
        `UPDATE pattern_extraction_history 
         SET status = ?, result_image = ?, updated_at = NOW() 
         WHERE id = ?`,
        [status, resultData.filename, recordId]
      );
    } else {
      await connection.query(
        `UPDATE pattern_extraction_history 
         SET status = ?, updated_at = NOW() 
         WHERE id = ?`,
        [status, recordId]
      );
    }
    
    await connection.commit();
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}

/**
 * 使用ComfyUI去除背景
 * @param {string} imageUrl - 原始图片URL
 * @returns {Promise<string>} 去背景后的图片URL
 */
async function removeBackgroundWithComfyUI(imageUrl) {
  try {
    // 1. 获取ComfyUI的基础URL
    const COMFYUI_BASE_URL = process.env.COMFYUI_GPU_URL;
    if (!COMFYUI_BASE_URL) {
      throw new Error('ComfyUI GPU URL未配置');
    }
    
    console.log(`[Remove Background] 开始处理图片: ${imageUrl}`);
    console.log(`[Remove Background] 使用ComfyUI URL: ${COMFYUI_BASE_URL}`);
    console.log(`[Remove Background] 使用工作流: ${COMFYUI_REMBG_WORKFLOW_NAME}`);
    console.log(`[Remove Background] 加载图像节点ID: ${COMFYUI_REMBG_LOAD_IMAGE_NODE_ID}`);
    console.log(`[Remove Background] 保存图像节点ID: ${COMFYUI_REMBG_SAVE_IMAGE_NODE_ID}`);
    
    // 2. 下载图片
    const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    const imageBuffer = Buffer.from(imageResponse.data);
    
    // 3. 生成唯一文件名
    const tempImageName = `temp_for_rembg_${Date.now()}_${uuidv4()}.png`;
    
    // 4. 上传图片到ComfyUI
    const FormData = require('form-data');
    const formData = new FormData();
    formData.append('image', imageBuffer, {
      filename: tempImageName,
      contentType: 'image/png'
    });
    formData.append('overwrite', 'true');
    
    console.log(`[Remove Background] 上传图片到ComfyUI: ${tempImageName}`);
    const uploadResponse = await axios.post(`${COMFYUI_BASE_URL}/upload/image`, formData, {
      headers: formData.getHeaders()
    });
    
    if (uploadResponse.status !== 200 || !uploadResponse.data || !uploadResponse.data.name) {
      console.error('[Remove Background] ComfyUI上传失败:', uploadResponse.data);
      throw new Error('上传图片到ComfyUI失败');
    }
    
    console.log('[Remove Background] ComfyUI上传成功:', uploadResponse.data);
    const uploadedImageName = uploadResponse.data.name;
    
    // 5. 获取去背景工作流
    const workflowJson = await getRemoveBackgroundWorkflow();
    console.log(`[Remove Background] 获取到工作流:`, JSON.stringify(workflowJson).substring(0, 200) + '...');
    
    // 6. 修改工作流，设置输入图像为上传后的图像名称
    if (!workflowJson[COMFYUI_REMBG_LOAD_IMAGE_NODE_ID]) {
      console.error(`[Remove Background] 工作流中未找到节点ID: ${COMFYUI_REMBG_LOAD_IMAGE_NODE_ID}`);
      console.log(`[Remove Background] 工作流中的节点IDs:`, Object.keys(workflowJson));
      throw new Error(`工作流中未找到加载图像节点ID: ${COMFYUI_REMBG_LOAD_IMAGE_NODE_ID}`);
    }
    
    workflowJson[COMFYUI_REMBG_LOAD_IMAGE_NODE_ID].inputs.image = uploadedImageName;
    console.log(`[Remove Background] 已设置输入图像: ${uploadedImageName}`);
    
    // 7. 准备提交数据
    const clientId = uuidv4();
    const promptData = {
      prompt: workflowJson,
      client_id: clientId
    };
    console.log(`[Remove Background] 准备提交任务，客户端ID: ${clientId}`);
    
    // 8. 提交任务到ComfyUI
    const response = await axios.post(`${COMFYUI_BASE_URL}/prompt`, promptData);
    
    if (!response.data || !response.data.prompt_id) {
      console.error('[Remove Background] 提交任务失败:', response.data);
      throw new Error('提交去背景任务到ComfyUI失败: 未获取到任务ID');
    }
    
    const taskId = response.data.prompt_id;
    console.log(`[Remove Background] 任务已提交，任务ID: ${taskId}`);
    
    // 9. 轮询任务状态直到完成
    let completed = false;
    let resultImageFilename = null;
    let attempts = 0;
    const maxAttempts = 30; // 最多等待60秒
    
    while (!completed && attempts < maxAttempts) {
      attempts++;
      // 等待2秒后检查状态
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log(`[Remove Background] 轮询任务状态 (${attempts}/${maxAttempts}): ${taskId}`);
      
      // 获取任务历史
      const historyResponse = await axios.get(`${COMFYUI_BASE_URL}/history`);
      
      if (!historyResponse.data || !historyResponse.data[taskId]) {
        // 任务可能还在队列中，继续等待
        console.log(`[Remove Background] 任务 ${taskId} 仍在队列中，继续等待...`);
        continue;
      }
      
      const taskHistory = historyResponse.data[taskId];
      console.log(`[Remove Background] 任务历史:`, JSON.stringify(taskHistory).substring(0, 200) + '...');
      
      // 检查是否有错误
      if (taskHistory.error) {
        console.error(`[Remove Background] 任务出错:`, taskHistory.error);
        throw new Error('去背景处理失败: ' + taskHistory.error);
      }
      
      // 检查是否有输出图像
      if (taskHistory.outputs) {
        console.log(`[Remove Background] 任务输出节点:`, Object.keys(taskHistory.outputs));
        
        if (taskHistory.outputs[COMFYUI_REMBG_SAVE_IMAGE_NODE_ID] &&
            taskHistory.outputs[COMFYUI_REMBG_SAVE_IMAGE_NODE_ID].images) {
          // 找到第一张输出图片
          const firstImage = taskHistory.outputs[COMFYUI_REMBG_SAVE_IMAGE_NODE_ID].images[0];
          resultImageFilename = firstImage.filename;
          console.log(`[Remove Background] 找到结果图片: ${resultImageFilename}`);
          completed = true;
        } else {
          console.log(`[Remove Background] 未找到节点 ${COMFYUI_REMBG_SAVE_IMAGE_NODE_ID} 的输出图像`);
        }
      }
    }
    
    if (!resultImageFilename) {
      throw new Error('去背景处理失败: 未获取到结果图片');
    }
    
    // 10. 构建结果图像URL
    const resultImageUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(resultImageFilename)}&type=output&subfolder=`;
    console.log(`[Remove Background] 处理完成，结果图片URL: ${resultImageUrl}`);
    
    return resultImageUrl;
  } catch (error) {
    console.error('使用ComfyUI去除背景失败:', error);
    throw new Error(`去背景失败: ${error.message}`);
  }
}

// API路由

/**
 * 开始印花提取任务
 * POST /api/pattern-extraction/extract
 */
router.post('/extract', authenticateToken, rateLimit, checkCredits(FEATURE_KEY), upload, async (req, res) => {
  try {
    // 1. 验证请求
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传图片'
      });
    }
    
    const enhanceDetail = req.body.enhance === 'true' || req.body.enhance === true;
    const removeBackground = req.body.removeBackground === 'true' || req.body.removeBackground === true;
    const vectorize = req.body.vectorize === 'true' || req.body.vectorize === true;
    
    // 获取尺寸参数（如果有）
    const width = parseInt(req.body.width) || 1024;
    const height = parseInt(req.body.height) || 1024;
    
    // 2. 获取工作流
    const workflowJson = await getPatternExtractionWorkflow();
    
    // 3. 提交任务到ComfyUI，传递尺寸选项
    const { taskId } = await submitComfyUITask(workflowJson, req.file.filename, { width, height });
    
    // 4. 保存任务到数据库
    const recordId = await saveTaskToDatabase(
      req.user.id, 
      taskId, 
      req.file.filename, 
      { enhanceDetail, removeBackground, vectorize, width, height }
    );
    
    // 5. 扣除积分 - 注意：根据前面的分析，这里应该删除这行代码，因为checkCredits中间件已经扣除了积分
    // await deductCredits(req, FEATURE_KEY);
    
    // 6. 开始后台轮询任务状态 (不等待结果)
    pollTaskStatus(taskId, recordId);
    
    // 7. 返回任务ID给前端
    res.status(201).json({
      success: true,
      message: '印花提取任务已提交',
      task: {
        id: taskId,
        record_id: recordId
      }
    });
  } catch (error) {
    console.error('提交印花提取任务失败:', error);
    res.status(500).json({
      success: false,
      message: `提交印花提取任务失败: ${error.message}`
    });
  }
});

/**
 * 获取任务状态
 * GET /api/pattern-extraction/task/:taskId
 */
router.get('/task/:taskId', authenticateToken, async (req, res) => {
  try {
    const { taskId } = req.params;
    
    // 1. 检查任务是否属于该用户
    const connection = await pool.getConnection();
    try {
      const [tasks] = await connection.query(
        `SELECT * FROM pattern_extraction_history WHERE task_id = ? AND user_id = ?`,
        [taskId, req.user.id]
      );
      
      if (tasks.length === 0) {
        connection.release();
        return res.status(404).json({
          success: false,
          message: '任务不存在或无权访问'
        });
      }
      
      const task = tasks[0];
      connection.release();
      
      // 2. 如果任务已完成或出错，直接返回结果
      if (task.status === 'completed' || task.status === 'error') {
        // 构建结果图像URL - 使用ComfyUI的view接口
        let resultImageUrl = null;
        if (task.result_image) {
          // 使用ComfyUI的view接口构建URL
          const COMFYUI_BASE_URL = process.env.COMFYUI_GPU_URL;
          if (COMFYUI_BASE_URL) {
            resultImageUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(task.result_image)}&type=output&subfolder=`;
          } else {
            // 降级处理
            const baseUrl = process.env.API_BASE_URL || `http://localhost:${process.env.PORT || 32009}`;
            resultImageUrl = `${baseUrl}/output/${task.result_image}`;
          }
        }
        
        return res.json({
          success: true,
          status: task.status,
          message: task.status === 'completed' ? '印花提取成功' : '印花提取失败',
          result: task.status === 'completed' ? {
            image_url: resultImageUrl,
            created_at: task.created_at,
            updated_at: task.updated_at
          } : null
        });
      }
      
      // 3. 如果任务还在处理中，从ComfyUI获取最新状态
      const taskStatus = await getTaskStatus(taskId);
      
      // 4. 如果状态有变化，更新数据库
      if (taskStatus.status !== task.status) {
        await updateTaskInDatabase(
          task.id, 
          taskStatus.status, 
          taskStatus.result
        );
        
        // 如果任务完成，构建结果图像URL
        if (taskStatus.status === 'completed' && taskStatus.result) {
          // 判断文件是否为SVG文件（矢量化结果）
          const isSvgFile = taskStatus.result.filename.toLowerCase().endsWith('.svg');
          
          if (isSvgFile) {
            // 使用矢量化专用URL
            taskStatus.result.image_url = `${COMFYUI_BASE_URL_FOR_VECTORIZE}/view?filename=${encodeURIComponent(taskStatus.result.filename)}&type=input&subfolder=${encodeURIComponent(taskStatus.result.subfolder || '')}`;
          } else {
            // 使用ComfyUI的view接口构建URL
            const COMFYUI_BASE_URL = process.env.COMFYUI_GPU_URL;
            if (COMFYUI_BASE_URL) {
              taskStatus.result.image_url = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(taskStatus.result.filename)}&type=${taskStatus.result.type}&subfolder=${encodeURIComponent(taskStatus.result.subfolder || '')}`;
            } else {
              // 降级处理
              const baseUrl = process.env.API_BASE_URL || `http://localhost:${process.env.PORT || 32009}`;
              taskStatus.result.image_url = `${baseUrl}/view?filename=${encodeURIComponent(taskStatus.result.filename)}&type=${taskStatus.result.type}&subfolder=${encodeURIComponent(taskStatus.result.subfolder || '')}`;
            }
          }
        }
      }
      
      return res.json({
        success: true,
        status: taskStatus.status,
        message: taskStatus.message,
        progress: taskStatus.progress || 0,
        result: taskStatus.result
      });
    } catch (dbError) {
      connection.release();
      throw dbError;
    }
  } catch (error) {
    console.error('获取任务状态失败:', error);
    res.status(500).json({
      success: false,
      message: `获取任务状态失败: ${error.message}`
    });
  }
});

/**
 * 获取用户的印花提取历史记录
 * GET /api/pattern-extraction/history
 */
router.get('/history', authenticateToken, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;
    const offset = (page - 1) * pageSize;
    
    const connection = await pool.getConnection();
    try {
      // 1. 获取历史记录总数
      const [[countResult]] = await connection.query(
        `SELECT COUNT(*) AS total FROM pattern_extraction_history WHERE user_id = ?`,
        [req.user.id]
      );
      
      // 2. 获取分页后的历史记录
      const [records] = await connection.query(
        `SELECT * FROM pattern_extraction_history 
         WHERE user_id = ? 
         ORDER BY created_at DESC 
         LIMIT ? OFFSET ?`,
        [req.user.id, pageSize, offset]
      );
      
      connection.release();
      
      // 3. 构建结果图像URL
      const COMFYUI_BASE_URL = process.env.COMFYUI_GPU_URL;
      const baseUrl = process.env.API_BASE_URL || `http://localhost:${process.env.PORT || 32009}`;
      
      const recordsWithUrls = records.map(record => {
        let resultImageUrl = null;
        if (record.result_image) {
          // 判断文件是否为SVG文件（矢量化结果）
          const isSvgFile = record.result_image.toLowerCase().endsWith('.svg');
          
          // 为SVG文件使用矢量化专用URL，为其他文件使用标准URL
          if (isSvgFile) {
            resultImageUrl = `${COMFYUI_BASE_URL_FOR_VECTORIZE}/view?filename=${encodeURIComponent(record.result_image)}&type=input&subfolder=`;
            console.log(`[Pattern Extraction History] 构建SVG矢量图URL: ${resultImageUrl}`);
          } else if (COMFYUI_BASE_URL) {
            // 普通图像使用标准ComfyUI URL
            resultImageUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(record.result_image)}&type=output&subfolder=`;
            console.log(`[Pattern Extraction History] 构建普通图片URL: ${resultImageUrl}`);
          } else {
            // 降级处理
            resultImageUrl = `${baseUrl}/output/${record.result_image}`;
          }
        }
        
        // 处理processing_info字段
        let processingInfo = null;
        if (record.processing_info) {
          try {
            if (typeof record.processing_info === 'string') {
              processingInfo = JSON.parse(record.processing_info);
            } else {
              processingInfo = record.processing_info;
            }
          } catch (e) {
            console.warn('解析处理信息失败:', e);
            processingInfo = {};
          }
        }
        
        return {
          ...record,
          result_image_url: resultImageUrl,
          processing_info: processingInfo
        };
      });
      
      // 4. 返回结果
      return res.json({
        success: true,
        total: countResult.total,
        page,
        pageSize,
        records: recordsWithUrls,
        hasMore: countResult.total > offset + pageSize
      });
    } catch (dbError) {
      connection.release();
      throw dbError;
    }
  } catch (error) {
    console.error('获取历史记录失败:', error);
    res.status(500).json({
      success: false,
      message: `获取历史记录失败: ${error.message}`
    });
  }
});

/**
 * 删除历史记录
 * DELETE /api/pattern-extraction/history/:id
 */
router.delete('/history/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    const connection = await pool.getConnection();
    try {
      // 1. 检查记录是否存在且属于该用户
      const [records] = await connection.query(
        `SELECT * FROM pattern_extraction_history WHERE id = ? AND user_id = ?`,
        [id, req.user.id]
      );
      
      if (records.length === 0) {
        connection.release();
        return res.status(404).json({
          success: false,
          message: '记录不存在或无权删除'
        });
      }
      
      // 2. 删除记录
      await connection.query(
        `DELETE FROM pattern_extraction_history WHERE id = ?`,
        [id]
      );
      
      connection.release();
      
      return res.json({
        success: true,
        message: '记录已删除'
      });
    } catch (dbError) {
      connection.release();
      throw dbError;
    }
  } catch (error) {
    console.error('删除历史记录失败:', error);
    res.status(500).json({
      success: false,
      message: `删除历史记录失败: ${error.message}`
    });
  }
});

/**
 * 后台轮询任务状态直到完成
 */
async function pollTaskStatus(taskId, recordId, interval = 5000, maxRetries = 120) {
  let retries = 0;
  
  const poll = async () => {
    try {
      // 1. 获取任务状态
      const taskStatus = await getTaskStatus(taskId);
      console.log(`轮询任务状态 [${taskId}]`, taskStatus.status);
      
      // 2. 根据状态采取行动
      if (taskStatus.status === 'completed') {
        // 任务完成，更新数据库
        await updateTaskInDatabase(recordId, 'completed', taskStatus.result);
        return;
      } else if (taskStatus.status === 'error') {
        // 任务出错，更新数据库
        await updateTaskInDatabase(recordId, 'error');
        return;
      } else if (taskStatus.status === 'processing' || taskStatus.status === 'pending') {
        // 继续轮询
        retries++;
        if (retries < maxRetries) {
          setTimeout(poll, interval);
        } else {
          console.error(`任务 [${taskId}] 超时，停止轮询`);
          await updateTaskInDatabase(recordId, 'timeout');
        }
      }
    } catch (error) {
      console.error(`轮询任务状态失败 [${taskId}]:`, error);
      retries++;
      if (retries < maxRetries) {
        setTimeout(poll, interval);
      } else {
        console.error(`任务 [${taskId}] 轮询超时，停止轮询`);
        try {
          await updateTaskInDatabase(recordId, 'error');
        } catch (dbError) {
          console.error(`更新任务状态失败 [${taskId}]:`, dbError);
        }
      }
    }
  };
  
  // 开始轮询
  poll();
}

/**
 * 更新印花提取结果
 * POST /api/pattern-extraction/update-result
 * 用于将抠图或矢量化后的结果更新到印花提取历史记录中
 */
router.post('/update-result', authenticateToken, async (req, res) => {
  try {
    const { taskId, newImageUrl, processingInfo } = req.body;
    
    if (!taskId || !newImageUrl) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    // 1. 检查任务是否属于该用户
    const connection = await pool.getConnection();
    try {
      const [tasks] = await connection.query(
        `SELECT * FROM pattern_extraction_history WHERE task_id = ? AND user_id = ?`,
        [taskId, req.user.id]
      );
      
      if (tasks.length === 0) {
        connection.release();
        return res.status(404).json({
          success: false,
          message: '任务不存在或无权访问'
        });
      }
      
      const task = tasks[0];
      
      // 2. 从URL中提取文件名
      let filename = '';
      try {
        const url = new URL(newImageUrl);
        
        // 检查是否是ComfyUI的view接口URL
        if (url.pathname.includes('/view')) {
          // 从查询参数中获取filename
          const params = new URLSearchParams(url.search);
          filename = params.get('filename');
          console.log(`[Pattern Extraction Update] 从ComfyUI view URL提取文件名: ${filename}`);
        } else {
          // 常规URL，从路径中获取文件名
          filename = decodeURIComponent(url.pathname.split('/').pop());
          console.log(`[Pattern Extraction Update] 从路径中提取文件名: ${filename}`);
        }
        
        if (!filename) {
          throw new Error('无法从URL中提取文件名');
        }
        
        // 检查URL是否包含矢量化服务器地址，如果是，确保使用正确的服务器访问
        const isSvgFile = filename.toLowerCase().endsWith('.svg');
        const isVectorizeUrl = url.origin === COMFYUI_BASE_URL_FOR_VECTORIZE || 
                              newImageUrl.includes('_vector_oss.svg');
                              
        if (isSvgFile && !isVectorizeUrl) {
          console.log(`[Pattern Extraction Update] 检测到SVG文件但使用非矢量化URL: ${newImageUrl}`);
          console.log(`[Pattern Extraction Update] 将转换为正确的矢量化服务器URL`);
          // 自动修正为正确的矢量化服务器URL
        }
      } catch (error) {
        console.warn(`[Pattern Extraction Update] URL解析失败: ${error.message}, 使用完整URL作为文件名`);
        // 如果URL解析失败，直接使用URL作为文件名
        filename = newImageUrl;
      }
      
      // 3. 更新任务结果
      await connection.query(
        `UPDATE pattern_extraction_history 
         SET result_image = ?, 
             processing_info = ?,
             updated_at = NOW() 
         WHERE id = ?`,
        [filename, JSON.stringify(processingInfo || {}), task.id]
      );
      
      connection.release();
      
      // 4. 返回成功响应
      return res.json({
        success: true,
        message: '印花提取结果已更新'
      });
    } catch (error) {
      if (connection) connection.release();
      throw error;
    }
  } catch (error) {
    console.error('更新印花提取结果失败:', error);
    res.status(500).json({
      success: false,
      message: `更新印花提取结果失败: ${error.message}`
    });
  }
});

/**
 * 使用ComfyUI去除背景
 * POST /api/pattern-extraction/remove-background-comfyui
 */
router.post('/remove-background-comfyui', authenticateToken, async (req, res) => {
  try {
    const { imageUrl } = req.body;
    
    if (!imageUrl) {
      return res.status(400).json({
        success: false,
        message: '缺少图片URL参数'
      });
    }
    
    // 使用ComfyUI去除背景
    const resultImageUrl = await removeBackgroundWithComfyUI(imageUrl);
    
    // 返回结果
    res.json({
      success: true,
      message: '背景去除成功',
      imageUrl: resultImageUrl
    });
  } catch (error) {
    console.error('去除背景失败:', error);
    res.status(500).json({
      success: false,
      message: `去除背景失败: ${error.message}`
    });
  }
});

module.exports = router; 