/**
 * Flux Kontext Pro API路由
 */

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../utils/auth');
const { checkAndDeductCredits } = require('../middleware/checkCredits');
const multer = require('multer');
const { 
    startFluxProcessing,
    startComfyFluxKontextProcessing, 
    getTaskStatus,
    getTaskDetails,
    getUserTasks,
    deleteTask,
    getComfyUIQueue
} = require('../flux-service');

// 为文件上传设置内存存储
const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

// POST /api/flux/process - 处理旧的、非ComfyUI的Flux任务
router.post('/process', authenticateToken, checkAndDeductCredits('flux_kontext_pro'), async (req, res) => {
    try {
        const result = await startFluxProcessing(req.body, req.user.id);
        res.json({ 
            success: true, 
            ...result, 
            newCredits: req.updatedCreditsInfo?.cumulativeCredits,
            remainingDailyFreeCredits: req.updatedCreditsInfo?.remainingDailyFreeCredits
        });
    } catch (error) {
        console.error('Flux处理路由错误:', error);
        res.status(500).json({ success: false, message: error.message });
    }
});

// POST /api/flux/comfy-process - 处理新的基于ComfyUI的Flux任务
router.post('/comfy-process', 
    authenticateToken, 
    checkAndDeductCredits('comfy_flux_kontext'), // 确保'comfy_flux_kontext'在feature_costs表中
    upload.single('image1'), // 使用 multer 处理名为 'image1' 的文件
    async (req, res) => {
        try {
            if (!req.file) {
                return res.status(400).json({ success: false, message: '缺少图片文件。' });
            }
            // 将 req.body (文本字段) 和 req.file (文件) 一起传递
            const result = await startComfyFluxKontextProcessing(req.body, req.file, req.user.id);
            res.json({ 
                success: true, 
                ...result, 
                newCredits: req.updatedCreditsInfo?.cumulativeCredits,
                remainingDailyFreeCredits: req.updatedCreditsInfo?.remainingDailyFreeCredits
            });
        } catch (error) {
            console.error('Comfy Flux处理路由错误:', error);
            res.status(500).json({ success: false, message: error.message });
    }
});

/**
 * 获取任务状态
 * GET /api/flux/task/:taskId
 */
router.get('/task/:taskId', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const taskId = req.params.taskId;
        
        const task = await getTaskDetails(taskId, userId);
        res.json(task);
    } catch (error) {
        console.error('获取任务详情失败:', error);
        res.status(404).json({ error: error.message || '任务不存在或无权访问' });
    }
});

/**
 * 获取用户历史任务
 * GET /api/flux/history
 */
router.get('/history', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.pageSize) || 10;
        
        const history = await getUserTasks(userId, page, pageSize);
        res.json(history);
    } catch (error) {
        console.error('获取历史任务失败:', error);
        res.status(500).json({ error: error.message || '服务器错误' });
    }
});

/**
 * 删除任务
 * DELETE /api/flux/task/:taskId
 */
router.delete('/task/:taskId', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const taskId = req.params.taskId;
        
        const success = await deleteTask(taskId, userId);
        
        if (success) {
            res.json({ success: true });
        } else {
            res.status(404).json({ error: '任务不存在或无权删除' });
        }
    } catch (error) {
        console.error('删除任务失败:', error);
        res.status(500).json({ error: error.message || '服务器错误' });
    }
});

/**
 * 获取ComfyUI队列状态
 * GET /api/flux/comfy-queue-status
 */
router.get('/comfy-queue-status', authenticateToken, async (req, res) => {
    try {
        const queueData = await getComfyUIQueue();
        if (!queueData) {
            return res.status(503).json({
                success: false,
                message: 'ComfyUI服务不可用或未配置',
                queue: {
                    remaining: 0,
                    available: false
                }
            });
        }
        
        // 返回队列信息
        return res.json({
            success: true,
            queue: {
                remaining: queueData.exec_info?.queue_remaining || 0,
                available: true
            }
        });
    } catch (error) {
        console.error('[API] 获取ComfyUI队列状态失败:', error);
        return res.status(500).json({
            success: false,
            message: '获取队列信息失败',
            error: error.message
        });
    }
});

module.exports = router; 