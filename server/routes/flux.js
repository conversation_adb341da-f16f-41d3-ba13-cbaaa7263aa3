const express = require('express');
const router = express.Router();
const fluxService = require('../flux-service');
const { checkCredits } = require('../middleware/checkCredits');
const { deductCredits } = require('../middleware/deductCredits');
const { requireAuth } = require('../middleware/requireAuth');
const multer = require('multer');
const upload = multer({ limits: { fileSize: 50 * 1024 * 1024 } }); // 50MB限制

/**
 * @route POST /api/flux/process
 * @desc 开始处理一个Flux任务
 * @access 需要授权
 */
router.post('/process', requireAuth, upload.none(), async (req, res) => {
    console.log('[Flux Router] 收到处理请求');
    
    try {
        const userId = req.user.id;
        const { prompt, input_image, model = 'pro', negative_prompt, seed, skip_credit_check } = req.body;
        
        // 验证必要参数
        if (!prompt || !input_image) {
            return res.status(400).json({
                success: false,
                error: '缺少必要参数：prompt 和 input_image'
            });
        }
        
        console.log(`[Flux Router] 用户 ${userId} 请求处理任务，提示词: ${prompt.substring(0, 30)}${prompt.length > 30 ? '...' : ''}`);
        
        // 如果不跳过积分检查，则进行积分扣除
        if (!skip_credit_check) {
            const feature = model === 'max' ? 'flux-kontext-max' : 'flux-kontext-pro';
            const creditCost = model === 'max' ? 4 : 3; // max版本消耗4积分，pro版本消耗3积分
            
            // 检查用户是否有足够的积分
            if (!await checkCredits(req, creditCost)) {
                return res.status(402).json({
                    success: false,
                    error: '积分不足',
                    required_credits: creditCost
                });
            }
            
            // 扣除积分
            const deductResult = await deductCredits(req, creditCost, `使用Flux Kontext ${model.toUpperCase()}图像编辑`);
            if (!deductResult.success) {
                return res.status(500).json({
                    success: false,
                    error: '扣除积分失败',
                    message: deductResult.message
                });
            }
            
            console.log(`[Flux Router] 用户 ${userId} 成功扣除 ${creditCost} 积分`);
        } else {
            console.log(`[Flux Router] 用户 ${userId} 跳过积分检查`);
        }
        
        // 调用服务处理任务
        const taskResult = await fluxService.startFluxProcessing({
            prompt,
            input_image,
            model,
            negative_prompt,
            seed: seed ? parseInt(seed) : undefined,
            api_provider: 'replicate' // 指定使用Replicate API
        }, userId);
        
        console.log(`[Flux Router] 任务已创建，ID: ${taskResult.taskId}`);
        
        res.status(200).json({
            success: true,
            taskId: taskResult.taskId,
            status: taskResult.status
        });
        
    } catch (error) {
        console.error('[Flux Router] 处理请求失败:', error);
        res.status(500).json({
            success: false,
            error: error.message || '处理请求失败'
        });
    }
});

/**
 * @route GET /api/flux/task/:taskId
 * @desc 获取任务状态
 * @access 需要授权
 */
router.get('/task/:taskId', requireAuth, async (req, res) => {
    const taskId = req.params.taskId;
    const userId = req.user.id;
    
    console.log(`[Flux Router] 用户 ${userId} 请求任务 ${taskId} 状态`);
    
    try {
        const taskDetails = await fluxService.getTaskDetails(taskId, userId);
        
        // 格式化返回数据
        const result = {
            id: taskDetails.id,
            status: taskDetails.status,
            created_at: taskDetails.created_at,
            completed_at: taskDetails.completed_at,
            progress: taskDetails.progress || null
        };
        
        // 根据任务状态添加特定数据
        if (taskDetails.status === 'succeeded') {
            result.output_image_url = taskDetails.output_image_url;
        } else if (taskDetails.status === 'failed') {
            result.error = taskDetails.error || '任务处理失败';
        }
        
        console.log(`[Flux Router] 任务 ${taskId} 状态: ${taskDetails.status}`);
        
        res.status(200).json(result);
    } catch (error) {
        console.error(`[Flux Router] 获取任务 ${taskId} 状态失败:`, error);
        res.status(error.message === '任务不存在或无权访问' ? 404 : 500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * @route GET /api/flux/tasks
 * @desc 获取用户任务列表
 * @access 需要授权
 */
router.get('/tasks', requireAuth, async (req, res) => {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.page_size) || 10;
    
    console.log(`[Flux Router] 用户 ${userId} 请求任务列表，页码: ${page}，每页数量: ${pageSize}`);
    
    try {
        const tasks = await fluxService.getUserTasks(userId, page, pageSize);
        
        console.log(`[Flux Router] 返回用户 ${userId} 的 ${tasks.items.length} 个任务记录`);
        
        res.status(200).json(tasks);
    } catch (error) {
        console.error(`[Flux Router] 获取用户 ${userId} 任务列表失败:`, error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * @route DELETE /api/flux/task/:taskId
 * @desc 删除任务
 * @access 需要授权
 */
router.delete('/task/:taskId', requireAuth, async (req, res) => {
    const taskId = req.params.taskId;
    const userId = req.user.id;
    
    console.log(`[Flux Router] 用户 ${userId} 请求删除任务 ${taskId}`);
    
    try {
        const success = await fluxService.deleteTask(taskId, userId);
        
        if (success) {
            console.log(`[Flux Router] 任务 ${taskId} 已成功删除`);
            res.status(200).json({
                success: true,
                message: '任务已成功删除'
            });
        } else {
            console.log(`[Flux Router] 删除任务 ${taskId} 失败，可能不存在或无权删除`);
            res.status(404).json({
                success: false,
                error: '任务不存在或无权删除'
            });
        }
    } catch (error) {
        console.error(`[Flux Router] 删除任务 ${taskId} 失败:`, error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router; 