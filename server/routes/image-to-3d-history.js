const express = require('express');
const router = express.Router();
const pool = require('../db/pool');
const { authenticateToken } = require('../utils/auth');
const path = require('path');

// 获取图片转3D历史记录
router.get('/', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;

        console.log(`获取用户 ${userId} 的图片转3D历史记录, page=${page}, limit=${limit}`);

        const connection = await pool.getConnection();
        try {
            // 获取总数
            const [countResult] = await connection.query(
                'SELECT COUNT(*) as total FROM image_to_3d_history WHERE user_id = ?',
                [userId]
            );
            const totalCount = countResult[0].total;
            const totalPages = Math.ceil(totalCount / limit);

            // 获取历史记录
            const [history] = await connection.query(
                `SELECT id, original_image, model_url, preview_url, original_image_url, thumbnail_url, name, 
                 status, created_at, updated_at, tripo_task_id, animated_model_url, animation_type, animation_status
                 FROM image_to_3d_history 
                 WHERE user_id = ? 
                 ORDER BY created_at DESC 
                 LIMIT ? OFFSET ?`,
                [userId, limit, offset]
            );

            connection.release();
            
            res.json({
                success: true,
                history: history,
                currentPage: page,
                totalPages,
                totalCount
            });
        } catch (error) {
            connection.release();
            throw error;
        }
    } catch (error) {
        console.error('获取图片转3D历史记录失败:', error);
        res.status(500).json({
            success: false,
            message: '获取历史记录失败',
            error: error.message
        });
    }
});

// 删除历史记录
router.delete('/:id', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const historyId = req.params.id;

        console.log(`删除图片转3D历史记录: user=${userId}, id=${historyId}`);

        const connection = await pool.getConnection();
        try {
            // 查询记录是否存在且属于当前用户
            const [records] = await connection.query(
                'SELECT * FROM image_to_3d_history WHERE id = ? AND user_id = ?',
                [historyId, userId]
            );

            if (records.length === 0) {
                connection.release();
                return res.status(404).json({
                    success: false,
                    message: '历史记录不存在或不属于当前用户'
                });
            }

            // 删除记录
            await connection.query(
                'DELETE FROM image_to_3d_history WHERE id = ? AND user_id = ?',
                [historyId, userId]
            );

            connection.release();
            res.json({
                success: true,
                message: '历史记录已删除'
            });
        } catch (error) {
            connection.release();
            throw error;
        }
    } catch (error) {
        console.error('删除图片转3D历史记录失败:', error);
        res.status(500).json({
            success: false,
            message: '删除历史记录失败',
            error: error.message
        });
    }
});

// 创建历史记录（由主路由调用，不直接暴露）
const createHistory = async (userId, data) => {
    try {
        const { originalImage, modelUrl, previewUrl, originalImageUrl, thumbnailUrl, name } = data;
        
        const connection = await pool.getConnection();
        try {
            const [result] = await connection.query(
                `INSERT INTO image_to_3d_history 
                (user_id, original_image, model_url, preview_url, original_image_url, thumbnail_url, name, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [userId, originalImage, modelUrl, previewUrl, originalImageUrl, thumbnailUrl, name, 'pending']
            );
            
            connection.release();
            return { success: true, id: result.insertId };
        } catch (error) {
            connection.release();
            throw error;
        }
    } catch (error) {
        console.error('创建图片转3D历史记录失败:', error);
        return { success: false, error: error.message };
    }
};

// 更新历史记录状态（由主路由调用，不直接暴露）
const updateHistoryStatus = async (historyId, status, modelUrl = undefined, previewUrl = undefined, thumbnail_url = undefined) => {
    try {
        const connection = await pool.getConnection();
        try {
            const updates = {}; // Start with an empty object
            // Only add fields to the update object if they are explicitly provided (not undefined)
            if (status !== undefined) updates.status = status;
            if (modelUrl !== undefined) updates.model_url = modelUrl; // Allow setting to null if passed explicitly
            if (previewUrl !== undefined) updates.preview_url = previewUrl;
            if (thumbnail_url !== undefined) updates.thumbnail_url = thumbnail_url;
            
            // Check if there's anything to update
            if (Object.keys(updates).length === 0) {
                console.log(`[UpdateHistory] No fields to update for history ID=${historyId}`);
                connection.release();
                return { success: true }; // No error, just nothing to do
            }
            
            console.log(`[UpdateHistory] Updating history ID=${historyId}:`, updates);
            
            const [result] = await connection.query(
                'UPDATE image_to_3d_history SET ? WHERE id = ?',
                [updates, historyId]
            );

            if (result.affectedRows === 0) {
                 console.warn(`[UpdateHistory] No rows affected when updating history ID=${historyId}. Record might not exist.`);
                 // Depending on requirements, you might want to return success: false here
            }
            
            connection.release();
            return { success: true };
        } catch (error) {
            connection.release();
            throw error;
        }
    } catch (error) {
        console.error(`[UpdateHistory] Error updating history ID=${historyId}:`, error);
        return { success: false, error: error.message };
    }
};

module.exports = { router, createHistory, updateHistoryStatus }; 