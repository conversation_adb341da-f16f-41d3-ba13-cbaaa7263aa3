const express = require('express');
const { authenticateToken } = require('../utils/auth');
const pool = require('../db/pool');

const router = express.Router();

// GET /api/remove-background/history (Fetch history for the logged-in user)
router.get('/history', authenticateToken, async (req, res) => {
    console.log('调用了 removeBackgroundHistory.js 的 /history'); // 调试日志
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 8; // Or another default limit
    const offset = (page - 1) * limit;

    let connection;
    try {
        connection = await pool.getConnection();

        // Query total count for pagination
        const countQuery = 'SELECT COUNT(*) as totalCount FROM remove_background_history WHERE user_id = ?';
        const [[{ totalCount }]] = await connection.query(countQuery, [userId]);

        // Query paginated history data, ordered by creation date descending
        const historyQuery = `
            SELECT id, user_id, original_image_filename, result_image_url, created_at 
            FROM remove_background_history 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?`;
        const [history] = await connection.query(historyQuery, [userId, limit, offset]);

        connection.release();

        const totalPages = Math.ceil(totalCount / limit);

        res.json({
            history,
            pagination: {
                totalCount,
                currentPage: page,
                totalPages,
                limit
            }
        });

    } catch (error) {
        if (connection) connection.release();
        console.error(`[Remove Background History] Error fetching history for user ${userId}:`, error);
        res.status(500).json({ error: '获取历史记录失败', details: error.message });
    }
});

module.exports = router; 