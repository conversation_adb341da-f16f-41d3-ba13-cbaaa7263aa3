const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { authenticateToken, requireAdmin } = require('../utils/auth');
const { exampleRateLimiter } = require('../middleware/rateLimit');
const pool = require('../db/pool');
const commentsRouter = require('./comments');

// 配置文件上传
const uploadDir = process.env.UPLOAD_DIR || 'uploads';
const uploadPath = path.join(__dirname, '..', uploadDir);

// 确保上传目录存在
if (!fs.existsSync(uploadPath)) {
    fs.mkdirSync(uploadPath, { recursive: true });
}

// 配置文件上传
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadPath);
    },
    filename: function (req, file, cb) {
        const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
        cb(null, uniqueName);
    }
});

// 新的多字段 upload 配置
const upload = multer({
    storage: storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 统一 5MB 限制，前端会进行更细致的压缩前检查
    fileFilter: function (req, file, cb) {
        const allowedTypes = /jpeg|jpg|png|gif/;
        const mimetype = allowedTypes.test(file.mimetype);
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        
        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('仅支持图片文件 (jpeg, jpg, png, gif)!'));
    }
});

// Helper function to process examples (get tags, author, build URLs, likes)
async function processExamples(examples, connection, currentUserId = null) {
    // 使用 Promise.all 来并行处理每个 example 的异步操作，提高效率
    const processedExamples = await Promise.all(examples.map(async (example) => {
        // 获取标签
        const [tags] = await connection.query(`
            SELECT t.name FROM tags t JOIN prompt_tags pt ON t.id = pt.tag_id WHERE pt.prompt_id = ?
        `, [example.id]);
        
        // Check if current user liked this example (only if currentUserId is provided)
        let isLiked = false;
        if (currentUserId) {
            const [likeCheck] = await connection.query(
                'SELECT 1 FROM prompt_likes WHERE user_id = ? AND prompt_id = ? LIMIT 1',
                [currentUserId, example.id]
            );
            isLiked = likeCheck.length > 0;
        }

        // 使用 Object.assign 或扩展运算符创建新对象，避免直接修改原始查询结果 (更安全)
        const processedExample = { 
            ...example, 
            tags: tags.map(tag => tag.name),
            likes_count: example.likes_count !== undefined ? example.likes_count : 0, // Ensure likes_count exists
            is_liked_by_current_user: isLiked, // Add liked status
        };

        // 组织作者信息
        processedExample.author = processedExample.author_id ? {
            id: processedExample.author_id,
            username: processedExample.author_username,
            nickname: processedExample.author_nickname,
            avatar_url: processedExample.author_avatar_url
        } : null;

        // 清理扁平字段
        delete processedExample.author_id;
        delete processedExample.author_username;
        delete processedExample.author_nickname;

        return processedExample;
    }));
    return processedExamples;
}

// POST /examples - 创建新案例
router.post('/', authenticateToken, exampleRateLimiter, upload.fields([
    { name: 'image', maxCount: 1 },
    { name: 'source_image', maxCount: 1 },
    { name: 'reference_image', maxCount: 1 },
    { name: 'flux_second_image', maxCount: 1 },
    { name: 'flux_result_image', maxCount: 1 }
]), async (req, res) => {
    try {
        const { title, prompt, category, tags, target_model } = req.body;
        const authorId = req.user.id; // 获取当前登录用户的 ID

        if (!title || !prompt || !category) {
            return res.status(400).json({ error: '标题、提示词和分类不能为空' });
        }
        
        // 可选：验证 target_model 是否为允许的值
        const allowedModels = ['MJ', 'FLUX', 'FLUX-多图', 'SDXL', '超级生图', '高级生图', 'GPT4O', 'GPT4O-编辑', null, ''];
        if (target_model && !allowedModels.includes(target_model)) {
            return res.status(400).json({ error: `无效的适用模型: ${target_model}` });
        }
        
        const connection = await pool.getConnection();
        await connection.beginTransaction();
        
        try {
            // 插入提示词案例
            const [exampleResult] = await connection.query(
                'INSERT INTO prompt_examples (title, prompt, category, target_model, image_file, source_image_file, reference_image_file, flux_second_image_file, flux_result_image_file, author_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
                [
                    title,
                    prompt,
                    category,
                    target_model,
                    req.files && req.files.image ? req.files.image[0].filename : null,
                    req.files && req.files.source_image ? req.files.source_image[0].filename : null,
                    req.files && req.files.reference_image ? req.files.reference_image[0].filename : null,
                    req.files && req.files.flux_second_image ? req.files.flux_second_image[0].filename : null,
                    req.files && req.files.flux_result_image ? req.files.flux_result_image[0].filename : null,
                    authorId
                ]
            );
            const exampleId = exampleResult.insertId;
            
            // 处理标签
            let parsedTags = [];
            if (tags && typeof tags === 'string') {
                try {
                    parsedTags = JSON.parse(tags);
                    if (!Array.isArray(parsedTags)) {
                        parsedTags = [tags];
                    }
                } catch (e) {
                    parsedTags = [tags];
                }
            } else if (Array.isArray(tags)) {
                parsedTags = tags;
            }
                
            for (const tagName of parsedTags) {
                // 检查标签是否存在，不存在则创建
                let [existingTag] = await connection.query('SELECT * FROM tags WHERE name = ?', [tagName]);
                
                let tagId;
                if (existingTag.length === 0) {
                    const [newTag] = await connection.query('INSERT INTO tags (name) VALUES (?)', [tagName]);
                    tagId = newTag.insertId;
                } else {
                    tagId = existingTag[0].id;
                }
                
                // 关联标签与提示词
                await connection.query(
                    'INSERT INTO prompt_tags (prompt_id, tag_id) VALUES (?, ?)',
                    [exampleId, tagId]
                );
            }
            
            // 获取完整的例子信息
            const [newExampleData] = await connection.query(`
                SELECT pe.*, u.username as author_username, u.nickname as author_nickname, u.avatar_url as author_avatar_url
                FROM prompt_examples pe
                LEFT JOIN users u ON pe.author_id = u.id
                WHERE pe.id = ?
            `, [exampleId]);

            if (newExampleData.length === 0) {
                throw new Error('无法检索新创建的案例');
            }
            const newExample = newExampleData[0];

            // 获取标签
            const [exampleTags] = await connection.query(`
                SELECT t.name FROM tags t JOIN prompt_tags pt ON t.id = pt.tag_id WHERE pt.prompt_id = ?
            `, [exampleId]);
            newExample.tags = exampleTags.map(tag => tag.name);

            // 组织作者信息
            newExample.author = newExample.author_id ? {
                id: newExample.author_id, 
                username: newExample.author_username,
                nickname: newExample.author_nickname,
                avatar_url: newExample.author_avatar_url
            } : null;
            
            // 清理掉扁平的作者字段
            delete newExample.author_id;
            delete newExample.author_username;
            delete newExample.author_nickname;

            await connection.commit();
            connection.release();
            
            res.status(201).json(newExample);
        } catch (error) {
            await connection.rollback();
            connection.release();
            throw error;
        }
    } catch (error) {
        console.error('创建案例错误:', error);
        
        // 如果上传了文件但创建记录失败，删除文件
        if (req.files) {
            try {
                if (req.files.image) fs.unlinkSync(req.files.image[0].path);
                if (req.files.source_image) fs.unlinkSync(req.files.source_image[0].path);
                if (req.files.reference_image) fs.unlinkSync(req.files.reference_image[0].path);
                if (req.files.flux_second_image) fs.unlinkSync(req.files.flux_second_image[0].path);
                if (req.files.flux_result_image) fs.unlinkSync(req.files.flux_result_image[0].path);
            } catch (unlinkError) {
                console.error('删除文件错误:', unlinkError);
            }
        }
        
        res.status(500).json({ error: '创建案例失败', details: error.message });
    }
});

// PUT /examples/:id - 更新案例
router.put('/:id', authenticateToken, upload.fields([
    { name: 'image', maxCount: 1 },
    { name: 'source_image', maxCount: 1 },
    { name: 'reference_image', maxCount: 1 },
    { name: 'flux_second_image', maxCount: 1 },
    { name: 'flux_result_image', maxCount: 1 }
]), async (req, res) => {
    try {
        const { id } = req.params;
        const { title, prompt, category, tags, keep_existing_image, target_model, 
               keep_existing_source_image, keep_existing_reference_image, keep_existing_flux_second_image, keep_existing_flux_result_image } = req.body;
        const exampleId = req.params.id;
        const authorId = req.user.id;

        // 验证target_model
        const allowedModels = ['MJ', 'FLUX', 'FLUX-多图', 'SDXL', '超级生图', '高级生图', 'GPT4O', 'GPT4O-编辑', null, ''];
        if (target_model && !allowedModels.includes(target_model)) {
            return res.status(400).json({ error: `无效的适用模型: ${target_model}` });
        }
        
        // 验证权限：只有管理员或案例的原始作者可以编辑
        const connection = await pool.getConnection();
        try {
            const [existingExampleRows] = await connection.query(
                'SELECT author_id, image_file, source_image_file, reference_image_file, flux_second_image_file, flux_result_image_file FROM prompt_examples WHERE id = ?', 
                [exampleId]
            );
            if (existingExampleRows.length === 0) {
                connection.release();
                return res.status(404).json({ error: '案例未找到' });
            }
            const existingExample = existingExampleRows[0];
            
            if (req.user.role !== 'admin' && existingExample.author_id !== authorId) {
                connection.release();
                return res.status(403).json({ error: '您没有权限编辑此案例' });
            }

            await connection.beginTransaction();

            let imageFileName = existingExample.image_file;
            let sourceImageFileName = existingExample.source_image_file;
            let referenceImageFileName = existingExample.reference_image_file;
            let fluxSecondImageFileName = existingExample.flux_second_image_file;
            let fluxResultImageFileName = existingExample.flux_result_image_file;

            // 处理主图片
            if (req.files && req.files.image && req.files.image[0]) {
                if (imageFileName) {
                    try {
                        fs.unlinkSync(path.join(uploadPath, imageFileName));
                    } catch (err) {
                        console.error('删除旧主图片失败:', err);
                    }
                }
                imageFileName = req.files.image[0].filename;
            } else if (keep_existing_image !== 'true') {
                if (imageFileName) {
                    try {
                        fs.unlinkSync(path.join(uploadPath, imageFileName));
                    } catch (err) {
                        console.error('删除旧主图片失败(未保留且无新图):', err);
                    }
                }
                imageFileName = null;
            }

            // 处理源图片
            if (req.files && req.files.source_image && req.files.source_image[0]) {
                if (sourceImageFileName) {
                    try {
                        fs.unlinkSync(path.join(uploadPath, sourceImageFileName));
                    } catch (err) {
                        console.error('删除旧源图片失败:', err);
                    }
                }
                sourceImageFileName = req.files.source_image[0].filename;
            } else if (keep_existing_source_image !== 'true') {
                if (sourceImageFileName) {
                    try {
                        fs.unlinkSync(path.join(uploadPath, sourceImageFileName));
                    } catch (err) {
                        console.error('删除旧源图片失败(未保留且无新图):', err);
                    }
                }
                sourceImageFileName = null;
            }

            // 处理参考图
            if (req.files && req.files.reference_image && req.files.reference_image[0]) {
                if (referenceImageFileName) {
                    try {
                        fs.unlinkSync(path.join(uploadPath, referenceImageFileName));
                    } catch (err) {
                        console.error('删除旧参考图失败:', err);
                    }
                }
                referenceImageFileName = req.files.reference_image[0].filename;
            } else if (keep_existing_reference_image !== 'true') {
                if (referenceImageFileName) {
                    try {
                        fs.unlinkSync(path.join(uploadPath, referenceImageFileName));
                    } catch (err) {
                        console.error('删除旧参考图失败(未保留且无新图):', err);
                    }
                }
                referenceImageFileName = null;
            }
            
            // 处理FLUX第二张图片
            if (req.files && req.files.flux_second_image && req.files.flux_second_image[0]) {
                if (fluxSecondImageFileName) {
                    try {
                        fs.unlinkSync(path.join(uploadPath, fluxSecondImageFileName));
                    } catch (err) {
                        console.error('删除旧FLUX第二张图片失败:', err);
                    }
                }
                fluxSecondImageFileName = req.files.flux_second_image[0].filename;
            } else if (keep_existing_flux_second_image !== 'true') {
                if (fluxSecondImageFileName) {
                    try {
                        fs.unlinkSync(path.join(uploadPath, fluxSecondImageFileName));
                    } catch (err) {
                        console.error('删除旧FLUX第二张图片失败(未保留且无新图):', err);
                    }
                }
                fluxSecondImageFileName = null;
            }
            
            // 处理FLUX结果图片
            if (req.files && req.files.flux_result_image && req.files.flux_result_image[0]) {
                if (fluxResultImageFileName) {
                    try {
                        fs.unlinkSync(path.join(uploadPath, fluxResultImageFileName));
                    } catch (err) {
                        console.error('删除旧FLUX结果图片失败:', err);
                    }
                }
                fluxResultImageFileName = req.files.flux_result_image[0].filename;
            } else if (keep_existing_flux_result_image !== 'true') {
                if (fluxResultImageFileName) {
                    try {
                        fs.unlinkSync(path.join(uploadPath, fluxResultImageFileName));
                    } catch (err) {
                        console.error('删除旧FLUX结果图片失败(未保留且无新图):', err);
                    }
                }
                fluxResultImageFileName = null;
            }
            
            // 更新 prompt_examples 表
            await connection.query(
                'UPDATE prompt_examples SET title = ?, prompt = ?, category = ?, target_model = ?, image_file = ?, source_image_file = ?, reference_image_file = ?, flux_second_image_file = ?, flux_result_image_file = ? WHERE id = ?',
                [title, prompt, category, target_model || null, imageFileName, sourceImageFileName, referenceImageFileName, fluxSecondImageFileName, fluxResultImageFileName, exampleId]
            );
            
            // 更新标签 (先删除旧的，再添加新的)
            console.log('删除旧标签关联...');
            await connection.query('DELETE FROM prompt_tags WHERE prompt_id = ?', [exampleId]);
            
            // 添加新标签关联
            if (tags) {
                console.log('处理新标签...');
                let tagList;
                try {
                    tagList = JSON.parse(tags);
                    console.log('解析的标签列表:', tagList);
                } catch (parseError) {
                    console.error('标签JSON解析失败:', parseError);
                    console.log('原始标签字符串:', tags);
                    tagList = [];
                }
                
                for (const tagName of tagList) {
                    console.log('处理标签:', tagName);
                    // 检查标签是否存在，不存在则创建
                    let [existingTag] = await connection.query('SELECT * FROM tags WHERE name = ?', [tagName]);
                    
                    let tagId;
                    if (existingTag.length === 0) {
                        console.log('创建新标签:', tagName);
                        const [newTag] = await connection.query('INSERT INTO tags (name) VALUES (?)', [tagName]);
                        tagId = newTag.insertId;
                    } else {
                        console.log('使用已存在的标签:', tagName, 'ID:', existingTag[0].id);
                        tagId = existingTag[0].id;
                    }
                    
                    // 关联标签与提示词
                    console.log('关联标签ID:', tagId, '与案例ID:', exampleId);
                    await connection.query(
                        'INSERT INTO prompt_tags (prompt_id, tag_id) VALUES (?, ?)',
                        [exampleId, tagId]
                    );
                }
            } else {
                console.log('没有提供标签');
            }
            
            // 获取更新后的例子完整信息
            console.log('获取更新后的案例信息...');
            const [updatedExampleData] = await connection.query(`
                SELECT pe.*, u.username as author_username, u.nickname as author_nickname, u.avatar_url as author_avatar_url
                FROM prompt_examples pe
                LEFT JOIN users u ON pe.author_id = u.id
                WHERE pe.id = ?
            `, [exampleId]);
            if (updatedExampleData.length === 0) throw new Error('更新后无法找到案例');
            const updatedExampleResult = updatedExampleData[0];

            const [exampleTags] = await connection.query(`
                SELECT t.id, t.name 
                FROM tags t
                JOIN prompt_tags pt ON t.id = pt.tag_id
                WHERE pt.prompt_id = ?
            `, [exampleId]);
            
            updatedExampleResult.tags = exampleTags.map(tag => tag.name);
            
            // 组织作者信息
            updatedExampleResult.author = updatedExampleResult.author_id ? {
                id: updatedExampleResult.author_id, 
                username: updatedExampleResult.author_username,
                nickname: updatedExampleResult.author_nickname,
                avatar_url: updatedExampleResult.author_avatar_url
            } : null;
            
            // 清理掉扁平的作者字段
            delete updatedExampleResult.author_id;
            delete updatedExampleResult.author_username;
            delete updatedExampleResult.author_nickname;
            
            console.log('提交事务...');
            await connection.commit();
            connection.release();
            
            console.log('案例更新成功:', updatedExampleResult.id);
            res.json(updatedExampleResult);
        } catch (error) {
            console.error('事务内部错误，执行回滚:', error);
            console.error('错误堆栈:', error.stack);
            await connection.rollback();
            connection.release();
            throw error;
        }
    } catch (error) {
        console.error('更新案例错误:', error);
        console.error('错误堆栈:', error.stack);
        console.log('请求体:', req.body);
        console.log('文件:', req.files);
        
        // 如果上传了文件但更新记录失败，删除文件
        if (req.files) {
            try {
                if (req.files.image) fs.unlinkSync(req.files.image[0].path);
                if (req.files.source_image) fs.unlinkSync(req.files.source_image[0].path);
                if (req.files.reference_image) fs.unlinkSync(req.files.reference_image[0].path);
                if (req.files.flux_second_image) fs.unlinkSync(req.files.flux_second_image[0].path);
                if (req.files.flux_result_image) fs.unlinkSync(req.files.flux_result_image[0].path);
                console.log('已删除上传的文件');
            } catch (unlinkError) {
                console.error('删除文件错误:', unlinkError);
            }
        }
        
        res.status(500).json({ error: '更新案例失败', message: error.message });
    }
});

// GET /examples - 获取所有提示词案例
router.get('/', authenticateToken, async (req, res) => {
    try {
        console.log('开始获取所有提示词案例');
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 12;
        const offset = (page - 1) * limit;
        const targetModel = req.query.target_model;
        const currentUserId = req.user.id;
        const sortBy = req.query.sort || 'date_desc';
        const categorySlug = req.query.category_slug;

        console.log(`请求参数: page=${page}, limit=${limit}, offset=${offset}, model=${targetModel || 'all'}, sort=${sortBy}, category=${categorySlug || 'all'}`);

        const connection = await pool.getConnection();
        console.log('数据库连接成功，准备查询数据');

        // 构建查询条件
        let whereClauses = [];
        let queryParams = [];

        if (targetModel !== undefined) { 
            if (targetModel === '') { 
                whereClauses.push('pe.target_model IS NULL');
            } else {
                whereClauses.push('pe.target_model = ?');
                queryParams.push(targetModel);
            }
        }

        if (categorySlug) {
            whereClauses.push('pe.category = ?');
            queryParams.push(categorySlug);
        }

        const whereSql = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

        // 查询总记录数
        const countQuery = `SELECT COUNT(*) as totalCount FROM prompt_examples pe ${whereSql}`;
        const [[{ totalCount }]] = await connection.query(countQuery, queryParams);
        console.log(`查询到总案例数 (筛选后): ${totalCount}`);

        // 确定排序方式
        let orderBySql = 'ORDER BY pe.id DESC';
        if (sortBy === 'likes_desc') {
            orderBySql = 'ORDER BY pe.likes_count DESC, pe.id DESC';
        }

        // 获取当前页的提示词案例
        const pageParams = [...queryParams, limit, offset];
        const [examples] = await connection.query(`
            SELECT pe.*, 
                u.username as author_username, 
                u.nickname as author_nickname, 
                u.avatar_url as author_avatar_url 
            FROM prompt_examples pe
            LEFT JOIN users u ON pe.author_id = u.id
            ${whereSql}
            ${orderBySql}
            LIMIT ? OFFSET ?
        `, pageParams);
        console.log(`查询到 ${examples.length} 条案例记录 (当前页)`);

        // 处理案例（标签、作者、URL）
        const processedExamples = await processExamples(examples, connection, currentUserId);

        connection.release();
        console.log('数据库连接已释放，返回案例数据');
        
        // 计算总页数
        const totalPages = Math.ceil(totalCount / limit);

        res.json({
            examples: processedExamples,
            pagination: {
                totalCount,
                currentPage: page,
                totalPages,
                limit
            }
        });
    } catch (error) {
        console.error('获取案例错误:', error);
        console.error('错误堆栈:', error.stack);
        res.status(500).json({ error: '获取案例失败', details: error.message });
    }
});

// GET /examples/search - 搜索提示词案例
router.get('/search', authenticateToken, async (req, res) => {
    try {
        const { query } = req.query;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 12;
        const offset = (page - 1) * limit;
        const targetModel = req.query.target_model;
        const currentUserId = req.user.id;
        const sortBy = req.query.sort || 'date_desc';

        if (!query) {
            return res.status(400).json({ error: '缺少搜索关键词' });
        }
        
        console.log(`搜索案例: query='${query}', page=${page}, limit=${limit}, model=${targetModel || 'all'}, sort=${sortBy}, category=${req.query.category_slug || 'all'}`);

        const connection = await pool.getConnection();
        
        // 构建查询条件
        const searchTerm = `%${query}%`;
        let whereClauses = ['(pe.title LIKE ? OR pe.prompt LIKE ? OR t.name LIKE ? OR u.username LIKE ? OR u.nickname LIKE ?)']
        let queryParams = [searchTerm, searchTerm, searchTerm, searchTerm, searchTerm];

        if (targetModel !== undefined) {
            if (targetModel === '') { 
                whereClauses.push('pe.target_model IS NULL');
            } else {
                whereClauses.push('pe.target_model = ?');
                queryParams.push(targetModel);
            }
        }
        
        if (req.query.category_slug) {
            whereClauses.push('pe.category = ?');
            queryParams.push(req.query.category_slug);
        }
        
        const whereSql = `WHERE ${whereClauses.join(' AND ')}`;
        
        // 查询总记录数
        const countQuery = `
            SELECT COUNT(DISTINCT pe.id) as totalCount 
            FROM prompt_examples pe
            LEFT JOIN users u ON pe.author_id = u.id
            LEFT JOIN prompt_tags pt ON pe.id = pt.prompt_id
            LEFT JOIN tags t ON pt.tag_id = t.id
            ${whereSql}
        `;
        const [[{ totalCount }]] = await connection.query(countQuery, queryParams);
        console.log(`搜索 '${query}' 找到的总案例数 (筛选后): ${totalCount}`);

        // 确定排序方式
        let orderBySql = 'ORDER BY pe.id DESC';
        if (sortBy === 'likes_desc') {
            orderBySql = 'ORDER BY pe.likes_count DESC, pe.id DESC';
        }

        // 获取当前页的提示词案例
        const pageParams = [...queryParams, limit, offset];
        const examplesQuery = `
            SELECT DISTINCT pe.*, 
                u.username as author_username, 
                u.nickname as author_nickname, 
                u.avatar_url as author_avatar_url
            FROM prompt_examples pe
            LEFT JOIN users u ON pe.author_id = u.id
            LEFT JOIN prompt_tags pt ON pe.id = pt.prompt_id
            LEFT JOIN tags t ON pt.tag_id = t.id
            ${whereSql}
            ${orderBySql}
            LIMIT ? OFFSET ?
        `;
        const [examples] = await connection.query(examplesQuery, pageParams);
        console.log(`查询到 ${examples.length} 条案例记录 (当前页)`);

        // 处理案例
        const processedExamples = await processExamples(examples, connection, currentUserId);

        connection.release();
        console.log('数据库连接已释放，返回搜索结果');
        
        // 计算总页数
        const totalPages = Math.ceil(totalCount / limit);

        // 返回统一的数据结构
        res.json({
            examples: processedExamples,
            pagination: {
                totalCount,
                currentPage: page,
                totalPages,
                limit
            }
        });
    } catch (error) {
        console.error('搜索案例错误:', error);
        res.status(500).json({ error: '搜索案例失败', details: error.message });
    }
});

// GET /examples/:id - 获取单个提示词案例详情
router.get('/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const currentUserId = req.user.id;
        console.log(`开始获取单个案例详情, ID: ${id}`);

        const connection = await pool.getConnection();
        
        console.log('查询案例及作者信息...');
        const [examples] = await connection.query(`
            SELECT pe.*, u.username as author_username, u.nickname as author_nickname, u.avatar_url as author_avatar_url
            FROM prompt_examples pe
            LEFT JOIN users u ON pe.author_id = u.id
            WHERE pe.id = ?
        `, [id]);
        
        if (examples.length === 0) {
            console.log('未找到案例，ID:', id);
            connection.release();
            return res.status(404).json({ error: '未找到该案例' });
        }
        
        const example = examples[0];
        console.log('案例基本信息获取成功:', example.id);

        // 处理单个案例
        const processedExamples = await processExamples([example], connection, currentUserId);
        const processedExample = processedExamples[0];

        connection.release();
        console.log('数据库连接已释放，返回案例详情');
        
        res.json(processedExample);
    } catch (error) {
        const connection = req.connection;
        console.error(`获取ID为 ${req.params.id} 的案例详情错误:`, error);
        console.error('错误堆栈:', error.stack);
        if (connection && connection.release) {
            try {
                connection.release();
                console.log('在错误处理中释放了数据库连接');
            } catch (releaseError) {
                console.error('错误处理中释放连接失败:', releaseError);
            }
        } else {
            console.warn('无法在错误处理中获取或释放数据库连接');
        }
        res.status(500).json({ error: '获取案例详情失败', details: error.message });
    }
});

// GET /examples/category/:category - 按分类获取提示词案例
router.get('/category/:category', authenticateToken, async (req, res) => {
    try {
        const { category } = req.params;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 12;
        const offset = (page - 1) * limit;
        const targetModel = req.query.target_model;
        const currentUserId = req.user.id;
        const sortBy = req.query.sort || 'date_desc';
        
        console.log(`获取分类案例: category=${category}, page=${page}, limit=${limit}, model=${targetModel || 'all'}, sort=${sortBy}`);

        const connection = await pool.getConnection();
        
        // 构建查询条件
        let whereClauses = ['pe.category = ?'];
        let queryParams = [category];

        if (targetModel !== undefined) {
            if (targetModel === '') {
                whereClauses.push('pe.target_model IS NULL');
            } else {
                whereClauses.push('pe.target_model = ?');
                queryParams.push(targetModel);
            }
        }

        const whereSql = `WHERE ${whereClauses.join(' AND ')}`;

        // 查询该分类的总记录数
        const countQuery = `SELECT COUNT(*) as totalCount FROM prompt_examples pe ${whereSql}`;
        const [[{ totalCount }]] = await connection.query(countQuery, queryParams);
        console.log(`分类 '${category}' 的总案例数 (筛选后): ${totalCount}`);

        // 确定排序方式
        let orderBySql = 'ORDER BY pe.id DESC';
        if (sortBy === 'likes_desc') {
            orderBySql = 'ORDER BY pe.likes_count DESC, pe.id DESC';
        }

        // 获取当前页的提示词案例
        const pageParams = [...queryParams, limit, offset];
        const [examples] = await connection.query(`
            SELECT pe.*, 
                u.username as author_username, 
                u.nickname as author_nickname, 
                u.avatar_url as author_avatar_url
            FROM prompt_examples pe
            LEFT JOIN users u ON pe.author_id = u.id
            ${whereSql}
            ${orderBySql}
            LIMIT ? OFFSET ?
        `, pageParams);
        console.log(`查询到 ${examples.length} 条案例记录 (当前页)`);
        
        // 处理案例
        const processedExamples = await processExamples(examples, connection, currentUserId);

        connection.release();
        console.log('数据库连接已释放，返回分类案例数据');

        // 计算总页数
        const totalPages = Math.ceil(totalCount / limit);

        // 返回统一的数据结构
        res.json({
            examples: processedExamples,
            pagination: {
                totalCount,
                currentPage: page,
                totalPages,
                limit
            }
        });
    } catch (error) {
        console.error('获取分类案例错误:', error);
        res.status(500).json({ error: '获取分类案例失败', details: error.message });
    }
});

// DELETE /examples/:id - 删除提示词案例
router.delete('/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.id;
        const userRole = req.user.role;
        
        const connection = await pool.getConnection();
        
        // 检查案例是否存在
        const [example] = await connection.query('SELECT * FROM prompt_examples WHERE id = ?', [id]);
        if (example.length === 0) {
            connection.release();
            return res.status(404).json({ error: '未找到该案例' });
        }

        // 权限检查
        if (example[0].author_id !== userId && userRole !== 'admin') {
            console.log(`权限不足: 用户 ${userId} (角色 ${userRole}) 尝试删除不属于自己的案例 (作者 ${example[0].author_id})`);
            connection.release();
            return res.status(403).json({ error: '您没有权限删除此案例' });
        }

        // 删除案例
        await connection.query('DELETE FROM prompt_examples WHERE id = ?', [id]);
        
        // 检查是否有图片关联此案例，并删除
        if (example[0].image_file) {
            const imagePath = path.join(uploadPath, example[0].image_file);
            if (fs.existsSync(imagePath)) {
                fs.unlinkSync(imagePath);
            }
        }
        
        // 删除源图片(如果存在)
        if (example[0].source_image_file) {
            const sourcePath = path.join(uploadPath, example[0].source_image_file);
            if (fs.existsSync(sourcePath)) {
                fs.unlinkSync(sourcePath);
            }
        }
        
        // 删除参考图(如果存在)
        if (example[0].reference_image_file) {
            const referencePath = path.join(uploadPath, example[0].reference_image_file);
            if (fs.existsSync(referencePath)) {
                fs.unlinkSync(referencePath);
            }
        }
        
        // 删除FLUX第二张图片(如果存在)
        if (example[0].flux_second_image_file) {
            const fluxSecondPath = path.join(uploadPath, example[0].flux_second_image_file);
            if (fs.existsSync(fluxSecondPath)) {
                fs.unlinkSync(fluxSecondPath);
            }
        }
        
        // 删除FLUX结果图片(如果存在)
        if (example[0].flux_result_image_file) {
            const fluxResultPath = path.join(uploadPath, example[0].flux_result_image_file);
            if (fs.existsSync(fluxResultPath)) {
                fs.unlinkSync(fluxResultPath);
            }
        }
        
        connection.release();
        res.json({ success: true, message: '案例已成功删除' });
    } catch (error) {
        console.error('删除案例错误:', error);
        res.status(500).json({ error: '删除案例失败' });
    }
});

// POST /examples/:id/like - 点赞案例
router.post('/:id/like', authenticateToken, async (req, res) => {
    const promptId = parseInt(req.params.id);
    const userId = req.user.id;

    if (isNaN(promptId)) {
        return res.status(400).json({ error: '无效的案例 ID' });
    }

    let connection;
    try {
        connection = await pool.getConnection();
        await connection.beginTransaction();

        // 1. 检查案例是否存在
        const [[example]] = await connection.query('SELECT id FROM prompt_examples WHERE id = ?', [promptId]);
        if (!example) {
            await connection.rollback();
            connection.release();
            return res.status(404).json({ error: '案例不存在' });
        }

        // 2. 检查是否已点赞
        const [[existingLike]] = await connection.query(
            'SELECT user_id FROM prompt_likes WHERE user_id = ? AND prompt_id = ?',
            [userId, promptId]
        );

        let newLikesCount = 0;
        let isLiked = false;

        if (!existingLike) {
            // 3. 如果未点赞，则添加点赞记录
            await connection.query(
                'INSERT INTO prompt_likes (user_id, prompt_id) VALUES (?, ?)',
                [userId, promptId]
            );

            // 4. 更新点赞计数 (使用原子操作)
            const [updateResult] = await connection.query(
                'UPDATE prompt_examples SET likes_count = likes_count + 1 WHERE id = ?',
                [promptId]
            );

            // 5. 获取最新的点赞数
            const [[{ likes_count }]] = await connection.query(
                'SELECT likes_count FROM prompt_examples WHERE id = ?',
                [promptId]
            );
            newLikesCount = likes_count;
            isLiked = true;

            await connection.commit();
            console.log(`用户 ${userId} 点赞了案例 ${promptId}`);
        } else {
            // 如果已点赞，则不执行任何操作，直接获取当前点赞数
            await connection.rollback(); //回滚因为没有执行操作
            const [[{ likes_count }]] = await connection.query(
                'SELECT likes_count FROM prompt_examples WHERE id = ?',
                [promptId]
            );
            newLikesCount = likes_count;
            isLiked = true; // 保持已点赞状态
            console.log(`用户 ${userId} 尝试重复点赞案例 ${promptId}`);
        }

        connection.release();
        res.json({ success: true, likes_count: newLikesCount, isLiked: isLiked });

    } catch (error) {
        if (connection) {
            await connection.rollback();
            connection.release();
        }
        console.error(`点赞案例 ${promptId} 失败:`, error);
        res.status(500).json({ error: '点赞失败', details: error.message });
    }
});

// DELETE /examples/:id/like - 取消点赞案例
router.delete('/:id/like', authenticateToken, async (req, res) => {
    const promptId = parseInt(req.params.id);
    const userId = req.user.id;

    if (isNaN(promptId)) {
        return res.status(400).json({ error: '无效的案例 ID' });
    }

    let connection;
    try {
        connection = await pool.getConnection();
        await connection.beginTransaction();

        // 1. 检查案例是否存在
        const [[example]] = await connection.query('SELECT id FROM prompt_examples WHERE id = ?', [promptId]);
        if (!example) {
            await connection.rollback();
            connection.release();
            return res.status(404).json({ error: '案例不存在' });
        }

        // 2. 检查是否已点赞 (需要先检查才能删除)
        const [deleteResult] = await connection.query(
            'DELETE FROM prompt_likes WHERE user_id = ? AND prompt_id = ?',
            [userId, promptId]
        );

        let newLikesCount = 0;
        let isLiked = false;

        if (deleteResult.affectedRows > 0) {
            // 3. 如果成功删除了点赞记录，则更新点赞计数
            // 确保点赞数不为负
            await connection.query(
                'UPDATE prompt_examples SET likes_count = GREATEST(0, likes_count - 1) WHERE id = ?',
                [promptId]
            );

            // 4. 获取最新的点赞数
            const [[{ likes_count }]] = await connection.query(
                'SELECT likes_count FROM prompt_examples WHERE id = ?',
                [promptId]
            );
            newLikesCount = likes_count;
            isLiked = false; // 状态变为未点赞

            await connection.commit();
            console.log(`用户 ${userId} 取消了对案例 ${promptId} 的点赞`);
        } else {
            // 如果没有删除记录（说明本来就没点赞），则不执行操作
            await connection.rollback(); //回滚因为没有执行操作
            const [[{ likes_count }]] = await connection.query(
                'SELECT likes_count FROM prompt_examples WHERE id = ?',
                [promptId]
            );
            newLikesCount = likes_count;
            isLiked = false; // 保持未点赞状态
            console.log(`用户 ${userId} 尝试取消点赞一个未点赞的案例 ${promptId}`);
        }

        connection.release();
        res.json({ success: true, likes_count: newLikesCount, isLiked: isLiked });

    } catch (error) {
        if (connection) {
            await connection.rollback();
            connection.release();
        }
        console.error(`取消点赞案例 ${promptId} 失败:`, error);
        res.status(500).json({ error: '取消点赞失败', details: error.message });
    }
});

// 将评论路由挂载到 /:id/comments
router.use('/:id/comments', commentsRouter);

// GET /users/me/examples - 获取当前用户创建的案例
router.get('/users/me/examples', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        console.log(`开始获取用户 ${userId} 创建的案例`);
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 12;
        const offset = (page - 1) * limit;
        const targetModel = req.query.target_model;
        const sortBy = req.query.sort || 'date_desc';

        console.log(`请求参数: page=${page}, limit=${limit}, offset=${offset}, model=${targetModel || 'all'}, sort=${sortBy}`);

        const connection = await pool.getConnection();
        console.log('数据库连接成功，准备查询用户案例数据');

        // 构建查询条件 - 基础条件是 author_id
        let whereClauses = ['pe.author_id = ?'];
        let queryParams = [userId];

        // 添加模型筛选
        if (targetModel !== undefined) {
            if (targetModel === '_NULL_') { // 明确筛选 NULL (使用特殊值)
                whereClauses.push('pe.target_model IS NULL');
            } else if (targetModel) { // 如果有值且不是 '_NULL_'
                whereClauses.push('pe.target_model = ?');
                queryParams.push(targetModel);
            }
            // 如果 targetModel 是空字符串 "", 不添加模型筛选条件 (查询所有)
        }

        const whereSql = `WHERE ${whereClauses.join(' AND ')}`;

        // 查询总记录数
        const countQuery = `SELECT COUNT(*) as totalCount FROM prompt_examples pe ${whereSql}`;
        const [[{ totalCount }]] = await connection.query(countQuery, queryParams);
        console.log(`用户 ${userId} 共创建了 ${totalCount} 条案例 (筛选后)`);

        // 确定排序方式
        let orderBySql = 'ORDER BY pe.id DESC'; // 默认按 ID 降序 (最新)
        if (sortBy === 'likes_desc') {
            orderBySql = 'ORDER BY pe.likes_count DESC, pe.id DESC'; // 按点赞数降序，ID 降序作为次要排序
        }

        // 获取当前页的提示词案例
        const pageParams = [...queryParams, limit, offset];
        const [examples] = await connection.query(`
          SELECT pe.*, 
                 u.username as author_username, 
                 u.nickname as author_nickname, 
                 u.avatar_url as author_avatar_url 
          FROM prompt_examples pe
          LEFT JOIN users u ON pe.author_id = u.id
          ${whereSql}
          ${orderBySql}
          LIMIT ? OFFSET ?
        `, pageParams);
        console.log(`查询到 ${examples.length} 条案例记录 (当前页)`);

        // 处理案例
        const processedExamples = await processExamples(examples, connection, userId);

        connection.release();
        console.log('数据库连接已释放，返回用户案例数据');

        // 计算总页数
        const totalPages = Math.ceil(totalCount / limit);

        res.json({
          examples: processedExamples,
          pagination: {
            totalCount,
            currentPage: page,
            totalPages,
            limit
          }
        });
    } catch (error) {
        console.error(`获取用户 ${req.user?.id} 的案例错误:`, error);
        res.status(500).json({ error: '获取我的案例失败', details: error.message });
    }
});

// GET /users/me/likes - 获取当前用户点赞的案例
router.get('/users/me/likes', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        console.log(`开始获取用户 ${userId} 点赞的案例`);
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 12;
        const offset = (page - 1) * limit;
        const sortBy = req.query.sort || 'date_desc';

        console.log(`请求参数: page=${page}, limit=${limit}, offset=${offset}, sort=${sortBy}`);

        const connection = await pool.getConnection();
        console.log('数据库连接成功，准备查询用户点赞数据');

        // 1. 查询用户点赞的 prompt_id 总数
        const countQuery = 'SELECT COUNT(*) as totalCount FROM prompt_likes WHERE user_id = ?';
        const [[{ totalCount }]] = await connection.query(countQuery, [userId]);
        console.log(`用户 ${userId} 共点赞了 ${totalCount} 条案例`);

        if (totalCount === 0) {
            connection.release();
            return res.json({
                examples: [],
                pagination: { totalCount: 0, currentPage: 1, totalPages: 0, limit: limit }
            });
        }

        // 构建基础查询，连接 prompt_likes 和 prompt_examples
        let baseQuery = `
            SELECT pe.*, 
                   u.username as author_username, 
                   u.nickname as author_nickname, 
                   u.avatar_url as author_avatar_url 
            FROM prompt_examples pe
            JOIN prompt_likes pl ON pe.id = pl.prompt_id
            LEFT JOIN users u ON pe.author_id = u.id
            WHERE pl.user_id = ?
        `;

        // 确定排序方式
        let orderBySql = 'ORDER BY pe.id DESC';
        if (sortBy === 'likes_desc') {
            orderBySql = 'ORDER BY pe.likes_count DESC, pe.id DESC';
        }

        // 2. 获取当前页的点赞案例
        const examplesQuery = `${baseQuery} ${orderBySql} LIMIT ? OFFSET ?`;
        const [examples] = await connection.query(examplesQuery, [userId, limit, offset]);
        console.log(`查询到 ${examples.length} 条点赞案例记录 (当前页)`);

        // 3. 处理案例
        const processedExamples = await processExamples(examples, connection, userId);

        connection.release();
        console.log('数据库连接已释放，返回用户点赞案例数据');

        // 计算总页数
        const totalPages = Math.ceil(totalCount / limit);

        res.json({
            examples: processedExamples,
            pagination: {
                totalCount,
                currentPage: page,
                totalPages,
                limit
            }
        });
    } catch (error) {
        console.error(`获取用户 ${req.user?.id} 的点赞案例错误:`, error);
        res.status(500).json({ error: '获取点赞案例失败', details: error.message });
    }
});

module.exports = router; 