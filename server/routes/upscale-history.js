const express = require('express');
const pool = require('../db/pool');
const { authenticateToken } = require('../utils/auth');

const router = express.Router();

// GET /api/upscale-history/
router.get('/', authenticateToken, async (req, res) => {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 8; // Default limit per page changed to 8
    const offset = (page - 1) * limit;

    console.log(`收到获取放大历史请求: userId=${userId}, page=${page}, limit=${limit}`);

    let connection;
    try {
        connection = await pool.getConnection();

        // Query total count
        const countQuery = 'SELECT COUNT(*) as totalCount FROM upscale_history WHERE user_id = ?';
        const [[{ totalCount }]] = await connection.query(countQuery, [userId]);
        console.log(`用户 ${userId} 的放大历史总数: ${totalCount}`);

        // Query paginated history data
        const historyQuery = `
            SELECT id, user_id, original_image_filename, upscaled_image_url, upscale_type, created_at 
            FROM upscale_history 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        `;
        const [history] = await connection.query(historyQuery, [userId, limit, offset]);
        console.log(`查询到 ${history.length} 条历史记录 (当前页)`);

        connection.release();

        const totalPages = Math.ceil(totalCount / limit);

        res.json({
            history: history,
            pagination: {
                totalCount,
                currentPage: page,
                totalPages,
                limit
            }
        });

    } catch (error) {
        if (connection) connection.release();
        console.error(`获取用户 ${userId} 的放大历史时出错:`, error);
        res.status(500).json({ error: '获取放大历史失败', details: error.message });
    }
});

// POST /api/upscale-history/ - Create a new upscale history record
router.post('/', authenticateToken, async (req, res) => {
    const userId = req.user.id;
    const { original_image_filename, upscaled_image_url } = req.body;
    // We might add upscale_type later if needed, for now it's nullable or defaults in DB

    console.log(`收到创建放大历史记录请求: userId=${userId}, original_filename=${original_image_filename}`);

    if (!original_image_filename || !upscaled_image_url) {
        return res.status(400).json({ error: '缺少原始文件名或放大后的图片 URL' });
    }

    let connection;
    try {
        connection = await pool.getConnection();
        const insertQuery = `
            INSERT INTO upscale_history (user_id, original_image_filename, upscaled_image_url) 
            VALUES (?, ?, ?)
        `;
        const [result] = await connection.query(insertQuery, [userId, original_image_filename, upscaled_image_url]);
        connection.release();

        console.log(`用户 ${userId} 的新放大历史记录已创建, ID: ${result.insertId}`);

        // Optionally return the created record
        res.status(201).json({ 
            message: '放大历史记录已创建',
            history: {
                id: result.insertId,
                user_id: userId,
                original_image_filename: original_image_filename,
                upscaled_image_url: upscaled_image_url,
                // upscale_type: null, // Or fetch default if needed
                created_at: new Date() // Approximate time
            }
        });

    } catch (error) {
        if (connection) connection.release();
        console.error(`为用户 ${userId} 创建放大历史记录时出错:`, error);
        res.status(500).json({ error: '创建放大历史记录失败', details: error.message });
    }
});

module.exports = router; 