const express = require('express');
const axios = require('axios');
const { authenticateToken } = require('../utils/auth');

const router = express.Router();

// 允许的图片域名白名单
const ALLOWED_DOMAINS = [
  'unsplash.com',
  'images.unsplash.com',
  'pixabay.com',
  'pexels.com',
  'cdn.pixabay.com',
  'img.freepik.com',
  'static.vecteezy.com',
  'thumbs.dreamstime.com',
  'i.imgur.com',
  'imgur.com',
  'github.com',
  'githubusercontent.com',
  'cloudinary.com',
  'res.cloudinary.com',
  'amazonaws.com',
  's3.amazonaws.com',
  'googleusercontent.com',
  'ggpht.com',
  'ytimg.com',
  'wikimedia.org',
  'upload.wikimedia.org',
  // 添加你需要的其他域名
];

// 验证URL是否在白名单中
const isAllowedDomain = (url) => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;
    
    return ALLOWED_DOMAINS.some(domain => 
      hostname === domain || hostname.endsWith('.' + domain)
    );
  } catch (error) {
    console.error('URL解析错误:', error);
    return false;
  }
};

// 图片代理端点
router.post('/api/proxy-image', authenticateToken, async (req, res) => {
  try {
    const { imageUrl } = req.body;
    
    // 验证请求参数
    if (!imageUrl) {
      return res.status(400).json({
        error: '缺少图片URL参数',
        code: 'MISSING_URL'
      });
    }
    
    // 验证URL格式
    let url;
    try {
      url = new URL(imageUrl);
    } catch (error) {
      return res.status(400).json({
        error: '无效的图片URL格式',
        code: 'INVALID_URL'
      });
    }
    
    // 验证域名白名单
    if (!isAllowedDomain(imageUrl)) {
      return res.status(403).json({
        error: '不允许访问此域名的图片',
        code: 'DOMAIN_NOT_ALLOWED',
        domain: url.hostname
      });
    }
    
    console.log(`代理图片请求: ${imageUrl}`);
    
    // 发起图片下载请求
    const response = await axios({
      method: 'GET',
      url: imageUrl,
      responseType: 'arraybuffer',
      timeout: 10000, // 10秒超时
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'image/*,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Referer': url.origin,
      },
      maxRedirects: 5,
      validateStatus: function (status) {
        return status >= 200 && status < 300;
      }
    });
    
    // 验证响应是否为图片
    const contentType = response.headers['content-type'];
    if (!contentType || !contentType.startsWith('image/')) {
      return res.status(400).json({
        error: 'URL指向的不是图片文件',
        code: 'NOT_IMAGE',
        contentType: contentType
      });
    }
    
    // 检查文件大小限制（10MB）
    const contentLength = response.headers['content-length'];
    if (contentLength && parseInt(contentLength) > 10 * 1024 * 1024) {
      return res.status(413).json({
        error: '图片文件过大（超过10MB）',
        code: 'FILE_TOO_LARGE'
      });
    }
    
    // 设置响应头
    res.set({
      'Content-Type': contentType,
      'Content-Length': response.headers['content-length'],
      'Cache-Control': 'public, max-age=3600',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    
    // 返回图片数据
    res.send(response.data);
    
    console.log(`图片代理成功: ${imageUrl} (${contentType}, ${response.data.length} bytes)`);
    
  } catch (error) {
    console.error('图片代理错误:', error);
    
    if (error.code === 'ENOTFOUND') {
      return res.status(404).json({
        error: '无法找到指定的图片URL',
        code: 'URL_NOT_FOUND'
      });
    } else if (error.code === 'ETIMEDOUT') {
      return res.status(504).json({
        error: '图片下载超时',
        code: 'DOWNLOAD_TIMEOUT'
      });
    } else if (error.response) {
      // 远程服务器返回的错误
      return res.status(error.response.status).json({
        error: '无法访问图片',
        code: 'REMOTE_ERROR',
        status: error.response.status,
        statusText: error.response.statusText
      });
    } else {
      return res.status(500).json({
        error: '图片代理服务内部错误',
        code: 'PROXY_ERROR'
      });
    }
  }
});

// 获取支持的域名列表
router.get('/api/proxy-image/allowed-domains', authenticateToken, (req, res) => {
  res.json({
    success: true,
    allowedDomains: ALLOWED_DOMAINS
  });
});

// 健康检查端点
router.get('/api/proxy-image/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString()
  });
});

module.exports = router; 