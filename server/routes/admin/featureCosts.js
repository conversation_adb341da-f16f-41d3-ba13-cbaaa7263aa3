const express = require('express');
const { authenticateToken, requireAdmin } = require('../../utils/auth');
const pool = require('../../db/pool');

const router = express.Router();

// GET /api/admin/feature-costs - 获取所有功能成本
router.get('/', authenticateToken, requireAdmin, async (req, res) => {
    console.log('[Admin FeatureCosts] GET request received');
    let connection;
    try {
        connection = await pool.getConnection();
        const [costs] = await connection.query('SELECT feature_key, cost, description, can_use_free_credits FROM feature_costs ORDER BY feature_key');
        connection.release();
        console.log('[Admin FeatureCosts] Costs fetched successfully:', costs.length);
        res.json(costs);
    } catch (error) {
        if (connection) connection.release();
        console.error('[Admin FeatureCosts] Error fetching feature costs:', error);
        res.status(500).json({ error: '获取功能成本失败' });
    }
});

// PUT /api/admin/feature-costs - 更新功能成本
router.put('/', authenticateToken, requireAdmin, async (req, res) => {
    console.log('[Admin FeatureCosts] PUT request received');
    const updates = req.body; // Expects an array: [{ feature_key: 'key', cost: new_cost }, ...]

    if (!Array.isArray(updates) || updates.length === 0) {
        return res.status(400).json({ error: '请求体必须是包含更新项的数组' });
    }

    let connection;
    try {
        connection = await pool.getConnection();
        await connection.beginTransaction();
        console.log('[Admin FeatureCosts] DB Connection acquired and transaction started.');

        let updateCount = 0;
        for (const update of updates) {
            const { feature_key, cost, can_use_free_credits } = update;
            
            let setClauses = [];
            let queryParams = [];

            // Validate and add cost to update if provided
            if (cost !== undefined) {
            const costInt = parseInt(cost, 10);
            if (isNaN(costInt) || costInt < 0) {
                    console.warn(`[Admin FeatureCosts] Invalid cost value received: ${cost} for key: ${feature_key}. Skipping cost update.`);
                } else {
                    setClauses.push('cost = ?');
                    queryParams.push(costInt);
                }
            }

            // Add can_use_free_credits to update if provided
            if (can_use_free_credits !== undefined) {
                setClauses.push('can_use_free_credits = ?');
                queryParams.push(Boolean(can_use_free_credits)); // Ensure it's a boolean
            }

            if (setClauses.length === 0) {
                console.warn(`[Admin FeatureCosts] No valid fields to update for key: ${feature_key}. Skipping.`);
                continue; 
            }

            queryParams.push(feature_key); // Add feature_key for the WHERE clause
            
            const updateQuery = `UPDATE feature_costs SET ${setClauses.join(', ')} WHERE feature_key = ?`;
            console.log(`[Admin FeatureCosts] Updating for ${feature_key}. Query: ${updateQuery.replace(/\?/g, (match, i) => queryParams[i-1] !== undefined ? queryParams[i-1] : match )}, Params:`, queryParams);
            
            const [result] = await connection.query(updateQuery, queryParams);
            
            if (result.affectedRows > 0) {
                updateCount++;
            } else {
                console.warn(`[Admin FeatureCosts] No feature found or cost unchanged for key: ${feature_key}`);
            }
        }

        await connection.commit();
        connection.release();
        console.log(`[Admin FeatureCosts] Transaction committed. Updated ${updateCount} feature costs.`);
        res.json({ success: true, message: `成功更新 ${updateCount} 项功能成本` });

    } catch (error) {
        if (connection) {
            try { await connection.rollback(); } catch (rbError) { console.error('Rollback failed:', rbError); }
            connection.release();
        }
        console.error('[Admin FeatureCosts] Error updating feature costs:', error);
        res.status(500).json({ error: '更新功能成本失败', details: error.message });
    }
});

// GET /api/feature-costs/public - 获取公开的功能成本信息（所有用户可访问）
router.get('/public', authenticateToken, async (req, res) => {
    console.log('[FeatureCosts] Public GET request received');
    let connection;
    try {
        connection = await pool.getConnection();
        const [costs] = await connection.query('SELECT feature_key, cost, description, can_use_free_credits FROM feature_costs ORDER BY feature_key');
        connection.release();
        console.log('[FeatureCosts] Public costs fetched successfully:', costs.length);
        res.json(costs);
    } catch (error) {
        if (connection) connection.release();
        console.error('[FeatureCosts] Error fetching public feature costs:', error);
        res.status(500).json({ error: '获取功能成本失败' });
    }
});

module.exports = router; 