'use strict';

const express = require('express');
const pool = require('../../db/pool');
const { authenticateToken } = require('../../utils/auth');

const router = express.Router();

// --- 中间件：检查是否为管理员 ---
// 注意：这假设 req.user 对象中包含 role 属性
const isAdmin = (req, res, next) => {
    // 增加日志记录
    console.log(`[Admin Check] User attempting access: ID=${req.user?.id}, Role=${req.user?.role}`); 
    
    if (req.user && req.user.role === 'admin') {
        console.log('[Admin Check] Access Granted.');
        next(); // 是管理员，继续
    } else {
        console.warn('[Admin Check] Access Denied. User is not an admin.');
        res.status(403).json({ error: '权限不足，需要管理员权限' });
    }
};

// --- 应用 authenticateToken 和 isAdmin 中间件到所有此路由下的请求 ---
router.use(authenticateToken);
router.use(isAdmin); // 确保只有管理员能访问以下路由

// --- 路由定义 ---

// GET /api/admin/users - 获取用户及其积分 (支持分页和搜索)
router.get('/users', async (req, res) => {
    console.log('[Admin API] Received request for GET /users with query:', req.query);
    let connection;

    // --- REMOVED TEMPORARY DEBUGGING CODE ---
    // try {
    //     connection = await pool.getConnection();
    //     console.log('[Admin API DEBUG] Attempting simplified query for user ID 1...');
    //     const [debugUser] = await connection.query('SELECT id, credits FROM users WHERE id = 1');
    //     console.log('[Admin API DEBUG] Result of simplified query for user ID 1:', JSON.stringify(debugUser, null, 2));
    //     if (connection) connection.release(); // Release connection after debug query
    // } catch (debugError) {
    //     console.error('[Admin API DEBUG] Error during simplified query:', debugError);
    //     if (connection) connection.release();
    // }
    // connection = null; // Reset connection variable
    // --- END REMOVED TEMPORARY DEBUGGING CODE ---

    // Pagination and Search parameters
    const page = parseInt(req.query.page, 10) || 1; // Default to page 1
    const limit = 10; // Or get from query, e.g., parseInt(req.query.limit, 10) || 10;
    const offset = (page - 1) * limit;
    const searchTerm = req.query.search ? req.query.search.trim() : ''; // Get search term

    let whereClause = '';
    let queryParams = [];
    let countParams = []; // Separate params for count query initially

    // Build WHERE clause for search
    if (searchTerm) {
        whereClause = 'WHERE (username LIKE ? OR email LIKE ? OR nickname LIKE ?)';
        const searchPattern = `%${searchTerm}%`;
        queryParams.push(searchPattern, searchPattern, searchPattern);
        countParams.push(searchPattern, searchPattern, searchPattern); // Count needs the same search params
    }

    try {
        connection = await pool.getConnection();

        // 1. Get total count of users matching the search criteria
        const countSql = `SELECT COUNT(*) as totalCount FROM users ${whereClause}`;
        console.log('[Admin API] Executing count query:', countSql, countParams);
        const [countResult] = await connection.query(countSql, countParams);
        const totalCount = countResult[0].totalCount;
        const totalPages = Math.ceil(totalCount / limit);
        console.log(`[Admin API] Total matching users: ${totalCount}, Total pages: ${totalPages}`);

        // 2. Get paginated users matching the search criteria
        const usersSql = `SELECT id, username, email, credits, nickname, role, created_at FROM users ${whereClause} ORDER BY id ASC LIMIT ? OFFSET ?`;
        // Add limit and offset to the parameters for the main query
        queryParams.push(limit, offset);
        console.log('[Admin API] Executing users query:', usersSql, queryParams);
        const [users] = await connection.query(usersSql, queryParams);

        console.log(`[Admin API] Found ${users.length} users for page ${page}.`);

        // --- DEBUG: Explicitly log credits for each user and send raw array ---
        console.log('[Admin API DEBUG] Logging credits for each fetched user:');
        users.forEach((user, index) => {
            console.log(`  User index ${index} (ID: ${user.id}): credits = ${user.credits}`);
        });
        // console.log('[Admin API DEBUG] Attempting to send raw users array...');
        // Temporarily send raw array to bypass potential issues with res.json or object structure
        // Note: This will break the frontend's expected pagination structure
        // return res.send(users); // <--- REMOVED DEBUG CODE
        // --- END DEBUG ---

        // --- Original Response structure (commented out for debugging) --- // <-- Restoring this block
        // --- DEBUG: Log raw data JUST BEFORE sending response ---
        // console.log('[Admin API DEBUG] Data JUST BEFORE sending response:', JSON.stringify({ users, pagination: { currentPage: page, totalPages, totalCount, limit } }, null, 2));
        // --- END DEBUG ---

        // Return structured response with users and pagination info
        res.json({ // <-- Restoring original res.json call
            users: users,
            pagination: {
                currentPage: page,
                totalPages: totalPages,
                totalCount: totalCount,
                limit: limit
            }
        });
        // --- End Original Response structure --- //

    } catch (error) {
        console.error('[Admin API] 获取用户列表失败:', error);
        res.status(500).json({ error: '获取用户列表时发生服务器内部错误' });
    } finally {
        if (connection) connection.release();
        console.log('[Admin API] GET /users request finished.');
    }
});

// PUT /api/admin/users/:userId/credits - 更新指定用户的积分
router.put('/users/:userId/credits', async (req, res) => {
    const targetUserId = parseInt(req.params.userId, 10);
    const { newCredits } = req.body; // 从请求体获取新的积分值
    const adminUserId = req.user.id; // 操作的管理员ID

    console.log(`[Admin API] Received request for PUT /users/${targetUserId}/credits`);
    console.log(`[Admin API] Data: newCredits=${newCredits}, AdminUserID=${adminUserId}`);

    if (isNaN(targetUserId) || targetUserId <= 0) {
        return res.status(400).json({ error: '无效的用户 ID' });
    }
    if (newCredits === undefined || newCredits === null || isNaN(parseInt(newCredits)) || parseInt(newCredits) < 0) {
        return res.status(400).json({ error: '无效的积分值，必须是非负数字' });
    }

    const finalNewCredits = parseInt(newCredits, 10);

    let connection;
    try {
        connection = await pool.getConnection();
        await connection.beginTransaction();

        // 1. 获取用户当前积分 (用于记录 balance_after)
        const [currentUserState] = await connection.query(
            'SELECT credits FROM users WHERE id = ?', 
            [targetUserId]
        );

        if (currentUserState.length === 0) {
            await connection.rollback();
            return res.status(404).json({ error: '未找到指定用户' });
        }
        const currentCredits = currentUserState[0].credits;
        const creditChange = finalNewCredits - currentCredits; // 计算变化量

        // 2. 更新用户积分
        const [updateResult] = await connection.query(
            'UPDATE users SET credits = ? WHERE id = ?',
            [finalNewCredits, targetUserId]
        );

        if (updateResult.affectedRows === 0) {
             await connection.rollback(); // 虽然上面检查了，但以防万一
             return res.status(404).json({ error: '更新积分失败，未找到用户' });
        }

        // 3. 记录积分变更日志 (credit_transactions)
        const transactionReason = 'admin_update'; // 标记为管理员操作
        const transactionNotes = `管理员 (ID: ${adminUserId}) 手动修改积分`;
        await connection.query(
            'INSERT INTO credit_transactions (user_id, change_amount, balance_after, reason, notes) VALUES (?, ?, ?, ?, ?)',
            [targetUserId, creditChange, finalNewCredits, transactionReason, transactionNotes]
        );

        await connection.commit();
        console.log(`[Admin API] 用户 ${targetUserId} 积分已成功更新为 ${finalNewCredits} (管理员 ID: ${adminUserId})`);
        res.json({ message: '用户积分更新成功', userId: targetUserId, newCredits: finalNewCredits });

    } catch (error) {
        console.error(`[Admin API] 更新用户 ${targetUserId} 积分失败:`, error);
        if (connection) await connection.rollback(); // 确保回滚
        // 检查是否是特定数据库错误，例如外键约束
        if (error.code === 'ER_NO_REFERENCED_ROW_2') {
             res.status(404).json({ error: '更新失败，关联的用户不存在' });
        } else {
             res.status(500).json({ error: '更新用户积分时发生服务器内部错误' });
        }
    } finally {
        if (connection) connection.release();
        console.log(`[Admin API] PUT /users/${targetUserId}/credits request finished.`);
    }
});

module.exports = router; 