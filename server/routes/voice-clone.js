const express = require('express');
const router = express.Router();
const multer = require('multer');
const axios = require('axios');
const FormData = require('form-data');
const { authenticateToken } = require('../utils/auth');
const { checkAndDeductCredits } = require('../middleware/checkCredits');
const WebSocket = require('ws');

// 文件上传配置（最大10MB，支持mp3/webm/wav等音频）
const upload = multer({ limits: { fileSize: 10 * 1024 * 1024 } });

// 上传音频并转发到 anyvoice
router.post('/upload', authenticateToken, upload.single('file'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ success: false, error: '未收到音频文件' });
        }
        // 解析前端传递的参数
        const { prefix = 'voice-models', audioFormat = 'webm;codecs=opus', channels = 1, sampleRate = 16000 } = req.body;
        // 需要前端传 cookie，或你可以在这里写死测试用cookie
        const cookie = req.headers['cookie'] || '';
        if (!cookie) {
            return res.status(400).json({ success: false, error: '缺少 anyvoice cookie' });
        }

        // 构造 form-data
        const form = new FormData();
        form.append('file', req.file.buffer, {
            filename: req.file.originalname,
            contentType: req.file.mimetype
        });
        form.append('prefix', prefix);
        form.append('audioFormat', audioFormat);
        form.append('channels', channels);
        form.append('sampleRate', sampleRate);

        // 发送到 anyvoice
        const response = await axios.post('https://anyvoice.net/api/upload', form, {
            headers: {
                ...form.getHeaders(),
                'cookie': cookie
            },
            maxContentLength: Infinity,
            maxBodyLength: Infinity
        });

        // 返回 anyvoice 的响应
        return res.json(response.data);
    } catch (error) {
        console.error('上传音频到 anyvoice 失败:', error?.response?.data || error.message);
        return res.status(500).json({ success: false, error: error?.response?.data || error.message });
    }
});

// 创建语音模型接口
router.post('/create-model', authenticateToken, checkAndDeductCredits('voice_clone'), async (req, res) => {
    try {
        // 1. 拿到参数
        const { name, type = 'Private', tag = [], audioSource, audioSourceMd5, prompt_text, language = 'zh', description = 'temp model' } = req.body;
        const cookie = req.headers['cookie'] || '';
        if (!cookie) {
            return res.status(400).json({ success: false, error: '缺少 anyvoice cookie' });
        }
        if (!audioSource || !audioSourceMd5 || !prompt_text) {
            return res.status(400).json({ success: false, error: '缺少必要参数' });
        }
        // 2. 组装 anyvoice 的请求体
        const payload = {
            name: name || 'AI声音克隆',
            type,
            tag,
            audioSource,
            audioSourceMd5,
            prompt_text,
            language,
            description
        };
        // 3. 转发到 anyvoice
        const response = await axios.post('https://anyvoice.net/api/voices', payload, {
            headers: {
                'Content-Type': 'application/json',
                'cookie': cookie
            }
        });
        // 4. 返回 anyvoice 的响应
        return res.json(response.data);
    } catch (error) {
        console.error('创建语音模型失败:', error?.response?.data || error.message);
        return res.status(500).json({ success: false, error: error?.response?.data || error.message });
    }
});

// 生成语音（TTS）接口
router.post('/tts', authenticateToken, checkAndDeductCredits('voice_clone'), async (req, res) => {
    try {
        const { voiceId, text, format = 'mp3' } = req.body;
        const cookie = req.headers['cookie'] || '';
        if (!cookie) {
            return res.status(400).json({ success: false, error: '缺少 anyvoice cookie' });
        }
        if (!voiceId || !text) {
            return res.status(400).json({ success: false, error: '缺少 voiceId 或 text' });
        }

        // anyvoice tts ws 地址
        const wsUrl = 'wss://anyvoice.net/api/tts';
        const ws = new WebSocket(wsUrl, {
            headers: {
                'cookie': cookie,
                'origin': 'https://anyvoice.net',
                'user-agent': req.headers['user-agent'] || 'Mozilla/5.0'
            }
        });

        let isResolved = false;
        let timeoutId;

        ws.on('open', () => {
            // 发送TTS请求
            ws.send(JSON.stringify({
                text,
                voiceId,
                format
            }));
            // 超时保护
            timeoutId = setTimeout(() => {
                if (!isResolved) {
                    isResolved = true;
                    ws.terminate();
                    return res.status(504).json({ success: false, error: 'TTS请求超时' });
                }
            }, 30000); // 30秒超时
        });

        ws.on('message', (data) => {
            try {
                const msg = JSON.parse(data);
                if (msg.audio_url) {
                    if (!isResolved) {
                        isResolved = true;
                        clearTimeout(timeoutId);
                        ws.close();
                        return res.json({ success: true, audio_url: msg.audio_url, duration: msg.duration, quotaExhausted: msg.quotaExhausted });
                    }
                } else if (msg.error) {
                    if (!isResolved) {
                        isResolved = true;
                        clearTimeout(timeoutId);
                        ws.close();
                        return res.status(500).json({ success: false, error: msg.error });
                    }
                }
            } catch (e) {
                // 忽略解析失败
            }
        });

        ws.on('error', (err) => {
            if (!isResolved) {
                isResolved = true;
                clearTimeout(timeoutId);
                return res.status(500).json({ success: false, error: 'WebSocket连接失败: ' + err.message });
            }
        });

        ws.on('close', () => {
            if (!isResolved) {
                isResolved = true;
                clearTimeout(timeoutId);
                return res.status(500).json({ success: false, error: 'WebSocket连接被关闭' });
            }
        });
    } catch (error) {
        return res.status(500).json({ success: false, error: error?.message || error });
    }
});

module.exports = router;
