const express = require('express');
const { authenticateToken } = require('../utils/auth');
const pool = require('../db/pool');

const router = express.Router();

// GET /api/feature-costs - 获取公开的功能成本信息（所有用户可访问）
router.get('/', authenticateToken, async (req, res) => {
    console.log('[FeatureCosts] Public GET request received');
    let connection;
    try {
        connection = await pool.getConnection();
        const [costs] = await connection.query('SELECT feature_key, cost, description, can_use_free_credits FROM feature_costs ORDER BY feature_key');
        connection.release();
        console.log('[FeatureCosts] Public costs fetched successfully:', costs.length);
        res.json(costs);
    } catch (error) {
        if (connection) connection.release();
        console.error('[FeatureCosts] Error fetching public feature costs:', error);
        res.status(500).json({ error: '获取功能成本失败' });
    }
});

module.exports = router;