const express = require('express');
const router = express.Router({ mergeParams: true });
const pool = require('../db/pool');
const { authenticateToken } = require('../utils/auth');
const { commentRateLimiter } = require('../middleware/rateLimit');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
// 工具函数：把 image_url 和 author_avatar_url 转成完整可用链接
function processCommentsUrls(comments) {
    const BASE_URL = 'https://www.yzycolour.top/prompt-examples/server/uploads/';
    return comments.map(comment => {
      if (comment.image_url) {
        comment.image_url = BASE_URL + comment.image_url;
      }
      if (comment.author_avatar_url) {
        comment.author_avatar_url = BASE_URL + comment.author_avatar_url;
      }
      return comment;
    });
  }
  
// --- 新增：配置Multer用于图片上传 ---
const uploadDir = path.join(__dirname, '..', 'uploads');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
        cb(null, uniqueName);
    }
});

const imageUpload = multer({
    storage: storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 限制5MB
    fileFilter: function (req, file, cb) {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('仅支持上传图片文件!'), false);
        }
    }
});
// --- Multer配置结束 ---

// GET /api/examples/:id/comments - 获取一个案例的所有评论
router.get('/', async (req, res) => {
    // 从 mergeParams 中获取父路由的 'id' 参数
    const { id } = req.params;

    if (!id) {
        return res.status(400).json({ error: '案例ID缺失' });
    }

    console.log(`[API] 正在获取案例 ${id} 的评论`);

    try {
        const [comments] = await pool.query(`
            SELECT 
                c.id,
                c.content,
                c.image_url,
                c.created_at,
                c.parent_comment_id,
                u.id AS author_id,
                u.username AS author_username,
                u.nickname AS author_nickname,
                u.avatar_url AS author_avatar_url
            FROM comments c
            JOIN users u ON c.user_id = u.id
            WHERE c.prompt_id = ?
            ORDER BY c.created_at ASC
        `, [id]);

        // 处理并返回带有完整URL的评论
        res.json(processCommentsUrls(comments));

    } catch (error) {
        console.error(`获取案例 ${id} 评论失败:`, error);
        res.status(500).json({ error: '获取评论失败', details: error.message });
    }
});

// POST /api/examples/:id/comments - 创建一条新评论
router.post('/', authenticateToken, commentRateLimiter, imageUpload.single('image'), async (req, res) => {
    // 从 mergeParams 中获取父路由的 'id' 参数
    const { id: promptId } = req.params;
    // 从 req.body 获取文本内容 (由 multer 处理)
    const { content, parent_comment_id = null } = req.body;
    // 从 req.file 获取图片信息
    const imageFileName = req.file ? req.file.filename : null;
    const userId = req.user.id;

    if (!promptId) {
        return res.status(400).json({ error: '案例ID缺失' });
    }
    // 即使有图片，文本内容也不能为空
    if (!content || !content.trim()) {
        return res.status(400).json({ error: '评论内容不能为空' });
    }
     if (content.trim().length > 500) { // 限制评论长度
        return res.status(400).json({ error: '评论内容不能超过500个字符' });
    }

    let connection;
    try {
        connection = await pool.getConnection();
        await connection.beginTransaction();

        // 1. 插入评论，包含图片URL
        const [commentResult] = await connection.query(
            'INSERT INTO comments (prompt_id, user_id, content, parent_comment_id, image_url) VALUES (?, ?, ?, ?, ?)',
            [promptId, userId, content.trim(), parent_comment_id, imageFileName]
        );
        const newCommentId = commentResult.insertId;

        // 2. 获取案例的作者和标题，用于创建通知
        const [[prompt]] = await connection.query('SELECT author_id, title FROM prompt_examples WHERE id = ?', [promptId]);
        
        // 3. 如果评论者不是作者本人，则创建通知
        if (prompt && prompt.author_id !== userId) {
            const notificationData = {
                prompt_title: prompt.title,
                content_summary: content.trim().substring(0, 50) // 截取部分内容作为摘要
            };
            await connection.query(
                'INSERT INTO notifications (recipient_id, actor_id, type, entity_type, entity_id, data) VALUES (?, ?, ?, ?, ?, ?)',
                [prompt.author_id, userId, 'new_comment', 'prompt_examples', promptId, JSON.stringify(notificationData)]
            );
             console.log(`[通知] 已为用户 ${prompt.author_id} 创建新评论通知`);
        }

        await connection.commit();

        // 4. 查询并返回完整的新评论信息
        const [[newComment]] = await connection.query(`
            SELECT 
                c.id,
                c.content,
                c.image_url,
                c.created_at,
                c.parent_comment_id,
                u.id AS author_id,
                u.username AS author_username,
                u.nickname AS author_nickname,
                u.avatar_url AS author_avatar_url
            FROM comments c
            JOIN users u ON c.user_id = u.id
            WHERE c.id = ?
        `, [newCommentId]);
        
        // 处理并返回完整URL的评论
        const processedComment = processCommentsUrls([newComment])[0];
        res.status(201).json(processedComment);

    } catch (error) {
        if (connection) await connection.rollback();
        console.error('创建评论失败:', error);
        res.status(500).json({ error: '创建评论失败', details: error.message });
    } finally {
        if (connection) connection.release();
    }
});

// DELETE /api/examples/:id/comments/:commentId - 删除评论
router.delete('/:commentId', authenticateToken, async (req, res) => {
    const { id: promptId, commentId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    try {
        const [[comment]] = await pool.query('SELECT user_id FROM comments WHERE id = ? AND prompt_id = ?', [commentId, promptId]);

        if (!comment) {
            return res.status(404).json({ error: '评论不存在' });
        }

        // 检查权限：只有评论者本人或管理员可以删除
        if (comment.user_id !== userId && userRole !== 'admin') {
            return res.status(403).json({ error: '您没有权限删除此评论' });
        }

        await pool.query('DELETE FROM comments WHERE id = ?', [commentId]);

        res.status(200).json({ success: true, message: '评论已删除' });

    } catch (error) {
        console.error(`删除评论 ${commentId} 失败:`, error);
        res.status(500).json({ error: '删除评论失败', details: error.message });
    }
});

// PUT /api/examples/:id/comments/:commentId - 编辑评论
router.put('/:commentId', authenticateToken, async (req, res) => {
    const { id: promptId, commentId } = req.params;
    const { content } = req.body;
    const userId = req.user.id;
    const userRole = req.user.role;

    if (!content || !content.trim()) {
        return res.status(400).json({ error: '评论内容不能为空' });
    }

    if (content.trim().length > 500) {
        return res.status(400).json({ error: '评论内容不能超过500个字符' });
    }

    try {
        // 检查评论是否存在以及用户是否有权限编辑
        const [[comment]] = await pool.query('SELECT user_id FROM comments WHERE id = ? AND prompt_id = ?', [commentId, promptId]);

        if (!comment) {
            return res.status(404).json({ error: '评论不存在' });
        }

        // 只有评论者本人可以编辑自己的评论
        if (comment.user_id !== userId) {
            return res.status(403).json({ error: '您没有权限编辑此评论' });
        }

        // 更新评论内容
        await pool.query('UPDATE comments SET content = ? WHERE id = ?', [content.trim(), commentId]);

        // 获取并返回更新后的评论
        const [[updatedComment]] = await pool.query(`
            SELECT 
                c.id,
                c.content,
                c.image_url,
                c.created_at,
                c.parent_comment_id,
                u.id AS author_id,
                u.username AS author_username,
                u.nickname AS author_nickname,
                u.avatar_url AS author_avatar_url
            FROM comments c
            JOIN users u ON c.user_id = u.id
            WHERE c.id = ?
        `, [commentId]);

        // 处理URL并返回
        const processedComment = processCommentsUrls([updatedComment])[0];
        res.json(processedComment);

    } catch (error) {
        console.error(`编辑评论 ${commentId} 失败:`, error);
        res.status(500).json({ error: '编辑评论失败', details: error.message });
    }
});

module.exports = router;
