const express = require('express');
const router = express.Router();
const pool = require('../db/pool');
const { authenticateToken } = require('../utils/auth');

// GET /api/notifications - Get all notifications for the current user
router.get('/', authenticateToken, async (req, res) => {
    const userId = req.user.id;
    try {
        const [notifications] = await pool.query(`
            SELECT n.*, u.username AS actor_username, u.nickname AS actor_nickname, u.avatar_url AS actor_avatar_url
            FROM notifications n
            LEFT JOIN users u ON n.actor_id = u.id
            WHERE n.recipient_id = ?
            ORDER BY n.created_at DESC
        `, [userId]);

        // Process notifications to nest actor info
        const processedNotifications = notifications.map(n => {
            const data = typeof n.data === 'string' ? JSON.parse(n.data) : n.data;
            const actor = n.actor_id ? {
                id: n.actor_id,
                username: n.actor_username,
                nickname: n.actor_nickname,
                avatar_url: n.actor_avatar_url
            } : null;
            
            // a new object to avoid deleting properties from the original
            const notificationResponse = { ...n, data, actor };
            delete notificationResponse.actor_username;
            delete notificationResponse.actor_nickname;
            delete notificationResponse.actor_avatar_url;

            return notificationResponse;
        });

        res.json(processedNotifications);
    } catch (error) {
        console.error('获取通知失败:', error);
        res.status(500).json({ error: '获取通知失败' });
    }
});

// GET /api/notifications/unread-count - Get unread notification count
router.get('/unread-count', authenticateToken, async (req, res) => {
    const userId = req.user.id;
    try {
        const [[{ count }]] = await pool.query(
            'SELECT COUNT(*) as count FROM notifications WHERE recipient_id = ? AND is_read = 0',
            [userId]
        );
        res.json({ count });
    } catch (error) {
        console.error('获取未读通知数失败:', error);
        res.status(500).json({ error: '获取未读通知数失败' });
    }
});

// POST /api/notifications/mark-read - Mark notifications as read
router.post('/mark-read', authenticateToken, async (req, res) => {
    const userId = req.user.id;
    const { notificationIds } = req.body; // Expect an array of IDs

    if (!notificationIds || !Array.isArray(notificationIds) || notificationIds.length === 0) {
        return res.status(400).json({ error: '需要提供通知 ID 数组' });
    }

    try {
        const [result] = await pool.query(
            'UPDATE notifications SET is_read = 1 WHERE recipient_id = ? AND id IN (?)',
            [userId, notificationIds]
        );
        res.json({ success: true, affectedRows: result.affectedRows });
    } catch (error) {
        console.error('标记通知已读失败:', error);
        res.status(500).json({ error: '标记通知已读失败' });
    }
});

module.exports = router; 