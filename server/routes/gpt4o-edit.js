const express = require('express');
const axios = require('axios');
const FormData = require('form-data');
const multer = require('multer'); // 引入 multer
const { authenticateToken } = require('../utils/auth');
const pool = require('../db/pool'); 
// REMOVE: Import credit deduction utility - we will use middleware instead
// const { deductCredits } = require('../utils/credits'); 

// ADD: Import the credit checking middleware
const { checkAndDeductCredits } = require('../middleware/checkCredits');
// OpenAI 模块在此文件中似乎没有直接使用，可以考虑移除
// const { OpenAI } = require("openai");

// --- Multer Configuration ---
// 限制文件大小为 25MB，符合 gpt-image-1 的要求
const upload = multer({
    storage: multer.memoryStorage(), // 将文件存储在内存中 (req.file.buffer)
    limits: { fileSize: 25 * 1024 * 1024 }, // 25MB
    fileFilter: (req, file, cb) => {
        // 允许 PNG, WEBP, JPG
        if (file.mimetype === 'image/png' || file.mimetype === 'image/jpeg' || file.mimetype === 'image/webp') {
            cb(null, true);
        } else {
            cb(new Error('不支持的文件类型！仅支持 PNG, JPG, WEBP。'), false);
        }
    }
}).array('image', 3); // 接收名为 'image' 的字段，最多包含3个文件

// --- ComfyUI Configuration ---
// Ensure this points to your ComfyUI instance
const COMFYUI_BASE_URL_FROM_ENV = process.env.COMFYUI_CPU_STORAGE_URL; // <-- Read the correct env variable
// const COMFYUI_DEFAULT_URL = 'https://u63642-a7c0-ee81c217.cqa1.seetacloud.com:8443'; // <-- Old incorrect default
const COMFYUI_DEFAULT_URL = 'https://u63642-a2b4-fccb0b0d.westx.seetacloud.com:8443'; // <-- New correct default provided by user

let COMFYUI_BASE_URL = COMFYUI_BASE_URL_FROM_ENV || COMFYUI_DEFAULT_URL;

if (!COMFYUI_BASE_URL_FROM_ENV) {
    console.warn(`[WARN] COMFYUI_CPU_STORAGE_URL environment variable not set. Falling back to default: ${COMFYUI_DEFAULT_URL}`);
} else {
    console.log(`[INFO] Using ComfyUI base URL from environment: ${COMFYUI_BASE_URL}`);
}

const router = express.Router();

const FEATURE_KEY = 'gpt4o_image_edit'; // Define cost key for this feature
// CORRECTED: Use the dedicated image editing endpoint
const EXTERNAL_API_URL = 'https://ai.comfly.chat/v1/images/edits'; 
// const EXTERNAL_API_URL = 'https://caca.yzycolour.top/v1/chat/completions'; // Your old proxy URL causing the loop

// Helper function to parse Base64 Data URL
function parseDataUrl(dataUrl) {
  const match = dataUrl.match(/^data:(image\/[a-z]+);base64,(.+)$/);
  if (!match) {
    // 如果不是标准 Data URL，但可能是纯Base64，尝试直接返回，让调用者处理
    // 或者根据具体情况决定是否抛出错误
    console.warn('[parseDataUrl] Input is not a standard Data URL. Assuming raw Base64 if needed.');
    // 为了让后续 ComfyUI 上传 b64_json 工作，这里返回一个预设的 mimeType
    return { mimeType: 'image/png', base64Data: dataUrl }; // 假设外部API返回的b64是png内容
  }
  return { mimeType: match[1], base64Data: match[2] };
}

// --- Helper function to upload a single image buffer to ComfyUI ---
async function uploadImageToComfyUI(userId, file, imageType /* 'main', 'ref1', 'ref2' */) {
    if (!file || !file.buffer || !file.originalname || !file.mimetype) {
        console.error(`[ComfyUploadHelper] Invalid file data provided for user ${userId}, type ${imageType}.`);
        return { success: false, error: 'Invalid file data for ComfyUI upload', type: imageType, original_filename: file?.originalname || 'unknown' };
    }

    try {
        const base64Data = file.buffer.toString('base64');
        const originalFilenameWithoutExt = file.originalname.split('.').slice(0, -1).join('.') || file.originalname;

        const comfyPayload = {
            // 使用更具体的subdir和filename_prefix以区分输入图片
            filename_prefix: `gpt4o_edit_input_${imageType}_${userId}_${originalFilenameWithoutExt}_${Date.now()}`,
            subdir: `gpt4o-edit/inputs/${userId}`, // 按用户ID分子目录存储输入图片
            base64: base64Data,
            mime_type: file.mimetype,
            overwrite: false // 通常不覆盖，每次上传都是新的
        };

        const comfyUploadEndpoint = `${COMFYUI_BASE_URL}/yzy/upload-base64`; // 假设 ComfyUI 有这样一个专门的 base64 上传接口
                                                                         // 或者沿用 download-url 如果它能按预期处理 base64
        // 检查现有代码，/yzy/download-url 似乎可以处理 base64 (见原先的 resultImageB64 上传部分)
        // 我们将复用 /yzy/download-url 逻辑，但需要确认其 payload 结构是否完全兼容
        // 从原代码看，/yzy/download-url 使用的是：
        // comfyPayload.url (for URL uploads)
        // comfyPayload.base64 + comfyPayload.mime_type (for base64 uploads for *result* images)
        // comfyPayload.filename_prefix, comfyPayload.subdir
        // 看起来是兼容的。

        console.log(`[ComfyUploadHelper] User ${userId}: Attempting to upload input image ${file.originalname} (type: ${imageType}) to ComfyUI.`);
        const comfyResponse = await axios.post(`${COMFYUI_BASE_URL}/yzy/download-url`, comfyPayload, {
            headers: { 'Content-Type': 'application/json' }
        });

        if (comfyResponse.data && comfyResponse.data.success && comfyResponse.data.filename) {
            const { filename, subfolder: comfySubdirValue, type: comfyImageType } = comfyResponse.data; // type here is from comfy, like "input" or "output"
            const viewUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(filename)}&subfolder=${encodeURIComponent(comfySubdirValue)}&type=${comfyImageType}`;
            console.log(`[ComfyUploadHelper] User ${userId}: Input image ${file.originalname} (type: ${imageType}) successfully uploaded to ComfyUI: ${viewUrl}`);
            return {
                success: true,
                comfy_url: viewUrl,
                original_filename: file.originalname,
                type: imageType // 返回我们定义的类型 'main', 'ref1', 'ref2'
            };
        } else {
            console.error(`[ComfyUploadHelper] User ${userId}: ComfyUI upload failed for input image ${file.originalname} (type: ${imageType}):`, comfyResponse.data);
            return {
                success: false,
                error: 'ComfyUI upload failed for input image. ' + (comfyResponse.data?.message || ''),
                original_filename: file.originalname,
                type: imageType,
                comfy_url: null
            };
        }
    } catch (error) {
        console.error(`[ComfyUploadHelper] User ${userId}: Error during ComfyUI upload for input image ${file.originalname} (type: ${imageType}):`, error.message);
        return {
            success: false,
            error: 'Exception during ComfyUI upload for input image. ' + (error.response?.data?.message || error.message),
            original_filename: file.originalname,
            type: imageType,
            comfy_url: null
        };
    }
}

// POST /api/gpt4o-edit/edit-image (路径在 index.js 中定义为 /api/edit-image)
// 1. authenticateToken
// 2. upload.single('image') <--- multer 中间件处理文件
// 3. checkAndDeductCredits
router.post('/edit-image', authenticateToken, upload, checkAndDeductCredits(FEATURE_KEY, false), async (req, res) => {
    const userId = req.user.id;

    const { prompt, model, n, quality, size, response_format } = req.body;
    const uploadedFiles = req.files; // req.files 将是一个包含所有以 'image' 为名上传的文件的数组

    console.log(`[GPT4o Edit] Received request from user ${userId} for feature ${FEATURE_KEY}.`);
    console.log(`[GPT4o Edit] Params: model=${model}, n=${n}, quality=${quality}, size=${size}, response_format=${response_format}, prompt=${prompt ? prompt.substring(0,50)+'...' : 'N/A'}`);

    if (!uploadedFiles || uploadedFiles.length === 0) {
        console.warn(`[GPT4o Edit] Missing image files from user ${userId}.`);
        return res.status(400).json({ error: '缺少图片文件 (image)' });
    }
    if (!uploadedFiles.some(file => file.fieldname === 'image')) { // 确保至少一个主图
        console.warn(`[GPT4o Edit] No primary image found in uploaded files for user ${userId}.`);
        return res.status(400).json({ error: '缺少主图片文件 (image)' });
    }

    console.log(`[GPT4o Edit] Received ${uploadedFiles.length} file(s):`);
    uploadedFiles.forEach((file, index) => {
        console.log(`  File ${index + 1}: ${file.originalname} (fieldname: ${file.fieldname})`);
    });

    if (!prompt || prompt.trim() === '') {
        console.warn(`[GPT4o Edit] Missing prompt from user ${userId}.`);
        return res.status(400).json({ error: '缺少编辑指令 (prompt)' });
    }
    if (model && model !== "gpt-image-1" && model !== "dall-e-2") { // 虽然用户说不要dall-e-2，但API文档里有
        console.warn(`[GPT4o Edit] Invalid model: ${model} from user ${userId}. Defaulting might occur at external API.`);
        // 不在此处强制错误，让外部API处理或默认
    }

    // --- Upload Input Images to ComfyUI and prepare for history ---
    const inputImageDetailsForHistory = [];
    const imageTypeMapping = ['main', 'ref1', 'ref2']; // Based on array order from multer

    for (let i = 0; i < uploadedFiles.length; i++) {
        const file = uploadedFiles[i];
        const imageType = imageTypeMapping[i] || `ref${i-1}`; // Fallback for more than 3, though UI might not support
        
        // Only upload if it's an actual image file
        if (file && file.buffer) {
            console.log(`[GPT4o Edit] User ${userId}: Uploading input image ${file.originalname} as type '${imageType}' to ComfyUI.`);
            const uploadResult = await uploadImageToComfyUI(userId, file, imageType);
            inputImageDetailsForHistory.push({
                type: imageType,
                original_filename: file.originalname,
                comfy_url: uploadResult.success ? uploadResult.comfy_url : null,
                upload_error: uploadResult.success ? null : uploadResult.error
            });
            if (!uploadResult.success) {
                console.warn(`[GPT4o Edit] User ${userId}: Failed to upload input image ${file.originalname} (type: ${imageType}) to ComfyUI. Error: ${uploadResult.error}`);
                // Decide if this is a critical failure (e.g., if main image fails)
                // For now, we'll log and continue, history will show null URL.
            }
        }
    }
    const inputImagesJsonForHistory = JSON.stringify(inputImageDetailsForHistory);
    console.log(`[GPT4o Edit] User ${userId}: Input images for history: ${inputImagesJsonForHistory}`);

    // --- Prepare Request for EXTERNAL_API_URL ---
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
        console.error('[GPT4o Edit] Error: OPENAI_API_KEY not configured.');
        return res.status(500).json({ error: '服务器配置错误' });
    }

    const externalFormData = new FormData();
    externalFormData.append('prompt', prompt.trim());

    // Add all uploaded files to the external API request
    // The external API expects files under the 'image' key.
    // The first image in uploadedFiles should be the primary image.
    // Subsequent images might be reference/mask images depending on the API's capabilities.
    // For OpenAI's /edits endpoint, it expects 'image' and optionally 'mask'.
    // Our current setup sends all files as 'image'. This might need adjustment
    // if the external API requires specific field names for main vs. reference images.
    // For now, assuming the first is main, and others are auxiliary if the API supports multiple 'image' fields.
    if (uploadedFiles && uploadedFiles.length > 0) {
        uploadedFiles.forEach((file, index) => {
             // OpenAI 'images/edits' endpoint takes 'image' and 'mask'.
             // If we have more than one image, and no explicit mask handling,
             // we send the first as 'image'. If we want to support masks,
             // the frontend and backend need to differentiate it.
             // For now, let's assume the API can take multiple 'image' keys if we send them,
             // or it primarily uses the first one.
             // The user request implies 'main image' and 'reference images'.
             // The OpenAI edits API documentation should be checked for how it handles multiple images.
             // It primarily supports one 'image' and one 'mask'.
             // If we send multiple 'image' keys, the behavior is undefined by OpenAI docs.
             // However, the current code already does this (loops and appends 'image').
             // We will retain this behavior and ensure the main image is appended.
            externalFormData.append('image', file.buffer, {
                filename: file.originalname,
                contentType: file.mimetype
            });
            console.log(`[GPT4o Edit] Appending file ${index + 1} (${file.originalname}) as 'image' to external API request.`);
        });
    }

    externalFormData.append('model', model || "gpt-image-1");
    if (n) externalFormData.append('n', n);
    if (quality && quality !== 'auto') externalFormData.append('quality', quality);
    if (size && size !== 'auto') externalFormData.append('size', size);
    externalFormData.append('response_format', response_format || 'b64_json');

    const headers = {
        ...externalFormData.getHeaders(),
        'Authorization': `Bearer ${apiKey}`,
        'Accept': 'application/json',
    };

    let externalApiResponse = null;
    let historyId = null;
    let comfyViewUrlForResult = null; // Renamed to avoid confusion with input comfy_urls
    let finalStatus = 'failed';
    let finalErrorMessage = null;

    try {
        console.log(`[GPT4o Edit] User ${userId}: Sending request to external API: ${EXTERNAL_API_URL}`);
        externalApiResponse = await axios.post(EXTERNAL_API_URL, externalFormData, { headers });
        console.log(`[GPT4o Edit] User ${userId}: Received response from external API.`);

        let resultImageUrlFromApi = null;
        let resultImageB64FromApi = null;

        if (externalApiResponse.data && Array.isArray(externalApiResponse.data.data) && externalApiResponse.data.data.length > 0) {
            const firstResult = externalApiResponse.data.data[0];
            if (firstResult.url) {
                resultImageUrlFromApi = firstResult.url;
            } else if (firstResult.b64_json) {
                resultImageB64FromApi = firstResult.b64_json;
            }
        }

        if (!resultImageUrlFromApi && !resultImageB64FromApi) {
             console.warn(`[GPT4o Edit] User ${userId}: Could not extract result image from external API response:`, externalApiResponse.data);
             finalErrorMessage = '未能从外部 API 获取有效图片结果。';
             throw new Error(finalErrorMessage);
        }

        // --- Transfer RESULT image to ComfyUI ---
        try {
            console.log(`[GPT4o Edit] User ${userId}: Attempting to transfer RESULT image to ComfyUI.`);
            const comfyPayloadForResult = {
                filename_prefix: `gpt4o_edit_result_${userId}_${Date.now()}`, // Distinguish result prefix
                subdir: `gpt4o-edit/outputs/${userId}` // Per-user output subdirectories
            };

            if (resultImageUrlFromApi) {
                comfyPayloadForResult.url = resultImageUrlFromApi;
                console.log(`[GPT4o Edit] Sending RESULT URL to ComfyUI: ${resultImageUrlFromApi}`);
            } else if (resultImageB64FromApi) {
                const b64PrefixMatch = resultImageB64FromApi.match(/^data:(image\/[a-z]+);base64,/);
                let pureBase64 = resultImageB64FromApi;
                let detectedMimeType = 'image/png';

                if (b64PrefixMatch) {
                    detectedMimeType = b64PrefixMatch[1];
                    pureBase64 = resultImageB64FromApi.substring(b64PrefixMatch[0].length);
                } else {
                    console.warn('[GPT4o Edit Debug] No Data URL prefix found on b64_json string for result. Assuming pure Base64.');
                }
                
                comfyPayloadForResult.base64 = pureBase64;
                comfyPayloadForResult.mime_type = detectedMimeType;
                console.log(`[GPT4o Edit] Sending RESULT PURE Base64 (MIME: ${detectedMimeType}) to ComfyUI.`);
            }

            const comfyDownloadEndpoint = `${COMFYUI_BASE_URL}/yzy/download-url`; // Same endpoint for upload via base64/url
            const comfyResponse = await axios.post(comfyDownloadEndpoint, comfyPayloadForResult, {
                headers: { 'Content-Type': 'application/json' }
            });

            if (comfyResponse.data && comfyResponse.data.success && comfyResponse.data.filename) {
                const { filename, subfolder: comfySubdirValue, type: comfyType } = comfyResponse.data;
                comfyViewUrlForResult = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(filename)}&subfolder=${encodeURIComponent(comfySubdirValue)}&type=${comfyType}`;
                console.log(`[GPT4o Edit] User ${userId}: RESULT image successfully transferred to ComfyUI: ${comfyViewUrlForResult}`);
                finalStatus = 'completed';
            } else {
                console.error(`[GPT4o Edit] User ${userId}: ComfyUI transfer failed for RESULT image:`, comfyResponse.data);
                finalErrorMessage = '结果图片生成成功，但转移到存储服务器失败。' + (comfyResponse.data?.message ? ` (${comfyResponse.data.message})` : '');
                finalStatus = 'failed'; // Keep it failed if result transfer fails
            }
        } catch (comfyError) {
            console.error(`[GPT4o Edit] User ${userId}: ComfyUI transfer error for RESULT image:`, comfyError.message);
            finalErrorMessage = '结果图片生成成功，但连接存储服务器时出错。' + (comfyError.response?.data?.message ? ` (${comfyError.response.data.message})` : '');
            finalStatus = 'failed'; // Keep it failed
        }
        // --- End ComfyUI Transfer for RESULT ---

        // --- Save History ---
        // Cost is already handled by middleware,但要确保 costUsed 一定是数字，不能为 '?'
        let costUsed = 0;
        if (typeof req.deductedCreditsAmount === 'number') {
            costUsed = req.deductedCreditsAmount;
        } else {
        try {
            const [[costData]] = await pool.query('SELECT cost FROM feature_costs WHERE feature_key = ?', [FEATURE_KEY]);
                if (costData && typeof costData.cost === 'number') {
                    costUsed = costData.cost;
                } else if (costData && typeof costData.cost === 'string' && !isNaN(Number(costData.cost))) {
                    costUsed = Number(costData.cost);
                } else {
                    costUsed = 0;
                }
            } catch (e) {
                costUsed = 0;
            }
        }

        try {
            console.log(`[GPT4o Edit] User ${userId}: Saving history record with status: ${finalStatus}. Input images JSON: ${inputImagesJsonForHistory}`);
            const [insertResult] = await pool.query(
                'INSERT INTO gpt4o_edit_history (user_id, prompt, input_images_json, result_image_url, model_used, cost, status, error_message) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                [
                    userId,
                    prompt.trim(),
                    inputImagesJsonForHistory, // Store the new detailed JSON
                    comfyViewUrlForResult,    // URL of the final result image on ComfyUI
                    model || "gpt-image-1",
                    costUsed,
                    finalStatus,
                    finalErrorMessage
                ]
            );
            historyId = insertResult.insertId;
            console.log(`[GPT4o Edit] User ${userId}: History record saved with ID: ${historyId}`);
        } catch (historyError) {
            console.error(`[GPT4o Edit] User ${userId}: Failed to save history record:`, historyError);
            // Even if history saving fails, the user should get their image if processing was successful.
        }

        // --- Send Response to Frontend ---
        if (finalStatus === 'completed' && comfyViewUrlForResult) {
            res.json({
                success: true,
                result: {
                    imageUrl: comfyViewUrlForResult // This is the edited image URL
                },
                historyId: historyId
            });
        } else {
            // If finalStatus is 'failed', finalErrorMessage should be set.
            res.status(500).json({
                success: false,
                error: finalErrorMessage || '图片编辑完成但存储或记录过程中发生错误。'
            });
        }

    } catch (error) {
        // This catch block handles errors primarily from the external API call or initial setup.
        // Errors from ComfyUI transfer (for result) or history saving are handled within the main try block.
        finalStatus = 'failed'; // Ensure status is marked failed.
        console.error(`[GPT4o Edit] User ${userId}: Error during external API call or main processing:`, error.message, error.stack);

        let httpStatus = 500;
        let message = '处理图片编辑请求失败';

        if (error.isAxiosError && error.response) {
            console.error(`[GPT4o Edit] User ${userId}: Axios error (external API): Status ${error.response.status}`, error.response.data);
            httpStatus = error.response.status; // Propagate status from external API if it's an HTTP error
            message = error.response.data?.error?.message || error.response.data?.error || message;
        } else if (error.message) {
            message = error.message; // Use error message if available
        }

        // Attempt to save a failed history record if not already done (e.g., if error happened before history save block)
        if (!historyId) { // Only if history hasn't been saved yet.
            try {
                const costUsedOnError = req.deductedCreditsAmount !== undefined ? req.deductedCreditsAmount : 0; // Assume 0 if not deducted or unknown
                console.log(`[GPT4o Edit] User ${userId}: Saving FAILED history record due to error: ${message}. Input JSON: ${inputImagesJsonForHistory}`);
                await pool.query(
                    'INSERT INTO gpt4o_edit_history (user_id, prompt, input_images_json, result_image_url, model_used, cost, status, error_message) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                    [
                        userId,
                        prompt ? prompt.trim() : "N/A",
                        inputImagesJsonForHistory, // Save what we have for input images
                        null, // No result image URL
                        model || "gpt-image-1",
                        costUsedOnError, // Log cost if available, otherwise 0
                        'failed',
                        message.substring(0, 1000) // Truncate error message if too long for DB
                    ]
                );
                console.log(`[GPT4o Edit] User ${userId}: FAILED history record saved.`);
            } catch (historySaveError) {
                console.error(`[GPT4o Edit] User ${userId}: CRITICAL - Failed to save error state to history:`, historySaveError);
            }
        }

        res.status(httpStatus).json({
            success: false,
            error: message
        });
    }
});

// GET /api/gpt4o-edit/history (路径在 index.js 中定义为 /api/history)
// 此部分基本不变，但 input_images_json 的内容现在是文件名数组字符串
router.get('/history', authenticateToken, async (req, res) => {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    try {
        const [historyRecords] = await pool.query(
            `SELECT id, prompt, input_images_json, result_image_url, model_used, cost, status, error_message, created_at
             FROM gpt4o_edit_history
             WHERE user_id = ?
             ORDER BY created_at DESC
             LIMIT ? OFFSET ?`,
            [userId, limit, offset]
        );

        const [[{ totalCount }]] = await pool.query(
            'SELECT COUNT(*) as totalCount FROM gpt4o_edit_history WHERE user_id = ?',
            [userId]
        );
        
        const processedHistory = historyRecords.map(record => {
            let parsedInputImages = []; // Default to empty array
            try {
                if (record.input_images_json) {
                    // Directly parse as it should now be a JSON array of objects
                    parsedInputImages = JSON.parse(record.input_images_json);
                    if (!Array.isArray(parsedInputImages)) {
                        // Fallback or error if it's not an array after parsing
                        console.warn(`[History] Parsed input_images_json for record ${record.id} is not an array:`, parsedInputImages);
                        parsedInputImages = []; // Reset to empty or handle as error
                    }
                }
            } catch (e) {
                console.warn(`[History] Failed to parse input_images_json for record ${record.id}: '${record.input_images_json}'. Error: ${e.message}`);
                // Keep parsedInputImages as empty array or decide on other error representation
            }
            return {
                ...record,
                input_images: parsedInputImages, // This will be the array of objects
                cost: record.cost !== null ? parseInt(record.cost, 10) : null,
                // input_images_json is kept as is from DB for debugging or other uses if needed by client
            };
        });

        res.json({
            success: true,
            data: processedHistory, 
            pagination: {
                currentPage: page,
                totalPages: Math.ceil(totalCount / limit),
                totalItems: totalCount,
                itemsPerPage: limit
            }
        });

    } catch (error) {
        console.error(`[GPT4o Edit History] Error fetching history for user ${userId}:`, error);
        res.status(500).json({ success: false, error: '获取历史记录失败' });
    }
});

module.exports = router;
