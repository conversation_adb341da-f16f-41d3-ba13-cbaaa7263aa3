const express = require('express');
const axios = require('axios');
const { authenticateToken } = require('../utils/auth');

const router = express.Router();
const JAAZ_API_BASE = process.env.JAAZ_API_BASE || 'http://localhost:57988';

// 中间件：验证Jaaz服务是否可用
const checkJaazService = async (req, res, next) => {
  try {
    await axios.get(`${JAAZ_API_BASE}/health`, { timeout: 5000 });
    next();
  } catch (error) {
    console.error('Jaaz服务不可用:', error.message);
    return res.status(503).json({ 
      error: 'AI设计服务暂时不可用，请稍后重试',
      code: 'SERVICE_UNAVAILABLE'
    });
  }
};

// 获取用户积分（使用现有积分系统）
router.get('/api/jaaz/user/credits', authenticateToken, async (req, res) => {
  try {
    const pool = require('../db/pool');
    const connection = await pool.getConnection();
    
    try {
      const [rows] = await connection.query(
        'SELECT credits FROM users WHERE id = ?',
        [req.user.id]
      );
      
      if (rows.length === 0) {
        return res.status(404).json({ error: '用户不存在' });
      }
      
      const user = rows[0];
      res.json({
        credits: user.credits || 0,
        remaining: user.credits || 0
      });
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('获取用户积分失败:', error);
    res.status(500).json({ error: '获取积分信息失败' });
  }
});

// 扣除用户积分（集成到现有积分系统）
router.post('/api/jaaz/user/deduct-credits', authenticateToken, async (req, res) => {
  try {
    const { amount = 5, operation = 'jaaz_ai_design_generation' } = req.body;
    const pool = require('../db/pool');
    const connection = await pool.getConnection();
    
    try {
      // 开始事务
      await connection.beginTransaction();
      
      // 检查用户积分
      const [rows] = await connection.query(
        'SELECT credits FROM users WHERE id = ? FOR UPDATE',
        [req.user.id]
      );
      
      if (rows.length === 0) {
        await connection.rollback();
        return res.status(404).json({ error: '用户不存在' });
      }
      
      const user = rows[0];
      const currentCredits = user.credits || 0;
      
      if (currentCredits < amount) {
        await connection.rollback();
        return res.status(400).json({ 
          error: '积分不足，请充值后使用AI设计功能',
          current: currentCredits,
          required: amount,
          message: 'AI设计功能需要付费积分，请联系管理员充值'
        });
      }
      
      // 扣除积分
      await connection.query(
        'UPDATE users SET credits = credits - ? WHERE id = ?',
        [amount, req.user.id]
      );
      
      // 记录消费日志（如果存在credit_logs表）
      try {
        await connection.query(
          'INSERT INTO credit_logs (user_id, operation, amount, balance_after, created_at) VALUES (?, ?, ?, ?, NOW())',
          [req.user.id, operation, -amount, currentCredits - amount]
        );
      } catch (logError) {
        // 如果credit_logs表不存在，忽略错误（向后兼容）
        console.warn('无法记录积分日志，可能credit_logs表不存在:', logError.message);
      }
      
      await connection.commit();
      
      res.json({
        success: true,
        deducted: amount,
        remaining: currentCredits - amount,
        message: `成功扣除${amount}积分，剩余${currentCredits - amount}积分`
      });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('扣除积分失败:', error);
    res.status(500).json({ error: '扣除积分失败' });
  }
});

// 检查功能费用
router.get('/api/jaaz/feature/cost/:featureKey', authenticateToken, async (req, res) => {
  try {
    const { featureKey } = req.params;
    const pool = require('../db/pool');
    const connection = await pool.getConnection();
    
    try {
      // 查询功能成本
      const [rows] = await connection.query(
        'SELECT cost FROM feature_costs WHERE feature_key = ?',
        [featureKey]
      );
      
      if (rows.length === 0) {
        return res.status(404).json({
          error: `未找到功能: ${featureKey}`,
          cost: 5 // 默认成本
        });
      }
      
      const cost = parseInt(rows[0].cost, 10);
      res.json({
        success: true,
        feature: featureKey,
        cost: cost
      });
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error(`获取功能成本错误 (${req.params.featureKey}):`, error);
    res.status(500).json({ 
      error: '获取功能成本失败',
      cost: 5 // 返回默认成本
    });
  }
});

// 代理所有其他Jaaz API请求
router.use('/api/jaaz/*', authenticateToken, checkJaazService, async (req, res) => {
  try {
    const jaazPath = req.path.replace('/api/jaaz', '');
    
    // 构建请求配置
    const requestConfig = {
      method: req.method,
      url: `${JAAZ_API_BASE}${jaazPath}`,
      headers: {
        'Content-Type': req.headers['content-type'] || 'application/json',
        'User-ID': req.user.id.toString(),
        'User-Email': req.user.email || req.user.username,
        'User-Role': req.user.role || 'user',
        'User-Credits': req.user.credits || 0, // 传递用户积分信息
      },
      timeout: 300000, // 5分钟超时，AI生成可能需要较长时间
    };
    
    // 添加请求体（如果有）
    if (req.body && Object.keys(req.body).length > 0) {
      requestConfig.data = req.body;
    }
    
    // 添加查询参数
    if (req.query && Object.keys(req.query).length > 0) {
      requestConfig.params = req.query;
    }
    
    console.log(`代理请求到Jaaz: ${requestConfig.method} ${requestConfig.url}`);
    
    const response = await axios(requestConfig);
    
    // 转发响应头
    if (response.headers['content-type']) {
      res.set('Content-Type', response.headers['content-type']);
    }
    
    res.status(response.status).json(response.data);
    
  } catch (error) {
    console.error('Jaaz代理请求失败:', error.message);
    
    if (error.response) {
      // Jaaz服务返回的错误
      res.status(error.response.status).json(error.response.data);
    } else if (error.code === 'ECONNREFUSED') {
      res.status(503).json({ 
        error: 'AI设计服务连接失败，请联系管理员',
        code: 'CONNECTION_REFUSED'
      });
    } else if (error.code === 'ETIMEDOUT') {
      res.status(504).json({ 
        error: 'AI设计服务响应超时，请稍后重试',
        code: 'TIMEOUT'
      });
    } else {
      res.status(500).json({ 
        error: '设计服务暂时不可用',
        code: 'PROXY_ERROR'
      });
    }
  }
});

// WebSocket代理（用于实时AI聊天）
router.ws = function(app) {
  const WebSocket = require('ws');
  
  app.ws('/api/jaaz/ws/*', (ws, req) => {
    const jaazPath = req.path.replace('/api/jaaz', '');
    const jaazWsUrl = `ws://localhost:57988${jaazPath}`;
    
    console.log(`WebSocket代理到Jaaz: ${jaazWsUrl}`);
    
    const jaazWs = new WebSocket(jaazWsUrl);
    
    // 转发消息
    ws.on('message', (message) => {
      if (jaazWs.readyState === WebSocket.OPEN) {
        jaazWs.send(message);
      }
    });
    
    jaazWs.on('message', (message) => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(message);
      }
    });
    
    // 错误处理
    ws.on('close', () => jaazWs.close());
    jaazWs.on('close', () => ws.close());
    
    ws.on('error', (error) => {
      console.error('WebSocket客户端错误:', error);
      jaazWs.close();
    });
    
    jaazWs.on('error', (error) => {
      console.error('WebSocket到Jaaz错误:', error);
      ws.close();
    });
  });
};

module.exports = router; 