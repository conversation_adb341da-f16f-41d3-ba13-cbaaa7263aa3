const express = require('express');
const { authenticateToken } = require('../utils/auth');
const pool = require('../db/pool');

const router = express.Router();

// GET /api/vectorize/history (Fetch history for the logged-in user)
router.get('/history', authenticateToken, async (req, res) => {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 8; // Consistent limit
    const offset = (page - 1) * limit;

    let connection;
    try {
        connection = await pool.getConnection();

        // Query total count
        const countQuery = 'SELECT COUNT(*) as totalCount FROM vectorize_image_history WHERE user_id = ?'; // Query correct table
        const [[{ totalCount }]] = await connection.query(countQuery, [userId]);

        // Query paginated history data
        const historyQuery = `
            SELECT id, user_id, original_image_filename, result_svg_url, created_at 
            FROM vectorize_image_history 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?`; // Query correct table
        const [history] = await connection.query(historyQuery, [userId, limit, offset]);

        connection.release();

        const totalPages = Math.ceil(totalCount / limit);

        res.json({
            history,
            pagination: {
                totalCount,
                currentPage: page,
                totalPages,
                limit
            }
        });

    } catch (error) {
        if (connection) connection.release();
        console.error(`[Vectorize History] Error fetching history for user ${userId}:`, error);
        res.status(500).json({ error: '获取历史记录失败', details: error.message });
    }
});

module.exports = router; 