// server/routes/ai-chat-history.js
const express = require('express');
const router = express.Router();
const pool = require('../db/pool'); // 假设您的 db/pool.js 路径正确
const { authenticateToken } = require('../utils/auth'); // 假设您的 utils/auth.js 路径正确

// --- Middleware to check ownership or admin ---
async function checkConversationOwnership(req, res, next) {
  try {
    const conversationId = parseInt(req.params.conversationId);
    const userId = req.user.id; // From authenticateToken
    const userRole = req.user.role;

    if (isNaN(conversationId)) {
      return res.status(400).json({ error: '无效的对话 ID' });
    }

    // 如果是管理员，则允许访问任何对话
    if (userRole === 'admin') {
      // 检查对话是否存在，即使是管理员
      const connectionCheck = await pool.getConnection();
      try {
        const [convExists] = await connectionCheck.query(
          'SELECT id FROM ai_conversations WHERE id = ?',
          [conversationId]
        );
        if (convExists.length === 0) {
          connectionCheck.release();
          return res.status(404).json({ error: '对话未找到 (管理员检查)' });
        }
      } finally {
        connectionCheck.release();
      }
      return next();
    }

    // 普通用户，检查所有权
    const connection = await pool.getConnection();
    try {
      const [rows] = await connection.query(
        'SELECT user_id FROM ai_conversations WHERE id = ?',
        [conversationId]
      );
      if (rows.length === 0) {
        connection.release();
        return res.status(404).json({ error: '对话未找到' });
      }
      if (rows[0].user_id !== userId) {
        connection.release();
        return res.status(403).json({ error: '您没有权限访问此对话' });
      }
    } finally {
      connection.release();
    }
    next();
  } catch (error) {
    console.error('检查对话所有权错误:', error);
    res.status(500).json({ error: '服务器内部错误', details: error.message });
  }
}

// GET /api/ai-chat/conversations - 获取当前用户的所有会话列表
router.get('/conversations', authenticateToken, async (req, res) => {
    const userId = req.user.id;
    // TODO: 后续可以考虑添加分页参数 const { page = 1, limit = 20 } = req.query;
    // const offset = (page - 1) * limit;

    try {
        const connection = await pool.getConnection();
        try {
            const [conversations] = await connection.query(
                `SELECT id, user_id, title, model_used, created_at, updated_at, last_active_at 
                 FROM ai_conversations 
                 WHERE user_id = ? 
                 ORDER BY last_active_at DESC`,
                // `LIMIT ? OFFSET ?`, // 分页时使用
                [userId /*, parseInt(limit), parseInt(offset) */]
            );
            connection.release();
            res.json(conversations);
        } catch (dbError) {
            connection.release();
            throw dbError; // 让外层 try-catch 处理
        }
    } catch (error) {
        console.error('获取会话列表错误:', error);
        res.status(500).json({ error: '获取会话列表失败', details: error.message });
    }
});

// POST /api/ai-chat/conversations - 创建新会话
router.post('/conversations', authenticateToken, async (req, res) => {
    const userId = req.user.id;
    const { title, model_used } = req.body; // 从请求体获取可选参数

    // 准备插入的数据
    const newConversationData = {
        user_id: userId,
        title: title || '新对话', // 如果没有提供标题，则使用默认值
        model_used: model_used || null, // 如果没有提供模型，则为 null
        last_active_at: new Date() // 设置最后活跃时间为当前
    };

    try {
        const connection = await pool.getConnection();
        try {
            const [result] = await connection.query(
                'INSERT INTO ai_conversations SET ?',
                newConversationData
            );
            const newConversationId = result.insertId;

            // 获取刚插入的完整会话信息
            const [newConversation] = await connection.query(
                'SELECT id, user_id, title, model_used, created_at, updated_at, last_active_at FROM ai_conversations WHERE id = ?',
                [newConversationId]
            );
            connection.release();

            if (newConversation.length > 0) {
                res.status(201).json(newConversation[0]);
            } else {
                // 理论上不应该发生，因为我们刚插入
                throw new Error('创建会话后未能检索到该会话');
            }
        } catch (dbError) {
            connection.release();
            throw dbError;
        }
    } catch (error) {
        console.error('创建新会话错误:', error);
        res.status(500).json({ error: '创建新会话失败', details: error.message });
    }
});

// GET /api/ai-chat/conversations/:conversationId/messages - 获取指定会话的所有消息
router.get('/conversations/:conversationId/messages', authenticateToken, checkConversationOwnership, async (req, res) => {
    const conversationId = parseInt(req.params.conversationId); // ID 已经由中间件验证过是数字且存在

    try {
        const connection = await pool.getConnection();
        try {
            const [messages] = await connection.query(
                `SELECT id, conversation_id, role, content, reasoning, timestamp, token_count 
                 FROM ai_conversation_messages 
                 WHERE conversation_id = ? 
                 ORDER BY timestamp ASC`,
                [conversationId]
            );
            connection.release();
            res.json(messages);
        } catch (dbError) {
            connection.release();
            throw dbError;
        }
    } catch (error) {
        console.error(`获取对话 ${conversationId} 的消息错误:`, error);
        res.status(500).json({ error: '获取消息失败', details: error.message });
    }
});

// POST /api/ai-chat/conversations/:conversationId/messages - 向指定会话添加新消息
router.post('/conversations/:conversationId/messages', authenticateToken, checkConversationOwnership, async (req, res) => {
    const conversationId = parseInt(req.params.conversationId);
    const { role, content, reasoning, token_count } = req.body;

    // 基本验证
    if (!role || !['user', 'assistant'].includes(role)) {
        return res.status(400).json({ error: "消息 'role' 必须是 'user' 或 'assistant'" });
    }
    if (!content) { // content 必须提供，并且应该是有效的 JSON 结构
        return res.status(400).json({ error: "消息 'content' 不能为空" });
    }
    // 可以添加更严格的 content JSON 结构验证

    const newMessageData = {
        conversation_id: conversationId,
        role: role,
        // 手动将 content 对象/数组转换为 JSON 字符串
        content: JSON.stringify(content), 
        reasoning: reasoning || null,
        token_count: token_count ? parseInt(token_count) : null,
        timestamp: new Date() // 设置消息时间戳为当前
    };

    let connection;
    try {
        connection = await pool.getConnection();
        await connection.beginTransaction(); // 开始事务

        // 1. 插入新消息
        const [result] = await connection.query(
            'INSERT INTO ai_conversation_messages SET ?',
            newMessageData // 现在 newMessageData.content 是一个 JSON 字符串了
        );
        const newMessageId = result.insertId;

        // 2. 更新会话的 last_active_at (和 updated_at，如果表定义如此)
        await connection.query(
            'UPDATE ai_conversations SET last_active_at = ? WHERE id = ?',
            [new Date(), conversationId]
        );
        
        // 3. (可选) 如果是用户的第一条消息，尝试更新会话标题
        // 这里可以根据 content 的类型来提取文本
        let newTitle = null;
        if (role === 'user') {
            const [[{ message_count }]] = await connection.query(
                'SELECT COUNT(*) as message_count FROM ai_conversation_messages WHERE conversation_id = ? AND role = ?',
                [conversationId, 'user']
            );
            if (parseInt(message_count) === 1) { // 这是用户的第一条消息
                if (Array.isArray(content)) {
                    const textPart = content.find(part => part.type === 'text');
                    if (textPart && textPart.text) {
                        newTitle = textPart.text.substring(0, 50) + (textPart.text.length > 50 ? '...' : '');
                    }
                } else if (typeof content === 'object' && content.type === 'text' && content.text) {
                    newTitle = content.text.substring(0, 50) + (content.text.length > 50 ? '...' : '');
                }
                if (newTitle) {
                    await connection.query(
                        'UPDATE ai_conversations SET title = ? WHERE id = ? AND title = ?', // 只有当标题还是默认值时才更新
                        [newTitle, conversationId, '新对话']
                    );
                }
            }
        }


        // 获取刚插入的完整消息信息
        const [newMessage] = await connection.query(
            'SELECT id, conversation_id, role, content, reasoning, timestamp, token_count FROM ai_conversation_messages WHERE id = ?',
            [newMessageId]
        );

        await connection.commit(); // 提交事务
        connection.release();

        if (newMessage.length > 0) {
            // 注意：从数据库读出的 content 字段应该已经是 JSON 对象/数组了（如果 mysql2 配置正确）
            // 或者可能需要在这里 JSON.parse(newMessage[0].content)
            // 取决于您的 mysql2 配置和数据库版本
            // 暂时先直接返回，如果前端加载历史时解析失败再调整
            res.status(201).json(newMessage[0]);
        } else {
            throw new Error('添加消息后未能检索到该消息');
        }
    } catch (error) {
        if (connection) {
            try {
                await connection.rollback(); // 如果出错，回滚事务
            } catch (rollbackError) {
                console.error('事务回滚失败:', rollbackError);
            }
            connection.release();
        }
        console.error(`向对话 ${conversationId} 添加消息错误:`, error);
        // 在响应中也包含更具体的数据库错误信息（仅开发环境）
        const details = process.env.NODE_ENV === 'development' ? error.sqlMessage || error.message : '内部服务器错误';
        res.status(500).json({ error: '添加消息失败', details: details });
    }
});

// PUT /api/ai-chat/conversations/:conversationId - 更新会话元数据 (例如标题)
router.put('/conversations/:conversationId', authenticateToken, checkConversationOwnership, async (req, res) => {
    const conversationId = parseInt(req.params.conversationId);
    const { title } = req.body;

    if (typeof title !== 'string' || title.trim() === '') {
        return res.status(400).json({ error: "会话标题 'title' 不能为空字符串" });
    }
    
    // 也可以在这里限制标题的最大长度，例如：
    // if (title.length > 255) {
    //     return res.status(400).json({ error: "会话标题过长，最大长度为255字符" });
    // }

    try {
        const connection = await pool.getConnection();
        try {
            const [result] = await connection.query(
                'UPDATE ai_conversations SET title = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [title.trim(), conversationId]
            );

            if (result.affectedRows === 0) {
                // 虽然 checkConversationOwnership 应该已经捕获了对话不存在的情况，但以防万一
                connection.release();
                return res.status(404).json({ error: '对话未找到或未作更改' });
            }

            // 获取更新后的完整会话信息
            const [updatedConversation] = await connection.query(
                'SELECT id, user_id, title, model_used, created_at, updated_at, last_active_at FROM ai_conversations WHERE id = ?',
                [conversationId]
            );
            connection.release();
            
            if (updatedConversation.length > 0) {
                res.json(updatedConversation[0]);
            } else {
                // 理论上不应该发生
                throw new Error('更新会话后未能检索到该会话');
            }
        } catch (dbError) {
            connection.release();
            throw dbError;
        }
    } catch (error) {
        console.error(`更新对话 ${conversationId} 错误:`, error);
        res.status(500).json({ error: '更新会话失败', details: error.message });
    }
});

// DELETE /api/ai-chat/conversations/:conversationId - 删除整个会话及其所有消息
router.delete('/conversations/:conversationId', authenticateToken, checkConversationOwnership, async (req, res) => {
    const conversationId = parseInt(req.params.conversationId); // ID 已经由中间件验证过是数字且对话存在并属于用户

    let connection;
    try {
        connection = await pool.getConnection();
        // 因为外键设置了 ON DELETE CASCADE，所以只需要删除 ai_conversations 表中的记录即可
        // 相关的 ai_conversation_messages 会自动被删除
        // 我们仍然在一个事务中执行，以防未来可能需要在此处添加其他相关删除操作
        await connection.beginTransaction();

        const [result] = await connection.query(
            'DELETE FROM ai_conversations WHERE id = ?',
            [conversationId]
        );

        await connection.commit();
        connection.release();

        if (result.affectedRows > 0) {
            res.status(200).json({ success: true, message: '对话已成功删除' });
            // 或者使用 204 No Content，并且不返回任何 body
            // res.status(204).send(); 
        } else {
            // 理论上 checkConversationOwnership 已经确保对话存在，但以防万一
            res.status(404).json({ error: '对话未找到或删除失败' });
        }
    } catch (error) {
        if (connection) {
            try {
                await connection.rollback();
            } catch (rollbackError) {
                 console.error('删除对话时事务回滚失败:', rollbackError);
            }
            connection.release();
        }
        console.error(`删除对话 ${conversationId} 错误:`, error);
        res.status(500).json({ error: '删除会话失败', details: error.message });
    }
});

// DELETE /api/ai-chat/conversations - 清空当前用户的所有会话
router.delete('/conversations', authenticateToken, async (req, res) => {
    const userId = req.user.id;

    let connection;
    try {
        connection = await pool.getConnection();
        await connection.beginTransaction();

        // 由于 ai_conversation_messages 表与 ai_conversations 表之间有外键约束
        // 并且在删除单个对话时依赖 ON DELETE CASCADE，这里也假设它会级联删除消息
        const [result] = await connection.query(
            'DELETE FROM ai_conversations WHERE user_id = ?',
            [userId]
        );

        await connection.commit();
        connection.release();

        // result.affectedRows 会给出删除了多少条对话记录
        res.status(200).json({ success: true, message: `成功清空 ${result.affectedRows} 条会话记录` });
        // 或者使用 204 No Content，并且不返回任何 body
        // res.status(204).send();

    } catch (error) {
        if (connection) {
            try {
                await connection.rollback();
            } catch (rollbackError) {
                 console.error('清空所有会话时事务回滚失败:', rollbackError);
            }
            connection.release();
        }
        console.error(`用户 ${userId} 清空所有会话错误:`, error);
        res.status(500).json({ error: '清空所有会话失败', details: error.message });
    }
});

module.exports = router;