const express = require('express');
const { authenticateToken } = require('../utils/auth');
const pool = require('../db/pool');
const deductCreditsRouter = require('./deductCredits');
// 假设每日免费积分为 20，后续可移至配置
// const DAILY_FREE_CREDITS_AMOUNT = 20; 

const router = express.Router();

// GET /api/credits/balance - 获取用户积分信息（累积积分 + 每日免费积分）
router.get('/balance', authenticateToken, async (req, res) => {
    const userId = req.user.id;
    console.log(`[Credits Balance] User ${userId} requested balance.`); // 添加日志

    if (!userId) {
        console.warn('[Credits Balance] Unauthorized access attempt.'); // 添加日志
        return res.status(401).json({ error: '用户未认证' });
    }

    let connection;
    try {
        connection = await pool.getConnection(); // 从连接池获取连接
        console.log(`[Credits Balance] DB Connection acquired for user ${userId}.`); // 添加日志

        // 查询用户积分和每日积分使用情况及激活状态
        // 注意：我们复用 daily_generations_used 和 last_generation_date 字段
        const [users] = await connection.query(
            'SELECT credits, daily_generations_used, last_generation_date, is_activated FROM users WHERE id = ?',
            [userId]
        );
        console.log(`[Credits Balance] User data query executed for user ${userId}.`); // 添加日志

        if (users.length === 0) {
            console.warn(`[Credits Balance] User ${userId} not found in database.`); // 添加日志
            connection.release(); // 释放连接
            return res.status(404).json({ error: '找不到用户' });
        }

        const user = users[0];
        const isActivated = user.is_activated === 1;
        const today = new Date().toISOString().slice(0, 10);
        // --- Fetch DAILY_FREE_CREDIT_ALLOWANCE --- START ---
        const [[dailyAllowanceData]] = await connection.query(
            'SELECT cost FROM feature_costs WHERE feature_key = ?',
            ['DAILY_FREE_CREDIT_ALLOWANCE']
        );

        if (!dailyAllowanceData || dailyAllowanceData.cost === undefined || dailyAllowanceData.cost === null) {
            console.warn(`[Credits Balance] User ${userId} - Daily free credit allowance not found or invalid in feature_costs.`);
            // Return error or a default value if critical, for now, we'll make it obvious if missing
            // return res.status(500).json({ error: '每日免费额度配置缺失' }); 
            // Or, use a fallback (though dynamic is preferred)
            // const DAILY_FREE_CREDITS_AMOUNT = 0; // Fallback if not found
        }
        // Use a default of 0 if not found, or handle error as appropriate for your app logic
        const DAILY_FREE_CREDITS_AMOUNT = dailyAllowanceData ? parseInt(dailyAllowanceData.cost, 10) : 0;
        if (isNaN(DAILY_FREE_CREDITS_AMOUNT) || DAILY_FREE_CREDITS_AMOUNT < 0) {
            console.warn(`[Credits Balance] User ${userId} - Invalid daily free credit allowance (${DAILY_FREE_CREDITS_AMOUNT}) retrieved.`);
            // DAILY_FREE_CREDITS_AMOUNT = 0; // Fallback for invalid value
        }
        console.log(`[Credits Balance] User ${userId} - Daily Free Credit Allowance from DB: ${DAILY_FREE_CREDITS_AMOUNT}`);
        // --- Fetch DAILY_FREE_CREDIT_ALLOWANCE --- END ---

        let remainingDailyFreeCredits = 0;
        let dailyCreditsUsedToday = user.daily_generations_used || 0; // 将 null 视为 0
        const lastResetDate = user.last_generation_date ? user.last_generation_date.toISOString().slice(0, 10) : null;

        console.log(`[Credits Balance] User ${userId} - Today: ${today}, Last Reset: ${lastResetDate}, Used Today Raw: ${user.daily_generations_used}`); // 添加日志

        // 计算剩余每日免费积分 (考虑每日重置)
        if (lastResetDate !== today) {
            // 如果今天未重置过 (包括从未生成过的情况)，则剩余次数为最大次数
            console.log(`[Credits Balance] User ${userId} - Daily credits resetting for today.`); // 添加日志
            remainingDailyFreeCredits = DAILY_FREE_CREDITS_AMOUNT;
            // 注意：这里的重置逻辑只用于 *计算* 剩余量。
            // 实际数据库的重置应该在 *消耗* 积分的操作之前进行。
            dailyCreditsUsedToday = 0; // 在计算中视为0，但不修改数据库
        } else {
            // 如果今天已重置过，则用最大次数减去已用次数
            remainingDailyFreeCredits = Math.max(0, DAILY_FREE_CREDITS_AMOUNT - dailyCreditsUsedToday);
        }
        console.log(`[Credits Balance] User ${userId} - Remaining Daily Free Credits calculated: ${remainingDailyFreeCredits}`); // 添加日志

        // 未激活用户在返回中设置每日免费积分为0
        const response = {
            success: true,
            cumulativeCredits: user.credits,           // 用户的累积积分
            is_activated: isActivated,                 // 账户激活状态
            remainingDailyFreeCredits: isActivated ? remainingDailyFreeCredits : 0, // 未激活用户显示0
            dailyFreeCreditsAmount: isActivated ? DAILY_FREE_CREDITS_AMOUNT : 0   // 未激活用户显示0
        };

        // 添加激活提醒消息
        if (!isActivated) {
            response.activation_message = '您的账户尚未激活，请查收邮箱并点击激活链接，激活后才能获得每日免费积分';
        }

        res.json(response);

    } catch (error) {
        console.error(`[Credits Balance] Error querying balance for user ${userId}:`, error); // 添加日志
        res.status(500).json({ error: '获取积分信息失败' });
    } finally {
        if (connection) {
             connection.release(); // 确保释放连接
             console.log(`[Credits Balance] DB Connection released for user ${userId}.`); // 添加日志
        }
    }
});

// 挂载扣除积分路由
router.use('/', deductCreditsRouter);

// 未来可以添加充值相关的路由
// router.post('/purchase', authenticateToken, async (req, res) => { ... });

module.exports = router; 