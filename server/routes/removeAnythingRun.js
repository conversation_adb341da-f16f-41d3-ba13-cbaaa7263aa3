const express = require('express');
const { authenticateToken } = require('../utils/auth');
const pool = require('../db/pool');
const fetch = require('node-fetch');
const multer = require('multer');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const os = require('os');
const axios = require('axios'); // 新增：用于ComfyUI API调用
const { checkAndDeductCredits } = require('../middleware/checkCredits');
const router = express.Router();

// 你可以把 RunningHub 的 key 配在 .env 文件里
const RUNNINGHUB_API_KEY = process.env.RUNNINGHUB_API_KEY;
const WEBAPP_ID = '1923389726278283265';

// ComfyUI 配置
const COMFYUI_BASE_URL = 'https://u63642-a2b4-fccb0b0d.westx.seetacloud.com:8443'; // 与image-to-video.js保持一致

// 用内存存储，图片不落盘
const upload = multer({ storage: multer.memoryStorage() });

/**
 * POST /api/remove-anything/run
 * 前端传：mainImage, maskImage, prompt, expand_value, strength_value, output_mp_value
 */
router.post('/run', 
    authenticateToken, 
    checkAndDeductCredits('remove_anything'),
    upload.fields([
    { name: 'mainImage', maxCount: 1 },
    { name: 'maskImage', maxCount: 1 }
]), async (req, res) => {
    try {
        console.log('收到 /api/remove-anything/run 请求');
        console.log('Request Body:', req.body);
        console.log('Request Files:', req.files);

        const userId = req.user.id;
        const mainImage = req.files?.['mainImage']?.[0];
        const maskImage = req.files?.['maskImage']?.[0];
        const { prompt, expand_value, strength_value, output_mp_value } = req.body;
        // let originalUserPrompt = prompt; // 保存原始用户提示

        if (!mainImage || !maskImage || !prompt) {
            console.error('文件或参数缺失:', { mainImage: !!mainImage, maskImage: !!maskImage, prompt: !!prompt });
            return res.status(400).json({ error: '缺少必要参数或文件上传失败' });
        }

        // 2. 上传图片到 RunningHub
        async function uploadToRunningHub(file) {
            console.log('上传文件到 RunningHub:', file.originalname);
            
            // 创建临时文件
            const tempDir = os.tmpdir();
            const tempFilePath = path.join(tempDir, file.originalname);
            
            try {
                // 写入临时文件
                fs.writeFileSync(tempFilePath, file.buffer);
                
                const formData = new FormData();
                // 添加文件流
                formData.append('file', fs.createReadStream(tempFilePath));
                formData.append('apiKey', RUNNINGHUB_API_KEY);
            
                const resp = await fetch('https://www.runninghub.cn/task/openapi/upload', {
                    method: 'POST',
                    body: formData
                });
                
                // 删除临时文件
                fs.unlinkSync(tempFilePath);
                
                const data = await resp.json();
                console.log('RunningHub 上传接口返回:', data);
                if (data.code === 0 && data.data && data.data.fileName) {
                    return data.data.fileName;
                }
                throw new Error('图片上传到 RunningHub 失败: ' + (data.msg || '未知错误'));
            } catch (fetchError) {
                // 确保临时文件被删除
                if (fs.existsSync(tempFilePath)) {
                    fs.unlinkSync(tempFilePath);
                }
                console.error('调用 RunningHub 上传接口失败:', fetchError);
                throw new Error('调用 RunningHub 上传接口失败');
            }
        }

        // 新增：上传图片到ComfyUI
        async function uploadToComfyUI(file, subfolder = 'remove-anything/inputs') {
            console.log('上传文件到 ComfyUI:', file.originalname);
            
            try {
                const formData = new FormData();
                formData.append('image', file.buffer, file.originalname);
                formData.append('subfolder', subfolder);
                formData.append('type', 'input');
                formData.append('overwrite', 'false');
                
                const response = await axios.post(`${COMFYUI_BASE_URL}/upload/image`, formData, {
                    headers: formData.getHeaders()
                });
                
                if (response.data && response.data.name) {
                    const { name, subfolder, type } = response.data;
                    const effectiveType = type || 'input';
                    const imageUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(name)}&subfolder=${encodeURIComponent(subfolder)}&type=${effectiveType}`;
                    console.log(`图片上传到 ComfyUI 成功: ${imageUrl}`);
                    return { name, subfolder, type: effectiveType, url: imageUrl };
                } else {
                    throw new Error('ComfyUI上传未返回预期数据');
                }
            } catch (error) {
                console.error('上传图片到 ComfyUI 失败:', error.message);
                throw new Error('上传图片到 ComfyUI 失败');
            }
        }

        // 上传原始图片和蒙版图片到ComfyUI
        let mainImageComfyUI = null;
        let maskImageComfyUI = null;
        
        try {
            mainImageComfyUI = await uploadToComfyUI(mainImage);
            maskImageComfyUI = await uploadToComfyUI(maskImage);
            console.log('图片已成功上传到ComfyUI:', {
                mainImage: mainImageComfyUI.url,
                maskImage: maskImageComfyUI.url
            });
        } catch (error) {
            console.error('上传图片到ComfyUI失败:', error);
            return res.status(500).json({ error: '上传图片到处理服务器失败，请重试' });
        }

        const mainImageFileName = await uploadToRunningHub(mainImage);
        const maskImageFileName = await uploadToRunningHub(maskImage);

        // 3. 提交任务
        const nodeInfoList = [
            { nodeId: '5', fieldName: 'image', fieldValue: mainImageFileName },
            { nodeId: '252', fieldName: 'image', fieldValue: maskImageFileName },
            { nodeId: '238', fieldName: 'text', fieldValue: prompt },
            { nodeId: '260', fieldName: 'value', fieldValue: expand_value },
            { nodeId: '272', fieldName: 'value', fieldValue: strength_value },
            { nodeId: '220', fieldName: 'value', fieldValue: output_mp_value }
        ];
        const body = { webappId: WEBAPP_ID, apiKey: RUNNINGHUB_API_KEY, nodeInfoList };

        const submitResp = await fetch('https://www.runninghub.cn/task/openapi/ai-app/run', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(body)
        });
        const submitData = await submitResp.json();
        if (submitData.code !== 0) throw new Error(submitData.msg || 'AI任务提交失败');
        const taskId = submitData.data.taskId;

        // 4. 轮询任务状态
        let status = '';
        let attempts = 0; // 可选：添加一个尝试次数计数器，用于日志或非常大的上限
        console.log(`[Task ID: ${taskId}] 开始轮询任务状态...`);
        while (true) { // 改为无限轮询
            attempts++;
            await new Promise(r => setTimeout(r, 5000)); // 保持3秒轮询间隔
            const statusResp = await fetch('https://www.runninghub.cn/task/openapi/status', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ apiKey: RUNNINGHUB_API_KEY, taskId })
            });
            const statusData = await statusResp.json();
            // console.log(`[Task ID: ${taskId}] 轮询尝试 #${attempts}, 状态数据:`, statusData); // 详细日志，可按需开启

            if (statusData.code === 0 && statusData.data) {
                const taskCurrentStatus = typeof statusData.data === 'object' ? statusData.data.taskStatus : statusData.data;
                console.log(`[Task ID: ${taskId}] 轮询尝试 #${attempts}, 获取状态: ${taskCurrentStatus}`);

                if (taskCurrentStatus === 'SUCCESS') {
                    status = 'SUCCESS';
                    console.log(`[Task ID: ${taskId}] 任务成功！`);
                    break;
                } else if (taskCurrentStatus === 'FAILED') {
                    status = 'FAILED';
                    console.warn(`[Task ID: ${taskId}] 任务失败。`);
                    break;
                }
                // 如果是其他状态 (如 RUNNING, PENDING)，则继续轮询
            } else {
                // API 返回非预期格式或错误码，可以考虑也记录并继续轮询或设置一个错误次数上限后跳出
                console.warn(`[Task ID: ${taskId}] 轮询尝试 #${attempts}, 获取状态API异常:`, statusData);
                // 简单的处理：如果API连续多次返回错误，也可以选择跳出，防止死循环
                // if (attempts > 20 && statusData.code !== 0) { // 例如：连续20次API都返回错误，则认为轮询本身出了问题
                //     console.error(`[Task ID: ${taskId}] 状态API连续返回错误，停止轮询。`);
                //     status = 'POLLING_ERROR'; // 自定义一个状态
                //     break;
                // }
            }
            // 可选：添加一个非常大的尝试上限，以防万一
            // if (attempts > 7200) { // 比如 7200次 * 3秒 = 6小时
            //     console.error(`[Task ID: ${taskId}] 达到最大轮询次数上限，停止轮询。`);
            //     status = 'TIMEOUT_MAX_ATTEMPTS';
            //     break;
            // }
        }

        if (status !== 'SUCCESS') {
            console.error(`[Task ID: ${taskId}] 最终任务状态: ${status}。无法获取结果。`);
            return res.status(500).json({ error: `AI任务最终状态为 ${status}，无法获取结果` });
        }

        // 5. 获取结果图片
        const outputResp = await fetch('https://www.runninghub.cn/task/openapi/outputs', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ apiKey: RUNNINGHUB_API_KEY, taskId })
        });
        const outputData = await outputResp.json();
        if (!(outputData.code === 0 && outputData.data && outputData.data.length > 0 && outputData.data[0].fileUrl)) {
            return res.status(500).json({ error: '获取结果图片失败' });
        }
        const runningHubResultUrl = outputData.data[0].fileUrl;
        
        // 新增：将RunningHub的结果图片下载到ComfyUI
        let finalResultUrl = runningHubResultUrl; // 默认使用RunningHub的URL
        
        try {
            console.log(`从RunningHub下载结果图片: ${runningHubResultUrl}`);
            const comfyPayload = {
                url: runningHubResultUrl,
                filename_prefix: `remove_anything_${userId}_${taskId.substring(0, 8)}`,
                subdir: 'remove-anything/outputs'
            };
            
            const comfyResponse = await axios.post(`${COMFYUI_BASE_URL}/yzy/download-url`, comfyPayload, {
                headers: { 'Content-Type': 'application/json' }
            });
            
            if (comfyResponse.data && comfyResponse.data.success) {
                const { filename, subfolder, type } = comfyResponse.data;
                finalResultUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(filename)}&subfolder=${encodeURIComponent(subfolder)}&type=${type}`;
                console.log(`结果图片成功转移到ComfyUI: ${finalResultUrl}`);
            } else {
                console.error(`ComfyUI转移失败:`, comfyResponse.data);
                // 保持使用RunningHub的URL作为备用
            }
        } catch (comfyError) {
            console.error(`ComfyUI转移错误:`, comfyError.message);
            // 保持使用RunningHub的URL作为备用
        }

        // 6. 存历史
        let connection;
        try {
            connection = await pool.getConnection();
            const insertQuery = `
                INSERT INTO remove_anything_history 
                (user_id, original_image_filename, mask_image_filename, result_image_url, prompt, expand_value, strength_value, output_mp_value, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            `;
            await connection.query(insertQuery, [
                userId,
                mainImage.originalname,
                maskImage.originalname,
                finalResultUrl, 
                prompt, // 历史记录中仍然保存用户输入的原始提示词
                expand_value || null,
                strength_value || null,
                output_mp_value || null
            ]);

            // +++ 获取最新的用户积分信息 +++
            let updatedBalance = null;
            try { // 将积分获取逻辑也放入 try...catch 以免影响主流程
                // 查询用户积分和每日积分使用情况
                const [users] = await connection.query(
                    'SELECT credits, daily_generations_used, last_generation_date FROM users WHERE id = ?',
                    [userId]
                );

                if (users.length > 0) {
                    const user = users[0];
                    const today = new Date().toISOString().slice(0, 10);
                    
                    const [[dailyAllowanceData]] = await connection.query(
                        'SELECT cost FROM feature_costs WHERE feature_key = ?',
                        ['DAILY_FREE_CREDIT_ALLOWANCE']
                    );
                    
                    const DAILY_FREE_CREDITS_AMOUNT = dailyAllowanceData ? parseInt(dailyAllowanceData.cost, 10) : 0;
                    
                    let remainingDailyFreeCredits = 0;
                    let dailyCreditsUsedToday = user.daily_generations_used || 0;
                    const lastResetDate = user.last_generation_date ? user.last_generation_date.toISOString().slice(0, 10) : null;

                    if (lastResetDate !== today) {
                        remainingDailyFreeCredits = DAILY_FREE_CREDITS_AMOUNT;
                    } else {
                        remainingDailyFreeCredits = Math.max(0, DAILY_FREE_CREDITS_AMOUNT - dailyCreditsUsedToday);
                    }
                    
                    updatedBalance = {
                        cumulativeCredits: user.credits,
                        remainingDailyFreeCredits: remainingDailyFreeCredits,
                        dailyFreeCreditsAmount: DAILY_FREE_CREDITS_AMOUNT
                    };
                    console.log(`[Remove Anything Run] User ${userId} - Fetched updated balance successfully.`);
                } else {
                     console.warn(`[Remove Anything Run] User ${userId} - Could not find user to fetch updated balance.`);
                }
            } catch (balanceError) {
                console.error(`[Remove Anything Run] User ${userId} - Error fetching updated balance:`, balanceError);
                // 不阻断，updatedBalance 将为 null
        }
            // --- 结束获取最新积分 ---

        // 7. 返回结果
        res.json({
            result_image_url: finalResultUrl,
            original_image_url: mainImageComfyUI.url,
            mask_image_url: maskImageComfyUI.url,
                prompt: prompt, // 返回给前端的也是原始提示词
            expand_value,
            strength_value,
                output_mp_value,
                updatedBalance: updatedBalance // +++ 返回最新积分 +++
            });

        } catch (dbErr) { // Catch for DB history insert or balance fetch
            console.error('[Remove Anything Run] DB operation error (history insert or balance fetch):', dbErr);
            // 即使历史记录或余额获取失败，如果主要功能已完成，仍然尝试返回结果
            // 但如果连接存在问题，可能res已经被发送或无法发送
            if (!res.headersSent) {
                 res.json({ // 返回一个不包含updatedBalance的成功结果（或根据错误类型决定是否报错）
                    result_image_url: finalResultUrl,
                    original_image_url: mainImageComfyUI.url,
                    mask_image_url: maskImageComfyUI.url,
                    prompt: prompt,
                    expand_value,
                    strength_value,
                    output_mp_value,
                    warning: 'Result generated, but failed to record history or fetch updated balance.'
                });
            }
        } finally {
            if (connection) connection.release();
        }

    } catch (err) {
        console.error('处理 /api/remove-anything/run 错误:', err);
        // 确保在顶层catch中如果连接已打开也释放
        // （但在这个结构中，connection主要在DB操作的try块内定义和使用）
        if (!res.headersSent) {
        res.status(500).json({ error: err.message || '服务端错误' });
        }
    }
});

module.exports = router;
