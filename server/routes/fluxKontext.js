const express = require('express');
const router = express.Router();
const axios = require('axios');
const multer = require('multer');
const upload = multer({ limits: { fileSize: 50 * 1024 * 1024 } }); // 50MB上限
const fs = require('fs');
const path = require('path');

// Replicate API密钥
const REPLICATE_API_KEY = '****************************************';

// Flux Kontext模型ID
const FLUX_KONTEXT_PRO_MODEL = 'black-forest-labs/flux-kontext-pro';
const FLUX_KONTEXT_MAX_MODEL = 'black-forest-labs/flux-kontext-max';

// 处理Flux Kontext图像编辑请求
router.post('/process', async (req, res) => {
  try {
    // 获取请求参数
    const { 
      prompt, 
      input_image, 
      model = 'pro', 
      negative_prompt = '',
      user_id,
      feature_key,
      skip_credit_check
    } = req.body;

    // 验证必要参数
    if (!prompt || !input_image) {
      return res.status(400).json({ 
        success: false, 
        error: '缺少必要参数prompt或input_image' 
      });
    }

    // 添加参数验证
    if (typeof prompt !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'prompt必须是字符串'
      });
    }

    // 选择要使用的模型
    const modelId = model.toLowerCase() === 'max' ? FLUX_KONTEXT_MAX_MODEL : FLUX_KONTEXT_PRO_MODEL;

    console.log(`开始处理Flux Kontext请求: ${prompt.substring(0, 50)}...`);

    // 创建Replicate API请求
    const response = await axios.post(
      'https://api.replicate.com/v1/predictions', 
      {
        version: modelId,
        input: {
          prompt: prompt,
          negative_prompt: negative_prompt,
          input_image: input_image.includes('data:') ? input_image : `data:image/jpeg;base64,${input_image}`
        }
      },
      {
        headers: {
          'Authorization': `Token ${REPLICATE_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    // 检查响应
    if (!response.data || !response.data.id) {
      throw new Error('Replicate API返回了无效的响应');
    }

    // 返回任务ID给前端
    res.status(200).json({
      success: true,
      taskId: response.data.id
    });

  } catch (error) {
    console.error('处理Flux Kontext请求失败:', error);
    
    res.status(500).json({
      success: false,
      error: error.message || '处理请求失败'
    });
  }
});

// 查询任务状态
router.get('/task/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    
    if (!taskId) {
      return res.status(400).json({
        success: false,
        error: '缺少任务ID'
      });
    }

    // 查询Replicate API获取任务状态
    const response = await axios.get(
      `https://api.replicate.com/v1/predictions/${taskId}`,
      {
        headers: {
          'Authorization': `Token ${REPLICATE_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    // 处理不同的任务状态
    const task = response.data;
    
    // 格式化返回数据
    const result = {
      id: task.id,
      status: task.status,
      created_at: task.created_at,
      completed_at: task.completed_at
    };

    // 处理不同状态的特定数据
    if (task.status === 'succeeded') {
      // 成功完成，返回输出的图像URL
      console.log('任务成功，API响应:', JSON.stringify(task.output));
      
      // 根据Replicate官方示例，输出可能直接就是URL
      if (typeof task.output === 'string') {
        result.output_image_url = task.output;
      }
      // 如果输出是数组，取第一个元素
      else if (Array.isArray(task.output) && task.output.length > 0) {
        result.output_image_url = task.output[0];
      }
      // 如果输出是对象，尝试各种可能的属性
      else if (task.output && typeof task.output === 'object') {
        if (task.output.url && typeof task.output.url === 'function') {
          // 官方示例中使用 output.url() 方法
          try {
            result.output_image_url = task.output.url();
          } catch (e) {
            console.error('调用output.url()方法失败:', e);
          }
        } else if (task.output.url && typeof task.output.url === 'string') {
          result.output_image_url = task.output.url;
        } else if (task.output.image) {
          result.output_image_url = task.output.image;
        } else if (task.output.images && Array.isArray(task.output.images) && task.output.images.length > 0) {
          result.output_image_url = task.output.images[0];
        } else {
          // 尝试找到任何可能的URL字段
          const possibleUrlKeys = Object.keys(task.output).filter(key => 
            typeof task.output[key] === 'string' && 
            (task.output[key].startsWith('http') || task.output[key].startsWith('data:image'))
          );
          
          if (possibleUrlKeys.length > 0) {
            result.output_image_url = task.output[possibleUrlKeys[0]];
          } else {
            console.warn('任务成功但无法识别输出格式:', JSON.stringify(task.output));
          }
        }
      }
      
      // 如果仍然没有找到URL，记录警告
      if (!result.output_image_url) {
        console.warn('任务成功但未找到图片URL，完整输出:', JSON.stringify(task.output));
        result.warning = '任务成功但未能提取图片URL';
      }
    } else if (task.status === 'failed') {
      // 处理失败
      result.error = task.error || '任务处理失败';
    } else if (task.status === 'processing') {
      // 处理中，添加进度信息如果有
      result.progress = {
        percentage: Math.round((task.progress || 0) * 100),
        detail: task.logs
      };
    } else if (task.status === 'starting') {
      result.status = 'pending';
    }

    res.json(result);

  } catch (error) {
    console.error('查询任务状态失败:', error);
    
    res.status(500).json({
      success: false,
      error: error.message || '查询任务状态失败'
    });
  }
});

module.exports = router; 