const express = require('express');
const multer = require('multer');
const axios = require('axios');
const FormData = require('form-data');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { authenticateToken } = require('../utils/auth');
const { checkAndDeductCredits } = require('../middleware/checkCredits');
const pool = require('../db/pool');

const router = express.Router();

// 配置文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB限制
  fileFilter: function (req, file, cb) {
    const allowedTypes = /jpeg|jpg|png|webp/;
    const mimetype = allowedTypes.test(file.mimetype);
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());

    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('仅支持 PNG, JPG, WEBP 格式的图片!'));
  }
});

// 定义ComfyUI服务器URL
const COMFYUI_WORKFLOW_EXEC_URL = process.env.COMFYUI_GPU_URL || 'https://u63642-8e01-0d92b3a9.cqa1.seetacloud.com:8443'; // GPU服务器
const COMFYUI_WORKFLOW_NAME_FOR_FLASH_LIGHT = '闪光灯'; // 数据库中存储的工作流名称，ID为7
const COMFYUI_WORKFLOW_NAME_FOR_BEAUTY = '美颜'; // 数据库中存储的工作流名称，ID为8
// 节点ID
const COMFYUI_LOAD_IMAGE_NODE_ID = "189"; // 闪光灯加载图像节点ID
const COMFYUI_SAVE_IMAGE_NODE_ID = "136"; // 闪光灯保存图像节点ID
// 美颜专用节点ID
const COMFYUI_BEAUTY_LOAD_IMAGE_NODE_ID = "218"; // 美颜加载图像节点ID
const COMFYUI_BEAUTY_SAVE_IMAGE_NODE_ID = "136"; // 美颜保存图像节点ID
const COMFYUI_BEAUTY_SAMPLER_NODE_ID = "31"; // 美颜采样器节点ID
const COMFYUI_BEAUTY_STRENGTH_NODE_ID = "209"; // 美颜强度控制节点ID
const COMFYUI_BEAUTY_ENHANCED_NODE_ID = "228"; // 美颜强化节点ID
const COMFYUI_BEAUTY_SLIMFACE_SWITCH_NODE_ID = "220"; // 高P瘦脸开关节点ID

const POLLING_INTERVAL = 3000; // 3秒
const MAX_POLLING_ATTEMPTS = 20; // 最多轮询20次 (60秒)

// 辅助函数：上传图片到ComfyUI
async function uploadImageToComfyUI(fileBuffer, originalFilename) {
  const formData = new FormData();
  formData.append('image', fileBuffer, originalFilename);

  console.log(`[FlashLight Helper] Uploading to ComfyUI: ${originalFilename} to ${COMFYUI_WORKFLOW_EXEC_URL}`);
  const response = await axios.post(`${COMFYUI_WORKFLOW_EXEC_URL}/upload/image`, formData, {
    headers: formData.getHeaders(),
  });
  if (response.status !== 200 || !response.data || !response.data.name) {
    console.error('[FlashLight] ComfyUI upload failed:', response.data);
    throw new Error('上传图片到ComfyUI失败');
  }
  console.log('[FlashLight] ComfyUI upload successful:', response.data);
  return response.data; // { name, subfolder, type }
}

// 辅助函数：从数据库获取ComfyUI工作流
async function getComfyUIWorkflow(workflowName) {
  let connection;
  try {
    connection = await pool.getConnection();
    const [rows] = await connection.query('SELECT workflow_json FROM workflows WHERE name = ?', [workflowName]);
    if (rows.length === 0) {
      throw new Error(`未在数据库中找到名为 "${workflowName}" 的工作流`);
    }
    const workflowJsonActuallyObject = rows[0].workflow_json;
    console.log(`[FlashLight Debug] Raw workflow_json from DB for "${workflowName}":`, typeof workflowJsonActuallyObject, workflowJsonActuallyObject);
    return workflowJsonActuallyObject;
  } catch (error) {
    console.error(`[FlashLight] 获取工作流 "${workflowName}" 失败:`, error);
    throw error;
  } finally {
    if (connection) connection.release();
  }
}

// 辅助函数：触发ComfyUI工作流
async function triggerComfyUIWorkflow(workflowJson, clientId) {
  console.log(`[FlashLight] Triggering ComfyUI workflow with clientId: ${clientId} on ${COMFYUI_WORKFLOW_EXEC_URL}`);
  const response = await axios.post(`${COMFYUI_WORKFLOW_EXEC_URL}/prompt`, {
    prompt: workflowJson,
    client_id: clientId,
  });
  if (response.status !== 200 || !response.data || !response.data.prompt_id) {
    console.error('[FlashLight] ComfyUI trigger failed:', response.data);
    throw new Error('触发ComfyUI工作流失败');
  }
  console.log('[FlashLight] ComfyUI workflow triggered:', response.data);
  return response.data.prompt_id;
}

// 辅助函数：轮询ComfyUI历史记录
async function pollComfyUIHistory(promptId) {
  let attempts = 0;
  console.log(`[FlashLight] Starting to poll for promptId: ${promptId} on ${COMFYUI_WORKFLOW_EXEC_URL}`);

  while (attempts < MAX_POLLING_ATTEMPTS) {
    let isQueued = false;
    try {
      console.log(`[FlashLight] Polling history for promptId: ${promptId}, execution attempt ${attempts + 1}/${MAX_POLLING_ATTEMPTS}`);
      const historyResponse = await axios.get(`${COMFYUI_WORKFLOW_EXEC_URL}/history/${promptId}`);
      
      if (historyResponse.status === 200 && historyResponse.data && historyResponse.data[promptId]) {
        const promptData = historyResponse.data[promptId];
        if (promptData.outputs && Object.keys(promptData.outputs).length > 0 && 
           (promptData.outputs[COMFYUI_SAVE_IMAGE_NODE_ID] || promptData.outputs[COMFYUI_BEAUTY_SAVE_IMAGE_NODE_ID])) {
          console.log('[FlashLight] Prompt finished (found in history):', promptData);
          return promptData;
        }
        console.log(`[FlashLight] Prompt ${promptId} found in history but not yet complete. Outputs:`, promptData.outputs);
      } else {
        console.log(`[FlashLight] Prompt ${promptId} not found or history incomplete. Checking queue status.`);
        const queueResponse = await axios.get(`${COMFYUI_WORKFLOW_EXEC_URL}/prompt`);
        if (queueResponse.status === 200 && queueResponse.data && queueResponse.data.exec_info) {
          const queueRemaining = queueResponse.data.exec_info.queue_remaining;
          console.log(`[FlashLight] Queue status: ${queueRemaining} tasks remaining.`);
          
          if (queueRemaining > 0) {
            console.log(`[FlashLight] Task ${promptId} likely queued. Waiting...`);
            isQueued = true;
          } else {
            console.log(`[FlashLight] Queue is empty but task ${promptId} not complete in history. Assuming it started, failed, or is invalid.`);
          }
        } else {
          console.warn('[FlashLight] Failed to get queue information. Assuming task might be running or failed.');
        }
      }
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.warn(`[FlashLight] History API returned 404 for ${promptId}. Checking queue.`);
        try {
          const queueResponseFor404 = await axios.get(`${COMFYUI_WORKFLOW_EXEC_URL}/prompt`);
          if (queueResponseFor404.status === 200 && queueResponseFor404.data && queueResponseFor404.data.exec_info && queueResponseFor404.data.exec_info.queue_remaining > 0) {
            console.log(`[FlashLight] Task ${promptId} (after history 404) likely queued. Waiting...`);
            isQueued = true;
          } else {
            console.log(`[FlashLight] Queue is empty (after history 404) or cannot get queue info for ${promptId}.`);
          }
        } catch (queueError) {
          console.warn('[FlashLight] Error checking queue after 404:', queueError.message);
        }
      } else {
        console.warn(`[FlashLight] Polling error: ${error.message}.`);
      }
    }

    if (!isQueued) {
      attempts++;
    }

    if (attempts < MAX_POLLING_ATTEMPTS) {
      await new Promise(resolve => setTimeout(resolve, POLLING_INTERVAL));
    }
  }
  throw new Error(`ComfyUI任务超时或未能成功获取结果 (耗尽${MAX_POLLING_ATTEMPTS}次执行阶段轮询)`);
}

// 辅助函数: 保存历史记录
async function saveHistory(userId, originalFilename, resultImageUrl, workflowName) {
  let historyConnection;
  try {
    historyConnection = await pool.getConnection();
    await historyConnection.query(
      'INSERT INTO flash_light_history (user_id, original_image_filename, result_image_url, workflow_name) VALUES (?, ?, ?, ?)',
      [userId, originalFilename, resultImageUrl, workflowName]
    );
    console.log(`[FlashLight] History saved for user ${userId}`);
  } catch (historyError) {
    console.error('[FlashLight] Failed to save history:', historyError);
  } finally {
    if (historyConnection) historyConnection.release();
  }
}

// 处理闪光灯和美颜的通用API路由
router.post('/flash-beauty',
  authenticateToken,
  upload.single('file'),
  async (req, res) => {
    if (!req.file) {
      return res.status(400).json({ success: false, message: '请上传图片文件。' });
    }

    const userId = req.user.id;
    const originalFilename = req.file.originalname;
    const clientId = uuidv4(); // 为本次请求生成唯一客户端ID
    
    // 获取请求参数
    const workflowName = req.body.workflowName || COMFYUI_WORKFLOW_NAME_FOR_FLASH_LIGHT;
    const isBeauty = workflowName === COMFYUI_WORKFLOW_NAME_FOR_BEAUTY;
    
    // 根据工作流类型选择功能key和对应的节点ID
    const featureKey = isBeauty ? 'beauty' : 'flash_light';
    const loadImageNodeId = isBeauty ? COMFYUI_BEAUTY_LOAD_IMAGE_NODE_ID : COMFYUI_LOAD_IMAGE_NODE_ID;
    const saveImageNodeId = isBeauty ? COMFYUI_BEAUTY_SAVE_IMAGE_NODE_ID : COMFYUI_SAVE_IMAGE_NODE_ID;
    
    // 先检查积分
    try {
      await checkAndDeductCredits(featureKey)(req, res, () => {});
      // 检查是否有错误消息(积分不足)
      if (res.headersSent) {
        return;
      }
    } catch (error) {
      console.error(`[FlashBeauty] 积分检查出错: ${error.message}`);
      return res.status(403).json({ success: false, message: `积分不足或检查失败: ${error.message}` });
    }

    try {
      // 1. 上传图片到ComfyUI
      const uploadedImageInfo = await uploadImageToComfyUI(req.file.buffer, originalFilename);
      const comfyImageFilename = uploadedImageInfo.name;

      // 2. 获取并准备工作流
      const workflow = await getComfyUIWorkflow(workflowName);
      
      // 3. 更新工作流中的输入图片文件名
      if (workflow[loadImageNodeId] && workflow[loadImageNodeId].inputs) {
        workflow[loadImageNodeId].inputs.image = comfyImageFilename;
      } else {
        throw new Error(`工作流中未找到有效的图片输入节点 "${loadImageNodeId}"`);
      }
      
      // 4. 对于美颜功能，需要额外配置一些参数
      if (isBeauty) {
        // 设置美颜强度
        const beautyStrength = parseFloat(req.body.beautyStrength || "0.8");
        if (workflow[COMFYUI_BEAUTY_STRENGTH_NODE_ID] && workflow[COMFYUI_BEAUTY_STRENGTH_NODE_ID].inputs) {
          workflow[COMFYUI_BEAUTY_STRENGTH_NODE_ID].inputs.strength = Math.min(Math.max(beautyStrength, 0), 1.5);
        }
        
        // 设置采样器随机种子
        if (workflow[COMFYUI_BEAUTY_SAMPLER_NODE_ID] && workflow[COMFYUI_BEAUTY_SAMPLER_NODE_ID].inputs) {
          workflow[COMFYUI_BEAUTY_SAMPLER_NODE_ID].inputs.seed = Math.floor(Math.random() * 1000000);
        }
        
        // 处理强化美颜选项
        const enhancedBeauty = req.body.enhancedBeauty === "1";
        if (!enhancedBeauty) {
          console.log('[FlashBeauty] 强化美颜未启用，修改KSampler节点31的输入连接');
          
          // 修改KSampler节点31的model输入连接
          if (workflow[COMFYUI_BEAUTY_SAMPLER_NODE_ID] && 
              workflow[COMFYUI_BEAUTY_SAMPLER_NODE_ID].inputs && 
              workflow[COMFYUI_BEAUTY_SAMPLER_NODE_ID].inputs.model) {
            
            // 将model输入从节点228改为节点209
            workflow[COMFYUI_BEAUTY_SAMPLER_NODE_ID].inputs.model = [
              COMFYUI_BEAUTY_STRENGTH_NODE_ID, // 209
              0  // 假设输出索引仍然是0
            ];
            
            console.log(`[FlashBeauty] KSampler节点31的model输入已修改为节点${COMFYUI_BEAUTY_STRENGTH_NODE_ID}`);
          }
        } else {
          console.log('[FlashBeauty] 强化美颜已启用');
        }
        
        // 处理高P瘦脸开关
        const slimFace = req.body.slimFace === "1";
        if (workflow[COMFYUI_BEAUTY_SLIMFACE_SWITCH_NODE_ID] && 
            workflow[COMFYUI_BEAUTY_SLIMFACE_SWITCH_NODE_ID].inputs) {
          workflow[COMFYUI_BEAUTY_SLIMFACE_SWITCH_NODE_ID].inputs.boolean = slimFace;
          console.log(`[FlashBeauty] 高P瘦脸已${slimFace ? '启用' : '禁用'}`);
        }
      }
      
      // 5. 触发ComfyUI执行
      const promptId = await triggerComfyUIWorkflow(workflow, clientId);

      // 6. 轮询历史记录
      const historyData = await pollComfyUIHistory(promptId);

      // 7. 提取输出图片信息
      const outputNodeData = historyData.outputs[saveImageNodeId];
      if (!outputNodeData || !outputNodeData.images || outputNodeData.images.length === 0) {
        throw new Error('ComfyUI工作流执行完毕，但未找到期望的输出图片');
      }
      
      const outputImage = outputNodeData.images[0];
      const resultFilename = outputImage.filename;
      const resultSubfolder = outputImage.subfolder;
      const resultType = outputImage.type; // 通常是'output'

      // 8. 构建结果图片URL
      let resultImageUrl = `${COMFYUI_WORKFLOW_EXEC_URL}/view?filename=${encodeURIComponent(resultFilename)}&type=${encodeURIComponent(resultType)}`;
      if (resultSubfolder) {
        resultImageUrl += `&subfolder=${encodeURIComponent(resultSubfolder)}`;
      }
      
      console.log(`[FlashBeauty] Result image URL: ${resultImageUrl}`);

      // 9. 保存历史记录
      await saveHistory(userId, originalFilename, resultImageUrl, workflowName);

      // 10. 构建代理后的图片URL给前端
      const backendApiBaseUrl = process.env.BACKEND_API_URL || 'https://caca.yzycolour.top';
      let proxiedImageUrl = `${backendApiBaseUrl}/api/images/comfy-output/${encodeURIComponent(resultType)}/${encodeURIComponent(resultFilename)}`;
      if (resultSubfolder) {
        proxiedImageUrl += `?subfolder=${encodeURIComponent(resultSubfolder)}`;
      }

      res.json({
        success: true,
        proxiedImageUrl: proxiedImageUrl,
        message: isBeauty ? '美颜处理成功' : '补光效果添加成功',
        newCredits: req.locals?.newCredits,
        newDailyFreeUsed: req.locals?.newDailyFreeUsed
      });

    } catch (error) {
      console.error(`[FlashBeauty] ComfyUI${isBeauty ? '美颜' : '补光'}处理流程出错:`, error.message, error.stack);
      res.status(500).json({ 
        success: false,
        error: `ComfyUI${isBeauty ? '美颜' : '补光'}服务处理失败`, 
        message: error.message 
      });
    }
  }
);

// 历史记录API路由
router.get('/history',
  authenticateToken,
  async (req, res) => {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 8;
    const offset = (page - 1) * limit;

    let connection;
    try {
      connection = await pool.getConnection();
      
      // 查询总记录数用于分页
      const countQuery = 'SELECT COUNT(*) as totalCount FROM flash_light_history WHERE user_id = ?';
      const [[{ totalCount }]] = await connection.query(countQuery, [userId]);
      
      // 如果没有记录，直接返回空数组
      if (totalCount === 0) {
        return res.json({
          success: true,
          history: [],
          pagination: {
            totalItems: 0,
            totalPages: 0,
            currentPage: page,
            itemsPerPage: limit
          }
        });
      }
      
      // 计算总页数
      const totalPages = Math.ceil(totalCount / limit);
      
      // 查询历史记录，按创建时间降序排列
      const historyQuery = `
        SELECT id, user_id, original_image_filename, result_image_url, created_at, workflow_name 
        FROM flash_light_history 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
      `;
      const [rows] = await connection.query(historyQuery, [userId, limit, offset]);
      
      res.json({
        success: true,
        history: rows,
        pagination: {
          totalItems: totalCount,
          totalPages,
          currentPage: page,
          itemsPerPage: limit
        }
      });
    } catch (error) {
      console.error('[Flash Beauty History] Error fetching history:', error);
      res.status(500).json({
        success: false,
        message: '获取历史记录失败，请稍后重试'
      });
    } finally {
      if (connection) connection.release();
    }
  }
);

module.exports = router; 