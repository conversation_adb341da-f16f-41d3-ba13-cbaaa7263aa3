const express = require('express');
const router = express.Router();
const pool = require('../db/pool');
const { authenticateToken } = require('../utils/auth'); // 引入身份验证中间件

/**
 * @route GET /api/ui-components/:featureArea
 * @description 获取指定功能区域的UI组件配置
 * @access Private
 */
router.get('/:featureArea', authenticateToken, async (req, res) => {
    const { featureArea } = req.params;

    try {
        const connection = await pool.getConnection();
        try {
            const [components] = await connection.query(
                'SELECT component_key, feature_area, component_type, label, placeholder, `options`, default_value, display_order FROM ui_components WHERE feature_area = ? AND is_active = TRUE ORDER BY display_order ASC',
                [featureArea]
            );

            // 后端负责将options字段从JSON字符串解析为JSON对象
            const parsedComponents = components.map(component => {
                if (component.options && typeof component.options === 'string') {
                    try {
                        component.options = JSON.parse(component.options);
                    } catch (e) {
                        console.error(`Error parsing JSON options for component_key ${component.component_key}:`, e);
                        // 如果解析失败，返回空数组，避免前端出错
                        component.options = []; 
                    }
                }
                return component;
            });

            res.json({ success: true, components: parsedComponents });
        } finally {
            connection.release();
        }
    } catch (error) {
        console.error(`Error fetching UI components for ${featureArea}:`, error);
        res.status(500).json({ success: false, error: '获取UI组件配置失败，请稍后重试' });
    }
});

module.exports = router; 