const express = require('express');
const { authenticateToken } = require('../utils/auth');
const pool = require('../db/pool');

const router = express.Router();

// GET /api/flash-light/history (获取登录用户的历史记录)
router.get('/history', authenticateToken, async (req, res) => {
    console.log('调用了 flashLightHistory.js 的 /history');
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 8; // 默认每页8条记录
    const offset = (page - 1) * limit;

    let connection;
    try {
        connection = await pool.getConnection();

        // 查询总记录数用于分页
        const countQuery = 'SELECT COUNT(*) as totalCount FROM flash_light_history WHERE user_id = ?';
        const [[{ totalCount }]] = await connection.query(countQuery, [userId]);

        // 查询分页后的历史数据，按创建时间降序排列
        const historyQuery = `
            SELECT id, user_id, original_image_filename, result_image_url, intensity, created_at 
            FROM flash_light_history 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?`;
        const [history] = await connection.query(historyQuery, [userId, limit, offset]);

        connection.release();

        const totalPages = Math.ceil(totalCount / limit);

        res.json({
            history,
            pagination: {
                totalCount,
                currentPage: page,
                totalPages,
                limit
            }
        });

    } catch (error) {
        if (connection) connection.release();
        console.error(`[Flash Light History] Error fetching history for user ${userId}:`, error);
        res.status(500).json({ error: '获取历史记录失败', details: error.message });
    }
});

module.exports = router; 