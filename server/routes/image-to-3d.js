const express = require('express');
const router = express.Router();
const multer = require('multer');
const axios = require('axios');
const FormData = require('form-data');
const path = require('path');
const fs = require('fs');
const fsPromises = require('fs').promises; // 引入 fs.promises
const { v4: uuidv4 } = require('uuid');
const { authenticateToken } = require('../utils/auth');
const { checkAndDeductCredits } = require('../middleware/checkCredits');
const { router: historyRouter, createHistory, updateHistoryStatus } = require('./image-to-3d-history');
const pool = require('../db/pool'); // Ensure pool is required for direct DB updates if needed

// --- Tripo3D 配置 ---
const TRIPO_API_KEY = process.env.TRIPO_API_KEY;
const TRIPO_BASE_URL = "https://api.tripo3d.ai/v2";
const TRIPO_HEADERS = {
    "Authorization": `Bearer ${TRIPO_API_KEY}`
};
const POLL_INTERVAL_MS = 5000; // 轮询间隔 (毫秒)

// --- 文件上传设置 ---
const uploadDir = process.env.UPLOAD_DIR || 'uploads';
const baseUploadPath = path.join(process.cwd(), uploadDir); // 基础上传目录 uploads/

// 特定于功能的上传子目录
const imageTo3dOriginalsPath = path.join(baseUploadPath, 'image-to-3d', 'originals');

// 确保上传目录存在
if (!fs.existsSync(imageTo3dOriginalsPath)) {
    fs.mkdirSync(imageTo3dOriginalsPath, { recursive: true });
}

// 配置文件存储
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, imageTo3dOriginalsPath); // 直接保存到目标子目录
    },
    filename: function (req, file, cb) {
        // 生成唯一文件名，保留原始扩展名
        const uniqueName = `user-${req.user.id}-${Date.now()}${path.extname(file.originalname)}`;
        cb(null, uniqueName);
    }
});

const upload = multer({
    storage: storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB 限制
    fileFilter: function (req, file, cb) {
        const allowedTypes = /jpeg|jpg|png|webp/;
        const mimetype = allowedTypes.test(file.mimetype);
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        
        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('仅支持 JPEG, PNG 和 WEBP 格式的图片!'));
    }
});

// --- 任务状态管理 (简单内存存储) ---
// 结构: { taskId: { historyId, userId, status, progress, resultUrl, error } }
const taskStatuses = {};

// --- ComfyUI Base URL ---
const COMFYUI_BASE_URL = 'https://u63642-a2b4-fccb0b0d.westx.seetacloud.com:8443';

// --- Tripo3D API 调用函数 ---

async function uploadImageToTripo(filePath, originalFilename) {
    console.log(`开始上传图片到 Tripo3D: ${originalFilename} (${filePath})`);
    const form = new FormData();
    form.append('file', fs.createReadStream(filePath), originalFilename);

    // 使用正确的端点
    const url = `${TRIPO_BASE_URL}/openapi/upload`;

    try {
        const response = await axios.post(url, form, {
            headers: {
                ...TRIPO_HEADERS,
                ...form.getHeaders()
            },
            maxContentLength: Infinity,
            maxBodyLength: Infinity
        });

        // 检查响应结构
        if (response.data && response.data.code === 0 && response.data.data && response.data.data.image_token) {
            const imageToken = response.data.data.image_token;
            console.log(`图片上传 Tripo3D 成功. Image Token: ${imageToken}`);
            return imageToken;
        } else {
            console.error(`Tripo3D 图片上传 API 返回失败或格式不符:`, response.data);
            return null;
        }
    } catch (error) {
        console.error(`Tripo3D 图片上传请求出错: ${error.message}`);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
        return null;
    }
}

async function createTripoGenerationTask(imageToken) {
    console.log(`使用 Image Token 创建 Tripo3D 生成任务: ${imageToken}`);
    const url = `${TRIPO_BASE_URL}/openapi/task`;
    const payload = {
        type: "image_to_model",
        file: {
            file_token: imageToken
        }
    };

    try {
        const response = await axios.post(url, payload, {
            headers: {
                ...TRIPO_HEADERS,
                "Content-Type": "application/json"
            }
        });

        // 检查响应结构
        if (response.data && response.data.code === 0 && response.data.data && response.data.data.task_id) {
            const taskId = response.data.data.task_id;
            console.log(`Tripo3D 生成任务创建成功. Task ID: ${taskId}`);
            return taskId;
        } else {
            console.error(`Tripo3D 创建生成任务 API 返回失败或格式不符:`, response.data);
            return null;
        }
    } catch (error) {
        console.error(`Tripo3D 创建生成任务请求出错: ${error.message}`);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
        return null;
    }
}

async function pollTripoTaskStatus(taskId, historyId, userId) {
    console.log(`开始后台轮询任务状态: ${taskId}, historyId: ${historyId}, userId: ${userId}`);
    const url = `${TRIPO_BASE_URL}/openapi/task/${taskId}`;

    // 更新任务状态为进行中
    if (historyId) {
        await updateHistoryStatus(historyId, 'processing');
    }

    const intervalId = setInterval(async () => {
        try {
            const response = await axios.get(url, { headers: TRIPO_HEADERS });

            if (response.data && response.data.code === 0 && response.data.data) {
                const taskData = response.data.data;
                const currentStatus = taskData.status;
                const progress = taskData.progress || 0;

                console.log(`轮询 Task ${taskId}: 状态=${currentStatus}, 进度=${progress}%`);

                // 更新内存中的状态
                taskStatuses[taskId] = { 
                    ...taskStatuses[taskId], 
                    historyId,
                    userId,
                    status: currentStatus, 
                    progress: progress 
                };

                // 根据状态处理
                if (currentStatus === "success") {
                    console.log(`任务 ${taskId} 完成!`);
                    clearInterval(intervalId); // Stop polling first

                    const outputData = taskData.output || {};
                    const tripoModelUrl = outputData.model || outputData.base_model || outputData.pbr_model;
                    const tripoPreviewUrl = outputData.rendered_image;

                    let comfyModelUrl = null;
                    let comfyPreviewUrl = null;
                    const comfyApiEndpoint = `${COMFYUI_BASE_URL}/yzy/download-url`;

                    // --- Trigger ComfyUI Download for Preview --- START ---
                    if (tripoPreviewUrl) {
                        try {
                            const originalPreviewExt = path.extname(new URL(tripoPreviewUrl).pathname).toLowerCase() || '.png';
                            // Use a prefix for the custom node, and let the node handle the full name generation
                            const previewFilePrefix = `task_${taskId}_preview_`; 
                            console.log(`[Poll ${taskId}] Triggering ComfyUI download for preview with prefix: ${previewFilePrefix}`);
                            const apiResponse = await axios.post(comfyApiEndpoint, {
                                url: tripoPreviewUrl,
                                filename_prefix: previewFilePrefix // Corrected parameter name
                                // subdir parameter is not used by the custom node for filename generation directly
                            }, { timeout: 120000 }); // Added 2 min timeout

                            if (apiResponse.data && apiResponse.data.success) {
                                // The custom node returns filename and subfolder (which is 'yzy_downloaded' by default)
                                comfyPreviewUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(apiResponse.data.filename)}&type=input&subfolder=${encodeURIComponent(apiResponse.data.subfolder || 'yzy_downloaded')}`;
                                console.log(`[Poll ${taskId}] ComfyUI preview download triggered. View URL: ${comfyPreviewUrl}`);
                            } else {
                                console.error(`[Poll ${taskId}] Failed to trigger ComfyUI preview download:`, apiResponse.data.error || 'Unknown error');
                            }
                        } catch (error) {
                            console.error(`[Poll ${taskId}] Error calling ComfyUI download API for preview: ${error.message}`);
                        }
                    }
                    // --- Trigger ComfyUI Download for Preview --- END ---

                    // --- Trigger ComfyUI Download for Model --- START ---
                    if (tripoModelUrl) {
                        try {
                            // Use a prefix for the custom node
                            const modelFilePrefix = `task_${taskId}_model_`;
                            console.log(`[Poll ${taskId}] Triggering ComfyUI download for model with prefix: ${modelFilePrefix}`);
                            const apiResponse = await axios.post(comfyApiEndpoint, {
                                url: tripoModelUrl,
                                filename_prefix: modelFilePrefix // Corrected parameter name
                                // subdir parameter is not used by the custom node for filename generation directly
                            }, { timeout: 300000 }); // Added 5 min timeout for model download

                            if (apiResponse.data && apiResponse.data.success) {
                                // The custom node returns filename and subfolder (which is 'yzy_downloaded' by default)
                                comfyModelUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(apiResponse.data.filename)}&type=input&subfolder=${encodeURIComponent(apiResponse.data.subfolder || 'yzy_downloaded')}`;
                                console.log(`[Poll ${taskId}] ComfyUI model download triggered. View URL: ${comfyModelUrl}`);
                    } else {
                                console.error(`[Poll ${taskId}] Failed to trigger ComfyUI model download:`, apiResponse.data.error || 'Unknown error');
                            }
                        } catch (error) {
                            console.error(`[Poll ${taskId}] Error calling ComfyUI download API for model: ${error.message}`);
                        }
                    }
                    // --- Trigger ComfyUI Download for Model --- END ---

                    // Update task status in memory
                    // Prioritize ComfyUI URLs. If ComfyUI download failed, these will be null.
                    taskStatuses[taskId].resultUrl = comfyModelUrl; 
                    taskStatuses[taskId].previewUrl = comfyPreviewUrl;
                    // Fallback to Tripo URLs only if ComfyUI URLs are null and you want to provide *something*
                    // For now, we'll stick to ComfyUI URLs or null to be clear.
                    // if (!comfyModelUrl) taskStatuses[taskId].resultUrl = tripoModelUrl; 
                    // if (!comfyPreviewUrl) taskStatuses[taskId].previewUrl = tripoPreviewUrl;
                    taskStatuses[taskId].status = 'completed';

                    console.log(`[Poll ${taskId}] Final Model URL (intended for DB and client): ${comfyModelUrl}`);
                    console.log(`[Poll ${taskId}] Final Preview URL (intended for DB and client): ${comfyPreviewUrl}`);

                    // Update history record with ComfyUI URLs (or null if failed)
                        if (historyId) {
                        await updateHistoryStatus(historyId, 'completed', comfyModelUrl, comfyPreviewUrl);
                    }

                } else if (currentStatus === "failed" || currentStatus === "cancelled" || currentStatus === "unknown") {
                    const errorMsg = taskData.error || '未知错误';
                    console.error(`任务 ${taskId} ${currentStatus}: ${errorMsg}`);
                    taskStatuses[taskId].status = 'failed';
                    taskStatuses[taskId].error = errorMsg || `任务状态: ${currentStatus}`;

                    // 更新历史记录状态
                    if (historyId) {
                        await updateHistoryStatus(historyId, 'failed');
                    }
                    clearInterval(intervalId);
                }
                // 其他状态继续轮询
            } else {
                console.error(`查询 Tripo3D 任务状态 API 返回失败或格式不符: Task ID=${taskId}`, response.data);
            }
        } catch (error) {
            console.error(`查询 Tripo3D 任务状态请求出错: Task ID=${taskId}, Error: ${error.message}`);
            if (error.response && error.response.status === 404) {
                console.error(`任务 ${taskId} 未找到 (404)，停止轮询。`);
                taskStatuses[taskId] = { 
                    ...taskStatuses[taskId],
                    historyId,
                    userId,
                    status: 'failed', 
                    progress: 0, 
                    error: '任务未找到 (404)', 
                    resultUrl: null 
                };

                // 更新历史记录状态
                if (historyId) {
                    await updateHistoryStatus(historyId, 'failed');
                }
                clearInterval(intervalId);
            }
        }
    }, POLL_INTERVAL_MS);
}

// --- API 端点 ---

// 处理图片上传和任务创建 (添加积分检查中间件)
router.post('/generate-3d', authenticateToken, checkAndDeductCredits('image_to_3d', false), upload.single('imageFile'), async (req, res) => {
    try {
        // 检查API密钥是否存在
        if (!TRIPO_API_KEY) {
            console.error('错误：缺少Tripo3D API密钥 (TRIPO_API_KEY)');
            return res.status(500).json({ 
                success: false, 
                message: '服务器配置不完整，无法处理请求。请联系管理员配置API密钥。'
            });
        }

        const userId = req.user.id;

        if (!req.file) {
            return res.status(400).json({ success: false, message: '没有提供图片文件' });
        }

        console.log('接收到上传文件:', req.file);
        // req.file.path 是文件的完整保存路径
        // req.file.filename 是在 destination 中的文件名

        // --- Directly Upload Original Image to ComfyUI --- START ---
        let comfyOriginalImageUrl = null;
        let comfyOriginalFilename = null;
        try {
            const originalImageBuffer = await fsPromises.readFile(req.file.path);
            const comfyFormData = new FormData();
            
            // --- Generate simpler filename --- START ---
            const originalExtension = path.extname(req.file.filename) || path.extname(req.file.originalname) || '.png'; // Use multer filename extension if possible
            const simpleFilename = `i23d-user${userId}-${Date.now()}-original${originalExtension}`;
            comfyOriginalFilename = simpleFilename; // Use this filename for ComfyUI and history
            // --- Generate simpler filename --- END ---

            comfyFormData.append('image', originalImageBuffer, comfyOriginalFilename);
            comfyFormData.append('type', 'input');
            comfyFormData.append('overwrite', 'true');

            console.log(`[Generate-3D] Uploading original image to ComfyUI: ${comfyOriginalFilename}`);
            const comfyUploadResponse = await axios.post(`${COMFYUI_BASE_URL}/upload/image`, comfyFormData, {
                headers: comfyFormData.getHeaders(),
                timeout: 60000 // 1 min timeout for original image upload
            });

            if (comfyUploadResponse.status === 200 && comfyUploadResponse.data && comfyUploadResponse.data.name) {
                const comfyUploadResult = comfyUploadResponse.data;
                comfyOriginalImageUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(comfyUploadResult.name)}&type=${encodeURIComponent(comfyUploadResult.type || 'input')}`;
                if (comfyUploadResult.subfolder) {
                     comfyOriginalImageUrl += `&subfolder=${encodeURIComponent(comfyUploadResult.subfolder)}`;
                }
                console.log(`[Generate-3D] ComfyUI original image upload successful. URL: ${comfyOriginalImageUrl}`);
            } else {
                 throw new Error(`ComfyUI original image upload failed: ${comfyUploadResponse.statusText} - ${JSON.stringify(comfyUploadResponse.data)}`);
            }
        } catch (uploadError) {
            console.error('[Generate-3D] Failed to upload original image to ComfyUI:', uploadError.message);
            // Decide how to proceed: fail request, or continue without comfy URL?
            // Let's fail the request for now if initial upload fails.
             try { await fsPromises.unlink(req.file.path); } catch (e) {} // Cleanup local file
             return res.status(500).json({ success: false, message: '处理失败 (无法上传原始图片)' });
        }
        // --- Directly Upload Original Image to ComfyUI --- END ---

        // 1. 创建历史记录条目 (初始状态为 pending)
        const historyData = {
            originalImage: comfyOriginalFilename,
            name: `3D模型 - ${path.basename(req.file.originalname, path.extname(req.file.originalname))}`,
            originalImageUrl: comfyOriginalImageUrl, 
            // modelUrl, previewUrl, thumbnailUrl 会在后续更新
        };
        const historyResult = await createHistory(userId, historyData);

        if (!historyResult.success || !historyResult.id) {
            console.error('创建历史记录失败:', historyResult.error);
            // 如果历史记录创建失败，可能需要删除已上传的文件以避免孤立文件
            try {
                await fsPromises.unlink(req.file.path);
                console.log('因历史记录创建失败，已删除上传的文件:', req.file.path);
            } catch (unlinkError) {
                console.error('删除上传文件失败:', unlinkError);
            }
            return res.status(500).json({ success: false, message: '处理请求失败 (无法记录历史)' });
        }
        const historyId = historyResult.id;

        // 2. 上传图片到 Tripo3D
        const imageToken = await uploadImageToTripo(req.file.path, comfyOriginalFilename);
         // --- Now delete local original file --- START ---
        if (imageToken) { // Only delete if Tripo upload was likely successful (token received)
            fsPromises.unlink(req.file.path)
                .then(() => console.log(`[Generate-3D] Deleted local original file: ${req.file.filename}`))
                .catch(err => console.error(`[Generate-3D] Failed to delete local original file: ${err.message}`));
        } else {
            // If Tripo upload failed, maybe keep the local file for retry or inspection?
            console.warn('[Generate-3D] Tripo3D upload failed, keeping local file:', req.file.filename);
        }
        // --- Now delete local original file --- END ---

        if (!imageToken) {
            await updateHistoryStatus(historyId, 'failed');
            return res.status(500).json({ success: false, message: '图片上传到 Tripo3D 失败' });
        }

        // 3. 创建 Tripo3D 生成任务
        const taskId = await createTripoGenerationTask(imageToken);
        if (!taskId) {
            // 更新历史记录状态
            await updateHistoryStatus(historyId, 'failed');
            return res.status(500).json({ success: false, message: '创建 Tripo3D 生成任务失败' });
        }

        // 3.1 新增：保存 Tripo3D 任务ID到历史表
        try {
            await pool.query('UPDATE image_to_3d_history SET tripo_task_id = ? WHERE id = ?', [taskId, historyId]);
            console.log(`[Generate-3D] 已写入 tripo_task_id=${taskId} 到历史记录 id=${historyId}`);
        } catch (e) {
            console.error(`[Generate-3D] 写入 tripo_task_id 失败:`, e);
        }

        // 4. 初始化任务状态并开始后台轮询
        taskStatuses[taskId] = { 
            historyId,
            userId,
            status: 'pending', 
            progress: 0, 
            resultUrl: null, 
            error: null 
        };
        
        // 异步开始轮询，不阻塞响应
        pollTripoTaskStatus(taskId, historyId, userId);

        // 5. 返回任务ID给前端
        res.json({ 
            success: true, 
            taskId: taskId, 
            historyId: historyId 
        });
    } catch (error) {
        console.error('处理图片转3D请求出错:', error);
        res.status(500).json({ 
            success: false, 
            message: '处理请求失败',
            error: error.message
        });
    }
});

// 查询任务状态
router.get('/task-status/:taskId', authenticateToken, (req, res) => {
    const taskId = req.params.taskId;
    const userId = req.user.id;
    const statusInfo = taskStatuses[taskId];

    // 检查任务是否存在
    if (!statusInfo) {
        return res.status(404).json({ success: false, message: '任务 ID 不存在' });
    }

    // 检查任务是否属于当前用户
    if (statusInfo.userId && statusInfo.userId !== userId) {
        return res.status(403).json({ success: false, message: '无权访问此任务' });
    }

    // 直接返回 taskStatuses 中的 resultUrl 和 previewUrl, 
    // 这些已经优先设置为 ComfyUI URL
    res.json({ 
        success: true, 
        status: statusInfo.status,
        progress: statusInfo.progress,
        resultUrl: statusInfo.resultUrl,    // This will be comfyModelUrl or null
        previewUrl: statusInfo.previewUrl,  // This will be comfyPreviewUrl or null
        error: statusInfo.error,
        historyId: statusInfo.historyId
    });
});

// 代理模型文件（解决CORS问题） -  此路由将被删除
/*
router.get('/proxy-model', async (req, res) => {
    const modelUrl = req.query.url;
    
    if (!modelUrl) {
        return res.status(400).json({ success: false, message: '未提供模型 URL' });
    }
    
    try {
        // 通过服务器请求模型文件
        const response = await axios({
            method: 'get',
            url: modelUrl,
            responseType: 'stream'
        });
        
        // 设置响应头
        res.setHeader('Content-Type', response.headers['content-type']);
        if (response.headers['content-length']) {
            res.setHeader('Content-Length', response.headers['content-length']);
        }
        res.setHeader('Cache-Control', 'public, max-age=86400'); // 缓存24小时
        
        // 将流直接传递给客户端
        response.data.pipe(res);
    } catch (error) {
        console.error(`代理模型请求出错: ${error.message}`);
        res.status(500).json({ 
            success: false, 
            message: '获取模型文件失败',
            error: error.message
        });
    }
});
*/

// 保存模型缩略图
router.post('/save-thumbnail', authenticateToken, async (req, res) => {
    const { taskId, thumbnailData } = req.body;
    const userId = req.user.id;

    if (!taskId || !thumbnailData) {
        return res.status(400).json({ 
            success: false, 
            message: '缺少必要参数'
        });
    }
    
    console.log(`接收到保存缩略图请求: taskId=${taskId}, 数据长度=${thumbnailData.length}`);
    
    // 验证任务是否属于当前用户
    const statusInfo = taskStatuses[taskId];
    
    if (!statusInfo) {
        console.log(`找不到任务状态信息: taskId=${taskId}`);
        return res.status(404).json({ 
            success: false, 
            message: '找不到任务信息'
        });
    }

    // 从 statusInfo 中获取 historyId
    const historyId = statusInfo.historyId;
    if (!historyId) {
        console.log(`[SaveThumbnail] 未在任务状态中找到 historyId: taskId=${taskId}`);
        return res.status(404).json({
            success: false,
            message: '任务历史ID未找到'
        });
    }
    
    if (statusInfo.userId !== userId) {
        console.log(`任务不属于当前用户: taskId=${taskId}, requestUserId=${userId}, taskUserId=${statusInfo.userId}`);
        return res.status(403).json({ 
            success: false, 
            message: '无权修改此任务'
        });
    }
    
    let comfyThumbnailUrl = null;
    let comfyThumbnailFilename = null;
    try {
        console.log(`[SaveThumbnail] Received thumbnail for task ${taskId}, history ${historyId}.`);
        const base64Data = thumbnailData.replace(/^data:image\/png;base64,/, "");
        const thumbnailBuffer = Buffer.from(base64Data, 'base64');
        
        // --- Directly Upload Thumbnail Buffer --- START ---
        const comfyFormData = new FormData();
        comfyThumbnailFilename = `${Date.now()}_task_${taskId}_thumb.png`;
        comfyFormData.append('image', thumbnailBuffer, comfyThumbnailFilename);
        comfyFormData.append('type', 'output'); // Save thumbnail as output type
        comfyFormData.append('overwrite', 'true');
        comfyFormData.append('subfolder', 'thumbnails'); // Optional: save in subfolder

        console.log(`[SaveThumbnail] Uploading thumbnail to ComfyUI: ${comfyThumbnailFilename}`);
        const comfyUploadResponse = await axios.post(`${COMFYUI_BASE_URL}/upload/image`, comfyFormData, {
            headers: comfyFormData.getHeaders(),
            timeout: 60000 // 1 min timeout
        });

        if (comfyUploadResponse.status === 200 && comfyUploadResponse.data && comfyUploadResponse.data.name) {
            const comfyUploadResult = comfyUploadResponse.data;
            comfyThumbnailUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(comfyUploadResult.name)}&type=${encodeURIComponent(comfyUploadResult.type || 'output')}`;
             if (comfyUploadResult.subfolder) {
                 comfyThumbnailUrl += `&subfolder=${encodeURIComponent(comfyUploadResult.subfolder)}`;
            }
            console.log(`[SaveThumbnail] ComfyUI thumbnail upload successful. URL: ${comfyThumbnailUrl}`);
        } else {
             throw new Error(`ComfyUI thumbnail upload failed: ${comfyUploadResponse.statusText} - ${JSON.stringify(comfyUploadResponse.data)}`);
        }
        // --- Directly Upload Thumbnail Buffer --- END ---

        // --- Update History --- 
        await updateHistoryStatus(historyId, undefined, undefined, undefined, comfyThumbnailUrl);
        console.log(`[SaveThumbnail] Updated history ${historyId} with ComfyUI thumbnail URL.`);
        // --- 
        
        res.json({ 
            success: true, 
            message: '缩略图上传成功',
            thumbnailUrl: comfyThumbnailUrl
        });

    } catch (error) {
        console.error(`[SaveThumbnail] Error processing thumbnail for task ${taskId}:`, error.message);
        res.status(500).json({ 
            success: false, 
            message: '保存缩略图失败',
            error: error.message
        });
    }
});

// 挂载历史记录路由
router.use('/history', historyRouter);

// 动画任务创建代理
router.post('/animate', authenticateToken, async (req, res) => {
    const { type, ...params } = req.body;
    if (!type) {
        return res.status(400).json({ success: false, message: '缺少 type 参数' });
    }

    // 构造 Tripo3D 请求体
    let payload = { type };
    if (type === 'animate_prerigcheck') {
        if (!params.original_model_task_id) {
            return res.status(400).json({ success: false, message: '缺少 original_model_task_id' });
        }
        payload.original_model_task_id = params.original_model_task_id;
    } else if (type === 'animate_rig') {
        if (!params.original_model_task_id) {
            return res.status(400).json({ success: false, message: '缺少 original_model_task_id' });
        }
        payload.original_model_task_id = params.original_model_task_id;
        if (params.out_format) payload.out_format = params.out_format;
        if (params.topology) payload.topology = params.topology;
        if (params.spec) payload.spec = params.spec;
    } else if (type === 'animate_retarget') {
        if (!params.original_model_task_id) {
            return res.status(400).json({ success: false, message: '缺少 original_model_task_id' });
        }
        payload.original_model_task_id = params.original_model_task_id;
        if (params.out_format) payload.out_format = params.out_format;
        if (params.animation) payload.animation = params.animation;
        if (params.bake_animation !== undefined) payload.bake_animation = params.bake_animation;
    } else {
        return res.status(400).json({ success: false, message: '不支持的 type 类型' });
    }

    try {
        const response = await axios.post(
            `${TRIPO_BASE_URL}/openapi/task`,
            payload,
            { headers: { ...TRIPO_HEADERS, 'Content-Type': 'application/json' } }
        );
        if (response.data && response.data.code === 0 && response.data.data && response.data.data.task_id) {
            res.json({ success: true, task_id: response.data.data.task_id });
        } else {
            res.status(500).json({ success: false, message: response.data?.message || 'Tripo3D API 返回异常', data: response.data });
        }
    } catch (error) {
        console.error('Tripo3D 动画API调用失败:', error.message, error.response?.data);
        res.status(500).json({
            success: false,
            message: error.response?.data?.message || error.message || 'Tripo3D API请求失败',
            data: error.response?.data
        });
    }
});

// 动画任务状态查询代理
router.get('/animation-task-status/:taskId', authenticateToken, async (req, res) => {
    const { taskId } = req.params;
    if (!taskId) {
        return res.status(400).json({ success: false, message: '缺少 taskId 参数' });
    }
    try {
        const response = await axios.get(
            `${TRIPO_BASE_URL}/openapi/task/${taskId}`,
            { headers: TRIPO_HEADERS }
        );
        if (response.data && response.data.code === 0 && response.data.data) {
            res.json({ success: true, data: response.data.data });
        } else {
            res.status(500).json({ success: false, message: response.data?.message || 'Tripo3D API 返回异常', data: response.data });
        }
    } catch (error) {
        console.error('Tripo3D 动画任务状态查询失败:', error.message, error.response?.data);
        res.status(500).json({
            success: false,
            message: error.response?.data?.message || error.message || 'Tripo3D API请求失败',
            data: error.response?.data
        });
    }
});

// 根据 taskId 查询历史记录
router.get('/history-by-taskid', authenticateToken, async (req, res) => {
    const { taskId } = req.query;
    const userId = req.user.id;
    
    if (!taskId) {
        return res.status(400).json({ success: false, message: '缺少 taskId 参数' });
    }
    
    try {
        const connection = await pool.getConnection();
        try {
            const [rows] = await connection.query(
                'SELECT id FROM image_to_3d_history WHERE user_id = ? AND tripo_task_id = ? LIMIT 1',
                [userId, taskId]
            );
            
            connection.release();
            
            if (rows.length === 0) {
                return res.json({ success: false, message: '未找到对应的历史记录' });
            }
            
            return res.json({ success: true, historyId: rows[0].id });
        } catch (error) {
            connection.release();
            throw error;
        }
    } catch (error) {
        console.error('查询历史记录失败:', error);
        res.status(500).json({ success: false, message: '查询失败', error: error.message });
    }
});

// 更新动画相关信息
router.post('/update-animation', authenticateToken, async (req, res) => {
    const { historyId, animatedModelUrl, animationType, animationStatus } = req.body;
    const userId = req.user.id;
    
    if (!historyId || !animatedModelUrl) {
        return res.status(400).json({ success: false, message: '缺少必需参数' });
    }
    
    try {
        const connection = await pool.getConnection();
        try {
            // 检查是否是用户自己的历史记录
            const [rows] = await connection.query(
                'SELECT id FROM image_to_3d_history WHERE id = ? AND user_id = ? LIMIT 1',
                [historyId, userId]
            );
            
            if (rows.length === 0) {
                connection.release();
                return res.status(403).json({ success: false, message: '无权修改此历史记录' });
            }
            
            // 更新动画信息
            await connection.query(
                'UPDATE image_to_3d_history SET animated_model_url = ?, animation_type = ?, animation_status = ? WHERE id = ?',
                [animatedModelUrl, animationType, animationStatus, historyId]
            );
            
            connection.release();
            return res.json({ success: true, message: '动画信息更新成功' });
        } catch (error) {
            connection.release();
            throw error;
        }
    } catch (error) {
        console.error('更新动画信息失败:', error);
        res.status(500).json({ success: false, message: '更新失败', error: error.message });
    }
});

// 添加URL下载代理接口 - 用于下载动画模型
router.post('/download-url', authenticateToken, async (req, res) => {
    const { url, filename_prefix } = req.body;
    
    if (!url) {
        return res.status(400).json({ success: false, message: '缺少url参数' });
    }
    
    try {
        // 将请求代理到ComfyUI的下载接口
        const comfyApiEndpoint = `${COMFYUI_BASE_URL}/yzy/download-url`;
        const response = await axios.post(comfyApiEndpoint, {
            url,
            filename_prefix
        }, { timeout: 300000 }); // 5分钟超时
        
        if (response.data && response.data.success) {
            // 构建完整的预览URL
            const { filename, subfolder = 'yzy_downloaded', type = 'input' } = response.data;
            const viewUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(filename)}&type=${encodeURIComponent(type)}&subfolder=${encodeURIComponent(subfolder)}`;
            
            res.json({
                success: true,
                filename: response.data.filename,
                subfolder: response.data.subfolder,
                type: response.data.type,
                view_url: viewUrl
            });
        } else {
            res.status(500).json({
                success: false,
                message: response.data?.message || 'ComfyUI下载失败',
                error: response.data?.error || '未知错误'
            });
        }
    } catch (error) {
        console.error('ComfyUI下载URL失败:', error.message);
        res.status(500).json({
            success: false,
            message: '下载请求失败',
            error: error.message
        });
    }
});

// 完整动画处理流程API（包括后端存储）
router.post('/complete-animation-process', authenticateToken, checkAndDeductCredits('image_to_3d_animation'), async (req, res) => {
    const { originalModelTaskId, animationType = 'walk' } = req.body;
    const userId = req.user.id;
    
    if (!originalModelTaskId) {
        return res.status(400).json({ success: false, message: '缺少原始模型任务ID' });
    }
    
    // 验证动画类型是否在支持列表中
    const supportedAnimations = ['walk', 'run', 'idle', 'climb', 'jump', 'slash', 'shoot', 'hurt', 'fall', 'turn'];
    const finalAnimationType = supportedAnimations.includes(animationType) ? animationType : 'walk';
    
    // 查询历史记录获取historyId
    let historyId = null;
    let historyRecord = null;
    
    try {
        const connection = await pool.getConnection();
        try {
            // 获取历史记录
            const [rows] = await connection.query(
                'SELECT id, tripo_task_id, model_url FROM image_to_3d_history WHERE tripo_task_id = ? AND user_id = ? LIMIT 1',
                [originalModelTaskId, userId]
            );
            
            if (rows.length === 0) {
                connection.release();
                return res.status(404).json({ success: false, message: '找不到对应的模型历史记录' });
            }
            
            historyId = rows[0].id;
            historyRecord = rows[0];
            connection.release();
            
        } catch (error) {
            connection.release();
            throw error;
        }
        
        // 更新动画状态为处理中
        await updateHistoryAnimation(historyId, null, finalAnimationType, 'processing');
        
        // 创建预检任务并立即返回taskId给前端
        const prerigCheckResp = await axios.post(
            `${TRIPO_BASE_URL}/openapi/task`,
            {
                type: 'animate_prerigcheck',
                original_model_task_id: originalModelTaskId
            },
            { headers: { ...TRIPO_HEADERS, 'Content-Type': 'application/json' } }
        );
        
        if (prerigCheckResp.data?.code !== 0 || !prerigCheckResp.data?.data?.task_id) {
            throw new Error(prerigCheckResp.data?.message || 'Tripo3D预检API返回异常');
        }
        
        const precheckTaskId = prerigCheckResp.data.data.task_id;
        
        // 保存任务信息到数据库中，这样前端可以查询后续任务状态
        await pool.query(
            'UPDATE image_to_3d_history SET animation_precheck_task_id = ?, animation_status = ? WHERE id = ?',
            [precheckTaskId, 'processing', historyId]
        );
        
        // 启动后台任务(不阻塞当前请求)
        processAnimationInBackground(precheckTaskId, originalModelTaskId, historyId, finalAnimationType, userId);
        
        // 立即返回预检任务ID给前端
        return res.json({
            success: true,
            message: '动画制作任务已启动',
            animationTaskId: precheckTaskId,
            historyId
        });
    } catch (error) {
        console.error('创建动画任务失败:', error);
        await updateHistoryAnimation(historyId, null, finalAnimationType, 'failed');
        return res.status(500).json({
            success: false,
            message: '动画制作任务启动失败',
            error: error.message
        });
    }
});

// 后台处理动画任务的函数
async function processAnimationInBackground(precheckTaskId, originalModelTaskId, historyId, animationType, userId) {
    try {
        console.log(`开始后台处理动画任务: precheck=${precheckTaskId}, model=${originalModelTaskId}, history=${historyId}`);
        
        // 更新预检任务ID和进度
        await pool.query(
            `UPDATE image_to_3d_history 
             SET animation_precheck_task_id = ?,
                 animation_progress = 10,
                 animation_progress_message = ?
             WHERE id = ?`,
            [precheckTaskId, '正在进行模型预检...', historyId]
        );
        
        // 轮询预检任务状态
        const prerigResult = await pollTripoTask(precheckTaskId);
        
        if (prerigResult.status !== 'success') {
            // 预检失败
            console.error(`动画预检失败: ${prerigResult.error || '未知错误'}`);
            await pool.query(
                `UPDATE image_to_3d_history 
                 SET animation_status = 'failed',
                     animation_progress = 0,
                     animation_progress_message = ?
                 WHERE id = ?`,
                [`预检失败: ${prerigResult.error || '未知错误'}`, historyId]
            );
            return;
        }
        
        const { riggable, topology } = prerigResult.output || {};
        if (!riggable || (topology !== 'bip' && topology !== 'quad')) {
            console.error(`模型不适合自动绑定骨骼: riggable=${riggable}, topology=${topology}`);
            await pool.query(
                `UPDATE image_to_3d_history 
                 SET animation_status = 'failed',
                     animation_progress = 0,
                     animation_progress_message = ?
                 WHERE id = ?`,
                ['模型不适合自动绑定骨骼，动画功能目前仅支持人形或四肢动物', historyId]
            );
            return;
        }
        
        // 更新进度为30%，开始骨骼绑定
        await pool.query(
            `UPDATE image_to_3d_history 
             SET animation_progress = 30,
                 animation_progress_message = ?
             WHERE id = ?`,
            ['预检完成，开始骨骼绑定...', historyId]
        );
        
        // 骨骼绑定
        const rigResp = await axios.post(
            `${TRIPO_BASE_URL}/openapi/task`,
            {
                type: 'animate_rig',
                original_model_task_id: originalModelTaskId,
                out_format: 'glb',
                topology: topology || 'bip',
                spec: (topology === 'bip' || !topology) ? 'tripo' : undefined
            },
            { headers: { ...TRIPO_HEADERS, 'Content-Type': 'application/json' } }
        );
        
        if (rigResp.data?.code !== 0 || !rigResp.data?.data?.task_id) {
            throw new Error(rigResp.data?.message || 'Tripo3D骨骼绑定API返回异常');
        }
        
        const rigTaskId = rigResp.data.data.task_id;
        console.log(`骨骼绑定任务创建成功: rigTaskId=${rigTaskId}`);
        
        // 更新骨骼绑定任务ID
        await pool.query(
            `UPDATE image_to_3d_history 
             SET animation_rig_task_id = ?
             WHERE id = ?`,
            [rigTaskId, historyId]
        );
        
        // 轮询骨骼绑定结果
        const rigResult = await pollTripoTask(rigTaskId);
        
        if (rigResult.status !== 'success' || !rigResult.output || !rigResult.output.model) {
            console.error(`骨骼绑定失败: ${rigResult.error || '未返回模型'}`);
            await pool.query(
                `UPDATE image_to_3d_history 
                 SET animation_status = 'failed',
                     animation_progress = 0,
                     animation_progress_message = ?
                 WHERE id = ?`,
                [`骨骼绑定失败: ${rigResult.error || '未返回模型'}`, historyId]
            );
            return;
        }
        
        // 更新进度为60%，开始应用动画
        await pool.query(
            `UPDATE image_to_3d_history 
             SET animation_progress = 60,
                 animation_progress_message = ?
             WHERE id = ?`,
            ['骨骼绑定完成，开始应用动画...', historyId]
        );
        
        // 动画重定向
        // 根据模型类型选择合适的动画
        let animationPreset = topology === 'quad' ? `preset:quad_${animationType}` : `preset:${animationType}`;
        // 如果四足动物模式下没有对应的预设，回退到walk或run
        if (topology === 'quad' && !['walk', 'run'].includes(animationType)) {
            animationPreset = 'preset:quad_walk'; // 四足动物默认使用walk
        }
        
        // 使用rigResult中的task_id或rigTaskId
        const modelTaskId = rigResult.task_id || rigTaskId;
        console.log(`使用模型任务ID进行动画重定向: ${modelTaskId}`);
        
        const retargetResp = await axios.post(
            `${TRIPO_BASE_URL}/openapi/task`,
            {
                type: 'animate_retarget',
                original_model_task_id: modelTaskId,
                out_format: 'glb',
                animation: animationPreset
            },
            { headers: { ...TRIPO_HEADERS, 'Content-Type': 'application/json' } }
        );
        
        if (retargetResp.data?.code !== 0 || !retargetResp.data?.data?.task_id) {
            throw new Error(retargetResp.data?.message || 'Tripo3D动画重定向API返回异常');
        }
        
        const retargetTaskId = retargetResp.data.data.task_id;
        console.log(`动画重定向任务创建成功: retargetTaskId=${retargetTaskId}`);
        
        // 更新动画重定向任务ID
        await pool.query(
            `UPDATE image_to_3d_history 
             SET animation_retarget_task_id = ?
             WHERE id = ?`,
            [retargetTaskId, historyId]
        );
        
        // 轮询动画重定向结果
        const retargetResult = await pollTripoTask(retargetTaskId);
        
        if (retargetResult.status !== 'success' || !retargetResult.output || !retargetResult.output.model) {
            console.error(`动画重定向失败: ${retargetResult.error || '未返回模型'}`);
            await pool.query(
                `UPDATE image_to_3d_history 
                 SET animation_status = 'failed',
                     animation_progress = 0,
                     animation_progress_message = ?
                 WHERE id = ?`,
                [`动画应用失败: ${retargetResult.error || '未返回模型'}`, historyId]
            );
            return;
        }
        
        // 获取到的动画模型URL
        const animatedModelUrl = retargetResult.output.model;
        console.log(`获取到动画模型URL: ${animatedModelUrl}`);
        
        // 更新进度为90%，开始下载模型
        await pool.query(
            `UPDATE image_to_3d_history 
             SET animation_progress = 90,
                 animation_progress_message = ?
             WHERE id = ?`,
            ['动画应用完成，开始下载模型...', historyId]
        );
        
        // 将动画模型通过ComfyUI下载并存储
        try {
            const modelFilePrefix = `anim_task_${retargetTaskId}_model_`;
            const comfyApiEndpoint = `${COMFYUI_BASE_URL}/yzy/download-url`;
            console.log(`开始下载动画模型: ${animatedModelUrl}`);
            
            const downloadResponse = await axios.post(comfyApiEndpoint, {
                url: animatedModelUrl,
                filename_prefix: modelFilePrefix
            }, { timeout: 300000 }); // 5分钟超时
            
            if (!downloadResponse.data?.success) {
                throw new Error(downloadResponse.data?.message || 'ComfyUI下载动画模型失败');
            }
            
            // 构建ComfyUI可访问的URL
            const { filename, subfolder = 'yzy_downloaded', type = 'input' } = downloadResponse.data;
            const comfyAnimatedModelUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(filename)}&type=${encodeURIComponent(type)}&subfolder=${encodeURIComponent(subfolder)}`;
            console.log(`动画模型下载成功: ${comfyAnimatedModelUrl}`);
            
            // 更新为完成状态 - 使用专门的字段
            await pool.query(
                `UPDATE image_to_3d_history 
                 SET animated_model_url = ?,
                     animation_type = ?,
                     animation_status = 'completed',
                     animation_progress = 100,
                     animation_progress_message = ?
                 WHERE id = ?`,
                [comfyAnimatedModelUrl, animationType, '动画制作完成', historyId]
            );
            
            console.log(`动画处理完成: historyId=${historyId}, 模型URL=${comfyAnimatedModelUrl}`);
        } catch (error) {
            console.error('下载动画模型失败:', error);
            await pool.query(
                `UPDATE image_to_3d_history 
                 SET animation_status = 'failed',
                     animation_progress = 0,
                     animation_progress_message = ?
                 WHERE id = ?`,
                [`下载动画模型失败: ${error.message}`, historyId]
            );
        }
    } catch (error) {
        console.error('后台处理动画任务失败:', error);
        // 更新失败状态
        try {
            await pool.query(
                `UPDATE image_to_3d_history 
                 SET animation_status = 'failed',
                     animation_progress = 0,
                     animation_progress_message = ?
                 WHERE id = ?`,
                [`处理失败: ${error.message}`, historyId]
            );
        } catch (dbError) {
            console.error('更新错误信息到数据库失败:', dbError);
        }
    }
}

// 轮询Tripo3D任务状态的辅助函数
async function pollTripoTask(taskId, maxAttempts = 60, pollInterval = 5000) {
    return new Promise((resolve, reject) => {
        let attempts = 0;
        const intervalId = setInterval(async () => {
            attempts++;
            if (attempts > maxAttempts) {
                clearInterval(intervalId);
                resolve({ status: 'failed', error: '任务轮询超时' });
                return;
            }
            
            try {
                const response = await axios.get(
                    `${TRIPO_BASE_URL}/openapi/task/${taskId}`,
                    { headers: TRIPO_HEADERS }
                );
                
                if (response.data?.code === 0 && response.data?.data) {
                    const taskData = response.data.data;
                    
                    if (taskData.status === "success") {
                        clearInterval(intervalId);
                        resolve(taskData);
                    } else if (taskData.status === "failed" || taskData.status === "cancelled") {
                        clearInterval(intervalId);
                        resolve(taskData);
                    }
                    // 其他状态 (running, pending) 则继续轮询
                }
            } catch (error) {
                console.error(`轮询任务 (ID: ${taskId}) 出错:`, error.message);
                // 网络等错误，继续轮询直到超时
            }
        }, pollInterval);
    });
}

// 更新历史记录中的动画信息
async function updateHistoryAnimation(historyId, animatedModelUrl, animationType, animationStatus, progressMessage = null) {
    try {
        const connection = await pool.getConnection();
        try {
            const updateFields = [];
            const updateValues = [];
            
            if (animatedModelUrl !== null) {
                updateFields.push('animated_model_url = ?');
                updateValues.push(animatedModelUrl);
            }
            
            if (animationType !== null) {
                updateFields.push('animation_type = ?');
                updateValues.push(animationType);
            }
            
            if (animationStatus !== null) {
                updateFields.push('animation_status = ?');
                updateValues.push(animationStatus);
                
                // 对于完成状态，设置进度为100%
                if (animationStatus === 'completed') {
                    updateFields.push('animation_progress = ?');
                    updateValues.push(100);
                    
                    // 如果没有提供进度消息，则设置默认值
                    if (!progressMessage) {
                        updateFields.push('animation_progress_message = ?');
                        updateValues.push('动画制作完成');
                    }
                } else if (animationStatus === 'failed') {
                    // 对于失败状态，重置进度
                    updateFields.push('animation_progress = ?');
                    updateValues.push(0);
                }
            }
            
            // 如果提供了进度消息
            if (progressMessage !== null) {
                updateFields.push('animation_progress_message = ?');
                updateValues.push(progressMessage);
            }
            
            // 检查是否有字段要更新
            if (updateFields.length === 0) {
                connection.release();
                return true;
            }
            
            updateValues.push(historyId);
            
            await connection.query(
                `UPDATE image_to_3d_history SET ${updateFields.join(', ')} WHERE id = ?`,
                updateValues
            );
            
            connection.release();
            return true;
        } catch (error) {
            connection.release();
            throw error;
        }
    } catch (error) {
        console.error('更新动画信息失败:', error);
        return false;
    }
}

// 添加到server/routes/image-to-3d.js
router.get('/animation-progress/:historyId', authenticateToken, async (req, res) => {
    const { historyId } = req.params;
    const userId = req.user.id;
    
    try {
        const connection = await pool.getConnection();
        try {
            const [rows] = await connection.query(
                `SELECT animation_status, animation_progress, animation_progress_message, animated_model_url 
                 FROM image_to_3d_history 
                 WHERE id = ? AND user_id = ?`,
                [historyId, userId]
            );
            
            connection.release();
            
            if (rows.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: '任务不存在或无权访问'
                });
            }
            
            return res.json({
                success: true,
                status: rows[0].animation_status,
                progress: rows[0].animation_progress || 0,
                message: rows[0].animation_progress_message || '',
                animatedModelUrl: rows[0].animated_model_url
            });
        } catch (error) {
            connection.release();
            throw error;
        }
    } catch (error) {
        console.error('获取动画进度失败:', error);
        return res.status(500).json({
            success: false,
            message: '获取进度失败',
            error: error.message
        });
    }
});

module.exports = router; 