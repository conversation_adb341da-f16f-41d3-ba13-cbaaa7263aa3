const express = require('express');
const multer = require('multer');
const fetch = require('node-fetch'); // 使用 node-fetch v2
const FormData = require('form-data'); // 需要安装 form-data
const { authenticateToken } = require('../utils/auth');
const pool = require('../db/pool'); // <-- Correct pool import
const { checkAndDeductCredits } = require('../middleware/checkCredits'); // Import the new middleware factory
require('dotenv').config(); // Ensure environment variables are loaded
const path = require('path'); // 用于获取文件名和处理路径

const router = express.Router();
const UPSCALE_COST = 2; // Define the cost for upscaling - REMOVED as cost is now dynamic

// 使用内存存储，因为我们只是转发文件
const storage = multer.memoryStorage();
const upload = multer({
    storage: storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB 限制 (与前端一致)
    fileFilter: function (req, file, cb) {
        // 允许的图片类型
        const allowedTypes = ['image/png', 'image/jpeg', 'image/webp'];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('无效的文件类型。仅支持 PNG, JPG, WEBP。'), false);
        }
    }
});

// POST /api/creative-upscale/
router.post('/', authenticateToken, upload.single('file'), checkAndDeductCredits('creative_upscale'), async (req, res) => {
    // Pass the feature key 'creative_upscale' to the middleware
    console.log('收到创意放大请求');

    if (!req.file) {
        console.log('错误：请求中未找到文件');
        return res.status(400).json({ error: "请求中必须包含 'file' 文件。" });
    }

    const recraftApiKey = process.env.RECRAFT_API_KEY;
    if (!recraftApiKey) {
        console.error('错误：未配置 RECRAFT_API_KEY 环境变量');
        return res.status(500).json({ error: '服务器配置错误，无法处理请求。' });
    }

    const recraftApiUrl = 'https://external.api.recraft.ai/v1/images/crispUpscale';

    try {
        console.log(`将文件转发到 Recraft API (Crisp Upscale): ${recraftApiUrl}`);
        // 使用 form-data 库来构建 multipart/form-data 请求体
        const formData = new FormData();
        formData.append('file', req.file.buffer, {
            filename: req.file.originalname, // 传递原始文件名
            contentType: req.file.mimetype, // 传递原始 MIME 类型
        });
        // 可以根据需要添加其他参数，例如 response_format
        // formData.append('response_format', 'url'); // 默认为 url，可以省略

        const response = await fetch(recraftApiUrl, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${recraftApiKey}`,
                // 'Content-Type': 'multipart/form-data' // fetch 会自动根据 formData 设置，但 form-data 库可以帮你生成正确的头
                ...formData.getHeaders(), // 从 form-data 获取 headers
            },
            body: formData, // 直接传递 formData 对象
        });

        console.log(`Recraft API 响应状态: ${response.status}`);

        // 检查响应状态码
        if (!response.ok) {
            let errorBody = '';
            try {
                errorBody = await response.text(); // 尝试读取错误响应体
                console.error(`Recraft API 错误响应体: ${errorBody}`);
                 // 尝试解析 JSON
                try {
                    const errorJson = JSON.parse(errorBody);
                    // 返回 Recraft 的错误信息（如果存在）
                    return res.status(response.status).json({ 
                        error: `Recraft API 错误: ${errorJson.message || response.statusText}`,
                        details: errorJson,
                    });
                } catch (jsonError) {
                     // 如果无法解析 JSON，返回原始文本
                    return res.status(response.status).json({ error: `Recraft API 错误: ${response.statusText}`, details: errorBody });
                }
            } catch (readError) {
                console.error('读取 Recraft API 错误响应体失败:', readError);
                 return res.status(response.status).json({ error: `Recraft API 错误: ${response.statusText}` });
            }
           
        }

        // 解析成功的 JSON 响应
        const result = await response.json();
        console.log('Recraft API 成功响应');

        // --- 新增：处理图片并上传到 ComfyUI --- START ---
        let finalImageUrl = result.image && result.image.url; // 默认为 Recraft 的 URL

        if (finalImageUrl) {
            try {
                console.log(`从 Recraft 下载图片: ${finalImageUrl}`);
                const imageDownloadResponse = await fetch(finalImageUrl);
                if (!imageDownloadResponse.ok) {
                    throw new Error(`下载 Recraft 图片失败: ${imageDownloadResponse.statusText}`);
                }
                const imageBuffer = await imageDownloadResponse.buffer(); // 获取图片 buffer

                // 从原始 URL 或请求中获取一个基础文件名
                let baseFileName = req.file.originalname; // 使用用户上传时的文件名作为基础
                try {
                    // 尝试从 Recraft URL 解析文件名（如果更合适）
                    const recraftUrlParts = new URL(finalImageUrl);
                    const recraftPathName = recraftUrlParts.pathname;
                    if (recraftPathName) {
                        const potentialName = path.basename(recraftPathName);
                        if (potentialName) baseFileName = potentialName;
                    }
                } catch (e) {
                    console.warn('无法从 Recraft URL 解析文件名，将使用原始上传文件名。');
                }
                // 创建一个在 ComfyUI 中可能唯一的文件名，并确保有 .png 后缀
                let comfyFileName = `${Date.now()}_${baseFileName}`;
                // 确保文件名有后缀，如果baseFileName已经有后缀，避免重复添加
                if (!path.extname(comfyFileName)) {
                    comfyFileName += '.png'; 
                } else {
                    // 如果 baseFileName 已经包含了类似 .webp 或 .jpeg 的后缀，
                    // 我们这里简单地替换为 .png 或者保留原始后缀并确保ComfyUI能处理。
                    // 为简单起见，我们先统一尝试确保最终是 .png。 
                    // 如果想保留原始，需要更复杂的逻辑判断 recraft 返回类型。
                    // const currentExt = path.extname(comfyFileName);
                    // comfyFileName = comfyFileName.replace(currentExt, '.png');
                    // 更安全的做法是移除现有后缀再添加，避免如 file.jpg.png
                    comfyFileName = comfyFileName.substring(0, comfyFileName.lastIndexOf('.')) + '.png';
                    // 但如果原始就没有点，上面的substring会出错，所以要结合检查
                    if (!path.extname(baseFileName)) { // 如果原始baseFileName就没后缀
                       comfyFileName = `${Date.now()}_${baseFileName}.png`;
                    } else { // 原始baseFileName有后缀
                       comfyFileName = `${Date.now()}_${baseFileName.substring(0, baseFileName.lastIndexOf('.'))}.png`;
                    }
                }

                const comfyUiUploadUrl = 'https://u63642-a2b4-fccb0b0d.westx.seetacloud.com:8443/upload/image';
                const comfyFormData = new FormData();
                comfyFormData.append('image', imageBuffer, comfyFileName);
                comfyFormData.append('type', 'input'); // 根据您的 ComfyUI API
                comfyFormData.append('overwrite', 'true'); // 可选，如果希望覆盖

                console.log(`上传图片到 ComfyUI: ${comfyUiUploadUrl} 文件名: ${comfyFileName}`);
                const comfyUploadResponse = await fetch(comfyUiUploadUrl, {
                    method: 'POST',
                    body: comfyFormData,
                    headers: comfyFormData.getHeaders(),
                });

                if (!comfyUploadResponse.ok) {
                    const comfyErrorText = await comfyUploadResponse.text();
                    throw new Error(`ComfyUI 上传失败: ${comfyUploadResponse.statusText} - ${comfyErrorText}`);
                }

                const comfyUploadResult = await comfyUploadResponse.json();
                console.log('ComfyUI 上传成功:', comfyUploadResult);

                if (comfyUploadResult && comfyUploadResult.name) {
                    let comfyViewUrl = `https://u63642-a2b4-fccb0b0d.westx.seetacloud.com:8443/view?filename=${encodeURIComponent(comfyUploadResult.name)}&type=${encodeURIComponent(comfyUploadResult.type || 'input')}`;
                    if (comfyUploadResult.subfolder) {
                        comfyViewUrl += `&subfolder=${encodeURIComponent(comfyUploadResult.subfolder)}`;
                    }
                    finalImageUrl = comfyViewUrl; // 更新为 ComfyUI 的永久链接
                    console.log(`生成的 ComfyUI 永久链接: ${finalImageUrl}`);
                } else {
                    console.warn('ComfyUI 返回结果中缺少文件名，将继续使用 Recraft URL');
                }

            } catch (comfyError) {
                console.error('处理图片并上传到 ComfyUI 时出错:', comfyError);
                // 如果上传到 ComfyUI 失败，可以选择回退到 Recraft URL，或者返回错误
                // 当前实现是：finalImageUrl 仍然是 Recraft 的 URL
                // 也可以在这里向客户端发送一个特定的警告或错误
            }
        }
        // --- 新增：处理图片并上传到 ComfyUI --- END ---


        // --- 修改：使用 finalImageUrl 保存到 upscale_history --- START ---
        if (result && finalImageUrl) { // 确保 result 存在，并且我们有 finalImageUrl (可能是 Recraft 或 ComfyUI 的)
            try {
                const userId = req.user.id;
                const originalFilename = req.file.originalname;
                // const upscaledUrl = result.image.url; // 旧代码，现在使用 finalImageUrl
                const upscaleType = 'crisp'; // Currently only support crisp

                const connection = await pool.getConnection(); // Get connection from pool
                try {
                    console.log(`正在保存放大历史记录到数据库: userId=${userId}, url=${finalImageUrl}`);
                    await connection.query(
                        'INSERT INTO upscale_history (user_id, original_image_filename, upscaled_image_url, upscale_type) VALUES (?, ?, ?, ?)',
                        [userId, originalFilename, finalImageUrl, upscaleType]
                    );
                    console.log('放大历史记录保存成功');
                } finally {
                    if (connection) connection.release(); // Ensure connection is released
                }
            } catch (dbError) {
                console.error('保存放大历史记录到数据库时出错:', dbError);
                // Log the error but don't block the main response to the user
            }
        }
        // --- 修改：使用 finalImageUrl 保存到 upscale_history --- END ---

        // 将 Recraft 的成功响应转发给客户端，但使用 finalImageUrl
        // res.json(result); // 旧代码
        // 构建新的响应，确保 image.url 是 finalImageUrl
        const clientResponse = {
            ...result, // 保留 Recraft 返回的其他可能数据
            image: {
                ...(result.image || {}), // 保留原始 image 对象的其他属性
                url: finalImageUrl // 确保 URL 是我们最终处理过的 URL
            }
        };
        res.json(clientResponse);

    } catch (error) {
        console.error('调用 Recraft API 时发生内部错误:', error);
        res.status(500).json({ error: '处理图片放大时发生内部错误。', details: error.message });
    }
});

module.exports = router; 