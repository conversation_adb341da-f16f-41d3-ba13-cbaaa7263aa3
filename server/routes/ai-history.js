const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../utils/auth'); // 导入认证中间件
const pool = require('../db/pool'); // 导入数据库连接池

// POST /api/ai/history - 保存 AI 生成历史记录
router.post('/', authenticateToken, async (req, res) => {
  const { prompt, imageUrls, model, width, height, generationTime } = req.body;
  const userId = req.user.id; // 从认证中间件获取用户 ID

  // 验证输入
  if (!prompt || !imageUrls || !Array.isArray(imageUrls) || imageUrls.length === 0) {
    return res.status(400).json({ error: '缺少必要的参数 (prompt, imageUrls)' });
  }

  let connection;
  try {
    connection = await pool.getConnection();
    const imageUrlsString = JSON.stringify(imageUrls); // 将图片 URL 数组转换为 JSON 字符串存储
    const generationTimeMs = generationTime ? parseInt(generationTime, 10) : null; // 解析生成时间

    const [result] = await connection.query(
      'INSERT INTO ai_generation_history (user_id, prompt, image_urls, model, width, height, generation_time_ms) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [userId, prompt, imageUrlsString, model, width, height, generationTimeMs]
    );

    res.status(201).json({ message: '历史记录保存成功', id: result.insertId });

  } catch (error) {
    console.error('保存 AI 生成历史记录时出错:', error);
    res.status(500).json({ error: '保存历史记录失败' });
  } finally {
    if (connection) connection.release();
  }
});

// GET /api/ai/history - 获取当前用户的 AI 生成历史记录 (带分页)
router.get('/', authenticateToken, async (req, res) => {
  const userId = req.user.id;
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10; // 默认每页 10 条
  const offset = (page - 1) * limit;

  let connection;
  try {
    connection = await pool.getConnection();

    // 获取总记录数
    const [totalResult] = await connection.query(
      'SELECT COUNT(*) as total FROM ai_generation_history WHERE user_id = ?',
      [userId]
    );
    const totalRecords = totalResult[0].total;
    const totalPages = Math.ceil(totalRecords / limit);

    // 获取当前页的记录 - 在 SELECT 语句中加入了 mj_task_id
    const [results] = await connection.query(
      'SELECT id, prompt, image_urls, model, width, height, created_at, generation_time_ms, status, comfy_prompt_id, mj_task_id, mj_buttons FROM ai_generation_history WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?',
      [userId, limit, offset]
    );

    // 解析 image_urls JSON 字符串回数组
    const history = results.map(record => {
      let parsedImageUrls = [];
      try {
        const rawValueFromRecord = record.image_urls;
        const rawValue = rawValueFromRecord || '[]';
        if (typeof rawValue === 'string') {
            const trimmedValue = rawValue.trim();
            parsedImageUrls = JSON.parse(trimmedValue);
        } else if (Array.isArray(rawValue)) {
            parsedImageUrls = rawValue;
        } else {
            parsedImageUrls = [];
        }
        if (!Array.isArray(parsedImageUrls)) {
            parsedImageUrls = [];
        }
      } catch (parseError) {
          console.error(`[AI History Error] Failed to parse image_urls for record ID ${record.id}. Value:`, record.image_urls, 'Error:', parseError);
          parsedImageUrls = [];
      }

      let parsedMjButtons = null; // 默认为 null，如果解析失败或无数据
      if (record.mj_buttons) {
        try {
          if (typeof record.mj_buttons === 'string') {
            parsedMjButtons = JSON.parse(record.mj_buttons);
          } else if (Array.isArray(record.mj_buttons)) { // 虽然通常是字符串，但也兼容已经是数组的情况
            parsedMjButtons = record.mj_buttons;
          }
        } catch (parseError) {
          console.error(`[AI History Error] Failed to parse mj_buttons for record ID ${record.id}. Value:`, record.mj_buttons, 'Error:', parseError);
          // 解析失败，保持 parsedMjButtons 为 null 或空数组，视需求而定
          // parsedMjButtons = []; 
        }
      }

      let displayModelName = record.model;
      if (record.model === 'comfyui_imagefx_advanced') {
          displayModelName = '高级生图';
      } else if (record.model === '3.0' || record.model === '2.1') {
          displayModelName = '超级生图';
      } else if (record.model && record.model.startsWith('midjourney')) {
          displayModelName = 'Midjourney';
      }

      return {
        ...record, // 展开 record，它现在包含了 mj_task_id
        image_urls: parsedImageUrls,
        status: record.status || 'UNKNOWN',
        display_model: displayModelName,
        model: record.model, // 原始 model 也返回
        mj_task_id: record.mj_task_id || null, // 显式确保 mj_task_id 被包含和处理
        mj_buttons: parsedMjButtons, // 返回解析后的 mj_buttons
        comfy_prompt_id: record.comfy_prompt_id || null
      };
    });

    res.json({
      history,
      pagination: {
        currentPage: page,
        totalPages,
        totalRecords,
        limit
      }
    });

  } catch (error) {
    console.error('获取 AI 生成历史记录时出错:', error);
    res.status(500).json({ error: '获取历史记录失败' });
  } finally {
    if (connection) connection.release();
  }
});

// DELETE /api/ai/history/:id - 删除单条历史记录
router.delete('/:id', authenticateToken, async (req, res) => {
  const userId = req.user.id; // 从认证中间件获取用户ID
  const historyId = req.params.id;

  if (!historyId) {
    return res.status(400).json({ success: false, error: '缺少历史记录ID' });
  }

  let connection;
  try {
    connection = await pool.getConnection();

    // 先检查记录是否存在且属于当前用户
    const [records] = await connection.query(
      'SELECT id FROM ai_generation_history WHERE id = ? AND user_id = ?',
      [historyId, userId]
    );

    if (records.length === 0) {
      return res.status(404).json({ 
        success: false, 
        error: '未找到指定的历史记录或您无权删除此记录' 
      });
    }

    // 删除记录
    await connection.query(
      'DELETE FROM ai_generation_history WHERE id = ? AND user_id = ?',
      [historyId, userId]
    );

    res.json({
      success: true,
      message: '历史记录已成功删除'
    });

  } catch (error) {
    console.error('删除 AI 生成历史记录时出错:', error);
    res.status(500).json({ 
      success: false,
      error: '删除历史记录失败', 
      details: error.message 
    });
  } finally {
    if (connection) connection.release();
  }
});

module.exports = router; 