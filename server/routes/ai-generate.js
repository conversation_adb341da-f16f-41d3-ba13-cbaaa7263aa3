const express = require('express');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config(); // 确保能读取 .env
const { authenticateToken } = require('../utils/auth');
const { checkAndDeductCredits } = require('../middleware/checkCredits'); // Import the new middleware factory
const pool = require('../db/pool'); // <<< Ensure pool is required
// console.log('>>>> [Debug in ai-generate.js] typeof checkCreditsForGeneration:', typeof checkCreditsForGeneration); // 移除或注释掉这行

// +++ ComfyUI Configuration +++
// const COMFYUI_BASE_URL = 'https://u63642-8e01-0d92b3a9.cqa1.seetacloud.com:8443'; // Your ComfyUI URL - Replaced by env variable
const COMFYUI_BASE_URL = process.env.COMFYUI_GPU_URL;
const COMFYUI_ADVANCED_GENERATION_WORKFLOW_NAME = 'comfy_高级生图工作流'; // Name in DB

if (!COMFYUI_BASE_URL) {
    console.error('[AI Generate Init] ERROR: COMFYUI_GPU_URL environment variable is not set. Please set it in your .env file.');
    // Optionally, you could throw an error here or use a default, but exiting or logging is safer for critical URLs.
}

// Helper function to get workflow from DB (similar to imageUtils.js)
async function getDbWorkflowByName(workflowName) {
  let connection;
  try {
    connection = await pool.getConnection();
    const [rows] = await connection.query('SELECT workflow_json FROM workflows WHERE name = ?', [workflowName]);
    if (rows.length === 0) {
      throw new Error(`未在数据库中找到名为 "${workflowName}" 的工作流`);
    }
    const workflowJson = rows[0].workflow_json; // Assuming it's already an object or correctly parsed string
    if (typeof workflowJson === 'string') {
        try {
            return JSON.parse(workflowJson);
        } catch (e) {
            console.error(`[AI Generate] Error parsing workflow_json for ${workflowName}:`, e);
            throw new Error(`工作流 "${workflowName}" 的JSON格式无效。`);
        }
    } else if (typeof workflowJson === 'object' && workflowJson !== null) {
        return workflowJson; // Already an object
    }
    throw new Error(`工作流 "${workflowName}" 的格式无法识别。`);
  } catch (error) {
    console.error(`[AI Generate] 获取工作流 "${workflowName}" 失败:`, error);
    throw error;
  } finally {
    if (connection) connection.release();
  }
}

const comfyUIWorkflowTemplate = { // The workflow you provided
  "10": {
    "inputs": {
      "prompt": [
        "13",
        0
      ],
      "seed": 950312, // Will be randomized
      "aspect_ratio": "16:9 (Landscape)", // Will be set dynamically
      "num_images": 1,
      "speak_and_recognation": {
        "__value__": [
          false,
          true
        ]
      }
    },
    "class_type": "ComfyUI-ImageFx",
    "_meta": {
      "title": "ComfyUI-ImageFx🖼️"
    }
  },
  "12": {
    "inputs": {
      "filename_prefix": "ComfyUI_Generated", // Changed prefix for clarity
      "images": [
        "10",
        0
      ]
    },
    "class_type": "SaveImage",
    "_meta": {
      "title": "保存图像"
    }
  },
  "13": {
    "inputs": {
      "prompt": [
        "14",
        0
      ],
      "api_key": "d94b3866a677983fd06d77db1db69ce0.jkPnbuvu1cStZK1l", // This API key is in the workflow
      "model": "glm-4-flash",
      "speak_and_recognation": {
        "__value__": [
          false,
          true
        ]
      }
    },
    "class_type": "ZhipuAINode",
    "_meta": {
      "title": "Zhipu AI GLM-4V"
    }
  },
  "14": {
    "inputs": {
      "action": "append",
      "tidy_tags": "yes",
      "text_a": "你需要将我输入的内容翻译成英语，最后只输出翻译内容，不要解释。",
      "text_b": "一只猪", // Will be user's prompt
      "text_c": "",
      "speak_and_recognation": {
        "__value__": [
          false,
          true
        ]
      },
      "result": "你需要将我输入的内容翻译成英语，最后只输出翻译内容，不要解释。, 一只猪"
    },
    "class_type": "StringFunction|pysssss",
    "_meta": {
      "title": "字符串操作"
    }
  }
};

const comfyUIAspectRatioMap = {
    "2016x864": "21:9 (Landscape)",  // 21:9
    "1664x936": "16:9 (Landscape)",  // 16:9
    "1584x1056": "3:2 (Landscape)",   // 3:2
    "1472x1104": "4:3 (Landscape)",   // 4:3
    "1328x1328": "1:1 (Square)",      // 1:1
    "1104x1472": "3:4 (Portrait)",    // 3:4
    "1056x1584": "2:3 (Portrait)",    // 2:3
    "936x1664": "9:16 (Portrait)"     // 9:16
};

function mapDimensionsToComfyUIAspectRatio(width, height) {
    const key = `${width}x${height}`;
    return comfyUIAspectRatioMap[key] || "1:1 (Square)"; // Default to 1:1 if no match
}
// +++ End ComfyUI Configuration +++

const router = express.Router();
// const AI_GENERATION_COST = 2; // Define the cost for AI generation - REMOVED

console.log(`[AI Generate Init] Process ID: ${process.pid} - Module loaded and currentAccountIndex initialized.`); // 新增日志

// --- 读取多个即梦账号配置 ---
const JIMENG_ACCOUNT_COUNT = parseInt(process.env.JIMENG_ACCOUNT_COUNT, 10) || 1;
console.log(`[AI Generate Init] Process ID: ${process.pid} - JIMENG_ACCOUNT_COUNT: ${JIMENG_ACCOUNT_COUNT}`); // 新增日志
const jimengAccounts = [];

for (let i = 0; i < JIMENG_ACCOUNT_COUNT; i++) {
    // 新增：打印尝试读取的每个环境变量的值
    console.log(`[AI Generate Init - Loading Account ${i}] Process ID: ${process.pid} - Attempting to load values:`);
    console.log(`  DEFAULT_COOKIE_${i}: '${process.env[`DEFAULT_COOKIE_${i}`]}'`);
    console.log(`  MS_TOKEN_${i}: '${process.env[`MS_TOKEN_${i}`]}'`);
    console.log(`  A_BOGUS_${i}: '${process.env[`A_BOGUS_${i}`]}'`);
    console.log(`  WEB_ID_${i}: '${process.env[`WEB_ID_${i}`]}'`);

    const account = {
        cookie: process.env[`DEFAULT_COOKIE_${i}`],
        msToken: process.env[`MS_TOKEN_${i}`],
        aBogus: process.env[`A_BOGUS_${i}`],
        webId: process.env[`WEB_ID_${i}`],
    };

    if (account.cookie && account.webId) {
        jimengAccounts.push(account);
        console.log(`[AI Generate Init] Process ID: ${process.pid} - Successfully loaded Jimeng account config ${i}. MS_TOKEN and A_BOGUS are optional for loading.`);
    } else {
        console.warn(`[AI Generate Init] Process ID: ${process.pid} - Jimeng account ${i} config incomplete (missing COOKIE or WEB_ID), skipping. Check .env: DEFAULT_COOKIE_${i}, WEB_ID_${i}`);
    }
}

console.log(`[AI Generate Init] Process ID: ${process.pid} - Total Jimeng accounts loaded: ${jimengAccounts.length}`); // 新增日志

if (jimengAccounts.length === 0) {
    console.error(`[AI Generate Init] Process ID: ${process.pid} - ERROR: No usable Jimeng account configurations found (COOKIE and WEB_ID are required). Please check .env file.`);
}

let currentAccountIndex = 0;
console.log(`[AI Generate Init] Process ID: ${process.pid} - Initial currentAccountIndex: ${currentAccountIndex}`); // 新增日志

// --- 辅助函数：获取当前轮询到的账号 ---
function getCurrentJimengAccount() {
    console.log(`[AI Generate GetAccount] Process ID: ${process.pid} - Entering getCurrentJimengAccount. Current currentAccountIndex before selection: ${currentAccountIndex}`); // 新增日志
    if (jimengAccounts.length === 0) {
        console.error(`[AI Generate GetAccount] Process ID: ${process.pid} - No accounts available!`);
        return null;
    }
    const accountToUse = jimengAccounts[currentAccountIndex];
    const usedIndex = currentAccountIndex;
    currentAccountIndex = (currentAccountIndex + 1) % jimengAccounts.length;
    console.log(`[AI Generate GetAccount] Process ID: ${process.pid} - Selected account at index: ${usedIndex}. Next index will be: ${currentAccountIndex}.`);
    return accountToUse;
}

// --- 辅助函数 --- 
function generateCustomUUID() {
    const uuid = uuidv4();
    return uuid.replace(/-/g, '').substring(0, 32);
}

// +++ 新增：处理中ComfyUI任务的状态常量 +++
const TASK_STATUS = {
    PENDING: 'PROCESSING',    // 任务已提交，正在等待或处理中
    COMPLETED: 'COMPLETED',   // 任务已完成，有结果
    FAILED: 'FAILED'          // 任务失败
};
// +++ 结束新增 +++

// +++ 新增：轮询间隔和最大尝试次数 +++
const POLLING_INTERVAL_MS = 10000; // 10秒钟轮询一次
const MAX_POLLING_ATTEMPTS = 180;   // 最多轮询180次 (约30分钟)
// +++ 结束新增 +++

// +++ 新增：保存ComfyUI生成任务到历史记录的函数 +++
async function saveComfyUITaskToHistory(userId, prompt, model, prompt_id, status = TASK_STATUS.PENDING, imageUrls = []) {
    let connection;
    try {
        connection = await pool.getConnection();
        
        // 检查是否已有相同 prompt_id 的记录
        const [existingRecords] = await connection.query(
            'SELECT id FROM ai_generation_history WHERE user_id = ? AND comfy_prompt_id = ?',
            [userId, prompt_id]
        );
        
        if (existingRecords.length > 0) {
            // 如果已存在记录，则更新
            const historyId = existingRecords[0].id;
            await connection.query(
                'UPDATE ai_generation_history SET status = ?, image_urls = ? WHERE id = ?',
                [status, JSON.stringify(imageUrls), historyId]
            );
            console.log(`[AI Generate] Updated history record ${historyId} for ComfyUI task ${prompt_id}, status: ${status}, imageUrls count: ${imageUrls.length}`);
            return historyId;
        } else {
            // 如果不存在，则创建新记录
            const [result] = await connection.query(
                'INSERT INTO ai_generation_history (user_id, prompt, model, comfy_prompt_id, status, image_urls, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())',
                [userId, prompt, model, prompt_id, status, JSON.stringify(imageUrls)]
            );
            console.log(`[AI Generate] Created new history record ${result.insertId} for ComfyUI task ${prompt_id}, status: ${status}`);
            return result.insertId;
        }
    } catch (error) {
        console.error(`[AI Generate] Error saving ComfyUI task to history:`, error);
        throw error;
    } finally {
        if (connection) connection.release();
    }
}

// 查询所有处理中的ComfyUI任务并更新状态
async function pollPendingComfyUITasks() {
    let connection;
    try {
        connection = await pool.getConnection();
        
        // 获取所有状态为"处理中"且尝试次数小于最大值的ComfyUI任务
        const [pendingTasks] = await connection.query(
            'SELECT id, user_id, prompt, model, comfy_prompt_id, polling_attempts FROM ai_generation_history WHERE status = ? AND comfy_prompt_id IS NOT NULL AND polling_attempts < ?',
            [TASK_STATUS.PENDING, MAX_POLLING_ATTEMPTS]
        );
        
        console.log(`[AI Generate Polling] Found ${pendingTasks.length} pending ComfyUI tasks to poll.`);
        
        if (pendingTasks.length === 0) {
            return; // 如果没有待处理任务，直接返回
        }
        
        // 首先获取ComfyUI的队列状态，避免每个任务都单独查询
        let queueInfo = null;
        try {
            const queueResponse = await axios.get(`${COMFYUI_BASE_URL}/prompt`);
            if (queueResponse.data && queueResponse.data.exec_info) {
                queueInfo = queueResponse.data.exec_info;
                console.log(`[AI Generate Polling] Current ComfyUI queue status: ${queueInfo.queue_remaining} task(s) remaining in queue`);
            }
        } catch (queueError) {
            console.error(`[AI Generate Polling] Error fetching ComfyUI queue status:`, queueError.message);
            // 如果无法获取队列状态，继续使用原有的历史记录检查方法
        }
        
        // 逐个查询任务状态
        for (const task of pendingTasks) {
            try {
                const actualComfyId = task.comfy_prompt_id.startsWith('comfy_') ? 
                    task.comfy_prompt_id.substring(6) : task.comfy_prompt_id;
                
                console.log(`[AI Generate Polling] Polling ComfyUI task ${actualComfyId} (history ID: ${task.id}, attempt: ${task.polling_attempts + 1}/${MAX_POLLING_ATTEMPTS})`);
                
                // 查询ComfyUI历史记录
                const historyResponse = await axios.get(`${COMFYUI_BASE_URL}/history/${actualComfyId}`);
                const historyData = historyResponse.data;
                
                // 更新轮询尝试次数
                await connection.query(
                    'UPDATE ai_generation_history SET polling_attempts = polling_attempts + 1 WHERE id = ?',
                    [task.id]
                );
                
                if (historyData && historyData[actualComfyId]) {
                    const promptEntry = historyData[actualComfyId];
                    const statusData = promptEntry.status;
                    
                    if (statusData && statusData.completed === true && statusData.status_str === 'success') {
                        // 任务成功完成
                        const outputs = promptEntry.outputs;
                        let imageUrls = [];
                        
                        // 提取图片URL
                        if (outputs) {
                            for (const nodeId in outputs) {
                                if (outputs[nodeId] && outputs[nodeId].images && outputs[nodeId].images.length > 0) {
                                    outputs[nodeId].images.forEach(imageInfo => {
                                        const imageUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(imageInfo.filename)}&subfolder=${encodeURIComponent(imageInfo.subfolder || '')}&type=${imageInfo.type}`;
                                        imageUrls.push(imageUrl);
                                    });
                                }
                            }
                        }
                        
                        if (imageUrls.length > 0) {
                            // 任务成功，更新历史记录
                            await connection.query(
                                'UPDATE ai_generation_history SET status = ?, image_urls = ? WHERE id = ?',
                                [TASK_STATUS.COMPLETED, JSON.stringify(imageUrls), task.id]
                            );
                            console.log(`[AI Generate Polling] ComfyUI task ${actualComfyId} completed successfully with ${imageUrls.length} images. Updated history ID: ${task.id}`);

                            // +++ DEBUGGING: Read back the record after update +++
                            const [updatedRecordArray] = await connection.query('SELECT * FROM ai_generation_history WHERE id = ?', [task.id]);
                            if (updatedRecordArray && updatedRecordArray.length > 0) {
                                console.log(`[AI Generate Polling - DEBUG] Record ${task.id} after successful update:`, JSON.stringify(updatedRecordArray[0]));
                            } else {
                                console.log(`[AI Generate Polling - DEBUG] Record ${task.id} NOT FOUND after successful update attempt!`);
                            }
                            // +++ END DEBUGGING +++
                        } else {
                            // 任务完成但没有图片
                            await connection.query(
                                'UPDATE ai_generation_history SET status = ? WHERE id = ?',
                                [TASK_STATUS.FAILED, task.id]
                            );
                            console.log(`[AI Generate Polling] ComfyUI task ${actualComfyId} completed but no images found. Updated history ID: ${task.id} as failed.`);
                            // +++ DEBUGGING: Read back the record after update +++
                            const [updatedRecordArray] = await connection.query('SELECT * FROM ai_generation_history WHERE id = ?', [task.id]);
                            if (updatedRecordArray && updatedRecordArray.length > 0) {
                                console.log(`[AI Generate Polling - DEBUG] Record ${task.id} after failed (no image) update:`, JSON.stringify(updatedRecordArray[0]));
                            } else {
                                console.log(`[AI Generate Polling - DEBUG] Record ${task.id} NOT FOUND after failed (no image) update attempt!`);
                            }
                            // +++ END DEBUGGING +++
                        }
                    } else if (statusData && statusData.status_str === 'failed') {
                        // 任务失败
                        await connection.query(
                            'UPDATE ai_generation_history SET status = ? WHERE id = ?',
                            [TASK_STATUS.FAILED, task.id]
                        );
                        console.log(`[AI Generate Polling] ComfyUI task ${actualComfyId} failed. Updated history ID: ${task.id}`);
                    } else if (statusData && statusData.status_str === 'running') {
                        // 任务正在执行中，不需要做任何更改，继续等待
                        console.log(`[AI Generate Polling] ComfyUI task ${actualComfyId} is currently running.`);
                    } else if (task.polling_attempts + 1 >= MAX_POLLING_ATTEMPTS) {
                        // 达到最大轮询次数，标记为失败
                        await connection.query(
                            'UPDATE ai_generation_history SET status = ? WHERE id = ?',
                            [TASK_STATUS.FAILED, task.id]
                        );
                        console.log(`[AI Generate Polling] ComfyUI task ${actualComfyId} timed out after ${MAX_POLLING_ATTEMPTS} attempts. Updated history ID: ${task.id} as failed.`);
                    }
                    // 如果任务仍在处理中且未达到最大尝试次数，则保持状态不变，下次继续轮询
                } else {
                    // 历史记录中没有找到任务，检查是否在队列中
                    console.log(`[AI Generate Polling] ComfyUI task ${actualComfyId} not found in history. Checking if it's in queue...`);
                    
                    // 检查队列状态
                    const isInQueue = queueInfo && queueInfo.queue_remaining > 0;
                    
                    if (isInQueue) {
                        console.log(`[AI Generate Polling] ComfyUI task ${actualComfyId} is likely in queue (queue_remaining: ${queueInfo.queue_remaining}). Will continue polling.`);
                        // 任务可能在队列中，继续等待
                    } else if (task.polling_attempts + 1 >= MAX_POLLING_ATTEMPTS) {
                        // 达到最大轮询次数且确认不在队列中，标记为失败
                        await connection.query(
                            'UPDATE ai_generation_history SET status = ? WHERE id = ?',
                            [TASK_STATUS.FAILED, task.id]
                        );
                        console.log(`[AI Generate Polling] ComfyUI task ${actualComfyId} not found in history or queue after ${MAX_POLLING_ATTEMPTS} attempts. Updated history ID: ${task.id} as failed.`);
                    } else {
                        // 未达到最大轮询次数，但不确定是否在队列中，继续等待
                        console.log(`[AI Generate Polling] ComfyUI task ${actualComfyId} not found in history, queue status unclear. Will continue polling.`);
                    }
                }
            } catch (error) {
                console.error(`[AI Generate Polling] Error polling ComfyUI task ${task.comfy_prompt_id}:`, error.message);
                
                // 如果达到最大轮询次数，标记为失败
                if (task.polling_attempts + 1 >= MAX_POLLING_ATTEMPTS) {
                    await connection.query(
                        'UPDATE ai_generation_history SET status = ? WHERE id = ?',
                        [TASK_STATUS.FAILED, task.id]
                    );
                    console.log(`[AI Generate Polling] Error polling ComfyUI task ${task.comfy_prompt_id} after ${MAX_POLLING_ATTEMPTS} attempts. Updated history ID: ${task.id} as failed.`);
                }
            }
        }
    } catch (error) {
        console.error('[AI Generate Polling] Error polling pending ComfyUI tasks:', error);
    } finally {
        if (connection) connection.release();
    }
}

// 启动轮询进程
function startPollingProcess() {
    console.log(`[AI Generate Init] Starting ComfyUI task polling process with interval ${POLLING_INTERVAL_MS}ms`);
    
    // 立即执行一次
    pollPendingComfyUITasks().catch(err => {
        console.error('[AI Generate Polling] Error in initial polling:', err);
    });
    
    // 设置定期执行
    setInterval(() => {
        pollPendingComfyUITasks().catch(err => {
            console.error('[AI Generate Polling] Error in scheduled polling:', err);
        });
    }, POLLING_INTERVAL_MS);
}

// 模块加载时启动轮询进程
startPollingProcess();
// +++ 结束新增 +++

// --- 路由处理 --- 

// POST /api/ai/generate
router.post('/generate', authenticateToken, checkAndDeductCredits('ai_generate'), async (req, res) => {
    console.log(`[AI Generate Route /generate] Process ID: ${process.pid} - Request received.`); // 新增日志
    
    const { prompt, model, width, height, num_images } = req.body;

    if (!prompt) {
        return res.status(400).json({ success: false, error: '缺少提示词 (prompt)' });
    }

    if (model === 'comfyui_imagefx_advanced') {
        console.log(`[AI Generate Route /generate] Process ID: ${process.pid} - Handling ComfyUI request.`);
        try {
            // Expect aspect_ratio_string directly from frontend for this model
            const { aspect_ratio_string } = req.body; 
            if (!aspect_ratio_string) {
                return res.status(400).json({ success: false, error: '高级生图模式缺少 aspect_ratio_string 参数。' });
            }

            const workflowTemplateFromDb = await getDbWorkflowByName(COMFYUI_ADVANCED_GENERATION_WORKFLOW_NAME);
            const workflow = JSON.parse(JSON.stringify(workflowTemplateFromDb)); // Deep clone

            // Modify workflow:
            // 1. User Prompt
            workflow["14"].inputs.text_b = prompt; // prompt is already extracted from req.body
            // 2. Seed
            workflow["10"].inputs.seed = Math.floor(Math.random() * 1000000); // Changed to generate between 0 and 999999
            // 3. Aspect Ratio - directly use the provided string
            workflow["10"].inputs.aspect_ratio = aspect_ratio_string;
            // 4. Number of Images
            workflow["10"].inputs.num_images = num_images ? parseInt(num_images, 10) : 1; // Use provided num_images or default to 1

            console.log(`[AI Generate Route /generate] Process ID: ${process.pid} - Modified ComfyUI workflow with prompt: ${prompt.substring(0,30)}..., aspect_ratio: ${workflow["10"].inputs.aspect_ratio}, num_images: ${workflow["10"].inputs.num_images}`);

            const comfyUIResponse = await axios.post(`${COMFYUI_BASE_URL}/prompt`, { prompt: workflow, client_id: uuidv4() }); // Added client_id

            if (comfyUIResponse.data && comfyUIResponse.data.prompt_id) {
                const promptId = comfyUIResponse.data.prompt_id;
                const fullPromptId = `comfy_${promptId}`;
                console.log(`[AI Generate Route /generate] Process ID: ${process.pid} - ComfyUI task submitted. Prompt ID: ${promptId}`);
                
                // +++ 修改：提交任务后立即创建历史记录 +++
                try {
                    const userId = req.user.id; // 从认证中间件获取用户ID
                    await saveComfyUITaskToHistory(
                        userId,
                        prompt,
                        model,
                        fullPromptId,
                        TASK_STATUS.PENDING,
                        [] // 空图片数组，表示还没有生成结果
                    );
                    console.log(`[AI Generate Route /generate] Process ID: ${process.pid} - Created initial history record for ComfyUI task ${promptId} for user ${userId}`);
                } catch (historyError) {
                    // 即使保存历史记录失败，也继续返回任务ID给前端（不阻止用户获取任务ID）
                    console.error(`[AI Generate Route /generate] Process ID: ${process.pid} - Error saving initial history for ComfyUI task:`, historyError);
                    // 如果需要，可以在这里添加更多错误处理逻辑
                }
                // +++ 结束修改 +++
                
                // Return in a format that frontend's polling can use, consistent with Jimeng's successful response structure
                res.json({
                    success: true, // Ensure success field is true
                    aigc_data: { 
                        history_record_id: fullPromptId // Prefix to identify ComfyUI tasks
                    }
                    // Optionally include original prompt and model for consistency if frontend uses them
                    // prompt: prompt, 
                    // model: model 
                });
            } else {
                console.error(`[AI Generate Route /generate] Process ID: ${process.pid} - ComfyUI task submission failed or unexpected response:`, comfyUIResponse.data);
                throw new Error('ComfyUI task submission failed.');
            }
        } catch (error) {
            console.error(`[AI Generate Route /generate] Process ID: ${process.pid} - Error during ComfyUI generation:`, error.response ? error.response.data : error.message);
            res.status(500).json({
                success: false,
                error: '高级生图请求失败',
                details: error.response ? error.response.data : error.message
            });
        }
        return; // End ComfyUI processing
    }

    // --- Existing Jimeng Logic ---
    const selectedAccount = getCurrentJimengAccount();
    if (!selectedAccount) {
        console.error(`[AI Generate Route /generate] Process ID: ${process.pid} - No usable Jimeng account for generate request.`);
        return res.status(500).json({
            success: false,
            error: '服务器配置不完整或无可用账号，无法处理生成请求。'
        });
    }

    // 对于 /generate 接口，MS_TOKEN 和 A_BOGUS 通常是必需的。
    // 我们可以在这里检查，如果选中的账号缺少这些值，则返回错误或尝试下一个账号（如果实现更复杂的轮询）。
    // 为简单起见，这里先假设如果 msToken 或 aBogus 为空/未定义，请求可能会失败，由即梦API决定。
    if (!selectedAccount.cookie || !selectedAccount.webId ) { // 再次确认基础项
        console.error(`[AI Generate Route /generate] Process ID: ${process.pid} - Selected Jimeng account is missing COOKIE or WEB_ID.`);
        return res.status(500).json({
            success: false,
            error: '服务器选中的账号配置不完整（COOKIE或WEB_ID缺失），无法处理请求。'
        });
    }
    // 如果msToken或aBogus不存在，params中的对应字段会是undefined，axios会忽略它们或发送空值

    try {
        const targetUrl = 'https://jimeng.jianying.com/mweb/v1/aigc_draft/generate';
        
        // 生成新的UUID
        const submitId = uuidv4();
        const componentId = generateCustomUUID();
        const imageComponentId = generateCustomUUID();
        const abilitiesId = generateCustomUUID();
        const generateId = generateCustomUUID();
        const coreParamId = generateCustomUUID();
        const largeImageInfoId = generateCustomUUID();
        const historyOptionId = generateCustomUUID();

        // 从请求体获取参数
        const requestedModel = model === "2.1" ? "high_aes_general_v21_L:general_v2.1_L" : "high_aes_general_v30l:general_v3.0_18b";
        const imageWidth = parseInt(width) || 1024;
        const imageHeight = parseInt(height) || 1024;

        // 构建请求体 (与 jimeng.js 类似)
        const requestBody = {
            extend: {
                root_model: requestedModel,
                template_id: ""
            },
            submit_id: submitId,
            metrics_extra: JSON.stringify({
                templateId: '',
                generateCount: 1,
                promptSource: 'custom',
                templateSource: '',
                lastRequestId: '',
                originRequestId: '',
                originSubmitId: '',
                isDefaultSeed: 1,
                originTemplateId: '',
                imageNameMapping: {},
                isUseAiGenPrompt: false,
                batchNumber: 1
            }),
            draft_content: JSON.stringify({
                type: 'draft',
                id: componentId,
                min_version: '3.0.2',
                min_features: [],
                is_from_tsn: true,
                version: '3.1.5',
                main_component_id: imageComponentId,
                component_list: [{
                    type: 'image_base_component',
                    id: imageComponentId,
                    min_version: '3.0.2',
                    generate_type: 'generate',
                    aigc_mode: 'workbench',
                    abilities: {
                        type: '',
                        id: abilitiesId,
                        generate: {
                            type: '',
                            id: generateId,
                            core_param: {
                                type: '',
                                id: coreParamId,
                                model: requestedModel,
                                prompt: prompt,
                                negative_prompt: '', // 可以考虑从前端传入
                                seed: Math.floor(Math.random() * 1000000000),
                                sample_strength: 0.5, // 可以考虑从前端传入
                                image_ratio: 1, // 可以根据宽高计算或从前端传入
                                large_image_info: {
                                    type: '',
                                    id: largeImageInfoId,
                                    height: imageHeight,
                                    width: imageWidth,
                                    resolution_type: '1k' // 可以根据宽高调整
                                }
                            },
                            history_option: {
                                type: '',
                                id: historyOptionId
                            }
                        }
                    }
                }]
            }),
            http_common_info: {
                aid: "513695"
            }
        };

        // 设置请求参数 (与 jimeng.js 类似)
        const params = {
            babi_param: encodeURIComponent(JSON.stringify({
                scenario: "image_video_generation",
                feature_key: "aigc_to_image",
                feature_entrance: "to_image",
                feature_entrance_detail: `to_image-${requestedModel}`
            })),
            aid: "513695",
            device_platform: "web",
            region: "CN",
            web_id: selectedAccount.webId,     // 使用选中账号的 WEB_ID
            msToken: selectedAccount.msToken,  // 使用选中账号的 MS_TOKEN
            a_bogus: selectedAccount.aBogus,   // 使用选中账号的 A_BOGUS
            // count: 20, // 这些参数在 generate 请求中可能不是必需的
            // cursor: "0"
        };

        // 生成时间戳
        const timestamp = Math.floor(Date.now() / 1000).toString();

        // 设置请求头 (与 jimeng.js 类似)
        const headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'appvr': '5.8.0',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'device-time': timestamp,
            'origin': 'https://jimeng.jianying.com',
            'pf': '7',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://jimeng.jianying.com/ai-tool/image/generate',
            'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"', // 根据实际情况考虑是否需要修改
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36', // 根据实际情况考虑是否需要修改
            'Cookie': selectedAccount.cookie  // 使用选中账号的 COOKIE
        };

        // 发送请求到目标服务器
        console.log(`[AI Generate Route /generate] Process ID: ${process.pid} - Sending generate request to Jimeng with selected account. Prompt: ${req.body.prompt.substring(0, 30)}...`);
        const response = await axios.post(targetUrl, requestBody, { 
            headers,
            params 
        });
        // 修改日志，确保记录完整的 response.data
        console.log(`[AI Generate Route /generate] Process ID: ${process.pid} - Jimeng response status: ${response.status}, data:`, JSON.stringify(response.data)); 
        
        // 修改开始：处理即梦响应并返回给前端
        if (response.data && response.data.ret === '0') {
            // 根据最新的日志，正确的路径是 response.data.data.aigc_data.history_record_id
            const outerData = response.data.data;
            const aigcData = outerData && outerData.aigc_data ? outerData.aigc_data : null;
            const jimengHistoryId = aigcData && aigcData.history_record_id ? aigcData.history_record_id : null;

            if (jimengHistoryId) {
                console.log(`[AI Generate Route /generate] Process ID: ${process.pid} - Jimeng task successful. Using Jimeng History ID for polling: ${jimengHistoryId}`);
                res.json({
                    success: true,
                    aigc_data: { 
                        history_record_id: jimengHistoryId
                    }
                });
            } else {
                // 更新错误日志中的路径，以反映我们尝试的正确路径
                console.error(`[AI Generate Route /generate] Process ID: ${process.pid} - Jimeng returned ret: '0' but no history_record_id found in response.data.data.aigc_data. Response:`, response.data);
                res.status(500).json({ 
                    success: false, 
                    error: 'AI服务成功接收任务，但未能获取任务ID进行后续查询。',
                    details: 'Missing history_record_id in Jimeng response data field' // Added missing quote
                });
            }
        } else {
            console.error(`[AI Generate Route /generate] Process ID: ${process.pid} - Jimeng API returned an error or unexpected response:`, response.data);
            let userErrorMessage = 'AI服务返回错误，请稍后再试。';
            if (response.data && typeof response.data.errmsg === 'string') { // Check type of errmsg
                userErrorMessage = `AI服务错误: ${response.data.errmsg}`;
                // 确保 ret 是字符串再比较
                if (String(response.data.ret) === '22113') { 
                    userErrorMessage = '提示词可能包含敏感内容，请修改后重试。';
                } else if (String(response.data.ret) === '22107') {
                    userErrorMessage = '当前系统繁忙（AI任务过多），请稍后再试。';
                }
            }
            res.status(response.status >= 400 ? response.status : 500).json({ 
                success: false, 
                error: userErrorMessage,
                details: response.data 
            });
        }
        // 修改结束

    } catch (error) {
        console.error(`[AI Generate Route /generate] Process ID: ${process.pid} - Error:`, error.response ? error.response.data : error.message);
        // 如果是特定账号的错误，可以考虑记录是哪个账号出的问题
        res.status(error.response ? error.response.status : 500).json({ 
            success: false, 
            error: '生成请求失败', 
            details: error.response ? error.response.data : error.message 
        });
    }
});

// POST /api/ai/query
router.post('/query', authenticateToken, async (req, res) => {
    console.log(`[AI Generate Route /query] Process ID: ${process.pid} - Request received.`); // 新增日志
    
    const { history_id } = req.body;

    if (!history_id) {
        return res.status(400).json({ success: false, error: '缺少 history_id' });
    }

    if (history_id.startsWith('comfy_')) {
        const actualComfyId = history_id.substring(6);
        console.log(`[AI Generate Route /query] Process ID: ${process.pid} - Handling ComfyUI query for prompt ID: ${actualComfyId}`);
        try {
            // 1. Get history for the specific prompt_id
            const historyResponse = await axios.get(`${COMFYUI_BASE_URL}/history/${actualComfyId}`);
            const historyData = historyResponse.data;

            if (historyData && historyData[actualComfyId]) {
                const promptEntry = historyData[actualComfyId];
                const statusData = promptEntry.status; // Status object from history
                // const outputs = promptEntry.outputs; // We will access this inside the conditional logic

                console.log(`[AI Generate Route /query] ComfyUI History for ${actualComfyId} - Status: ${statusData ? JSON.stringify(statusData) : 'N/A'}, Outputs object keys: ${promptEntry.outputs ? Object.keys(promptEntry.outputs).join(', ') : 'N/A'}`);

                if (statusData && statusData.completed === true && statusData.status_str === 'success') {
                    const outputs = promptEntry.outputs;
                    let imageUrls = [];
                    if (outputs) {
                        for (const nodeId in outputs) {
                            if (outputs[nodeId] && outputs[nodeId].images && outputs[nodeId].images.length > 0) {
                                outputs[nodeId].images.forEach(imageInfo => {
                                    const imageUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(imageInfo.filename)}&subfolder=${encodeURIComponent(imageInfo.subfolder || '')}&type=${imageInfo.type}`;
                                    imageUrls.push(imageUrl);
                                });
                            }
                        }
                    }
                    if (imageUrls.length > 0) {
                        console.log(`[AI Generate Route /query] Process ID: ${process.pid} - ComfyUI task ${actualComfyId} complete. Image URLs:`, imageUrls);
                        return res.json({
                            success: true,
                            imageUrls: imageUrls
                        });
                    } else {
                        console.warn(`[AI Generate Route /query] Process ID: ${process.pid} - ComfyUI task ${actualComfyId} reported success but NO image output found in ANY node of outputs. Outputs:`, outputs);
                        return res.json({ success: false, error: '任务成功但未找到图片输出（检查所有节点）', imageUrls: [] });
                    }
                } else if (statusData && statusData.status_str === 'failed') {
                    console.error(`[AI Generate Route /query] Process ID: ${process.pid} - ComfyUI task ${actualComfyId} failed. Status:`, statusData);
                    return res.json({ success: false, error: '高级生图任务失败', imageUrls: [] });
                } else {
                    // Task is still running, pending, or in an indeterminate state according to history.
                    // Frontend will poll again based on empty imageUrls.
                    console.log(`[AI Generate Route /query] Process ID: ${process.pid} - ComfyUI task ${actualComfyId} still processing or status unclear via /history. Status: ${statusData ? statusData.status_str : 'N/A'}`);
                    return res.json({ success: true, imageUrls: [] }); 
                }
            } else {
                // Prompt_id not found in /history response, check /prompt for queue status
                console.warn(`[AI Generate Route /query] Process ID: ${process.pid} - ComfyUI history not found for prompt ID: ${actualComfyId}. Checking queue...`);
                try {
                    const queueResponse = await axios.get(`${COMFYUI_BASE_URL}/prompt`);
                    if (queueResponse.data && queueResponse.data.exec_info && queueResponse.data.exec_info.queue_remaining > 0) {
                        // Check if our prompt_id is in the queue (optional, simple check for now)
                        // For a more robust check, you might need to iterate through queueResponse.data.exec_info.queue if it lists IDs
                        console.log(`[AI Generate Route /query] Process ID: ${process.pid} - ComfyUI task ${actualComfyId} is likely queued. Queue remaining: ${queueResponse.data.exec_info.queue_remaining}`);
                        return res.json({ success: true, imageUrls: [] }); // Task is queued, poll again
                    } else {
                        console.warn(`[AI Generate Route /query] Process ID: ${process.pid} - ComfyUI task ${actualComfyId} not in history and queue is empty or info unavailable. Assuming error or invalid ID.`);
                        return res.json({ success: false, error: '任务未在历史记录中找到，队列也为空或无法访问', imageUrls: [] });
                    }
                } catch (queueError) {
                    console.error(`[AI Generate Route /query] Process ID: ${process.pid} - Error querying ComfyUI /prompt (queue) for ${actualComfyId}:`, queueError.message);
                    return res.json({ success: false, error: '查询任务队列失败，无法确认任务状态', imageUrls: [] });
                }
            }
        } catch (error) {
            let errorMessage = '查询高级生图结果失败';
            if (error.response && error.response.status === 404) {
                 // This case should ideally be caught by the logic above (prompt_id not in historyData)
                 // but as a fallback if axios throws directly for 404 before the above check.
                console.warn(`[AI Generate Route /query] Process ID: ${process.pid} - ComfyUI /history/${actualComfyId} returned 404. Task may not exist or completed long ago and purged.`);
                errorMessage = '任务历史不存在或已被清除';
                // We could still try checking the queue here as a last resort if needed.
                return res.json({ success: false, error: errorMessage, imageUrls: [] });
            }
            console.error(`[AI Generate Route /query] Process ID: ${process.pid} - General error querying ComfyUI history for ${actualComfyId}:`, error.response ? JSON.stringify(error.response.data) : error.message);
            res.status(500).json({
                success: false,
                error: errorMessage,
                details: error.response ? JSON.stringify(error.response.data) : error.message
            });
        }
        return; // End ComfyUI query processing
    }

    // --- Existing Jimeng Query Logic ---
    const selectedAccount = getCurrentJimengAccount(); // 查询也使用轮询账号
    if (!selectedAccount) {
        console.error(`[AI Generate Route /query] Process ID: ${process.pid} - No usable Jimeng account for query request.`);
        return res.status(500).json({
            success: false,
            error: '服务器配置不完整或无可用账号，无法处理查询请求。'
        });
    }
    
    if (!selectedAccount.cookie) { // 查询主要依赖 Cookie
        console.error(`[AI Generate Route /query] Process ID: ${process.pid} - 错误：当前选中的即梦账号 COOKIE 配置不完整。`);
        return res.status(500).json({
            success: false,
            error: '服务器选中的账号 COOKIE 配置不完整，无法处理查询请求。'
        });
    }

    try {
        const targetUrl = 'https://jimeng.jianying.com/mweb/v1/get_history_by_ids';

        if (!history_id) {
            return res.status(400).json({ success: false, error: '缺少 history_id' });
        }
        
        // 构建请求体
        const requestBody = {
            history_ids: [history_id]
        };

        // +++ 新增：打印请求详细信息 +++
        console.log(`[AI Generate Route /query] Process ID: ${process.pid} - 即梦查询请求详情:`);
        console.log(`  目标URL: ${targetUrl}`);
        console.log(`  请求Body:`, JSON.stringify(requestBody, null, 2));
        console.log(`  History ID: ${history_id}`);
        console.log(`  使用Cookie长度: ${selectedAccount.cookie ? selectedAccount.cookie.length : 0} 字符`);
        console.log(`  Cookie前缀: ${selectedAccount.cookie ? selectedAccount.cookie.substring(0, 50) + '...' : 'N/A'}`);
        // +++ 结束新增 +++

        // 设置请求头 (与 jimeng.js 类似, 但可以简化一些)
        const headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'appvr': '5.8.0',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'origin': 'https://jimeng.jianying.com',
            'pf': '7',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://jimeng.jianying.com/ai-tool/image/generate',
            'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
            'Cookie': selectedAccount.cookie // 使用选中账号的 COOKIE
        };

        // 发送请求到目标服务器
        console.log(`[AI Generate Route /query] Process ID: ${process.pid} - Sending query request to Jimeng with selected account. History ID: ${history_id}`);
        
        // +++ 新增：记录请求开始时间 +++
        const requestStartTime = Date.now();
        // +++ 结束新增 +++
        
        const response = await axios.post(targetUrl, requestBody, { headers });
        
        // +++ 新增：详细记录响应信息 +++
        const requestDuration = Date.now() - requestStartTime;
        console.log(`[AI Generate Route /query] Process ID: ${process.pid} - 即梦查询响应详情:`);
        console.log(`  响应状态: ${response.status}`);
        console.log(`  响应时间: ${requestDuration}ms`);
        console.log(`  响应Headers:`, JSON.stringify(response.headers, null, 2));
        console.log(`  完整响应数据:`, JSON.stringify(response.data, null, 2));
        // +++ 结束新增 +++

        // 解析即梦的响应并提取图片 URL
        console.log(`[AI Generate Route /query] Process ID: ${process.pid} - 开始解析即梦响应数据:`);
        console.log(`  response.data 存在: ${!!response.data}`);
        console.log(`  response.data.data 存在: ${!!(response.data && response.data.data)}`);
        console.log(`  response.data.data[${history_id}] 存在: ${!!(response.data && response.data.data && response.data.data[history_id])}`);
        
        if (response.data && response.data.data) {
            console.log(`  response.data.data 的所有key:`, Object.keys(response.data.data));
        }
        
        if (response.data.data && response.data.data[history_id]) {
            const historyData = response.data.data[history_id];
            
            console.log(`[AI Generate Route /query] Process ID: ${process.pid} - 找到历史数据，开始解析:`);
            console.log(`  historyData:`, JSON.stringify(historyData, null, 2));
            console.log(`  historyData.status: ${historyData.status}`);
            console.log(`  historyData.fail_code: ${historyData.fail_code}`);
            console.log(`  historyData.fail_msg: ${historyData.fail_msg}`);
            console.log(`  historyData.task.status: ${historyData.task ? historyData.task.status : 'N/A'}`);
            
            // 检查任务是否失败 - 即梦状态码30表示失败
            if (historyData.status === 30 || (historyData.task && historyData.task.status === 30)) {
                const failCode = historyData.fail_code;
                const failMsg = historyData.fail_msg;
                
                console.error(`[AI Generate Route /query] Process ID: ${process.pid} - 即梦任务失败:`);
                console.error(`  失败代码: ${failCode}`);
                console.error(`  失败消息: ${failMsg}`);
                
                // 根据失败代码返回用户友好的错误消息
                let userErrorMessage = '生成任务失败';
                if (failCode === '2038' || failMsg === 'InputTextRisk') {
                    userErrorMessage = '提示词可能包含敏感内容，请修改后重试';
                } else if (failCode === '22107') {
                    userErrorMessage = '当前系统繁忙，请稍后再试';
                } else if (failMsg) {
                    userErrorMessage = `生成失败: ${failMsg}`;
                } else if (failCode) {
                    userErrorMessage = `生成失败 (错误代码: ${failCode})`;
                }
                
                return res.json({
                    success: false,
                    error: userErrorMessage,
                    imageUrls: [],
                    fail_code: failCode,
                    fail_msg: failMsg
                });
            }
            
            // 如果没有失败，继续检查图片数据
            console.log(`  historyData.item_list 存在: ${!!(historyData.item_list)}`);
            console.log(`  historyData.aigc_data 存在: ${!!(historyData.aigc_data)}`);
            console.log(`  historyData.aigc_data?.item_list 存在: ${!!(historyData.aigc_data?.item_list)}`);
            
            const items = historyData.item_list || historyData.aigc_data?.item_list || [];
            
            console.log(`[AI Generate Route /query] Process ID: ${process.pid} - 找到 ${items.length} 个项目进行处理`);
            items.forEach((item, index) => {
                console.log(`  项目 ${index}:`, JSON.stringify(item, null, 2));
                console.log(`  项目 ${index} - item.image 存在: ${!!(item.image)}`);
                console.log(`  项目 ${index} - item.image?.large_images 存在: ${!!(item.image?.large_images)}`);
                console.log(`  项目 ${index} - item.image?.large_images 长度: ${item.image?.large_images?.length || 0}`);
                if (item.image?.large_images?.[0]) {
                    console.log(`  项目 ${index} - 第一个大图 image_url: ${item.image.large_images[0].image_url}`);
                }
            });
            
            const imageUrls = [];
            items.forEach(item => {
                if (item.image?.large_images?.[0]?.image_url) {
                    imageUrls.push(item.image.large_images[0].image_url);
                }
            });
            
            console.log(`[AI Generate Route /query] Process ID: ${process.pid} - Found ${imageUrls.length} image URLs for History ID ${history_id} using selected account.`);
            
            if (imageUrls.length > 0) {
                console.log(`[AI Generate Route /query] Process ID: ${process.pid} - 提取到的图片URLs:`);
                imageUrls.forEach((url, index) => {
                    console.log(`  图片 ${index + 1}: ${url}`);
                });
                
                // 成功获取到图片，返回结果
                return res.json({
                    success: true,
                    imageUrls: imageUrls
                });
            } else {
                // 没有找到图片，可能任务还在处理中
                console.warn(`[AI Generate Route /query] Process ID: ${process.pid} - 任务存在但暂无图片，继续等待...`);
                return res.json({
                    success: true,
                    imageUrls: [] // 空数组表示继续轮询
                });
            }
        } else {
            // 没有找到对应的历史记录，可能任务还在处理中或ID无效
            console.warn(`[AI Generate Route /query] Process ID: ${process.pid} - 未找到指定History ID的数据:`);
            if (response.data && response.data.data) {
                console.warn(`  可用的History IDs:`, Object.keys(response.data.data));
                console.warn(`  查询的History ID: ${history_id}`);
                console.warn(`  ID匹配检查: ${Object.keys(response.data.data).includes(history_id)}`);
            }
            
            return res.json({
                success: true,
                imageUrls: [] // 空数组表示继续轮询
            });
        }

    } catch (error) {
        // +++ 新增：更详细的错误记录 +++
        console.error(`[AI Generate Route /query] Process ID: ${process.pid} - 即梦查询出错详情:`);
        console.error(`  Error message: ${error.message}`);
        console.error(`  Error code: ${error.code}`);
        if (error.response) {
            console.error(`  HTTP状态: ${error.response.status}`);
            console.error(`  响应Headers:`, JSON.stringify(error.response.headers, null, 2));
            console.error(`  错误响应Body:`, JSON.stringify(error.response.data, null, 2));
        } else if (error.request) {
            console.error(`  请求已发送但无响应:`, error.request);
        } else {
            console.error(`  请求配置错误:`, error.config);
        }
        console.error(`  完整错误对象:`, error);
        // +++ 结束新增 +++
        
        // 返回给前端的格式，表明查询失败
        res.status(error.response ? error.response.status : 500).json({ 
            success: false, // 表示查询 API 调用失败
            error: '查询结果失败', 
            details: error.response ? error.response.data : error.message 
        });
    }
});

// +++ 新增：获取 ComfyUI 队列状态的路由 +++
router.get('/comfy_queue_status', authenticateToken, async (req, res) => {
    console.log(`[AI Generate Route /comfy_queue_status] Process ID: ${process.pid} - Request received.`);
    
    if (!COMFYUI_BASE_URL) {
        console.error(`[AI Generate Route /comfy_queue_status] Process ID: ${process.pid} - COMFYUI_BASE_URL is not defined.`);
        return res.status(500).json({
            success: false,
            error: 'ComfyUI 服务未配置 (基础URL未设置)。'
        });
    }

    try {
        const comfyQueueUrl = `${COMFYUI_BASE_URL}/prompt`;
        console.log(`[AI Generate Route /comfy_queue_status] Process ID: ${process.pid} - Fetching ComfyUI queue status from: ${comfyQueueUrl}`);
        
        const response = await axios.get(comfyQueueUrl);
        
        console.log(`[AI Generate Route /comfy_queue_status] Process ID: ${process.pid} - ComfyUI /prompt response status: ${response.status}`);
        
        // 修改：判断队列状态逻辑
        // ComfyUI 的 /prompt 响应通常是 { "exec_info": { "queue_remaining": N } }
        // 但 queue_remaining 包括了当前正在执行的任务，这对用户来说不太友好
        // 我们修改为只返回还在等待的任务数量
        if (response.data && response.data.exec_info) {
            // 检查当前是否有正在执行的任务 (如果正在执行则 ExecutingPrompt 不为 null)
            const isExecuting = response.data.exec_info.ExecutingPrompt !== null;
            // queue_remaining 包括了当前执行的任务，所以如果有正在执行的任务且队列数大于0，则减1
            if (isExecuting && response.data.exec_info.queue_remaining > 0) {
                response.data.exec_info.queue_remaining -= 1;
                console.log(`[AI Generate Route /comfy_queue_status] Adjusted queue count: one task is currently executing and excluded from waiting count`);
            }
        }
        
        // 返回修正后的数据
        res.json(response.data); 

    } catch (error) {
        console.error(`[AI Generate Route /comfy_queue_status] Process ID: ${process.pid} - Error fetching ComfyUI queue status:`, error.response ? error.response.data : error.message);
        res.status(error.response ? error.response.status : 500).json({
            success: false,
            error: '获取 ComfyUI 队列状态失败',
            details: error.response ? error.response.data : error.message
        });
    }
});
// +++ 结束新增 ++

// +++ 新增：添加历史记录接口 +++
// 注意：此路由与 ai-history.js 重复，但添加了对 comfy_prompt_id 和 status 的处理
// 为避免冲突，这里留下这段代码，但注释掉，使用 ai-history.js 中的路由处理
/*
router.get('/history', authenticateToken, async (req, res) => {
    // 原有代码...保留注释便于参考
});

// POST /api/ai/history - 保存生成历史记录
router.post('/history', authenticateToken, async (req, res) => {
    // 原有代码...保留注释便于参考
});
*/
// +++ 结束新增 +++

module.exports = router; 