const express = require('express');
const multer = require('multer');
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
// --- 导入认证和积分检查中间件 ---
const { authenticateToken } = require('../utils/auth');
const { checkAndDeductCredits } = require('../middleware/checkCredits');
const pool = require('../db/pool'); // 确保连接池已导入并在作用域内

const router = express.Router();

// 复用 index.js 中的 multer 配置或创建一个临时的内存存储
// 这里我们用内存存储，因为文件只需要传递给外部 API，不需要保存在本地
const upload = multer({
  storage: multer.memoryStorage(), // 使用内存存储
  limits: { fileSize: 10 * 1024 * 1024 }, // ComfyUI 可能处理稍大的图片，可以适当调整
  fileFilter: function (req, file, cb) {
    // 允许 PNG, JPG, WEBP (根据 Recraft 文档)
    const allowedTypes = /jpeg|jpg|png|webp/;
    const mimetype = allowedTypes.test(file.mimetype);
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());

    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('仅支持 PNG, JPG, WEBP 格式的图片!'));
  }
});

// POST /api/images/remove-background
// --- 应用 authenticateToken 和 checkAndDeductCredits 中间件 ---
router.post('/remove-background',
    authenticateToken, 
    checkAndDeductCredits('remove_background'), // 使用 'remove_background' 作为功能 key
    upload.single('file'), 
    async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ success: false, message: '请上传图片文件。' });
  }

  const recraftApiKey = process.env.RECRAFT_API_KEY;
  if (!recraftApiKey) {
    console.error('Recraft API Key (RECRAFT_API_KEY) 未配置在 .env 文件中');
    return res.status(500).json({ success: false, message: '服务器配置错误，无法处理图片。请检查 .env 文件。' });
  }

  // 使用与comfy-remove-background路由一致的ComfyUI基础URL定义
  // 这解决了之前硬编码URL与后来定义的COMFYUI_BASE_URL不一致的问题，很可能是404的原因
  const ACTUAL_COMFYUI_BASE_URL = process.env.COMFYUI_CPU_STORAGE_URL || 'https://u63642-a7c0-ee81c217.cqa1.seetacloud.com:8443/'; // <--- 修改：用于 Recraft 结果转存到 CPU ComfyUI
  const apiUrl = 'https://external.api.recraft.ai/v1/images/removeBackground';

  let finalImageUrl = null;
  let operationSuccessful = false;
  let responseMessage = '';
  let httpStatusCode = 200;

  try {
    const formData = new FormData();
    formData.append('file', req.file.buffer, {
      filename: req.file.originalname,
      contentType: req.file.mimetype,
    });
    console.log(`调用 Recraft API: ${apiUrl}`);
    const response = await axios.post(apiUrl, formData, {
      headers: {
        ...formData.getHeaders(),
        'Authorization': `Bearer ${recraftApiKey}`
      },
      responseType: 'json',
    });

    console.log('Recraft API 响应:', response.data);

    if (response.data && response.data.image && response.data.image.url) {
        const recraftImageUrl = response.data.image.url;
        const userId = req.user.id; 
        const originalFilename = req.file.originalname;
        console.log('[Remove Background] Recraft processing successful. Temporary URL:', recraftImageUrl);

        finalImageUrl = recraftImageUrl; // 默认情况下，如果OSS失败，这是备用

        try {
            console.log(`[Remove Background] Downloading image from Recraft: ${recraftImageUrl}`);
            const imageDownloadResponse = await axios.get(recraftImageUrl, { responseType: 'arraybuffer' });
            if (imageDownloadResponse.status !== 200) {
                throw new Error(`Failed to download image from Recraft: ${imageDownloadResponse.statusText}`);
            }
            const imageBuffer = Buffer.from(imageDownloadResponse.data);

            let baseFileName = path.basename(originalFilename, path.extname(originalFilename));
            const comfyFileName = `${Date.now()}_${baseFileName}_rb_oss.png`; // 添加 _oss 后缀以区分

            const comfyUiUploadUrl = `${ACTUAL_COMFYUI_BASE_URL}/upload/image`; // 使用修正后的URL
            const comfyFormData = new FormData();
            comfyFormData.append('image', imageBuffer, comfyFileName);
            comfyFormData.append('type', 'input'); // 上传到 input，稍后用 view 查看
            comfyFormData.append('overwrite', 'true');

            console.log(`[Remove Background] Uploading image to ComfyUI (OSS): ${comfyUiUploadUrl}, filename: ${comfyFileName}`);
            const comfyUploadResponse = await axios.post(comfyUiUploadUrl, comfyFormData, {
                headers: comfyFormData.getHeaders(),
            });

            if (comfyUploadResponse.status !== 200 || !comfyUploadResponse.data || !comfyUploadResponse.data.name) {
                const errorText = comfyUploadResponse.data ? JSON.stringify(comfyUploadResponse.data) : comfyUploadResponse.statusText;
                throw new Error(`ComfyUI (OSS) upload failed: ${errorText}`);
            }

            const comfyUploadResult = comfyUploadResponse.data;
            console.log('[Remove Background] ComfyUI (OSS) upload successful:', comfyUploadResult);

            let ossViewUrl = `${ACTUAL_COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(comfyUploadResult.name)}&type=${encodeURIComponent(comfyUploadResult.type || 'input')}`;
            if (comfyUploadResult.subfolder) {
                ossViewUrl += `&subfolder=${encodeURIComponent(comfyUploadResult.subfolder)}`;
            }
            finalImageUrl = ossViewUrl; // 更新为 ComfyUI (OSS) 的永久链接
            operationSuccessful = true;
            responseMessage = '背景移除并成功存储。';
            console.log(`[Remove Background] Generated ComfyUI (OSS) permanent link: ${finalImageUrl}`);

            // 只有当OSS上传成功，才保存历史记录
            let historyConnection; 
            try {
                historyConnection = await pool.getConnection(); 
                await historyConnection.query(
                    'INSERT INTO remove_background_history (user_id, original_image_filename, result_image_url, processor) VALUES (?, ?, ?, ?)',
                    [userId, originalFilename, finalImageUrl, 'recraft_comfy_oss'] // 明确processor
                );
                console.log(`[Remove Background] History saved for user ${userId} with ComfyUI (OSS) URL: ${finalImageUrl}`);
            } catch (historyError) {
                console.error('[Remove Background] Failed to save history (even after successful OSS upload):', historyError);
                // 非致命，但记录下来
            } finally {
                if (historyConnection) historyConnection.release(); 
            }

        } catch (comfyOssError) {
            console.error('[Remove Background] Error during ComfyUI (OSS) processing step:', comfyOssError.message);
            operationSuccessful = false; // 标记OSS步骤失败
            // finalImageUrl 保持为 recraftImageUrl
            responseMessage = `背景移除处理成功，但永久存储到图床失败: ${comfyOssError.message}。您可以使用临时链接。`;
            // 提醒用户这是一个临时链接
            // 注意：此时 finalImageUrl 仍是 Recraft 的 URL
        }
    } else {
      operationSuccessful = false;
      responseMessage = '从 Recraft API 返回的响应格式无效。';
      httpStatusCode = 500;
    }

    if (operationSuccessful) {
        res.status(httpStatusCode).json({ 
            success: true,
            imageUrl: finalImageUrl,
            message: responseMessage,
            newCredits: req.locals?.newCredits,
            newDailyFreeUsed: req.locals?.newDailyFreeUsed
        });
    } else {
        // 如果 Recraft 成功但OSS失败，finalImageUrl 可能是 Recraft URL
        // 如果 Recraft 就失败了，finalImageUrl 可能是 null
        res.status(httpStatusCode === 200 ? 500 : httpStatusCode).json({ // 如果之前没设置错误码，则默认为500
            success: false,
            message: responseMessage,
            imageUrl: finalImageUrl // 返回 Recraft URL 作为回退，或 null
        });
    }

  } catch (error) {
    console.error('处理 /remove-background 时发生错误:', error.response ? error.response.data : error.message);
    let errMsg = '处理图片背景时发生内部服务器错误。';
    if (error.response && error.response.data && error.response.data.error) {
        errMsg = `图片处理失败: ${error.response.data.error.message || '未知错误'}`;
    } else if (error.message) {
        errMsg = error.message; // 使用捕获到的错误消息
    }
    res.status(500).json({ 
        success: false, 
        message: errMsg 
    });
  }
});

// --- 新增：POST /api/images/vectorize ---
router.post('/vectorize',
    authenticateToken,
    checkAndDeductCredits('vectorize_image'), // 使用 'vectorize_image' key
    upload.single('file'), // 复用之前的内存存储 multer 实例
    async (req, res) => {
        if (!req.file) {
            return res.status(400).json({ error: '请上传图片文件。' });
        }

        const recraftApiKey = process.env.RECRAFT_API_KEY;
        if (!recraftApiKey) {
            console.error('Recraft API Key (RECRAFT_API_KEY) 未配置在 .env 文件中');
            return res.status(500).json({ error: '服务器配置错误，无法处理图片。请检查 .env 文件。' });
        }

        const apiUrl = 'https://external.api.recraft.ai/v1/images/vectorize'; // <--- Vectorize API URL

        try {
            const formData = new FormData();
            formData.append('file', req.file.buffer, {
                filename: req.file.originalname,
                contentType: req.file.mimetype,
            });
            // response_format 默认为 url，无需指定

            console.log(`[Vectorize] 调用 Recraft API: ${apiUrl}`);

            const response = await axios.post(apiUrl, formData, {
                headers: {
                    ...formData.getHeaders(),
                    'Authorization': `Bearer ${recraftApiKey}` // 发送 API Key
                },
                responseType: 'json',
            });

            console.log('[Vectorize] Recraft API 响应:', response.data);

            if (response.data && response.data.image && response.data.image.url) {
                const recraftSvgUrl = response.data.image.url; // Recraft's temporary SVG URL
                const userId = req.user.id;
                const originalFilename = req.file ? req.file.originalname : 'unknown_original.svg'; // Default to .svg extension

                console.log('[Vectorize] Recraft vectorization successful. Temporary URL:', recraftSvgUrl);

                // --- 新增：下载 SVG 并上传到 ComfyUI --- START ---
                let finalSvgUrl = recraftSvgUrl; // Default to Recraft URL, fallback if ComfyUI fails
                try {
                    console.log(`[Vectorize] Downloading SVG from Recraft: ${recraftSvgUrl}`);
                    // SVG is text-based, but fetching as arraybuffer and then Buffer is fine for upload
                    const svgDownloadResponse = await axios.get(recraftSvgUrl, { responseType: 'arraybuffer' });
                    if (svgDownloadResponse.status !== 200) {
                        throw new Error(`Failed to download SVG from Recraft: ${svgDownloadResponse.statusText}`);
                    }
                    const svgBuffer = Buffer.from(svgDownloadResponse.data);

                    let baseFileName = path.basename(originalFilename, path.extname(originalFilename));
                    const comfyFileName = `${Date.now()}_${baseFileName}_vector_oss.svg`; // Ensure .svg extension

                    // 使用与上面remove-background一致的ComfyUI URL
                    const ACTUAL_COMFYUI_BASE_URL_FOR_VECTORIZE = process.env.COMFYUI_BASE_URL_FOR_VECTORIZE || 'https://u63642-a2b4-fccb0b0d.westx.seetacloud.com:8443';
                    const comfyUiUploadUrl = `${ACTUAL_COMFYUI_BASE_URL_FOR_VECTORIZE}/upload/image`;

                    const comfyFormData = new FormData();
                    comfyFormData.append('image', svgBuffer, comfyFileName); // Uploading SVG as 'image'
                    comfyFormData.append('type', 'input');
                    comfyFormData.append('overwrite', 'true');

                    console.log(`[Vectorize] Uploading SVG to ComfyUI: ${comfyUiUploadUrl}, filename: ${comfyFileName}`);
                    const comfyUploadResponse = await axios.post(comfyUiUploadUrl, comfyFormData, {
                        headers: comfyFormData.getHeaders(),
                    });

                    if (comfyUploadResponse.status !== 200 || !comfyUploadResponse.data || !comfyUploadResponse.data.name) {
                        const errorText = comfyUploadResponse.data ? JSON.stringify(comfyUploadResponse.data) : comfyUploadResponse.statusText;
                        throw new Error(`ComfyUI SVG upload failed: ${errorText}`);
                    }

                    const comfyUploadResult = comfyUploadResponse.data;
                    console.log('[Vectorize] ComfyUI SVG upload successful:', comfyUploadResult);

                    let comfyViewUrl = `${ACTUAL_COMFYUI_BASE_URL_FOR_VECTORIZE}/view?filename=${encodeURIComponent(comfyUploadResult.name)}&type=${encodeURIComponent(comfyUploadResult.type || 'input')}`;
                    if (comfyUploadResult.subfolder) {
                        comfyViewUrl += `&subfolder=${encodeURIComponent(comfyUploadResult.subfolder)}`;
                    }
                    finalSvgUrl = comfyViewUrl; // Update to ComfyUI's permanent link
                    console.log(`[Vectorize] Generated ComfyUI permanent link for SVG: ${finalSvgUrl}`);

                } catch (comfyError) {
                    console.error('[Vectorize] Error during ComfyUI processing for SVG:', comfyError.message);
                    // finalSvgUrl remains recraftSvgUrl in case of error
                }
                // --- 新增：下载 SVG 并上传到 ComfyUI --- END ---

                // --- 保存历史记录 (使用 finalSvgUrl) --- START ---
                let historyConnection; 
                try {
                    // const pool = require('../db/pool'); // Assumed to be available from top of file
                    historyConnection = await pool.getConnection(); 
                    await historyConnection.query(
                        'INSERT INTO vectorize_image_history (user_id, original_image_filename, result_svg_url) VALUES (?, ?, ?)',
                        [userId, originalFilename, finalSvgUrl] // Use finalSvgUrl
                    );
                    console.log(`[Vectorize] History saved for user ${userId} with SVG URL: ${finalSvgUrl}`);
                } catch (historyError) {
                    console.error('[Vectorize] Failed to save SVG history:', historyError);
                } finally {
                    if (historyConnection) historyConnection.release(); 
                }
                // --- 保存历史记录 --- END ---

                // --- 成功响应 ---
                res.json({
                    svgUrl: finalSvgUrl // Return the final SVG URL (ComfyUI or Recraft's)
                    // updatedCredits: req.locals?.updatedCredits // 可选
                });
            } else {
                throw new Error('从 Recraft API 返回的响应格式无效 (vectorize)。');
            }

        } catch (error) {
            console.error('[Vectorize] 调用 Recraft API 时出错:', error.response ? error.response.data : error.message);
             if (error.response && error.response.data && error.response.data.error) {
                return res.status(error.response.status || 500).json({
                    error: `图片转换失败: ${error.response.data.error.message || '未知错误'}`,
                    details: error.response.data.error.details
                });
            } else if (error.message.includes('无效') || error.message.includes('格式无效')) {
                 return res.status(500).json({ error: '图片转换服务返回了无效的数据。' });
            }
            res.status(500).json({ error: '转换图片为 SVG 时发生内部服务器错误。' });
        }
    }
);

// #############################################################################
// ##                                                                         ##
// ##          新的 ComfyUI 抠图功能                                           ##
// ##                                                                         ##
// #############################################################################

const COMFYUI_GPU_URL_FALLBACK = 'https://u63642-8e01-0d92b3a9.cqa1.seetacloud.com:8443'; // GPU 服务器地址
const COMFYUI_CPU_STORAGE_URL_FALLBACK = 'https://u63642-a2b4-fccb0b0d.westx.seetacloud.com:8443'; // CPU 存储服务器地址

// 用于执行 ComfyUI 内部抠图工作流的服务器 (例如 GPU 服务器)
const COMFYUI_WORKFLOW_EXEC_URL = process.env.COMFYUI_GPU_URL || COMFYUI_GPU_URL_FALLBACK;
// 用于存储最终结果的服务器 (例如 CPU 存储服务器) - 这个变量在此处定义，但具体使用要看场景
const COMFYUI_STORAGE_TARGET_URL = process.env.COMFYUI_CPU_STORAGE_URL || COMFYUI_CPU_STORAGE_URL_FALLBACK;

const COMFYUI_WORKFLOW_NAME_FOR_BG_REMOVE = 'comfy_抠图标准流'; // 你在数据库中存储此工作流的名称
const COMFYUI_LOAD_IMAGE_NODE_ID = "47";
const COMFYUI_SAVE_IMAGE_NODE_ID = "49";
const POLLING_INTERVAL = 3000; // 3秒
const MAX_POLLING_ATTEMPTS = 20; // 最多轮询20次 (60秒)

// 辅助函数：上传图片到 ComfyUI
async function uploadImageToComfyUI(fileBuffer, originalFilename) {
  const formData = new FormData();
  formData.append('image', fileBuffer, originalFilename);
  // formData.append('type', 'input'); // 通常上传到 input 目录
  // formData.append('overwrite', 'true'); // 允许覆盖同名文件

  // 使用与上面一致的 COMFYUI_BASE_URL
  const ACTUAL_COMFYUI_BASE_URL_FOR_HELPER = COMFYUI_WORKFLOW_EXEC_URL; // <--- 修改：内部抠图工作流的图片上传到执行服务器

  console.log(`[ComfyBGRemove Helper] Uploading to ComfyUI: ${originalFilename} to ${ACTUAL_COMFYUI_BASE_URL_FOR_HELPER}`);
  const response = await axios.post(`${ACTUAL_COMFYUI_BASE_URL_FOR_HELPER}/upload/image`, formData, {
    headers: formData.getHeaders(),
  });
  if (response.status !== 200 || !response.data || !response.data.name) {
    console.error('[ComfyBGRemove] ComfyUI upload failed:', response.data);
    throw new Error('上传图片到 ComfyUI 失败');
  }
  console.log('[ComfyBGRemove] ComfyUI upload successful:', response.data);
  return response.data; // { name, subfolder, type }
}

// 辅助函数：从数据库获取 ComfyUI 工作流
async function getComfyUIWorkflow(workflowName) {
  let connection;
  try {
    connection = await pool.getConnection();
    const [rows] = await connection.query('SELECT workflow_json FROM workflows WHERE name = ?', [workflowName]);
    if (rows.length === 0) {
      throw new Error(`未在数据库中找到名为 "${workflowName}" 的工作流`);
    }
    // 2. 获取 workflow_json 字段的值
    const workflowJsonActuallyObject = rows[0].workflow_json;

    // !!!!! 在这里加一个日志输出 !!!!!
    console.log(`[ComfyBGRemove Debug] Raw workflow_json (actually object) from DB for "${workflowName}":`, typeof workflowJsonActuallyObject, workflowJsonActuallyObject);

    // 3. 直接返回该对象，因为驱动程序已将其解析
    return workflowJsonActuallyObject; 
  } catch (error) {
    console.error(`[ComfyBGRemove] 获取工作流 "${workflowName}" 失败:`, error);
    throw error;
  } finally {
    if (connection) connection.release();
  }
}

// 辅助函数：触发 ComfyUI 工作流
async function triggerComfyUIWorkflow(workflowJson, clientId) {
  // 使用与上面一致的 COMFYUI_BASE_URL
  const ACTUAL_COMFYUI_BASE_URL_FOR_TRIGGER = COMFYUI_WORKFLOW_EXEC_URL; // <--- 修改：触发在执行服务器上的工作流
  console.log(`[ComfyBGRemove] Triggering ComfyUI workflow with clientId: ${clientId} on ${ACTUAL_COMFYUI_BASE_URL_FOR_TRIGGER}`);
  const response = await axios.post(`${ACTUAL_COMFYUI_BASE_URL_FOR_TRIGGER}/prompt`, {
    prompt: workflowJson,
    client_id: clientId,
  });
  if (response.status !== 200 || !response.data || !response.data.prompt_id) {
    console.error('[ComfyBGRemove] ComfyUI trigger failed:', response.data);
    throw new Error('触发 ComfyUI 工作流失败');
  }
  console.log('[ComfyBGRemove] ComfyUI workflow triggered:', response.data);
  return response.data.prompt_id;
}

// 辅助函数：轮询 ComfyUI 历史记录
async function pollComfyUIHistory(promptId) {
  const ACTUAL_COMFYUI_BASE_URL_FOR_POLL = COMFYUI_WORKFLOW_EXEC_URL;
  let attempts = 0;
  // 移除 MAX_QUEUE_CHECKS 和 queueChecks，排队阶段理论上无限轮询，直到任务开始或队列为空

  console.log(`[ComfyBGRemove] Starting to poll for promptId: ${promptId} on ${ACTUAL_COMFYUI_BASE_URL_FOR_POLL}`);

  while (attempts < MAX_POLLING_ATTEMPTS) {
    let isQueued = false; // 标记任务是否当前轮次被判定为在排队
    try {
      // 1. 检查任务是否已完成 (直接查 history)
      console.log(`[ComfyBGRemove] Polling history for promptId: ${promptId}, execution attempt ${attempts + 1}/${MAX_POLLING_ATTEMPTS}`);
      const historyResponse = await axios.get(`${ACTUAL_COMFYUI_BASE_URL_FOR_POLL}/history/${promptId}`);
      
      if (historyResponse.status === 200 && historyResponse.data && historyResponse.data[promptId]) {
        const promptData = historyResponse.data[promptId];
        if (promptData.outputs && Object.keys(promptData.outputs).length > 0 && promptData.outputs[COMFYUI_SAVE_IMAGE_NODE_ID]) {
          console.log('[ComfyBGRemove] Prompt finished (found in history):', promptData);
          return promptData; // 任务完成
        }
        // 如果在history中找到了，但输出还不完整，说明任务正在运行但未完成
        console.log(`[ComfyBGRemove] Prompt ${promptId} found in history but not yet complete. Outputs:`, promptData.outputs);
        // 任务已开始执行（或至少已在history中），消耗执行阶段的轮询次数
        // isQueued 保持 false

      } else {
        // 如果在 history 中没有找到 promptId 或响应不是预期的 (例如 historyResponse.data[promptId] 为空)
        // 这时我们检查队列状态，因为任务可能在排队或尚未被 ComfyUI 记录到 history
        console.log(`[ComfyBGRemove] Prompt ${promptId} not found or history incomplete. Checking queue status.`);
        const queueResponse = await axios.get(`${ACTUAL_COMFYUI_BASE_URL_FOR_POLL}/prompt`);
        if (queueResponse.status === 200 && queueResponse.data && queueResponse.data.exec_info) {
          const queueRemaining = queueResponse.data.exec_info.queue_remaining;
          console.log(`[ComfyBGRemove] Queue status: ${queueRemaining} tasks remaining.`);
          
          if (queueRemaining > 0) {
            // 队列不为空，任务可能在排队。
            console.log(`[ComfyBGRemove] Task ${promptId} likely queued (history not ready, queue > 0). Waiting...`);
            isQueued = true; // 标记为排队，本轮不消耗 execution attempt
          } else {
            // 队列为空，但任务仍然不在 history 或未完成。
            // 这可能意味着任务刚开始、快速失败了，或者prompt_id无效。
            // 此时我们认为它应该进入执行/失败的判断，消耗一个attempt。
            console.log(`[ComfyBGRemove] Queue is empty but task ${promptId} not complete in history. Assuming it started, failed, or is invalid.`);
            // isQueued 保持 false
          }
        } else {
          // 获取队列信息失败。
          console.warn('[ComfyBGRemove] Failed to get queue information. Assuming task might be running or failed.');
          // isQueued 保持 false
        }
      }
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // History API 返回 404，任务可能还未开始，或者 prompt_id 错误。检查队列。
        console.warn(`[ComfyBGRemove] History API returned 404 for ${promptId}. Checking queue.`);
        try {
            const queueResponseFor404 = await axios.get(`${ACTUAL_COMFYUI_BASE_URL_FOR_POLL}/prompt`);
            if (queueResponseFor404.status === 200 && queueResponseFor404.data && queueResponseFor404.data.exec_info && queueResponseFor404.data.exec_info.queue_remaining > 0) {
                console.log(`[ComfyBGRemove] Task ${promptId} (after history 404) likely queued. Waiting...`);
                isQueued = true; // 标记为排队
            } else {
                console.log(`[ComfyBGRemove] Queue is empty (after history 404) or cannot get queue info for ${promptId}.`);
                // isQueued 保持 false, 将消耗 attempt
            }
        } catch (queueError) {
            console.warn('[ComfyBGRemove] Error checking queue after 404:', queueError.message);
            // isQueued 保持 false, 将消耗 attempt
        }
      } else {
        // 其他类型的错误，可能是网络问题或服务器内部错误。
        console.warn(`[ComfyBGRemove] Polling error: ${error.message}.`);
        // isQueued 保持 false, 将消耗 attempt
      }
    }

    if (!isQueued) {
        attempts++; // 只有当任务不被认为是排队时，才消耗执行阶段的轮询次数
    }

    if (attempts < MAX_POLLING_ATTEMPTS) { // 只有在还有尝试次数时才等待
        await new Promise(resolve => setTimeout(resolve, POLLING_INTERVAL));
    }
  }
  throw new Error(`ComfyUI 任务超时或未能成功获取结果 (耗尽 ${MAX_POLLING_ATTEMPTS} 次执行阶段轮询)`);
}

// 新的 ComfyUI 抠图路由
router.post('/comfy-remove-background',
  authenticateToken,
  checkAndDeductCredits('comfy_remove_background'), // 新的积分功能key
  upload.single('file'),
  async (req, res) => {
    if (!req.file) {
      return res.status(400).json({ error: '请上传图片文件。' });
    }

    const userId = req.user.id;
    const originalFilename = req.file.originalname;
    const clientId = uuidv4(); // 为本次请求生成唯一客户端ID

    try {
      // 1. 上传图片到 ComfyUI
      const uploadedImageInfo = await uploadImageToComfyUI(req.file.buffer, originalFilename);
      const comfyImageFilename = uploadedImageInfo.name; // ComfyUI 返回的 input 文件名

      // 2. 获取并准备工作流
      const workflow = await getComfyUIWorkflow(COMFYUI_WORKFLOW_NAME_FOR_BG_REMOVE);
      
      // 3. 获取前端传递的 rem_mode
      const userSelectedRemMode = req.body.rem_mode; // 从 FormData 获取

      // 4. 更新工作流中的 rem_mode (如果用户提供了)
      if (userSelectedRemMode && workflow[COMFYUI_LOAD_IMAGE_NODE_ID]) { // COMFYUI_LOAD_IMAGE_NODE_ID 应该是背景移除节点的 ID，比如 "46"
          // 假设背景移除节点是 "46"，并且 rem_mode 在其 inputs 中
          // 你需要确认你的工作流中，包含 rem_mode 的节点 ID 是什么
          const bgRemoveNodeId = "46"; // 直接使用 "46" 或者用一个常量
          if (workflow[bgRemoveNodeId] && workflow[bgRemoveNodeId].inputs) {
              workflow[bgRemoveNodeId].inputs.rem_mode = userSelectedRemMode;
              console.log(`[ComfyBGRemove] Workflow's rem_mode updated to: ${userSelectedRemMode}`);
          } else {
              console.warn(`[ComfyBGRemove] Node ${bgRemoveNodeId} or its inputs not found in workflow for updating rem_mode.`);
          }
      }
      
      // 5. 更新工作流中的输入图片文件名
      if (workflow[COMFYUI_LOAD_IMAGE_NODE_ID] && workflow[COMFYUI_LOAD_IMAGE_NODE_ID].inputs) {
        workflow[COMFYUI_LOAD_IMAGE_NODE_ID].inputs.image = comfyImageFilename;
      } else {
        throw new Error(`工作流中未找到有效的图片输入节点 "${COMFYUI_LOAD_IMAGE_NODE_ID}"`);
      }
      
      // 6. 触发 ComfyUI 执行
      const promptId = await triggerComfyUIWorkflow(workflow, clientId);

      // 7. 轮询历史记录
      const historyData = await pollComfyUIHistory(promptId);

      // 8. 提取输出图片信息
      const outputNodeData = historyData.outputs[COMFYUI_SAVE_IMAGE_NODE_ID];
      if (!outputNodeData || !outputNodeData.images || outputNodeData.images.length === 0) {
        throw new Error('ComfyUI 工作流执行完毕，但未找到期望的输出图片');
      }
      
      const outputImage = outputNodeData.images[0]; // 通常抠图只输出一张
      const resultFilename = outputImage.filename;
      const resultSubfolder = outputImage.subfolder;
      const resultType = outputImage.type; // 通常是 'output'

      // 9. 构建结果图片 URL
      let resultImageUrl = `${COMFYUI_WORKFLOW_EXEC_URL}/view?filename=${encodeURIComponent(resultFilename)}&type=${encodeURIComponent(resultType)}`;
      if (resultSubfolder) {
        resultImageUrl += `&subfolder=${encodeURIComponent(resultSubfolder)}`;
      }
      
      console.log(`[ComfyBGRemove] Result image URL: ${resultImageUrl}`);

      // 10. 保存历史记录 (复用 remove_background_history 表)
      let historyConnection;
      try {
        historyConnection = await pool.getConnection();
        await historyConnection.query(
          'INSERT INTO remove_background_history (user_id, original_image_filename, result_image_url, processor) VALUES (?, ?, ?, ?)',
          [userId, originalFilename, resultImageUrl, 'comfyui'] // processor 是 'comfyui'
        );
        console.log(`[ComfyBGRemove] History saved for user ${userId}`);
      } catch (historyError) {
        console.error('[ComfyBGRemove] Failed to save history:', historyError);
        // 非致命错误，继续返回结果给用户
      } finally {
        if (historyConnection) historyConnection.release();
      }

      // 11. 构建代理后的图片 URL 给前端
      // 注意：这里的 API_BASE_URL 需要确保是你的前端可以访问到的后端服务地址
      // 通常在生产环境中，前端和后端可能在同一个域，或者通过Nginx等代理配置
      // 如果你的前端直接访问 caca.yzycolour.top，那么这里就可以硬编码或通过环境变量配置
      const backendApiBaseUrl = process.env.BACKEND_API_URL || 'https://caca.yzycolour.top'; // 或者你的实际后端API基础URL
      let proxiedImageUrl = `${backendApiBaseUrl}/api/images/comfy-output/${encodeURIComponent(resultType)}/${encodeURIComponent(resultFilename)}`;
      if (resultSubfolder) {
        proxiedImageUrl += `?subfolder=${encodeURIComponent(resultSubfolder)}`;
      }

      res.json({
        success: true,
        proxiedImageUrl: proxiedImageUrl, // <--- 添加这个字段
        message: '使用 ComfyUI 抠图成功',
        newCredits: req.locals?.newCredits, // 从 checkAndDeductCredits 中间件获取更新后的积分
        newDailyFreeUsed: req.locals?.newDailyFreeUsed // 和每日免费使用次数
      });

    } catch (error) {
      console.error('[ComfyBGRemove] ComfyUI 抠图流程出错:', error.message, error.stack);
      // 清理可能已上传的文件等 (如果需要)
      res.status(500).json({ 
          success: false,
          error: 'ComfyUI 抠图服务处理失败', 
          message: error.message 
      });
    }
  }
);

// 新的 ComfyUI 图片输出代理接口
router.get('/comfy-output/:type/:filename', authenticateToken, async (req, res) => {
  try {
    const { type, filename } = req.params;
    const subfolder = req.query.subfolder; // 子文件夹通过查询参数传递

    if (!type || !filename) {
      return res.status(400).json({ error: '缺少必要的图片参数 (type, filename)' });
    }

    // 重新构造 ComfyUI 的 view URL
    // COMFYUI_BASE_URL 应该已经在文件顶部定义了
    let comfyViewUrl = `${COMFYUI_WORKFLOW_EXEC_URL}/view?filename=${encodeURIComponent(filename)}&type=${encodeURIComponent(type)}`;
    if (subfolder) {
      comfyViewUrl += `&subfolder=${encodeURIComponent(subfolder)}`;
    }

    console.log(`[ComfyOutputProxy] Attempting to fetch image from ComfyUI: ${comfyViewUrl}`);

    // 从 ComfyUI 获取图片流
    const imageResponse = await axios({
      method: 'get',
      url: comfyViewUrl,
      responseType: 'stream' // 非常重要：获取流式响应
    });

    // 设置正确的 Content-Type
    let contentType = imageResponse.headers['content-type'];
    console.log(`[ComfyOutputProxy] Content-Type from ComfyUI: ${contentType}`);

    if (!contentType || contentType.includes('octet-stream') || contentType.includes('text/plain')) {
        // 如果 ComfyUI 未提供有效类型，或类型不明确，则根据文件名后缀判断
        // 这是一个常见的场景，特别是如果 ComfyUI 的 view 接口配置不当时
        const lowerFilename = filename.toLowerCase();
        if (lowerFilename.endsWith('.png')) {
            contentType = 'image/png';
        } else if (lowerFilename.endsWith('.jpg') || lowerFilename.endsWith('.jpeg')) {
            contentType = 'image/jpeg';
        } else if (lowerFilename.endsWith('.webp')) {
            contentType = 'image/webp';
        } else if (lowerFilename.endsWith('.gif')) {
            contentType = 'image/gif';
        } else {
            contentType = 'application/octet-stream'; // 最后的备选项
        }
        console.log(`[ComfyOutputProxy] Content-Type determined by extension: ${contentType}`);
    }
    res.setHeader('Content-Type', contentType);
    
    // 将 ComfyUI 的图片流直接 pipe 到我们的响应流中
    imageResponse.data.pipe(res);

  } catch (error) {
    console.error('[ComfyOutputProxy] 代理 ComfyUI 图片输出时出错:', error.response ? error.response.data : error.message, error.stack);
    if (error.response && error.response.status === 404) {
        return res.status(404).json({ error: '在 ComfyUI 服务器上未找到指定的图片文件。' });
    }
    res.status(500).json({ error: '获取 ComfyUI 输出图片失败' });
  }
});

module.exports = router; 
