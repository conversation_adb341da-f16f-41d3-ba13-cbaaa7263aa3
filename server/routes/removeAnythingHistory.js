const express = require('express');
const { authenticateToken } = require('../utils/auth');
const pool = require('../db/pool');

const router = express.Router();

console.log('removeAnythingHistory 路由文件已加载');

// GET /api/remove-anything/history
router.get('/history', authenticateToken, async (req, res) => {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 8;
    const offset = (page - 1) * limit;

    let connection;
    try {
        connection = await pool.getConnection();

        // 查询总数
        const countQuery = 'SELECT COUNT(*) as totalCount FROM remove_anything_history WHERE user_id = ?';
        const [[{ totalCount }]] = await connection.query(countQuery, [userId]);

        // 查询分页数据
        const historyQuery = `
            SELECT id, user_id, original_image_filename, mask_image_filename, result_image_url, prompt, expand_value, strength_value, output_mp_value, created_at
            FROM remove_anything_history
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?`;
        const [history] = await connection.query(historyQuery, [userId, limit, offset]);

        connection.release();

        const totalPages = Math.ceil(totalCount / limit);

        res.json({
            history,
            pagination: {
                totalCount,
                currentPage: page,
                totalPages,
                limit
            }
        });

    } catch (error) {
        if (connection) connection.release();
        console.error(`[Remove Anything History] Error fetching history for user ${userId}:`, error);
        res.status(500).json({ error: '获取历史记录失败', details: error.message });
    }
});

// POST /api/remove-anything/history - 保存新的历史记录
router.post('/history', authenticateToken, async (req, res) => {
    console.log('收到 POST /api/remove-anything/history 请求');
    const userId = req.user.id;
    const {
        original_image_filename,
        mask_image_filename,
        result_image_url,
        prompt,
        expand_value,
        strength_value,
        output_mp_value
    } = req.body;

    // 基本参数校验
    if (!result_image_url || !prompt) { // 至少需要结果图片和提示词
        return res.status(400).json({ error: '缺少必要的历史记录参数' });
    }

    let connection;
    try {
        connection = await pool.getConnection();
        const insertQuery = `
            INSERT INTO remove_anything_history 
            (user_id, original_image_filename, mask_image_filename, result_image_url, prompt, expand_value, strength_value, output_mp_value, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        `;
        await connection.query(insertQuery, [
            userId,
            original_image_filename || null,
            mask_image_filename || null,
            result_image_url,
            prompt,
            expand_value || null,
            strength_value || null,
            output_mp_value || null
        ]);
        connection.release();
        res.status(201).json({ message: '历史记录已保存' });
    } catch (error) {
        if (connection) connection.release();
        console.error(`[Remove Anything History] Error saving history for user ${userId}:`, error);
        res.status(500).json({ error: '保存历史记录失败', details: error.message });
    }
});

module.exports = router;
