const express = require('express');
const axios = require('axios');
const pool = require('../db/pool');
const { checkAndDeductCredits } = require('../middleware/checkCredits');
const { authenticateToken } = require('../utils/auth');
const router = express.Router();

// Midjourney API配置
const MJ_BASE_URL = process.env.MJ_BASE_URL || 'https://your-mj-api-base-url';
const MJ_API_KEY = process.env.MJ_API_KEY || 'sk-xxx';

// 支持的模式及积分消耗key
const MODE_CONFIG = {
  fast: {
    url: '/mj-fast/mj/submit/imagine',
    costKey: 'mj_fast_imagine',
  },
  relax: {
    url: '/mj-relax/mj/submit/imagine',
    costKey: 'mj_relax_imagine',
  },
};

// 动态选择功能key的中间件
function setFeatureKey(req, res, next) {
  const mode = req.body.mode || 'fast';
  req.featureKey = mode === 'relax' ? 'mj_relax_imagine' : 'mj_fast_imagine';
  next();
}

// 提交文生图任务（链式中间件写法）
router.post(
  '/submit/imagine',
  authenticateToken,
  setFeatureKey,
  (req, res, next) => checkAndDeductCredits(req.featureKey)(req, res, next),
  async (req, res) => {
    let connection;
    try {
      const { prompt, mode = 'fast', base64Array = [] } = req.body;
      const userId = req.user.id;

      if (!prompt) {
        return res.status(400).json({ error: '缺少prompt' });
      }
      if (!MODE_CONFIG[mode]) {
        return res.status(400).json({ error: '无效的mode' });
      }

      const selectedMode = MODE_CONFIG[mode];
      const mjAPIPath = selectedMode.url;
      const fullAPIURL = `${MJ_BASE_URL}${mjAPIPath}`;

      console.log(`[MJ Submit] User ${userId} submitting to ${fullAPIURL} with prompt: ${prompt}`);

      connection = await pool.getConnection();
      await connection.beginTransaction();

      const mjRes = await axios.post(
        fullAPIURL,
        { prompt, base64Array },
        {
          headers: {
            'Authorization': `Bearer ${MJ_API_KEY}`,
            'Content-Type': 'application/json',
          },
        }
      );

      console.log('[MJ Submit] MJ API Raw Response:', JSON.stringify(mjRes.data, null, 2));

      const mjApiResult = mjRes.data;
      let mjApiTaskId = '';
      let mjApiDescription = '任务已提交';
      let initialStatus = 'PENDING';
      let initialProgress = '0%';

      if (mjApiResult.code === 1 || mjApiResult.code === 22) {
        mjApiTaskId = mjApiResult.result;
        mjApiDescription = mjApiResult.description || mjApiDescription;
        if (mjApiResult.code === 22 && mjApiResult.properties && mjApiResult.properties.numberOfQueues !== undefined) {
          mjApiDescription = `排队中，前面还有${mjApiResult.properties.numberOfQueues}个任务`;
        }
      } else {
        console.error('[MJ Submit] MJ API returned error:', mjApiResult);
        await connection.rollback();
        return res.status(500).json({ error: mjApiResult.description || 'Midjourney任务提交失败', details: mjApiResult });
      }
      
      if (!mjApiTaskId) {
        console.error('[MJ Submit] Failed to get MJ Task ID from API response:', mjApiResult);
        await connection.rollback();
        return res.status(500).json({ error: 'Midjourney任务提交成功，但未能获取任务ID', details: mjApiResult });
      }

      const [insertResult] = await connection.query(
        `INSERT INTO ai_generation_history (user_id, prompt, model, mj_task_id, status, image_urls, created_at, task_provider, description, progress) VALUES (?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?)`,
        [userId, prompt, `midjourney_${mode}`, mjApiTaskId, initialStatus, '[]', 'midjourney', mjApiDescription, initialProgress]
      );
      const historyId = insertResult.insertId;

      await connection.commit();
      res.json({ success: true, taskId: mjApiTaskId, historyId: historyId, description: mjApiDescription });
      console.log(`[MJ Submit] Task ${mjApiTaskId} (DB ID: ${historyId}) submitted for user ${userId}.`);

    } catch (err) {
      if (connection) await connection.rollback();
      console.error('MJ任务提交失败', err.response ? JSON.stringify(err.response.data, null, 2) : err.message || err);
      const errorMsg = err.response && err.response.data && err.response.data.description ? err.response.data.description : (err.message || 'Midjourney任务提交失败，请检查MJ服务状态或API配置');
      res.status(500).json({ error: errorMsg, details: err.response ? err.response.data : null});
    } finally {
      if (connection) {
        connection.release();
      }
    }
  }
);

// 查询单个MJ任务状态
router.get('/task/:id/fetch', async (req, res) => {
  try {
    const { id } = req.params;
    // 修正URL，移除/fast/前缀
    const url = `${MJ_BASE_URL}/mj/task/${id}/fetch`;
    const mjRes = await axios.get(url, {
      headers: {
        'Authorization': `Bearer ${MJ_API_KEY}`,
        'Content-Type': 'application/json',
      },
    });
    res.json(mjRes.data);
  } catch (err) {
    console.error('MJ任务状态查询失败', err);
    res.status(500).json({ error: 'Midjourney任务状态查询失败' });
  }
});

// 后端定时轮询未完成MJ任务
async function pollPendingMJTasks() {
  let rows;
  try {
    // 查询所有PENDING或IN_PROGRESS的MJ任务 (增加了IN_PROGRESS，确保进行中的也能更新队列信息)
    // 使用 pool.query 直接执行查询，并获取 status 字段
    [rows] = await pool.query(
      `SELECT id, mj_task_id, model, status FROM ai_generation_history WHERE status IN ('PENDING', 'IN_PROGRESS') AND mj_task_id IS NOT NULL AND task_provider = 'midjourney'`
    );

    if (rows.length === 0) {
      // console.log('[MJ Polling] No pending MJ tasks to poll.');
      return;
    }
    console.log(`[MJ Polling] Found ${rows.length} pending MJ tasks to poll.`);
  } catch (err) {
    console.error('MJ任务轮询主逻辑失败 (获取任务列表时):', err);
    return; // 如果无法获取任务列表，则退出
  }

  let connection;
  try {
    connection = await pool.getConnection(); // 获取一个连接用于后续所有任务的更新事务

    for (const row of rows) {
      try {
        // API文档指出 /mj/task/{id}/fetch 可以查询所有类型的任务，无需区分mode
        // 使用标准路径格式，直接访问/mj/task/{id}/fetch API
        const url = `${MJ_BASE_URL}/mj/task/${row.mj_task_id}/fetch`;

        console.log(`[MJ Polling] Polling MJ task ${row.mj_task_id} from URL: ${url}`);
        const mjRes = await axios.get(url, {
          headers: {
            'Authorization': `Bearer ${MJ_API_KEY}`,
            'Content-Type': 'application/json',
          },
          timeout: 15000, // 增加超时时间
        });

        const mjTask = mjRes.data;
        console.log(`[MJ Polling] MJ task ${row.mj_task_id} status: ${mjTask.status}, progress: ${mjTask.progress}, description: ${mjTask.description}`);

        await connection.beginTransaction(); // 开始事务

        // 无论状态如何，都先更新 description 和 progress
        let updateQuery = `UPDATE ai_generation_history SET description = ?, progress = ?`;
        const updateParams = [mjTask.description, mjTask.progress];

        if (mjTask.status === 'SUCCESS') {
          updateQuery += `, status = ?, image_urls = ?, finish_time = ?, mj_buttons = ?`;
          // 将毫秒时间戳转换为MySQL DATETIME格式
          const finishDateTime = mjTask.finishTime ? new Date(mjTask.finishTime).toISOString().slice(0, 19).replace('T', ' ') : null;
          updateParams.push(
            'SUCCESS',
            JSON.stringify(mjTask.imageUrl ? [mjTask.imageUrl] : []), // 确保是JSON数组
            finishDateTime, // 使用转换后的DATETIME格式
            mjTask.buttons ? JSON.stringify(mjTask.buttons) : null // 保存 buttons 数组为 JSON 字符串，如果不存在则为 null
          );
        } else if (mjTask.status === 'FAILURE') {
          updateQuery += `, status = ?, error_message = ?, finish_time = ?`;
          // 将毫秒时间戳转换为MySQL DATETIME格式
          const finishDateTime = mjTask.finishTime ? new Date(mjTask.finishTime).toISOString().slice(0, 19).replace('T', ' ') : null;
          updateParams.push(
            'FAILURE',
            mjTask.failReason,
            finishDateTime // 使用转换后的DATETIME格式
          );
        } else if (mjTask.status === 'IN_PROGRESS' && row.status !== 'IN_PROGRESS') { // row.status 来自数据库
            // 如果API状态是IN_PROGRESS，且数据库状态还不是IN_PROGRESS，则更新数据库状态
            updateQuery += `, status = ?`;
            updateParams.push('IN_PROGRESS');
        }
        // 对于其他状态 (如 NOT_START, SUBMITTED, MODAL)，只更新 description 和 progress

        updateQuery += ` WHERE id = ?`;
        updateParams.push(row.id);

        await connection.query(updateQuery, updateParams);
        await connection.commit(); // 提交事务
        console.log(`[MJ Polling] MJ task ${row.mj_task_id} (DB ID: ${row.id}) updated in DB. Status: ${mjTask.status}`);

      } catch (err) {
        if (connection && typeof connection.rollback === 'function') { // 确保connection有效且有rollback方法
            try {
                await connection.rollback(); // 出错则回滚
            } catch (rbError) {
                console.error(`[MJ Polling] Rollback error for task ${row.mj_task_id}:`, rbError);
            }
        }
        console.error(`轮询单个MJ任务失败 ${row.mj_task_id}`, err.message || err.code || err);
      }
    }
  } catch (err) {
    console.error('MJ任务轮询主逻辑失败 (处理任务更新时):', err);
  } finally {
    if (connection) {
      connection.release();
      // console.log('数据库连接释放 (来自 MJ Polling)');
    }
  }
}

// 每10秒轮询一次
setInterval(pollPendingMJTasks, 10000);

// 新增：处理Midjourney按钮动作的API
router.post(
  '/submit/action',
  authenticateToken,
  checkAndDeductCredits('ai_mj_action'),
  async (req, res) => {
    let connection;
    try {
        const { customId, taskId, historyId } = req.body; // taskId 是原始MJ任务ID, historyId 是原始数据库历史记录ID
        const userId = req.user.id;

        if (!customId || !taskId || !historyId) {
            return res.status(400).json({ success: false, error: 'Missing customId, taskId, or historyId' });
        }

        console.log(`[MJ Action API] Received action: customId=${customId}, taskId=${taskId}, historyId=${historyId} for user ${userId}`);

        // 1. 调用 Midjourney 服务商的 /mj/submit/action API
        const mjActionUrl = `${MJ_BASE_URL}/mj/submit/action`;
        const mjActionResponse = await axios.post(
            mjActionUrl,
            { customId, taskId }, // 根据MJ API文档，发送 customId 和 taskId
            {
                headers: {
                    'Authorization': `Bearer ${MJ_API_KEY}`,
                    'Content-Type': 'application/json',
                },
            }
        );

        console.log('[MJ Action API] MJ Service Raw Response:', JSON.stringify(mjActionResponse.data, null, 2));
        const mjServiceResult = mjActionResponse.data;

        if (!(mjServiceResult.code === 1 || mjServiceResult.code === 22) || !mjServiceResult.result) {
            console.error('[MJ Action API] MJ Service returned an error or invalid response:', mjServiceResult);
            return res.status(500).json({
                success: false,
                error: mjServiceResult.description || 'Failed to submit action to Midjourney service or invalid response.',
                details: mjServiceResult
            });
        }

        const newMjTaskId = mjServiceResult.result; // 这是新的Midjourney任务ID
        const mjActionDescription = mjServiceResult.description || 'Action submitted successfully to MJ service.';

        // 2. 获取原始历史记录信息
        connection = await pool.getConnection();
        const [originalHistoryRows] = await connection.query(
            'SELECT prompt, model FROM ai_generation_history WHERE id = ? AND user_id = ?',
            [historyId, userId]
        );

        if (originalHistoryRows.length === 0) {
            await connection.release();
            return res.status(404).json({ success: false, error: 'Original history record not found or access denied.' });
        }
        const originalHistory = originalHistoryRows[0];

        // 3. 创建新的历史记录条目
        const initialStatus = 'PENDING';
        const initialProgress = '0%';
        // 可以在 description 中指明这是由哪个 customId 产生的操作
        const newDescription = `Action via ${customId.substring(0,30)}... based on task ${taskId.substring(0,8)}... Description: ${mjActionDescription}`;

        const [insertResult] = await connection.query(
            `INSERT INTO ai_generation_history 
                (user_id, prompt, model, mj_task_id, status, image_urls, created_at, task_provider, description, progress, finish_time, mj_buttons) 
             VALUES (?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?, NULL, NULL)`,
            [userId, originalHistory.prompt, originalHistory.model, newMjTaskId, initialStatus, '[]', 'midjourney', newDescription, initialProgress]
        );
        const newHistoryId = insertResult.insertId;

        res.json({
            success: true,
            newTaskId: newMjTaskId,
            newHistoryId: newHistoryId,
            description: 'Midjourney action processed and new task created.'
        });
        console.log(`[MJ Action API] New MJ task ${newMjTaskId} (DB ID: ${newHistoryId}) created for user ${userId} from action.`);

    } catch (error) {
        console.error('[MJ Action API] Error processing MJ action:', error.response ? JSON.stringify(error.response.data, null, 2) : error.message || error);
        const errorMsg = error.response && error.response.data && error.response.data.description 
            ? error.response.data.description 
            : (error.message || 'Failed to process Midjourney action.');
        res.status(500).json({ 
            success: false, 
            error: errorMsg,
            details: error.response ? error.response.data : null 
        });
    } finally {
        if (connection) {
            connection.release();
        }
    }
});

module.exports = router; 