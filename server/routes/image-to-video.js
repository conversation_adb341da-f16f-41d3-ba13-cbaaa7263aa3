const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { authenticateToken } = require('../utils/auth'); // 确保路径正确
const fetch = require('node-fetch'); // 用于调用阿里云 API
const pool = require('../db/pool'); // <--- 引入数据库连接池
const axios = require('axios'); // For ComfyUI calls
const FormData = require('form-data'); // For ComfyUI file uploads
const sharp = require('sharp'); // 新增：引入sharp
const { checkAndDeductCredits } = require('../middleware/checkCredits'); // 引入中间件
// const { storeVideoSegment, getJobStatus, getFullVideoUrl } = require('../services/videoSegmentService');

// --- ComfyUI Configuration ---
const COMFYUI_BASE_URL = 'https://u63642-a2b4-fccb0b0d.westx.seetacloud.com:8443'; // Your ComfyUI instance

// 确保 DASHSCOPE_API_KEY 环境变量已设置
if (!process.env.DASHSCOPE_API_KEY) {
    console.error("错误：DASHSCOPE_API_KEY 环境变量未设置。");
    // 在生产环境中，可能希望进程退出或采取其他错误处理措施
}

// --- Multer Setup for Image Upload --- (与 index.js 中的 upload 配置类似，但可以独立)
// 与 index.js 中的 uploadDir 和 uploadPath 保持一致
const uploadDir = process.env.UPLOAD_DIR || 'uploads';
const uploadPath = path.join(__dirname, '..', uploadDir); //退回到 server/ 再进入 uploads/

// 确保上传目录存在 (如果路由文件在不同目录，需要调整路径)
if (!fs.existsSync(uploadPath)) {
    fs.mkdirSync(uploadPath, { recursive: true });
}

const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadPath);
    },
    filename: function (req, file, cb) {
        // 使用 uuid 或其他方式确保文件名唯一
        const { v4: uuidv4 } = require('uuid');
        const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
        cb(null, uniqueName);
    }
});

const imageUpload = multer({
    storage: storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB 与前端一致
    fileFilter: function (req, file, cb) {
        const allowedTypes = /jpeg|jpg|png|webp/;
        const mimetype = allowedTypes.test(file.mimetype);
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('仅支持 PNG, JPG, WEBP 格式的图片文件!'));
    }
});

// --- 阿里云图生视频 API 端点 ---
const ALIYUN_I2V_SUBMIT_URL = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/video-generation/video-synthesis';
// --- 阿里云首尾帧图生视频 API 端点 ---
const ALIYUN_KF2V_SUBMIT_URL = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/image2video/video-synthesis';
const ALIYUN_I2V_STATUS_URL_PREFIX = 'https://dashscope.aliyuncs.com/api/v1/tasks/';

// --- 服务端轮询间隔时间 (毫秒) ---
const POLL_INTERVAL_MS = 10000; // 10秒轮询一次

// --- 任务状态跟踪 ---
const videoTasks = {};

// 工具函数：压缩图片为高质量JPG
async function compressImageToJpg(inputPath, outputPath, maxSize = 1280, quality = 88) {
    return sharp(inputPath)
        .resize({ width: maxSize, height: maxSize, fit: 'inside', withoutEnlargement: true })
        .jpeg({ quality, mozjpeg: true })
        .toFile(outputPath);
}

// --- 后台轮询阿里云图生视频任务状态 ---
async function pollAliyunTaskStatus(taskId, userId, localImagePath) {
    console.log(`开始后台轮询图生视频任务状态: ${taskId}, 用户ID: ${userId}, LocalImagePath:`, localImagePath);
    
    // 记录该任务正在轮询中
    videoTasks[taskId] = {
        userId,
        polling: true,
        lastCheck: new Date(),
        status: 'PENDING',
        aliyunVideoUrl: null // Store Aliyun video URL temporarily
    };
    
    const intervalId = setInterval(async () => {
        try {
            // 检查上次轮询时间，超过30分钟未完成则终止轮询
            const now = new Date();
            const lastCheck = videoTasks[taskId]?.lastCheck || now;
            const minutesPassed = (now - lastCheck) / (1000 * 60);
            
            if (minutesPassed > 30) {
                console.log(`任务 ${taskId} 轮询超过30分钟仍未完成，停止轮询`);
                clearInterval(intervalId);
                videoTasks[taskId] = null;
                return;
            }
            
            // 更新最后检查时间
            if (videoTasks[taskId]) {
                videoTasks[taskId].lastCheck = now;
            }
            
            const aliyunStatusUrl = `${ALIYUN_I2V_STATUS_URL_PREFIX}${taskId}`;
            console.log(`后台轮询图生视频任务 ${taskId} 状态，URL: ${aliyunStatusUrl}`);
            
            const aliyunResponse = await fetch(aliyunStatusUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${process.env.DASHSCOPE_API_KEY}`
                }
            });

            const aliyunResult = await aliyunResponse.json();
            console.log(`任务 ${taskId} 后台轮询状态查询结果:`, aliyunResult);

            const taskStatus = aliyunResult.output?.task_status;
            let videoUrlResult = aliyunResult.output?.video_url; // Use let for potential modification
            const taskErrorMessage = aliyunResult.output?.message;

            // 更新内存中的任务状态
            if (videoTasks[taskId]) {
                if (taskStatus) videoTasks[taskId].status = taskStatus;
                if (videoUrlResult) videoTasks[taskId].aliyunVideoUrl = videoUrlResult;
            }

            if (!aliyunResponse.ok || !taskStatus) {
                console.error(`后台轮询任务 ${taskId} 状态失败:`, aliyunResult);
                return; // 继续下次轮询，而不是立即终止
            }

            // 更新数据库中的任务状态
            let connection;
            try {
                connection = await pool.getConnection();
                let updateQuery = 'UPDATE image_to_video_history SET status = ?';
                const queryParams = [taskStatus];
                let finalVideoUrl = null; // This will be the ComfyUI URL or Aliyun fallback

                if (taskStatus.toUpperCase() === 'SUCCEEDED' && videoUrlResult) {
                    try {
                        console.log(`任务 ${taskId} SUCCEEDED, Aliyun video URL: ${videoUrlResult}. Attempting to transfer to ComfyUI.`);
                        const comfyPayload = {
                            url: videoUrlResult,
                            filename_prefix: `i2v_vid_${userId}_${taskId.substring(0, 8)}`,
                            subdir: 'image-to-video/outputs'
                        };
                        const comfyResponse = await axios.post(`${COMFYUI_BASE_URL}/yzy/download-url`, comfyPayload, {
                            headers: { 'Content-Type': 'application/json' }
                        });

                        if (comfyResponse.data && comfyResponse.data.success) {
                            const { filename, subfolder: comfySubdirValue, type: comfyType } = comfyResponse.data;
                            finalVideoUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(filename)}&subfolder=${encodeURIComponent(comfySubdirValue)}&type=${comfyType}`;
                            console.log(`任务 ${taskId} 成功转移到 ComfyUI: ${finalVideoUrl}`);
                        } else {
                            console.error(`任务 ${taskId} ComfyUI transfer failed:`, comfyResponse.data);
                            finalVideoUrl = videoUrlResult; // Fallback to Aliyun URL
                        }
                    } catch (comfyError) {
                        console.error(`任务 ${taskId} ComfyUI transfer error:`, comfyError.message);
                        finalVideoUrl = videoUrlResult; // Fallback to Aliyun URL
                    }
                    updateQuery += ', video_url = ?';
                    queryParams.push(finalVideoUrl);

                } else if (taskStatus.toUpperCase() === 'FAILED') {
                    updateQuery += ', error_message = ?';
                    queryParams.push(taskErrorMessage || '任务执行失败，详情未知。');
                }

                updateQuery += ' WHERE aliyun_task_id = ?';
                queryParams.push(taskId);

                await connection.query(updateQuery, queryParams);
                console.log(`后台轮询：图生视频任务 ${taskId} 状态已更新为: ${taskStatus}`);
                
                // 如果任务已完成或失败，停止轮询
                if (taskStatus.toUpperCase() === 'SUCCEEDED' || taskStatus.toUpperCase() === 'FAILED' || taskStatus.toUpperCase() === 'UNKNOWN_ERROR') {
                    console.log(`后台轮询：任务 ${taskId} 已完成，状态为 ${taskStatus}，停止轮询`);
                    clearInterval(intervalId);
                    
                    // 删除临时文件
                    if (typeof localImagePath === 'string') {
                        // 兼容旧版单文件模式
                        if (fs.existsSync(localImagePath)) {
                            fs.unlink(localImagePath, (err) => {
                                if (err) console.error(`后台轮询：删除本地临时图片 ${localImagePath} 失败:`, err);
                                else console.log(`后台轮询：本地临时图片 ${localImagePath} 已成功删除。`);
                            });
                        } else {
                            console.warn(`后台轮询：本地临时图片路径 ${localImagePath} 无效或文件不存在，无法删除。`);
                        }
                        // 新增：删除.aliyun.jpg压缩图
                        const aliyunJpgPath = localImagePath.replace(/\.[^.]+$/, '.aliyun.jpg');
                        if (fs.existsSync(aliyunJpgPath)) {
                            fs.unlink(aliyunJpgPath, (err) => {
                                if (err) console.error(`后台轮询：删除本地压缩图片 ${aliyunJpgPath} 失败:`, err);
                                else console.log(`后台轮询：本地压缩图片 ${aliyunJpgPath} 已成功删除。`);
                            });
                        }
                    } else if (localImagePath && typeof localImagePath === 'object') {
                        // 新版多文件模式（首尾帧）
                        if (localImagePath.firstFrame && fs.existsSync(localImagePath.firstFrame)) {
                            fs.unlink(localImagePath.firstFrame, (err) => {
                                if (err) console.error(`后台轮询：删除首帧临时图片 ${localImagePath.firstFrame} 失败:`, err);
                                else console.log(`后台轮询：首帧临时图片 ${localImagePath.firstFrame} 已成功删除。`);
                            });
                        }
                        
                        if (localImagePath.lastFrame && fs.existsSync(localImagePath.lastFrame)) {
                            fs.unlink(localImagePath.lastFrame, (err) => {
                                if (err) console.error(`后台轮询：删除尾帧临时图片 ${localImagePath.lastFrame} 失败:`, err);
                                else console.log(`后台轮询：尾帧临时图片 ${localImagePath.lastFrame} 已成功删除。`);
                            });
                        }
                        // 新增：删除首尾帧.aliyun.jpg压缩图
                        if (localImagePath.firstAliyun && fs.existsSync(localImagePath.firstAliyun)) {
                            fs.unlink(localImagePath.firstAliyun, (err) => {
                                if (err) console.error(`后台轮询：删除首帧压缩图片 ${localImagePath.firstAliyun} 失败:`, err);
                                else console.log(`后台轮询：首帧压缩图片 ${localImagePath.firstAliyun} 已成功删除。`);
                            });
                        }
                        if (localImagePath.lastAliyun && fs.existsSync(localImagePath.lastAliyun)) {
                            fs.unlink(localImagePath.lastAliyun, (err) => {
                                if (err) console.error(`后台轮询：删除尾帧压缩图片 ${localImagePath.lastAliyun} 失败:`, err);
                                else console.log(`后台轮询：尾帧压缩图片 ${localImagePath.lastAliyun} 已成功删除。`);
                            });
                        }
                    }
                    
                    videoTasks[taskId] = null;
                }
            } catch (dbError) {
                console.error('后台轮询：更新图生视频历史记录数据库失败:', dbError);
            } finally {
                if (connection) connection.release();
            }

        } catch (error) {
            console.error(`后台轮询图生视频任务 ${taskId} 出错:`, error);
            // 轮询出错，但不停止轮询，让它继续尝试
        }
    }, POLL_INTERVAL_MS);
}

// 新的中间件，用于动态确定 featureKey 并调用 checkAndDeductCredits
function dynamicCreditCheckForImageToVideo(req, res, next) {
    const model = req.body.model; // 从请求体中获取模型
    let featureKey;

    if (model === 'wanx2.1-i2v-turbo') {
        featureKey = 'image_to_video_turbo';
    } else if (model === 'wanx2.1-i2v-plus') {
        featureKey = 'image_to_video_plus';
    } else {
        // 如果模型无效或未提供，可以提前返回错误，或让 checkAndDeductCredits 内部处理 featureKey 无效的情况
        return res.status(400).json({ message: '无效的模型类型，无法确定计费规则。' });
    }

    // 调用原始的 checkAndDeductCredits 工厂函数，并立即执行返回的中间件
    checkAndDeductCredits(featureKey, false)(req, res, next);
}

// --- 路由定义 ---

// POST /api/image-to-video/submit - 提交图生视频任务
router.post('/submit', 
    authenticateToken, 
    imageUpload.single('image'), 
    dynamicCreditCheckForImageToVideo, // <--- 使用新的动态检查中间件
    async (req, res) => {
    console.log('图生视频 /submit 请求体:', req.body);
    console.log('图生视频 /submit 上传文件:', req.file);
    const userId = req.user.id;

    if (!req.file) {
        return res.status(400).json({ message: '缺少图片文件。' });
    }
    if (!req.body.prompt) {
        return res.status(400).json({ message: '缺少提示词 (prompt)。' });
    }
    if (!req.body.model) {
        return res.status(400).json({ message: '缺少模型参数 (model)。' });
    }
    if (!req.body.resolution) {
        return res.status(400).json({ message: '缺少分辨率参数 (resolution)。' });
    }

    const { prompt, model, resolution } = req.body;
    const imageName = req.file.filename;
    const localImagePath = req.file.path;
    // 新增：生成压缩JPG路径
    const aliyunJpgPath = localImagePath.replace(/\.[^.]+$/, '.aliyun.jpg');
    const aliyunJpgName = imageName.replace(/\.[^.]+$/, '.aliyun.jpg');
    let compressed = false;
    try {
        await compressImageToJpg(localImagePath, aliyunJpgPath, 1280, 88);
        compressed = true;
    } catch (e) {
        console.error('图片压缩失败，使用原图:', e);
    }
    let connection; 

    try {
        connection = await pool.getConnection();
        await connection.beginTransaction();

        // --- 0. Prepare public URL for the uploaded image for Aliyun ---
        // Ensure your server is configured to serve files from the 'uploadDir' (e.g., 'uploads')
        // The 'uploadDir' should be relative to your app's public serving directory if using Express static,
        // or you need a specific route to serve these files.
        // For this example, we assume 'uploadDir' is served at the root of API_BASE_URL.
        // If your API_BASE_URL is 'https://caca.yzycolour.top/api', and uploadDir is 'uploads'
        // and 'uploads' is served at 'https://caca.yzycolour.top/uploads', then this is fine.
        // Otherwise, adjust 'imageUrlForAliyun' accordingly.

        // It seems API_BASE_URL from .env might be like 'http://localhost:32009' for local dev,
        // or 'https://caca.yzycolour.top' for production (without /api suffix for file serving).
        // Let's construct it carefully.
        // const appBaseUrl = process.env.NODE_ENV === 'production' 
        //     ? 'https://caca.yzycolour.top' 
        //     : `http://localhost:${process.env.PORT || 32009}`;
        
        // 更可靠的方式：直接使用配置好的公网基础URL
        // 这个环境变量应该在你的生产环境中设置为 'https://caca.yzycolour.top'
        // 在本地开发时，如果你的本地服务也是通过特定域名（如localhost）和端口提供静态文件，可以相应设置。
        const appPublicBaseUrl = process.env.APP_PUBLIC_BASE_URL || 'https://caca.yzycolour.top';

        const imageUrlForAliyun = compressed
            ? `${appPublicBaseUrl}/${uploadDir}/${aliyunJpgName}`
            : `${appPublicBaseUrl}/${uploadDir}/${imageName}`;
        console.log(`生成的阿里云图片URL: ${imageUrlForAliyun}`);


        // --- 0.1. Upload original image to ComfyUI for persistence (can happen in parallel or after Aliyun submission) ---
        let original_image_comfyui_url = null;
        let comfyui_original_filename = null;
        try {
            const imageFormData = new FormData();
            imageFormData.append('image', fs.createReadStream(localImagePath));
            imageFormData.append('subfolder', 'image-to-video/inputs');
            imageFormData.append('type', 'input');
            imageFormData.append('overwrite', 'false'); // Or true, depending on desired behavior

            const comfyUploadResponse = await axios.post(`${COMFYUI_BASE_URL}/upload/image`, imageFormData, {
                headers: imageFormData.getHeaders()
            });

            if (comfyUploadResponse.data && comfyUploadResponse.data.name) {
                const { name, subfolder, type } = comfyUploadResponse.data;
                comfyui_original_filename = name;
                const effectiveType = type || 'input'; // Default to 'input' if type is not in response
                original_image_comfyui_url = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(name)}&subfolder=${encodeURIComponent(subfolder)}&type=${effectiveType}`;
                console.log(`原始图片上传到 ComfyUI 成功: ${original_image_comfyui_url}`);
            } else {
                throw new Error('ComfyUI upload did not return expected data.');
            }
        } catch (uploadError) {
            console.error('上传原始图片到 ComfyUI 失败:', uploadError.message);
            await connection.rollback();
            // Delete the temp uploaded file by multer
            fs.unlink(localImagePath, (err) => {
                if (err) console.error('删除本地临时图片失败 (ComfyUI upload error):', err);
            });
            return res.status(500).json({ message: '上传原始图片到处理服务器失败，请重试。' });
        }

        // --- 1. 确定功能键并获取成本 --- (此部分将被移除，因为已由 dynamicCreditCheckForImageToVideo 和 checkAndDeductCredits 处理)
        /*
        let featureKey;
        if (model === 'wanx2.1-i2v-turbo') {
            featureKey = 'image_to_video_turbo';
        } else if (model === 'wanx2.1-i2v-plus') {
            featureKey = 'image_to_video_plus';
        } else {
            await connection.rollback(); //  确保在返回前有回滚
            return res.status(400).json({ message: '无效的模型类型，无法确定功能成本。' });
        }

        const [costRows] = await connection.query('SELECT cost FROM feature_costs WHERE feature_key = ?', [featureKey]);
        if (costRows.length === 0) {
            await connection.rollback();
            return res.status(500).json({ message: `功能 ${featureKey} 的成本未配置。` });
        }
        const costIncurred = parseInt(costRows[0].cost);
        if (isNaN(costIncurred) || costIncurred < 0) { 
            await connection.rollback();
            return res.status(500).json({ message: `功能 ${featureKey} 的成本配置无效。` });
        }
        */
        // costIncurred 将从 req.locals (如果 checkCredits 设置了) 或直接认为已处理

        // --- 2. 检查并扣除用户积分 --- (此部分将被移除)
        /*
        // ... (所有获取 DAILY_FREE_CREDITS_AMOUNT, 查询用户积分, 计算和更新积分的逻辑) ...
        */

        // --- 3. 调用阿里云 API ---
        // const imageUrlForAliyun was defined above, using the locally served temp file.

        const payload = {
            model: model,
            input: { prompt: prompt, img_url: imageUrlForAliyun },
            parameters: { resolution: resolution }
        };

        console.log('调用阿里云图生视频创建任务 API，Payload:', JSON.stringify(payload));
        const aliyunResponse = await fetch(ALIYUN_I2V_SUBMIT_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.DASHSCOPE_API_KEY}`,
                'X-DashScope-Async': 'enable'
            },
            body: JSON.stringify(payload)
        });
        const aliyunResult = await aliyunResponse.json();
        console.log('阿里云 API 响应:', aliyunResult);

        const aliyunTaskId = aliyunResult.output?.task_id;
        const initialTaskStatus = aliyunResult.output?.task_status || 'PENDING';

        if (!aliyunResponse.ok || (aliyunResult.code && aliyunResult.code !== "Success" && !aliyunTaskId) ) {
            const errorMessage = aliyunResult.message || `阿里云 API 错误 (HTTP ${aliyunResponse.status})`;
            console.error('阿里云图生视频任务提交失败:', errorMessage, '完整响应:', aliyunResult);
            // 如果阿里云提交失败，需要回滚积分
            await connection.rollback(); 
            // Local temp file will be cleaned up in 'finally' or if ComfyUI upload also fails before this.
            // No need to specifically delete localImagePath here if other operations might still need it or if 'finally' handles it.
            return res.status(aliyunResponse.status_code || aliyunResponse.status || 500).json({ 
                message: `图生视频任务提交失败: ${errorMessage}`,
                details: aliyunResult
            });
        }
        if (!aliyunTaskId) {
            console.error('阿里云 API 响应中缺少 task_id:', aliyunResult);
            await connection.rollback(); // 回滚积分
            return res.status(500).json({
                message: '图生视频任务提交成功，但未能从阿里云获取任务ID。',
                details: aliyunResult
            });
        }

        // --- 4. 将任务信息存入历史记录表 --- 
        // 需要 costIncurred。如果 checkAndDeductCredits 中间件能通过 req.locals 将其传递过来，则使用。
        // 否则，需要重新查询一次成本（但这不理想）。
        // 假设 checkAndDeductCredits 已经将实际使用的 featureKey 和 cost 放入 req.locals (例如 req.locals.featureKeyUsed, req.locals.costUsed)
        // 如果没有，我们需要在 dynamicCreditCheckForImageToVideo 中重新查询成本以记录历史，
        // 或者修改 checkAndDeductCredits 以传递这些信息。
        // 为简单起见，我们先假设需要重新获取成本用于记录。但最佳实践是让 checkAndDeductCredits 提供此信息。

        let featureKeyForHistory;
        if (model === 'wanx2.1-i2v-turbo') {
            featureKeyForHistory = 'image_to_video_turbo';
        } else if (model === 'wanx2.1-i2v-plus') {
            featureKeyForHistory = 'image_to_video_plus';
        } else { /* ... */ }

        const [costRowsForHistory] = await connection.query('SELECT cost FROM feature_costs WHERE feature_key = ?', [featureKeyForHistory]);
        const costIncurredForHistory = (costRowsForHistory.length > 0 && !isNaN(parseInt(costRowsForHistory[0].cost))) ? parseInt(costRowsForHistory[0].cost) : 0;

        await connection.query(
            `INSERT INTO image_to_video_history 
             (user_id, aliyun_task_id, original_image_filename, original_image_comfyui_url, prompt, model_used, resolution_used, status, cost_incurred)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`, 
            [userId, aliyunTaskId, comfyui_original_filename, original_image_comfyui_url, prompt, model, resolution, initialTaskStatus, costIncurredForHistory]
        );
        console.log(`图生视频任务 ${aliyunTaskId} 已存入历史记录，用户ID: ${userId}, 消耗积分: ${costIncurredForHistory}, 原始图片ComfyUI文件名: ${comfyui_original_filename}`);
        
        await connection.commit(); // 所有操作成功，提交事务

        // Local temporary file is no longer needed by Aliyun (as it would have fetched it)
        // and ComfyUI upload is also done (or attempted).
        // It's good practice to clean it up if it's not auto-cleaned by multer or OS.
        // However, if ComfyUI upload failed but Aliyun succeeded, we might lose the original if deleted too soon.
        // The fs.unlink was here before, let's ensure it's correctly placed.
        // If Aliyun succeeds, we commit. If ComfyUI upload (which happens before Aliyun now in flow) fails, we rollback and return.
        // So, if we reach here, both ComfyUI upload (attempt) and Aliyun submission (attempt) are past.
        // The original fs.unlink was after commit:
        // fs.unlink(localImagePath, (err) => { // <<--- Temporarily remove this immediate unlink
        //     if (err) console.error('提交成功后删除本地临时图片失败 (fs.unlink):', err);
        //     else console.log('本地临时图片已删除 (fs.unlink):', localImagePath);
        // });
        
        // --- 5. 启动后台轮询 ---
        // Pass the localImagePath to the polling function so it can delete it later
        pollAliyunTaskStatus(aliyunTaskId, userId, localImagePath); 
        
        res.json(aliyunResult); // 返回阿里云的响应

    } catch (error) {
        console.error('图生视频 /submit 路由出错:', error);
        if (connection) {
            await connection.rollback(); // 确保任何错误都回滚事务
        }
        // 如果发生错误，并且图片已上传，考虑删除 (local temp file by multer)
        // const uploadedImagePathOnCatch = path.join(uploadPath, imageName); // imageName is temp multer name
        if (localImagePath && fs.existsSync(localImagePath)) { // Check localImagePath
            fs.unlink(localImagePath, (err) => {
                if (err) console.error('因catch块错误删除本地图片失败:', err);
            });
        }
        res.status(500).json({ message: '提交图生视频任务失败，服务器内部错误。', details: error.message });
    } finally {
        if (connection) {
            connection.release();
        }
    }
});

// GET /api/image-to-video/status/:taskId - 查询图生视频任务状态
router.get('/status/:taskId', authenticateToken, async (req, res) => {
    const { taskId } = req.params;
    // const userId = req.user.id; // 可选：用于验证任务归属

    if (!taskId) {
        return res.status(400).json({ message: '缺少任务 ID (taskId)。' });
    }

    const aliyunStatusUrl = `${ALIYUN_I2V_STATUS_URL_PREFIX}${taskId}`;
    console.log(`查询阿里云图生视频任务状态，URL: ${aliyunStatusUrl}`);

    try {
        const aliyunResponse = await fetch(aliyunStatusUrl, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${process.env.DASHSCOPE_API_KEY}`
            }
        });

        const aliyunResult = await aliyunResponse.json();
        console.log(`任务 ${taskId} 状态查询，阿里云 API 响应:`, aliyunResult);

        const taskStatus = aliyunResult.output?.task_status;
        let videoUrlResult = aliyunResult.output?.video_url; // Aliyun URL
        const taskErrorMessage = aliyunResult.output?.message;

        if (!aliyunResponse.ok || (aliyunResult.code && aliyunResult.code !== "Success" && !taskStatus)) {
            const errorMessage = aliyunResult.message || `阿里云 API 错误 (HTTP ${aliyunResponse.status})`;
             console.error(`查询任务 ${taskId} 状态失败:`, errorMessage, '完整响应:', aliyunResult);
            return res.status(aliyunResponse.status_code || aliyunResponse.status || 500).json({ 
                message: `查询任务状态失败: ${errorMessage}`,
                details: aliyunResult 
            });
        }

        // 检查 task_status 是否存在
        if (!taskStatus) {
             console.error(`阿里云 API 响应中缺少 task_status (任务ID: ${taskId}):`, aliyunResult);
             return res.status(500).json({
                message: '查询任务状态成功，但未能从阿里云获取明确的任务状态。',
                details: aliyunResult
            });
        }

        let connection;
        let userIdForComfy = null;

        try {
            connection = await pool.getConnection();

            // If task succeeded and video_url is present, try to move to ComfyUI
            // We need user_id for filename prefix. Fetch it from history.
            if (taskStatus.toUpperCase() === 'SUCCEEDED' && videoUrlResult) {
                const [[taskEntry]] = await connection.query('SELECT user_id FROM image_to_video_history WHERE aliyun_task_id = ?', [taskId]);
                if (taskEntry && taskEntry.user_id) {
                    userIdForComfy = taskEntry.user_id;
                }
            }
            
            let updateQuery = 'UPDATE image_to_video_history SET status = ?';
            const queryParams = [taskStatus];
            let finalVideoUrl = null; // ComfyUI URL or Aliyun fallback

            if (taskStatus.toUpperCase() === 'SUCCEEDED' && videoUrlResult) {
                if (userIdForComfy) {
                    try {
                        console.log(`任务 ${taskId} (via /status route) SUCCEEDED, Aliyun video URL: ${videoUrlResult}. Attempting to transfer to ComfyUI.`);
                        const comfyPayload = {
                            url: videoUrlResult,
                            filename_prefix: `i2v_vid_${userIdForComfy}_${taskId.substring(0, 8)}`,
                            subdir: 'image-to-video/outputs'
                        };
                        const comfyResponse = await axios.post(`${COMFYUI_BASE_URL}/yzy/download-url`, comfyPayload, {
                            headers: { 'Content-Type': 'application/json' }
                        });

                        if (comfyResponse.data && comfyResponse.data.success) {
                            const { filename, subfolder: comfySubdirValue, type: comfyType } = comfyResponse.data;
                            finalVideoUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(filename)}&subfolder=${encodeURIComponent(comfySubdirValue)}&type=${comfyType}`;
                            console.log(`任务 ${taskId} (via /status route) 成功转移到 ComfyUI: ${finalVideoUrl}`);
                        } else {
                            console.error(`任务 ${taskId} (via /status route) ComfyUI transfer failed:`, comfyResponse.data);
                            finalVideoUrl = videoUrlResult; 
                        }
                    } catch (comfyError) {
                        console.error(`任务 ${taskId} (via /status route) ComfyUI transfer error:`, comfyError.message);
                        finalVideoUrl = videoUrlResult;
                    }
                } else {
                    console.warn(`任务 ${taskId} (via /status route): userId not found, cannot transfer to ComfyUI. Using Aliyun URL.`);
                    finalVideoUrl = videoUrlResult; // Fallback if userId couldn't be fetched
                }
                updateQuery += ', video_url = ?';
                queryParams.push(finalVideoUrl);

            } else if (taskStatus.toUpperCase() === 'FAILED') {
                updateQuery += ', error_message = ?';
                queryParams.push(taskErrorMessage || '任务执行失败，详情未知。');
            }

            updateQuery += ' WHERE aliyun_task_id = ?';
            queryParams.push(taskId);

            await connection.query(updateQuery, queryParams);
            console.log(`图生视频任务 ${taskId} 状态已更新为: ${taskStatus}`);
        } catch (dbError) {
            console.error('更新图生视频历史记录数据库失败:', dbError);
        } finally {
            if (connection) connection.release();
        }

        res.json(aliyunResult);

    } catch (error) {
        console.error(`查询图生视频任务 ${taskId} 状态时出错:`, error);
        res.status(500).json({ message: '查询任务状态失败，服务器内部错误。', details: error.message });
    }
});

// GET /api/image-to-video/history - 获取用户图生视频历史记录
router.get('/history', authenticateToken, async (req, res) => {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 5; // 调整为每页5条以便测试
    const offset = (page - 1) * limit;

    let connection;
    try {
        connection = await pool.getConnection();

        const countQuery = 'SELECT COUNT(*) as totalCount FROM image_to_video_history WHERE user_id = ?';
        const [[{ totalCount }]] = await connection.query(countQuery, [userId]);

        if (totalCount === 0) {
            return res.json({
                history: [],
                pagination: { totalCount: 0, currentPage: 1, totalPages: 0, limit: limit }
            });
        }

        const historyQuery = `
            SELECT id, user_id, aliyun_task_id, original_image_filename, original_image_comfyui_url, 
                   last_frame_filename, last_frame_comfyui_url, api_type, 
                   prompt, model_used, resolution_used, status, video_url, error_message, created_at, cost_incurred 
            FROM image_to_video_history 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        `;
        const [historyRows] = await connection.query(historyQuery, [userId, limit, offset]);

        const processedHistory = historyRows.map(row => ({
            ...row,
            // original_image_url is now directly from original_image_comfyui_url column
            original_image_url: row.original_image_comfyui_url || null,
            // 确保 api_type 属性存在，默认为 'i2v'
            api_type: row.api_type || 'i2v'
        }));

        const totalPages = Math.ceil(totalCount / limit);

        res.json({
            history: processedHistory,
            pagination: {
                totalCount,
                currentPage: page,
                totalPages,
                limit
            }
        });

    } catch (error) {
        console.error(`获取用户 ${userId} 的图生视频历史记录失败:`, error);
        res.status(500).json({ message: '获取历史记录失败，服务器内部错误。', details: error.message });
    } finally {
        if (connection) connection.release();
    }
});

// 新增: GET /api/image-to-video/task-ui-state/:taskId - 获取任务UI状态信息
router.get('/task-ui-state/:taskId', authenticateToken, async (req, res) => {
    const { taskId } = req.params;
    const userId = req.user.id;

    if (!taskId) {
        return res.status(400).json({ message: '缺少任务ID' });
    }

    let connection;
    try {
        connection = await pool.getConnection();
        
        // 从数据库获取最新任务状态
        const [rows] = await connection.query(
            'SELECT status, video_url, error_message FROM image_to_video_history WHERE aliyun_task_id = ? AND user_id = ?', 
            [taskId, userId]
        );
        
        if (rows.length === 0) {
            return res.status(404).json({ 
                message: '未找到任务记录',
                should_end_loading: true // 任务不存在，应该结束loading
            });
        }
        
        const taskRecord = rows[0];
        const taskStatus = taskRecord.status;
        
        // 构建UI状态响应
        const uiState = {
            task_id: taskId,
            status: taskStatus,
            video_url: taskRecord.video_url || null,
            error_message: taskRecord.error_message || null,
            // 关键字段: 告诉前端是否应该结束loading状态
            should_end_loading: ['SUCCEEDED', 'FAILED', 'UNKNOWN_ERROR'].includes(taskStatus.toUpperCase())
        };
        
        res.json(uiState);
    } catch (error) {
        console.error(`获取任务 ${taskId} UI状态信息失败:`, error);
        // 出错时也应该结束loading
        res.status(500).json({ 
            message: '获取任务状态失败',
            should_end_loading: true,
            error: error.message
        });
    } finally {
        if (connection) connection.release();
    }
});

// POST /api/image-to-video/submit-kf2v - 提交首尾帧生视频任务
router.post('/submit-kf2v', authenticateToken, imageUpload.fields([
    { name: 'first_frame', maxCount: 1 },
    { name: 'last_frame', maxCount: 1 }
]), checkAndDeductCredits('kf_to_video', false), async (req, res) => {
    console.log('首尾帧图生视频 /submit-kf2v 请求体:', req.body);
    console.log('首尾帧图生视频 /submit-kf2v 上传文件:', req.files);
    const userId = req.user.id;

    // 验证两张图片都已上传
    if (!req.files || !req.files.first_frame || !req.files.first_frame[0] || !req.files.last_frame || !req.files.last_frame[0]) {
        return res.status(400).json({ message: '缺少首帧或尾帧图片。' });
    }
    
    if (!req.body.prompt) {
        return res.status(400).json({ message: '缺少提示词 (prompt)。' });
    }
    
    if (!req.body.model) {
        return res.status(400).json({ message: '缺少模型参数 (model)。' });
    }
    
    if (!req.body.resolution) {
        return res.status(400).json({ message: '缺少分辨率参数 (resolution)。' });
    }

    const { prompt, model, resolution } = req.body;
    const firstFrameFile = req.files.first_frame[0];
    const lastFrameFile = req.files.last_frame[0];
    const firstFramePath = firstFrameFile.path;
    const lastFramePath = lastFrameFile.path;
    // 新增：生成压缩JPG路径
    const firstAliyunJpgPath = firstFramePath.replace(/\.[^.]+$/, '.aliyun.jpg');
    const lastAliyunJpgPath = lastFramePath.replace(/\.[^.]+$/, '.aliyun.jpg');
    const firstAliyunJpgName = firstFrameFile.filename.replace(/\.[^.]+$/, '.aliyun.jpg');
    const lastAliyunJpgName = lastFrameFile.filename.replace(/\.[^.]+$/, '.aliyun.jpg');
    let compressedFirst = false, compressedLast = false;
    try {
        await compressImageToJpg(firstFramePath, firstAliyunJpgPath, 1280, 88);
        compressedFirst = true;
    } catch (e) {
        console.error('首帧图片压缩失败，使用原图:', e);
    }
    try {
        await compressImageToJpg(lastFramePath, lastAliyunJpgPath, 1280, 88);
        compressedLast = true;
    } catch (e) {
        console.error('尾帧图片压缩失败，使用原图:', e);
    }
    let connection; 

    try {
        connection = await pool.getConnection();
        await connection.beginTransaction();

        // --- 准备图片公开URL ---
        const appPublicBaseUrl = process.env.APP_PUBLIC_BASE_URL || 'https://caca.yzycolour.top';
        const firstFrameUrlForAliyun = compressedFirst
            ? `${appPublicBaseUrl}/${uploadDir}/${firstAliyunJpgName}`
            : `${appPublicBaseUrl}/${uploadDir}/${firstFrameFile.filename}`;
        const lastFrameUrlForAliyun = compressedLast
            ? `${appPublicBaseUrl}/${uploadDir}/${lastAliyunJpgName}`
            : `${appPublicBaseUrl}/${uploadDir}/${lastFrameFile.filename}`;
        
        console.log(`生成的首帧图片URL: ${firstFrameUrlForAliyun}`);
        console.log(`生成的尾帧图片URL: ${lastFrameUrlForAliyun}`);

        // --- 将两张图片上传到ComfyUI ---
        let firstFrameComfyuiUrl = null;
        let lastFrameComfyuiUrl = null;
        let firstFrameComfyuiFilename = null;
        let lastFrameComfyuiFilename = null;
        
        try {
            // 上传首帧图片
            const firstFrameFormData = new FormData();
            firstFrameFormData.append('image', fs.createReadStream(firstFramePath));
            firstFrameFormData.append('subfolder', 'image-to-video/inputs/first-frames');
            firstFrameFormData.append('type', 'input');
            firstFrameFormData.append('overwrite', 'false');

            const firstFrameUploadResponse = await axios.post(`${COMFYUI_BASE_URL}/upload/image`, firstFrameFormData, {
                headers: firstFrameFormData.getHeaders()
            });

            if (firstFrameUploadResponse.data && firstFrameUploadResponse.data.name) {
                const { name, subfolder, type } = firstFrameUploadResponse.data;
                firstFrameComfyuiFilename = name;
                const effectiveType = type || 'input';
                firstFrameComfyuiUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(name)}&subfolder=${encodeURIComponent(subfolder)}&type=${effectiveType}`;
                console.log(`首帧图片上传到 ComfyUI 成功: ${firstFrameComfyuiUrl}`);
            } else {
                throw new Error('首帧图片上传到ComfyUI未返回预期数据');
            }
            
            // 上传尾帧图片
            const lastFrameFormData = new FormData();
            lastFrameFormData.append('image', fs.createReadStream(lastFramePath));
            lastFrameFormData.append('subfolder', 'image-to-video/inputs/last-frames');
            lastFrameFormData.append('type', 'input');
            lastFrameFormData.append('overwrite', 'false');

            const lastFrameUploadResponse = await axios.post(`${COMFYUI_BASE_URL}/upload/image`, lastFrameFormData, {
                headers: lastFrameFormData.getHeaders()
            });

            if (lastFrameUploadResponse.data && lastFrameUploadResponse.data.name) {
                const { name, subfolder, type } = lastFrameUploadResponse.data;
                lastFrameComfyuiFilename = name;
                const effectiveType = type || 'input';
                lastFrameComfyuiUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(name)}&subfolder=${encodeURIComponent(subfolder)}&type=${effectiveType}`;
                console.log(`尾帧图片上传到 ComfyUI 成功: ${lastFrameComfyuiUrl}`);
            } else {
                throw new Error('尾帧图片上传到ComfyUI未返回预期数据');
            }
        } catch (uploadError) {
            console.error('上传图片到 ComfyUI 失败:', uploadError.message);
            await connection.rollback();
            // 删除临时文件
            fs.unlink(firstFramePath, (err) => {
                if (err) console.error('删除首帧临时图片失败:', err);
            });
            fs.unlink(lastFramePath, (err) => {
                if (err) console.error('删除尾帧临时图片失败:', err);
            });
            return res.status(500).json({ message: '上传图片到处理服务器失败，请重试。' });
        }

        // --- 1. 确定功能键并获取成本 ---
        let featureKey;
        if (model === 'wanx2.1-kf2v-plus') {
            featureKey = 'kf2v_plus';
        } else {
            await connection.rollback();
            return res.status(400).json({ message: '无效的模型类型，首尾帧目前仅支持wanx2.1-kf2v-plus。' });
        }

        const [costRows] = await connection.query('SELECT cost FROM feature_costs WHERE feature_key = ?', [featureKey]);
        if (costRows.length === 0) {
            await connection.rollback();
            return res.status(500).json({ message: `功能 ${featureKey} 的成本未配置。` });
        }
        const costIncurred = parseInt(costRows[0].cost);
        if (isNaN(costIncurred) || costIncurred < 0) {
            await connection.rollback();
            return res.status(500).json({ message: `功能 ${featureKey} 的成本配置无效。` });
        }

        // --- 2. 检查并扣除用户积分 (每日免费额度优先) ---
        // const DAILY_FREE_CREDITS_AMOUNT = 20; // 后续可以考虑移至共享配置文件 -- REMOVE THIS

        // --- Fetch DAILY_FREE_CREDIT_ALLOWANCE --- START ---
        const [[dailyAllowanceData]] = await connection.query(
            'SELECT cost FROM feature_costs WHERE feature_key = ?',
            ['DAILY_FREE_CREDIT_ALLOWANCE']
        );
        if (!dailyAllowanceData || dailyAllowanceData.cost === undefined || dailyAllowanceData.cost === null) {
            console.error(`[KF2V Submit] User ${userId} - Daily free credit allowance not found or invalid.`);
            await connection.rollback();
            return res.status(500).json({ message: '服务器错误：每日免费额度配置缺失或无效。' });
        }
        const DAILY_FREE_CREDITS_AMOUNT = parseInt(dailyAllowanceData.cost, 10);
        if (isNaN(DAILY_FREE_CREDITS_AMOUNT) || DAILY_FREE_CREDITS_AMOUNT < 0) {
            console.error(`[KF2V Submit] User ${userId} - Invalid daily free credit allowance (${DAILY_FREE_CREDITS_AMOUNT}) retrieved.`);
            await connection.rollback();
            return res.status(500).json({ message: '服务器错误：每日免费额度配置无效。' });
        }
        console.log(`[KF2V Submit] User ${userId} - Daily Free Credit Allowance from DB: ${DAILY_FREE_CREDITS_AMOUNT}`);
        // --- Fetch DAILY_FREE_CREDIT_ALLOWANCE --- END ---

        const [userRows] = await connection.query(
            'SELECT id, credits, daily_generations_used, last_generation_date FROM users WHERE id = ? FOR UPDATE',
            [userId]
        );

            if (userRows.length === 0) {
                await connection.rollback();
                return res.status(404).json({ message: '用户不存在。' });
            }

        let user = userRows[0];
        const today = new Date().toISOString().slice(0, 10);
        let cumulativeCredits = user.credits || 0;
        let dailyCreditsUsed = user.daily_generations_used || 0;
        const lastResetDateDb = user.last_generation_date;
        const lastResetDateStr = lastResetDateDb ? new Date(lastResetDateDb).toISOString().slice(0, 10) : null;
        let remainingDailyFreeCreditsToday = 0;

        if (lastResetDateStr !== today) {
            console.log(`用户 ${userId} (首尾帧): 执行每日积分重置。上次重置: ${lastResetDateStr}, 今天: ${today}`);
            dailyCreditsUsed = 0;
            await connection.query(
                'UPDATE users SET daily_generations_used = 0, last_generation_date = ? WHERE id = ?',
                [today, userId]
            );
            remainingDailyFreeCreditsToday = DAILY_FREE_CREDITS_AMOUNT;
        } else {
            remainingDailyFreeCreditsToday = Math.max(0, DAILY_FREE_CREDITS_AMOUNT - dailyCreditsUsed);
        }
        console.log(`用户 ${userId} (首尾帧): 任务成本 ${costIncurred}。可用每日免费 ${remainingDailyFreeCreditsToday}, 可用累积 ${cumulativeCredits}`);
        
        if (costIncurred > 0) {
            if (remainingDailyFreeCreditsToday >= costIncurred) {
                const newDailyUsed = dailyCreditsUsed + costIncurred;
                await connection.query(
                    'UPDATE users SET daily_generations_used = ? WHERE id = ?',
                    [newDailyUsed, userId]
                );
                console.log(`用户 ${userId} (首尾帧) 功能 ${featureKey}: 使用 ${costIncurred} 每日免费积分。新每日已用: ${newDailyUsed}`);
            } else if (remainingDailyFreeCreditsToday + cumulativeCredits >= costIncurred) {
                const neededFromCumulative = costIncurred - remainingDailyFreeCreditsToday;
                const newCumulativeCredits = cumulativeCredits - neededFromCumulative;
                const newDailyUsed = DAILY_FREE_CREDITS_AMOUNT;

                await connection.query(
                    'UPDATE users SET credits = ?, daily_generations_used = ? WHERE id = ?',
                    [newCumulativeCredits, newDailyUsed, userId]
                );
                console.log(`用户 ${userId} (首尾帧) 功能 ${featureKey}: 使用 ${remainingDailyFreeCreditsToday} 免费积分和 ${neededFromCumulative} 累积积分。新累积: ${newCumulativeCredits}。每日已用额度已满.`);
            } else {
                console.log(`用户 ${userId} (首尾帧) 功能 ${featureKey}: 积分不足。需要: ${costIncurred}, 可用免费: ${remainingDailyFreeCreditsToday}, 可用累积: ${cumulativeCredits}`);
                await connection.rollback();
                return res.status(402).json({
                    message: '积分不足，无法执行操作。',
                    details: { required: costIncurred, available_daily: remainingDailyFreeCreditsToday, available_cumulative: cumulativeCredits }
                });
            }
        } else {
             console.log(`用户 ${userId} (首尾帧) 功能 ${featureKey}: 成本为 0，不扣除积分。`);
        }
        // --- 积分检查和扣除结束 ---

        // --- 3. 调用阿里云首尾帧API ---
        const payload = {
            model: model,
            input: { 
                prompt: prompt, 
                first_frame_url: firstFrameUrlForAliyun,
                last_frame_url: lastFrameUrlForAliyun
            },
            parameters: { 
                resolution: resolution,
                prompt_extend: true // 开启提示词优化
            }
        };

        console.log('调用阿里云首尾帧图生视频创建任务 API，Payload:', JSON.stringify(payload));
        const aliyunResponse = await fetch(ALIYUN_KF2V_SUBMIT_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.DASHSCOPE_API_KEY}`,
                'X-DashScope-Async': 'enable'
            },
            body: JSON.stringify(payload)
        });
        const aliyunResult = await aliyunResponse.json();
        console.log('阿里云首尾帧 API 响应:', aliyunResult);

        const aliyunTaskId = aliyunResult.output?.task_id;
        const initialTaskStatus = aliyunResult.output?.task_status || 'PENDING';

        if (!aliyunResponse.ok || (aliyunResult.code && aliyunResult.code !== "Success" && !aliyunTaskId)) {
            const errorMessage = aliyunResult.message || `阿里云 API 错误 (HTTP ${aliyunResponse.status})`;
            console.error('阿里云首尾帧图生视频任务提交失败:', errorMessage, '完整响应:', aliyunResult);
            await connection.rollback();
            return res.status(aliyunResponse.status || 500).json({ 
                message: `首尾帧图生视频任务提交失败: ${errorMessage}`,
                details: aliyunResult
            });
        }
        
        if (!aliyunTaskId) {
            console.error('阿里云 API 响应中缺少 task_id:', aliyunResult);
            await connection.rollback();
            return res.status(500).json({
                message: '首尾帧图生视频任务提交成功，但未能从阿里云获取任务ID。',
                details: aliyunResult
            });
        }

        // --- 4. 将任务信息存入历史记录表 (添加首尾帧相关字段) ---
        // 检查是否需要修改表结构 - 在实际部署前需要添加这些字段
        // ALTER TABLE image_to_video_history 
        // ADD COLUMN last_frame_filename VARCHAR(255) NULL,
        // ADD COLUMN last_frame_comfyui_url TEXT NULL,
        // ADD COLUMN api_type VARCHAR(10) NOT NULL DEFAULT 'i2v';
        
        await connection.query(
            `INSERT INTO image_to_video_history 
             (user_id, aliyun_task_id, original_image_filename, original_image_comfyui_url, 
              last_frame_filename, last_frame_comfyui_url, prompt, model_used, 
              resolution_used, status, cost_incurred, api_type)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, 
            [userId, aliyunTaskId, firstFrameComfyuiFilename, firstFrameComfyuiUrl, 
             lastFrameComfyuiFilename, lastFrameComfyuiUrl, prompt, model, 
             resolution, initialTaskStatus, costIncurred, 'kf2v']
        );
        
        console.log(`首尾帧图生视频任务 ${aliyunTaskId} 已存入历史记录，用户ID: ${userId}, 消耗积分: ${costIncurred}`);
        
        await connection.commit();

        // --- 5. 启动后台轮询 ---
        // 传递两个图片路径以便后续删除
        const localImagePaths = {
            firstFrame: firstFramePath,
            lastFrame: lastFramePath,
            firstAliyun: firstAliyunJpgPath,
            lastAliyun: lastAliyunJpgPath
        };
        pollAliyunTaskStatus(aliyunTaskId, userId, localImagePaths);
        
        res.json(aliyunResult);

    } catch (error) {
        console.error('首尾帧图生视频 /submit-kf2v 路由出错:', error);
        if (connection) {
            await connection.rollback();
        }
        // 删除临时文件
        fs.unlink(firstFramePath, (err) => {
            if (err) console.error('因catch块错误删除首帧临时图片失败:', err);
        });
        fs.unlink(lastFramePath, (err) => {
            if (err) console.error('因catch块错误删除尾帧临时图片失败:', err);
        });
        res.status(500).json({ message: '提交首尾帧图生视频任务失败，服务器内部错误。', details: error.message });
    } finally {
        if (connection) {
            connection.release();
        }
    }
});

module.exports = router; 