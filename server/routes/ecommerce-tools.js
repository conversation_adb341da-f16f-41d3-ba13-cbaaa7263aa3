const express = require('express');
const router = express.Router();
const pool = require('../db/pool');
const { authenticateToken } = require('../utils/auth');

// GET /api/ecommerce-tools - 获取所有可用的电商工具
router.get('/', authenticateToken, async (req, res) => {
  try {
    const [tools] = await pool.query(
      'SELECT tool_key, name, description, image_url FROM ecommerce_tools WHERE is_active = TRUE ORDER BY sort_order'
    );
    
    res.json({
      success: true,
      tools
    });
  } catch (error) {
    console.error('获取电商工具列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取电商工具列表失败'
    });
  }
});

module.exports = router;
