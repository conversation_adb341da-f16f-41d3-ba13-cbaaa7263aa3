const express = require('express');
const crypto = require('crypto');
const bcrypt = require('bcrypt');
const pool = require('../db/pool'); // Adjust path to db pool
const mailer = require('../utils/mailer'); // Adjust path to mailer
require('dotenv').config({ path: '../.env' }); // Ensure .env is loaded relative to root maybe? Or assume index.js loads it. Let's assume index.js loads it globally.

const router = express.Router();

// --- 忘记密码 - 请求重置链接 ---
router.post('/forgot-password', async (req, res) => {
    const { email } = req.body;
    console.log(`[Forgot Password V2] 收到忘记密码请求，邮箱: ${email}`);

    const sendGenericResponse = () => {
        console.log('[Forgot Password V2] 发送通用响应');
        return res.json({ message: '如果您的邮箱地址在我们系统中注册过，您将会收到一封包含重置密码链接的邮件。' });
    };

    if (!email) {
        console.log('[Forgot Password V2] 邮箱地址为空');
        return sendGenericResponse();
    }

    let connection;
    try {
        connection = await pool.getConnection();
        console.log('[Forgot Password V2] 数据库连接成功');

        const [users] = await connection.query('SELECT * FROM users WHERE email = ?', [email]);

        if (users.length === 0) {
            console.log(`[Forgot Password V2] 邮箱 ${email} 未找到对应用户`);
        } else {
            const user = users[0];
            console.log(`[Forgot Password V2] 找到用户 ID: ${user.id}, 用户名: ${user.username}`);

            const originalToken = crypto.randomBytes(32).toString('hex');
            console.log('[Forgot Password V2] 生成原始令牌 (前缀): ', originalToken.substring(0, 10) + '...');

            const SALT_ROUNDS = 10;
            const hashedToken = await bcrypt.hash(originalToken, SALT_ROUNDS);
            console.log('[Forgot Password V2] 哈希令牌 (前缀): ', hashedToken.substring(0, 10) + '...');

            const expires = new Date();
            expires.setHours(expires.getHours() + 1);
            const expiresUTCString = expires.toISOString().slice(0, 19).replace('T', ' ');
            console.log('[Forgot Password V2] 令牌过期时间 (UTC): ', expiresUTCString);

            await connection.query('DELETE FROM password_resets WHERE user_id = ?', [user.id]);
            console.log(`[Forgot Password V2] 已删除用户 ${user.id} 的旧重置令牌`);
            await connection.query(
                'INSERT INTO password_resets (user_id, token, expires_at) VALUES (?, ?, ?)',
                [user.id, hashedToken, expiresUTCString]
            );
            console.log(`[Forgot Password V2] 新重置令牌(Hashed)已存入数据库，用户 ID: ${user.id}`);

            // --- Corrected URL Building Logic V2 ---
            const frontendUrl = process.env.FRONTEND_URL || '';
            const baseUrl = frontendUrl.replace(/\/+$/, ''); // Ensure no trailing slash
            // Simply append the filename to the base URL from environment variable
            const resetUrl = `${baseUrl}/reset-password.html?token=${originalToken}`;
            console.log('[Forgot Password V2] 生成的重置 URL (包含原始令牌): ', resetUrl);
            // --- End Corrected URL Building Logic V2 ---

            const transporter = mailer;
            const mailOptions = {
                from: process.env.MAIL_FROM,
                to: user.email,
                subject: '密码重置请求 - 提示词案例管理系统',
                text: `您好，\n\n我们收到了您的密码重置请求。请点击以下链接重置您的密码 (链接将在1小时后失效)：\n${resetUrl}\n\n如果您没有请求重置密码，请忽略此邮件。\n\n提示词案例管理系统`,
                html: `<p>您好，</p><p>我们收到了您的密码重置请求。请点击以下链接重置您的密码 (链接将在1小时后失效)：</p><p><a href="${resetUrl}">${resetUrl}</a></p><p>如果您没有请求重置密码，请忽略此邮件。</p><p>提示词案例管理系统</p>`
            };

            try {
                console.log(`[Forgot Password V2] 准备发送邮件至 ${user.email}`);
                const info = await transporter.sendMail(mailOptions);
                console.log('[Forgot Password V2] 邮件已发送: %s', info.messageId);
            } catch (mailError) {
                console.error('[Forgot Password V2] 发送邮件失败:', mailError);
            }
        }

        return sendGenericResponse();

    } catch (error) {
        console.error('[Forgot Password V2] 处理忘记密码请求时出错:', error);
        return sendGenericResponse(); // Still send generic response on internal error
    } finally {
        if (connection) {
            connection.release();
            console.log('[Forgot Password V2] 数据库连接已在 finally 块中释放');
        }
    }
});


// --- 重置密码 - 验证令牌并更新密码 ---
router.post('/reset-password', async (req, res) => {
    const { token: originalToken, password } = req.body;
    console.log(`[Reset Password V2] 收到重置密码请求, 原始令牌 (前缀): ${originalToken ? originalToken.substring(0, 10) + '...' : '无'}`);

    if (!originalToken || !password) {
        console.log('[Reset Password V2] 令牌或密码缺失');
        return res.status(400).json({ error: '令牌和新密码不能为空' });
    }
    if (password.length < 6) {
        console.log('[Reset Password V2] 密码过短');
        return res.status(400).json({ error: '密码长度不能少于6位' });
    }

    let connection;
    try {
        connection = await pool.getConnection();
        console.log('[Reset Password V2] 数据库连接成功');

        console.log('[Reset Password V2] 正在查找所有未过期的重置记录 (比较 UTC 时间)...');
        const [potentialHashedTokens] = await connection.query(
            'SELECT * FROM password_resets WHERE expires_at > UTC_TIMESTAMP()'
        );
        console.log(`[Reset Password V2] 找到 ${potentialHashedTokens.length} 条未过期的记录`);

        let validRecordFound = false;
        let userIdToUpdate = null;

        for (const record of potentialHashedTokens) {
            console.log(`[Reset Password V2] 正在比较原始令牌与 DB 记录 (ID: ${record.id}, UserID: ${record.user_id}, HashedToken: ${record.token.substring(0, 10)}...).`);
            // Correct comparison using originalToken
            const isMatch = await bcrypt.compare(originalToken, record.token);

            if (isMatch) {
                console.log(`[Reset Password V2] 令牌匹配成功! (UserID: ${record.user_id})`);
                validRecordFound = true;
                userIdToUpdate = record.user_id;
                break;
            }
        }

        if (validRecordFound && userIdToUpdate) {
            console.log(`[Reset Password V2] 准备更新用户 ${userIdToUpdate} 的密码`);
            const SALT_ROUNDS = 10;
            const hashedNewPassword = await bcrypt.hash(password, SALT_ROUNDS);
            console.log(`[Reset Password V2] 新密码已 Hashed`);

            await connection.query('UPDATE users SET password = ? WHERE id = ?', [hashedNewPassword, userIdToUpdate]);
            console.log(`[Reset Password V2] 用户 ${userIdToUpdate} 的密码已更新`);

            await connection.query('DELETE FROM password_resets WHERE user_id = ?', [userIdToUpdate]);
            console.log(`[Reset Password V2] 用户 ${userIdToUpdate} 的所有密码重置令牌已删除`);

            res.json({ message: '密码重置成功' });

        } else {
            console.log('[Reset Password V2] 未找到有效的重置令牌或令牌已过期');
            res.status(400).json({ error: '无效的令牌或链接已过期' });
        }

    } catch (error) {
        console.error('[Reset Password V2] 处理重置密码请求时出错:', error);
        res.status(500).json({ error: '重置密码时发生内部错误' });
    } finally {
        if (connection) {
            connection.release();
            console.log('[Reset Password V2] 数据库连接已在 finally 块中释放');
        }
    }
});


module.exports = router; 