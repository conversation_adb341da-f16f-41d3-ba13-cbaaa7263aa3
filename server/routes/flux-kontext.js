/**
 * Flux Kontext API 路由
 * 专门处理Replicate平台上的Flux Kontext请求
 * 前端已经扣除积分，因此此路由不再扣除积分
 */

const express = require('express');
const router = express.Router();
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const { authenticateToken } = require('../utils/auth');
const multer = require('multer');
const upload = multer({ limits: { fileSize: 50 * 1024 * 1024 } }); // 50MB限制
const db = require('../db/pool');

// Replicate API配置
const REPLICATE_API_URL = 'https://api.replicate.com/v1/predictions';
const REPLICATE_API_KEY = process.env.REPLICATE_API_KEY || '****************************************';
const REPLICATE_FLUX_KONTEXT_PRO_MODEL = 'black-forest-labs/flux-kontext-pro';
const REPLICATE_FLUX_KONTEXT_MAX_MODEL = 'black-forest-labs/flux-kontext-max';

// ComfyUI配置
const COMFYUI_BASE_URL = process.env.COMFYUI_BASE_URL || 'https://u63642-a2b4-fccb0b0d.westx.seetacloud.com:8443';

// 添加日志函数
function logFluxKontext(message, data = null) {
    const logMsg = `[Flux Kontext] ${message}`;
    if (data) {
        console.log(logMsg, typeof data === 'object' ? JSON.stringify(data) : data);
    } else {
        console.log(logMsg);
    }
}

/**
 * @route POST /api/flux-kontext/process
 * @desc 处理Flux Kontext图片编辑请求，使用Replicate API
 * @access 需要授权
 * @note 此API不扣除积分，前端需先扣除积分
 */
router.post('/process', authenticateToken, upload.none(), async (req, res) => {
    logFluxKontext('收到处理请求');
    
    try {
        const userId = req.user.id;
        const { prompt, input_image, model = 'pro', negative_prompt, seed } = req.body;
        
        // 验证必要参数
        if (!prompt || !input_image) {
            return res.status(400).json({
                success: false,
                error: '缺少必要参数：prompt 和 input_image'
            });
        }
        
        logFluxKontext(`用户 ${userId} 请求处理任务，模型：${model}，提示词：${prompt.substring(0, 30)}${prompt.length > 30 ? '...' : ''}`);

        // 选择要使用的模型
        const modelVersion = model && model.toLowerCase() === 'max' ? 
            REPLICATE_FLUX_KONTEXT_MAX_MODEL : 
            REPLICATE_FLUX_KONTEXT_PRO_MODEL;
            
        // 准备请求数据
        const requestData = {
            version: modelVersion,
            input: {
                prompt: prompt,
                input_image: input_image
            }
        };
        
        // 添加可选参数
        if (negative_prompt) {
            requestData.input.negative_prompt = negative_prompt;
            logFluxKontext('添加负面提示词');
        }
        
        if (seed !== undefined) {
            requestData.input.seed = parseInt(seed);
            logFluxKontext(`添加随机种子: ${requestData.input.seed}`);
        }
        
        // 发送请求到Replicate API
        logFluxKontext(`发送请求到Replicate API，模型: ${modelVersion}`);
        const response = await axios.post(REPLICATE_API_URL, requestData, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Token ${REPLICATE_API_KEY}`
            }
        });
            
        const responseData = response.data;
        logFluxKontext(`收到Replicate API响应，任务ID: ${responseData.id}`);
        
        // 将任务信息存入数据库
        logFluxKontext(`将任务信息存入数据库, 用户ID: ${userId}, Replicate任务ID: ${responseData.id}`);
        const connection = await db.getConnection();
        let dbTaskId;
        try {
            const [insertResult] = await connection.query(
                `INSERT INTO flux_tasks 
                (flux_task_id, user_id, prompt, polling_url, status, created_at, params, task_type, api_provider) 
                VALUES (?, ?, ?, ?, ?, NOW(), ?, ?, ?)`,
                [
                    responseData.id,
                    userId,
                    prompt,
                    `https://api.replicate.com/v1/predictions/${responseData.id}`,
                    'pending',
                    JSON.stringify(requestData),
                    'replicate',
                    'replicate'
                ]
            );
            dbTaskId = insertResult.insertId;
            logFluxKontext(`任务已存入数据库，数据库ID: ${dbTaskId}`);
            connection.release();
        } catch (dbError) {
            connection.release();
            throw dbError;
        }
        
        // 启动后台轮询任务
        logFluxKontext(`启动后台轮询任务, 数据库任务ID: ${dbTaskId}, Replicate任务ID: ${responseData.id}`);
        startPollingTask(dbTaskId, responseData.id);
        
        // 返回响应
        res.status(200).json({
            success: true,
            taskId: dbTaskId,
            status: 'pending'
        });
        
    } catch (error) {
        logFluxKontext(`处理请求失败: ${error.message}`);
        res.status(500).json({
            success: false,
            error: error.message || '处理请求失败'
        });
    }
});

/**
 * @route GET /api/flux-kontext/task/:taskId
 * @desc 获取任务状态
 * @access 需要授权
 */
router.get('/task/:taskId', authenticateToken, async (req, res) => {
    const taskId = req.params.taskId;
    const userId = req.user.id;
    
    logFluxKontext(`用户 ${userId} 请求任务 ${taskId} 状态`);
    
    try {
        // 查询任务状态
        const connection = await db.getConnection();
        let taskDetails;
        try {
            const [rows] = await connection.query(
                `SELECT id, flux_task_id, prompt, status, output_image_url, 
                        error, progress, created_at, completed_at, params 
                FROM flux_tasks 
                WHERE id = ? AND user_id = ?`,
                [taskId, userId]
            );
            connection.release();
            
            if (rows.length === 0) {
                return res.status(404).json({
                    success: false,
                    error: '任务不存在或无权访问'
                });
            }
            
            taskDetails = rows[0];
        } catch (dbError) {
            connection.release();
            throw dbError;
        }
        
        // 格式化返回数据
        const result = {
            id: taskDetails.id,
            status: taskDetails.status,
            created_at: taskDetails.created_at,
            completed_at: taskDetails.completed_at,
            progress: taskDetails.progress || null
        };
        
        // 根据任务状态添加特定数据
        if (taskDetails.status === 'succeeded') {
            // 成功完成，直接使用数据库中已保存的图片URL
            if (taskDetails.output_image_url) {
                result.output_image_url = taskDetails.output_image_url;
                console.log('[Flux Kontext] 从数据库获取到图片URL:', result.output_image_url);
            } 
            // 如果数据库中没有URL，尝试从其他字段提取
            else {
                console.log('[Flux Kontext] 数据库中没有图片URL，尝试从其他字段提取');
                // 原来的提取逻辑...
            }
        } else if (taskDetails.status === 'failed') {
            result.error = taskDetails.error || '任务处理失败';
        }
        
        logFluxKontext(`任务 ${taskId} 状态: ${taskDetails.status}`);
        
        res.status(200).json(result);
    } catch (error) {
        logFluxKontext(`获取任务 ${taskId} 状态失败: ${error.message}`);
        res.status(500).json({
            success: false,
            error: error.message || '获取任务状态失败'
        });
    }
});

/**
 * 启动后台轮询任务
 * @param {String} taskId - 内部任务ID
 * @param {String} replicateTaskId - Replicate API任务ID
 */
function startPollingTask(taskId, replicateTaskId) {
    logFluxKontext(`开始轮询任务状态, 内部任务ID: ${taskId}, Replicate任务ID: ${replicateTaskId}`);
    
    // 构建轮询URL
    const pollingUrl = `https://api.replicate.com/v1/predictions/${replicateTaskId}`;
    
    // 使用setTimeout实现轮询，避免阻塞主线程
    const poll = async () => {
        try {
            // 检查任务是否已完成或失败
            const connection = await db.getConnection();
            let taskStatus;
            try {
                const [rows] = await connection.query(
                    'SELECT status FROM flux_tasks WHERE id = ?',
                    [taskId]
                );
                connection.release();
                
                if (rows.length === 0) {
                    logFluxKontext(`任务 ${taskId} 不存在，停止轮询`);
                    return;
                }
                
                taskStatus = rows[0].status;
                
                if (taskStatus === 'succeeded' || taskStatus === 'failed') {
                    logFluxKontext(`任务 ${taskId} 已完成或失败，停止轮询`);
                    return; // 任务已完成，停止轮询
                }
            } catch (dbError) {
                connection.release();
                throw dbError;
            }

            // 发送请求到Replicate API
            logFluxKontext(`发送轮询请求到: ${pollingUrl}`);
            const response = await axios.get(pollingUrl, {
                headers: {
                    'Authorization': `Token ${REPLICATE_API_KEY}`
                }
            });

            const data = response.data;
            logFluxKontext(`轮询响应状态: ${data.status}`);
            
            // 根据Replicate API的状态更新任务
            switch (data.status) {
                case 'succeeded':
                    // 处理成功
                    logFluxKontext(`任务 ${taskId} 处理成功`);
                    await updateTaskSuccess(taskId, data);
                    break;
                    
                case 'failed':
                    // 处理失败
                    logFluxKontext(`任务 ${taskId} 处理失败: ${data.error || '未知错误'}`);
                    await updateTaskFailure(taskId, data.error || '未知错误');
                    break;
                    
                case 'canceled':
                    // 任务被取消
                    logFluxKontext(`任务 ${taskId} 被取消`);
                    await updateTaskFailure(taskId, '任务被取消');
                    break;
                    
                case 'processing':
                case 'starting':
                    // 设置任务进度 - Replicate API提供了progress百分比
                    const progress = data.progress ? Math.round(data.progress * 100) : null;
                    if (progress !== null) {
                        logFluxKontext(`任务 ${taskId} 处理中，进度: ${progress}%`);
                        await updateTaskProgress(taskId, progress);
                    }
                    
                    // 继续轮询
                    logFluxKontext(`任务 ${taskId} 仍在处理中，2秒后再次轮询`);
                    setTimeout(poll, 2000);
                    break;
                    
                default:
                    // 未知状态，继续轮询
                    logFluxKontext(`任务 ${taskId} 状态未知: ${data.status}，继续轮询`);
                    setTimeout(poll, 2000);
                    break;
            }
        } catch (error) {
            logFluxKontext(`轮询任务 ${taskId} 出错: ${error.message}`);
            await updateTaskFailure(taskId, error.message || '轮询出错');
        }
    };

    // 启动首次轮询
    setTimeout(poll, 2000);
}

/**
 * 更新任务进度
 * @param {String} taskId - 内部任务ID
 * @param {Number} progress - 进度百分比
 */
async function updateTaskProgress(taskId, progress) {
    logFluxKontext(`更新任务 ${taskId} 的进度为 ${progress}%`);
    
    const connection = await db.getConnection();
    try {
        await connection.query(
            `UPDATE flux_tasks 
            SET progress = ? 
            WHERE id = ?`,
            [
                progress,
                taskId
            ]
        );
        connection.release();
    } catch (err) {
        connection.release();
        logFluxKontext(`更新任务 ${taskId} 进度时出错: ${err.message}`);
    }
}

/**
 * 更新任务成功状态
 * @param {String} taskId - 内部任务ID
 * @param {Object} data - Replicate API返回的数据
 */
async function updateTaskSuccess(taskId, data) {
    logFluxKontext(`更新任务 ${taskId} 为成功状态`);
    
    try {
        // 直接从data.output获取URL，支持字符串形式
        const replicateImageUrl = typeof data.output === 'string' ? 
            data.output : 
            (Array.isArray(data.output) && data.output.length > 0 ? data.output[0] : null);
        
        if (!replicateImageUrl) {
            logFluxKontext(`警告: 任务 ${taskId} 成功但没有图片URL`);
            // 记录完整响应以便调试
            logFluxKontext('完整API响应:', data);
            
            const connection = await db.getConnection();
            try {
                await connection.query(
                    `UPDATE flux_tasks 
                    SET status = 'succeeded', 
                        completed_at = NOW(), 
                        result_data = ? 
                    WHERE id = ?`,
                    [
                        JSON.stringify(data),
                        taskId
                    ]
                );
                connection.release();
            } catch (dbError) {
                connection.release();
                throw dbError;
            }
            return;
        }
        
        logFluxKontext(`任务 ${taskId} 成功，Replicate图片URL: ${replicateImageUrl}`);
        
        // 获取用户ID，用于构建文件名前缀
        const connection = await db.getConnection();
        let userId, finalImageUrl;
        
        try {
            const [userRows] = await connection.query(
                'SELECT user_id FROM flux_tasks WHERE id = ?',
                [taskId]
            );
            
            if (userRows.length === 0) {
                logFluxKontext(`警告: 无法获取任务 ${taskId} 的用户ID`);
                await connection.query(
                    `UPDATE flux_tasks 
                    SET status = 'succeeded', 
                        output_image_url = ?, 
                        completed_at = NOW(), 
                        result_data = ? 
                    WHERE id = ?`,
                    [
                        replicateImageUrl,
                        JSON.stringify(data),
                        taskId
                    ]
                );
                connection.release();
                return;
            }
            
            userId = userRows[0].user_id;
            finalImageUrl = replicateImageUrl; // 默认使用Replicate API返回的URL
            
            // 将图片下载到ComfyUI
            try {
                logFluxKontext(`尝试将任务 ${taskId} 的图片下载到ComfyUI`);
                const comfyResponse = await axios.post(`${COMFYUI_BASE_URL}/yzy/download-url`, {
                    url: replicateImageUrl,
                    filename_prefix: `replicate_${userId}_${taskId.substring(0, 8)}`,
                    subdir: 'replicate/outputs'
                }, {
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (comfyResponse.data && comfyResponse.data.success) {
                    const { filename, subfolder, type } = comfyResponse.data;
                    finalImageUrl = `${COMFYUI_BASE_URL}/view?filename=${encodeURIComponent(filename)}&subfolder=${encodeURIComponent(subfolder)}&type=${type}`;
                    logFluxKontext(`图片成功转移到ComfyUI: ${finalImageUrl}`);
                } else {
                    logFluxKontext(`ComfyUI转移失败`, comfyResponse.data);
                }
            } catch (comfyError) {
                logFluxKontext(`ComfyUI转移错误: ${comfyError.message}`);
            }
            
            // 更新数据库
            await connection.query(
                `UPDATE flux_tasks 
                SET status = 'succeeded', 
                    output_image_url = ?, 
                    completed_at = NOW(), 
                    result_data = ? 
                WHERE id = ?`,
                [
                    finalImageUrl,
                    JSON.stringify(data),
                    taskId
                ]
            );
            connection.release();
            logFluxKontext(`任务 ${taskId} 已更新为成功状态，图片URL: ${finalImageUrl}`);
        } catch (dbError) {
            connection.release();
            throw dbError;
        }
    } catch (error) {
        logFluxKontext(`更新任务 ${taskId} 成功状态失败: ${error.message}`);
        throw error;
    }
}

/**
 * 更新任务失败状态
 * @param {String} taskId - 内部任务ID
 * @param {String} error - 错误信息
 */
async function updateTaskFailure(taskId, error) {
    logFluxKontext(`更新任务 ${taskId} 为失败状态，错误: ${error}`);
    
    const connection = await db.getConnection();
    try {
        await connection.query(
            `UPDATE flux_tasks 
            SET status = 'failed', 
                error = ?, 
                completed_at = NOW() 
            WHERE id = ?`,
            [
                error,
                taskId
            ]
        );
        connection.release();
        logFluxKontext(`任务 ${taskId} 已更新为失败状态`);
    } catch (err) {
        connection.release();
        logFluxKontext(`更新任务 ${taskId} 失败状态时出错: ${err.message}`);
        throw err;
    }
}

module.exports = router; 