// Load environment variables FIRST
require('dotenv').config();

// --- DEBUG: Check if .env variables are loaded ---
console.log('--- Environment Variables Check ---');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '********' : '(Not Set)'); // Don't log the actual password
console.log('DB_NAME:', process.env.DB_NAME);
console.log('JWT_SECRET:', process.env.JWT_SECRET ? '********' : '(Not Set)');
console.log('MAIL_HOST:', process.env.MAIL_HOST);
console.log('PORT:', process.env.PORT);
console.log('----------------------------------');
// --- END DEBUG ---

const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const compression = require('compression'); // 引入 compression
const pool = require('./db/pool'); // Import pool
const { authenticateToken, requireAdmin, generateToken, isFirstUser, hashPassword, comparePassword, createActivationToken, resendActivationEmail, verifyActivationToken } = require('./utils/auth'); // Import auth functions
const mailer = require('./utils/mailer'); // Import mailer configuration
const { commentRateLimiter, exampleRateLimiter, avatarRateLimiter } = require('./middleware/rateLimit');
const SSOAuth = require('./utils/sso'); // 导入SSO认证工具

const app = express();
const PORT = process.env.PORT || 32009;

// 添加更详细的错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason);
});

// 创建上传目录
const uploadDir = process.env.UPLOAD_DIR || 'uploads';
const uploadPath = path.join(__dirname, uploadDir);
if (!fs.existsSync(uploadPath)) {
  fs.mkdirSync(uploadPath, { recursive: true });
}

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

// 新的多字段 upload 配置
const upload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 统一 5MB 限制，前端会进行更细致的压缩前检查
  fileFilter: function (req, file, cb) {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const mimetype = allowedTypes.test(file.mimetype);
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    
    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('仅支持图片文件 (jpeg, jpg, png, gif)!'));
  }
}).fields([
  { name: 'image', maxCount: 1 },
  { name: 'source_image', maxCount: 1 },
  { name: 'reference_image', maxCount: 1 }
]);

// --- 新增：为头像上传创建专门的 Multer 实例 ---
const avatarUpload = multer({
  storage: storage, // 可以复用相同的存储配置
  limits: { fileSize: 2 * 1024 * 1024 }, // 头像限制 2MB
  fileFilter: function (req, file, cb) { // 可以复用相同的文件类型过滤器
    const allowedTypes = /jpeg|jpg|png|gif/;
    const mimetype = allowedTypes.test(file.mimetype);
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('仅支持图片文件 (jpeg, jpg, png, gif)!'));
  }
}).single('avatar'); // 指定处理名为 'avatar' 的单个文件
// --- 结束新增 ---

// 中间件配置
app.use(cors({
  origin: process.env.CORS_ORIGIN || '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization']
}));
// 只处理 application/json
app.use(express.json({
  type: ['application/json', 'application/*+json'],
  limit: '50mb' // <--- 增加 JSON 请求体大小限制
}));
app.use(express.urlencoded({ extended: true, limit: '50mb' })); // 同样为 urlencoded 设置限制
app.use(compression()); // 使用 compression 中间件
app.use(`/${uploadDir}`, express.static(uploadPath));

// API路由

// 用户注册
app.post('/api/auth/register', async (req, res) => {
  try {
    const { email, password } = req.body;

    // 验证输入
    if (!email || !password) {
      return res.status(400).json({ error: '邮箱和密码不能为空' });
    }

    // 简单的邮箱格式验证
    const emailRegex = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (!emailRegex.test(String(email).toLowerCase())) {
        return res.status(400).json({ error: '请输入有效的邮箱地址' });
    }
    
    // 密码长度验证 (与前端保持一致)
     if (password.length < 6) {
         return res.status(400).json({ error: '密码长度不能少于6位' });
     }

    const connection = await pool.getConnection(); // Use imported pool
    try {
      // 检查邮箱是否已存在 (同时检查 username 和 email 字段)
      const [existingUsers] = await connection.query(
        'SELECT * FROM users WHERE username = ? OR email = ?',
        [email, email] // 使用 email 同时检查两个字段
      );

      if (existingUsers.length > 0) {
        return res.status(400).json({ error: '该邮箱已被注册' });
      }

      // 检查是否是第一个用户
      const isAdmin = await isFirstUser(pool); // Pass pool to isFirstUser

      // 加密密码
      const hashedPassword = await hashPassword(password);

      // 插入新用户 (将 email 同时存入 username 和 email 字段)
      // 设置 is_activated 字段为 0，表示未激活
      const [result] = await connection.query(
        'INSERT INTO users (username, password, role, email, is_activated) VALUES (?, ?, ?, ?, 0)',
        [email, hashedPassword, isAdmin ? 'admin' : 'user', email]
      );

      const userId = result.insertId;
      
      // 第一个用户默认激活
      if (isAdmin) {
        await connection.query('UPDATE users SET is_activated = 1 WHERE id = ?', [userId]);
      } else {
        // 非管理员用户需要发送激活邮件
        try {
          // 创建激活令牌并发送邮件
          await createActivationToken(userId, email, mailer);
        } catch (emailError) {
          console.error('发送激活邮件失败:', emailError);
          // 不阻止注册流程，但记录错误
        }
      }
      
      // 获取完整的用户信息
      const [newUser] = await connection.query(
        'SELECT id, username, role, nickname, avatar_url, email, is_activated FROM users WHERE id = ?',
        [userId]
      );
      
      const user = newUser[0];
      
      // 如果是第一个用户，生成 token 并允许直接登录
      if (isAdmin) {
        const token = generateToken(user);
        res.status(201).json({
          message: '注册成功',
          user: user,
          token
        });
      } else {
        // 非管理员用户需要激活
        res.status(201).json({
          message: '注册成功，请前往邮箱激活账户',
          user: user
        });
      }
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({ error: '注册失败，请稍后重试' });
  }
});

// 用户登录
app.post('/api/auth/login', async (req, res) => {
  try {
    const { identifier, password } = req.body;

    // 验证输入
    if (!identifier || !password) {
      return res.status(400).json({ error: '邮箱/用户名和密码不能为空' });
    }

    const connection = await pool.getConnection(); // Use imported pool
    try {
      // 查找用户 (通过 username 或 email)
      const [users] = await connection.query(
        'SELECT * FROM users WHERE username = ? OR email = ?',
        [identifier, identifier]
      );

      if (users.length === 0) {
        return res.status(401).json({ error: '邮箱/用户名或密码错误' });
      }

      const user = users[0];

      // 验证密码
      const isValidPassword = await comparePassword(password, user.password);
      if (!isValidPassword) {
        return res.status(401).json({ error: '邮箱/用户名或密码错误' });
      }
      
      // 检查账户是否已激活 - 只提示但不阻止登录
      let activationMessage = null;
      if (user.is_activated === 0) {
        // 尝试重新发送激活邮件
        try {
          await resendActivationEmail(user.email, mailer);
        } catch (emailError) {
          console.error('重新发送激活邮件失败:', emailError);
          // 不阻止登录流程，但记录错误
        }
        
        // 告知用户需要激活，但允许登录
        activationMessage = '您的账户尚未激活，请查收邮箱并点击激活链接，激活后才能获得每日免费积分';
      }

      // 生成 token
      const token = generateToken(user); // Use imported generateToken

      // 构建完整的头像 URL
      let fullAvatarUrl = null;
      if (user.avatar_url) {
          const baseUrl = process.env.API_BASE_URL || `http://localhost:${PORT}`;
          fullAvatarUrl = `${baseUrl}/${uploadDir}/${user.avatar_url}`;
      }

      res.json({
        message: '登录成功',
        activation_message: activationMessage, // 添加激活提示信息
        user: { // 返回完整的用户信息
          id: user.id,
          username: user.username,
          role: user.role,
          nickname: user.nickname,
          avatar_url: fullAvatarUrl,
          email: user.email,
          is_activated: user.is_activated
        },
        token
      });
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({ error: '登录失败，请稍后重试' });
  }
});

// 用户登出
app.post('/api/auth/logout', (req, res) => {
  // 服务端登出可以很简单，因为JWT是无状态的
  // 主要的登出逻辑在客户端（清除token）
  // 这里我们只返回一个成功消息
  res.status(200).json({ message: '登出成功' });
});

// 获取当前用户信息
app.get('/api/auth/me', authenticateToken, (req, res) => { // Use imported authenticateToken
  // req.user 已经包含了从 authenticateToken 中获取的 nickname 和 avatar_url (原始文件名)
  // 需要构建完整的 URL
  let fullAvatarUrl = null;
  if (req.user.avatar_url) {
      const baseUrl = process.env.API_BASE_URL || `http://localhost:${PORT}`;
      fullAvatarUrl = `${baseUrl}/${uploadDir}/${req.user.avatar_url}`;
  }

  res.json({
    user: {
      id: req.user.id,
      username: req.user.username,
      role: req.user.role,
      nickname: req.user.nickname,
      avatar_url: fullAvatarUrl, // 返回构建好的完整 URL 或 null
      email: req.user.email // 添加 email 字段
    }
  });
});

// 获取功能成本信息（供前端按钮显示使用）
app.get('/api/features/cost', authenticateToken, async (req, res) => {
  const featureKey = req.query.key;
  
  if (!featureKey) {
    return res.status(400).json({
      success: false,
      message: '缺少功能标识符 (key)'
    });
  }
  
  try {
    const connection = await pool.getConnection();
    try {
      // 查询功能成本
      const [rows] = await connection.query(
        'SELECT cost FROM feature_costs WHERE feature_key = ?',
        [featureKey]
      );
      
      if (rows.length === 0) {
        return res.status(404).json({
          success: false,
          message: `找不到功能: ${featureKey}`
        });
      }
      
      const cost = parseInt(rows[0].cost, 10);
      if (isNaN(cost)) {
        return res.status(500).json({
          success: false,
          message: `功能 ${featureKey} 的成本配置无效`
        });
      }
      
      return res.json({
        success: true,
        cost
      });
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error(`获取功能成本错误 (${featureKey}):`, error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，无法获取功能成本'
    });
  }
});

// JAAZ系统专用API端点 - 使用API密钥认证而不是用户Token
app.get('/api/jaaz/features/cost', async (req, res) => {
  const featureKey = req.query.key;
  const apiKey = req.header('X-API-Key');
  
  // 验证API密钥（简单但安全的方式）
  const validApiKey = process.env.JAAZ_API_KEY || 'jaaz-default-key-2024';
  if (!apiKey || apiKey !== validApiKey) {
    return res.status(401).json({
      success: false,
      message: 'API密钥验证失败'
    });
  }
  
  if (!featureKey) {
    return res.status(400).json({
      success: false,
      message: '缺少功能标识符 (key)'
    });
  }
  
  try {
    const connection = await pool.getConnection();
    try {
      // 查询功能成本
      const [rows] = await connection.query(
        'SELECT cost FROM feature_costs WHERE feature_key = ?',
        [featureKey]
      );
      
      if (rows.length === 0) {
        return res.status(404).json({
          success: false,
          message: `找不到功能: ${featureKey}`
        });
      }
      
      const cost = parseInt(rows[0].cost, 10);
      if (isNaN(cost)) {
        return res.status(500).json({
          success: false,
          message: `功能 ${featureKey} 的成本配置无效`
        });
      }
      
      return res.json({
        success: true,
        cost
      });
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error(`JAAZ获取功能成本错误 (${featureKey}):`, error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，无法获取功能成本'
    });
  }
});

// 获取所有功能成本（供前端显示使用）
app.get('/api/features/costs', authenticateToken, async (req, res) => {
  try {
    const connection = await pool.getConnection();
    try {
      // 查询所有功能成本
      const [rows] = await connection.query(
        'SELECT feature_key, cost, description FROM feature_costs'
      );
      
      return res.json({
        success: true,
        costs: rows.map(row => ({
          key: row.feature_key,
          cost: parseInt(row.cost, 10),
          description: row.description
        }))
      });
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('获取所有功能成本错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，无法获取功能成本列表'
    });
  }
});

// 新增：更新用户个人资料
app.put('/api/profile', authenticateToken, avatarRateLimiter, avatarUpload, async (req, res) => {
  try {
    const userId = req.user.id;
    const { nickname } = req.body;
    const newAvatarFile = req.file;

    console.log(`开始更新用户资料, User ID: ${userId}`);
    console.log('请求体:', { nickname });
    console.log('上传文件:', newAvatarFile ? newAvatarFile.filename : '无');

    const connection = await pool.getConnection();
    try {
      // 获取当前用户信息，特别是旧的头像 URL
      const [currentUserData] = await connection.query('SELECT avatar_url FROM users WHERE id = ?', [userId]);
      const oldAvatarFilename = currentUserData[0]?.avatar_url;
      console.log('旧头像文件名:', oldAvatarFilename || '无');

      let newAvatarFilename = oldAvatarFilename; // 默认为旧文件名
      let shouldDeleteOldAvatar = false;

      if (newAvatarFile) {
        // 如果上传了新头像
        console.log('处理新上传的头像');
        newAvatarFilename = newAvatarFile.filename;
        if (oldAvatarFilename && oldAvatarFilename !== newAvatarFilename) {
          shouldDeleteOldAvatar = true;
          console.log('标记删除旧头像');
        }
      }

      // 更新数据库
      console.log('更新数据库...', { nickname, avatar_url: newAvatarFilename });
      await connection.query(
        'UPDATE users SET nickname = ?, avatar_url = ? WHERE id = ?',
        [nickname || null, newAvatarFilename, userId] // 如果昵称为空字符串，存为 null
      );
      console.log('数据库更新成功');

      // 如果需要，删除旧头像文件
      if (shouldDeleteOldAvatar && oldAvatarFilename) {
        const oldAvatarPath = path.join(uploadPath, oldAvatarFilename);
        console.log('尝试删除旧头像文件:', oldAvatarPath);
        if (fs.existsSync(oldAvatarPath)) {
          try {
            fs.unlinkSync(oldAvatarPath);
            console.log('旧头像文件已删除');
          } catch (unlinkErr) {
            console.error('删除旧头像文件失败:', unlinkErr);
            // 不中断流程，记录错误即可
          }
        } else {
           console.log('旧头像文件不存在，无需删除');
        }
      }

      // 获取更新后的完整用户信息
      console.log('获取更新后的用户信息...');
      const [updatedUser] = await connection.query(
          'SELECT id, username, role, nickname, avatar_url FROM users WHERE id = ?',
          [userId]
      );
      connection.release();
      console.log('数据库连接已释放');

      // 构建完整的头像 URL
      let fullAvatarUrl = null;
      if (updatedUser[0].avatar_url) {
          // 获取正确的域名和端口
          const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
          const baseUrl = process.env.API_BASE_URL || `${protocol}://localhost:${PORT}`;
          fullAvatarUrl = `${baseUrl}/${uploadDir}/${updatedUser[0].avatar_url}`;
          // 增加更多日志
          console.log('头像文件信息:', {
              filename: updatedUser[0].avatar_url,
              uploadDir: uploadDir,
              baseUrl: baseUrl,
              fullPath: path.join(uploadPath, updatedUser[0].avatar_url),
              fullUrl: fullAvatarUrl
          });
          
          // 检查文件是否存在
          const filePath = path.join(uploadPath, updatedUser[0].avatar_url);
          if (fs.existsSync(filePath)) {
              console.log('头像文件存在于服务器上:', filePath);
          } else {
              console.warn('警告：头像文件不存在于服务器上:', filePath);
          }
      }

      // 返回更新后的用户信息
      res.json({
        message: '个人资料更新成功',
        user: {
          id: updatedUser[0].id,
          username: updatedUser[0].username,
          role: updatedUser[0].role,
          nickname: updatedUser[0].nickname,
          avatar_url: fullAvatarUrl
        }
      });

    } catch (dbError) {
      console.error('数据库操作错误:', dbError);
      connection.release(); // 确保释放连接
      // 如果上传了新文件但数据库操作失败，尝试删除新上传的文件
      if (newAvatarFile) {
         const newAvatarPath = path.join(uploadPath, newAvatarFile.filename);
         if (fs.existsSync(newAvatarPath)) {
            try { fs.unlinkSync(newAvatarPath); console.log('已删除因错误上传的新头像文件'); } catch (e) {}
         }
      }
      throw dbError; // 重新抛出错误
    }
  } catch (error) {
    console.error('更新个人资料错误:', error);
    res.status(500).json({ error: '更新个人资料失败', message: error.message });
  }
});

// 获取所有标签
app.get('/api/tags', authenticateToken, async (req, res) => {
  try {
    const connection = await pool.getConnection();
    const [tags] = await connection.query('SELECT * FROM tags ORDER BY name');
    connection.release();
    res.json(tags);
  } catch (error) {
    console.error('获取标签错误:', error);
    res.status(500).json({ error: '获取标签失败' });
  }
});

// 创建新标签 (需要管理员权限)
app.post('/api/tags', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { name } = req.body;
    
    if (!name) {
      return res.status(400).json({ error: '标签名称不能为空' });
    }
    
    const connection = await pool.getConnection();
    
    // 检查标签是否已存在
    const [existingTag] = await connection.query('SELECT * FROM tags WHERE name = ?', [name]);
    
    if (existingTag.length > 0) {
      connection.release();
      return res.json(existingTag[0]);
    }
    
    // 创建新标签
    const [result] = await connection.query('INSERT INTO tags (name) VALUES (?)', [name]);
    const [newTag] = await connection.query('SELECT * FROM tags WHERE id = ?', [result.insertId]);
    
    connection.release();
    res.status(201).json(newTag[0]);
  } catch (error) {
    console.error('创建标签错误:', error);
    res.status(500).json({ error: '创建标签失败' });
  }
});

// 更新标签 (需要管理员权限)
app.put('/api/tags/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({ error: '标签名称不能为空' });
    }

    const connection = await pool.getConnection();
    try {
      // 检查标签是否存在
      const [existingTag] = await connection.query('SELECT * FROM tags WHERE id = ?', [id]);
      if (existingTag.length === 0) {
        connection.release();
        return res.status(404).json({ error: '未找到该标签' });
      }

      // 检查新名称是否与其它标签冲突
      const [conflictTag] = await connection.query('SELECT * FROM tags WHERE name = ? AND id != ?', [name, id]);
      if (conflictTag.length > 0) {
        connection.release();
        return res.status(400).json({ error: '该标签名称已存在' });
      }

      // 更新标签
      await connection.query('UPDATE tags SET name = ? WHERE id = ?', [name, id]);

      // 获取更新后的标签
      const [updatedTag] = await connection.query('SELECT * FROM tags WHERE id = ?', [id]);

      connection.release();
      res.json(updatedTag[0]);
    } catch (dbError) {
      connection.release();
      throw dbError;
    }
  } catch (error) {
    console.error('更新标签错误:', error);
    res.status(500).json({ error: '更新标签失败' });
  }
});

// 删除标签 (需要管理员权限)
app.delete('/api/tags/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const connection = await pool.getConnection();
    try {
      // 检查标签是否存在
      const [tag] = await connection.query('SELECT * FROM tags WHERE id = ?', [id]);
      if (tag.length === 0) {
        connection.release();
        return res.status(404).json({ error: '未找到该标签' });
      }

      // 检查是否有案例使用此标签
      const [examples] = await connection.query('SELECT COUNT(*) as count FROM prompt_tags WHERE tag_id = ?', [id]);
      if (examples[0].count > 0) {
        connection.release();
        return res.status(400).json({ error: `无法删除标签，有 ${examples[0].count} 个案例正在使用此标签` });
      }

      // 删除标签
      await connection.query('DELETE FROM tags WHERE id = ?', [id]);

      connection.release();
      res.json({ success: true, message: '标签已成功删除' });
    } catch (dbError) {
      connection.release();
      throw dbError;
    }
  } catch (error) {
    console.error('删除标签错误:', error);
    res.status(500).json({ error: '删除标签失败' });
  }
});
// 获取所有分类
app.get('/api/categories', authenticateToken, async (req, res) => {
  try {
    const connection = await pool.getConnection();
    const [categories] = await connection.query('SELECT * FROM categories ORDER BY name');
    connection.release();
    res.json(categories);
  } catch (error) {
    console.error('获取分类错误:', error);
    res.status(500).json({ error: '获取分类失败' });
  }
});

// 获取单个分类
app.get('/api/categories/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const connection = await pool.getConnection();
    const [categories] = await connection.query('SELECT * FROM categories WHERE id = ?', [id]);
    connection.release();
    
    if (categories.length === 0) {
      return res.status(404).json({ error: '未找到该分类' });
    }
    
    res.json(categories[0]);
  } catch (error) {
    console.error('获取分类错误:', error);
    res.status(500).json({ error: '获取分类失败' });
  }
});

// 创建新分类 (需要管理员权限)
app.post('/api/categories', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { name, slug, description } = req.body;
    
    if (!name || !slug) {
      return res.status(400).json({ error: '分类名称和标识符不能为空' });
    }
    
    const connection = await pool.getConnection();
    
    // 检查slug是否已存在
    const [existingCategories] = await connection.query('SELECT * FROM categories WHERE slug = ?', [slug]);
    if (existingCategories.length > 0) {
      connection.release();
      return res.status(400).json({ error: '分类标识符已存在' });
    }
    
    // 插入新分类
    const [result] = await connection.query(
      'INSERT INTO categories (name, slug, description) VALUES (?, ?, ?)',
      [name, slug, description]
    );
    
    // 获取新创建的分类
    const [newCategory] = await connection.query('SELECT * FROM categories WHERE id = ?', [result.insertId]);
    
    connection.release();
    res.status(201).json(newCategory[0]);
  } catch (error) {
    console.error('创建分类错误:', error);
    res.status(500).json({ error: '创建分类失败' });
  }
});

// 更新分类 (需要管理员权限)
app.put('/api/categories/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, slug, description } = req.body;
    
    if (!name || !slug) {
      return res.status(400).json({ error: '分类名称和标识符不能为空' });
    }
    
    const connection = await pool.getConnection();
    
    // 检查分类是否存在
    const [existingCategory] = await connection.query('SELECT * FROM categories WHERE id = ?', [id]);
    if (existingCategory.length === 0) {
      connection.release();
      return res.status(404).json({ error: '未找到该分类' });
    }
    
    // 如果slug已更改，检查新slug是否与其他分类冲突
    if (slug !== existingCategory[0].slug) {
      const [conflictCategory] = await connection.query('SELECT * FROM categories WHERE slug = ? AND id != ?', [slug, id]);
      if (conflictCategory.length > 0) {
        connection.release();
        return res.status(400).json({ error: '分类标识符已存在' });
      }
    }
    
    // 更新分类
    await connection.query(
      'UPDATE categories SET name = ?, slug = ?, description = ? WHERE id = ?',
      [name, slug, description, id]
    );
    
    // 获取更新后的分类
    const [updatedCategory] = await connection.query('SELECT * FROM categories WHERE id = ?', [id]);
    
    connection.release();
    res.json(updatedCategory[0]);
  } catch (error) {
    console.error('更新分类错误:', error);
    res.status(500).json({ error: '更新分类失败' });
  }
});

// 删除分类 (需要管理员权限)
app.delete('/api/categories/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    const connection = await pool.getConnection();
    
    // 检查分类是否存在
    const [category] = await connection.query('SELECT * FROM categories WHERE id = ?', [id]);
    if (category.length === 0) {
      connection.release();
      return res.status(404).json({ error: '未找到该分类' });
    }
    
    // 检查是否有案例使用此分类
    const [examples] = await connection.query('SELECT COUNT(*) as count FROM prompt_examples WHERE category = ?', [category[0].slug]);
    if (examples[0].count > 0) {
      connection.release();
      return res.status(400).json({ error: `无法删除分类，有 ${examples[0].count} 个案例正在使用此分类` });
    }
    
    // 删除分类
    await connection.query('DELETE FROM categories WHERE id = ?', [id]);
    
    connection.release();
    res.json({ success: true, message: '分类已成功删除' });
  } catch (error) {
    console.error('删除分类错误:', error);
    res.status(500).json({ error: '删除分类失败' });
  }
});
// --- 管理员功能 ---

// 获取指定用户详情 (需要管理员权限)
app.get('/api/admin/users/:id', authenticateToken, requireAdmin, async (req, res) => { // Use imported middleware
    try {
        const { id } = req.params;
        const connection = await pool.getConnection();
        try {
            const [users] = await connection.query(
                'SELECT id, username, email, role, nickname, avatar_url, created_at FROM users WHERE id = ?',
                [id]
            );
            connection.release();

            if (users.length === 0) {
                return res.status(404).json({ error: '未找到该用户' });
            }

            const user = users[0];
            // 处理头像 URL (如果需要完整 URL)
            // const baseUrl = process.env.API_BASE_URL || `http://localhost:${PORT}`;
            // user.avatar_url = user.avatar_url ? `${baseUrl}/${uploadDir}/${user.avatar_url}` : null;

            res.json(user); // 返回原始数据

        } catch (dbError) {
            connection.release();
            throw dbError;
        }
    } catch (error) {
        console.error(`管理员获取用户 ${req.params.id} 详情错误:`, error);
        res.status(500).json({ error: '获取用户详情失败', details: error.message });
    }
});

// 更新用户信息 (需要管理员权限)
app.put('/api/admin/users/:id', authenticateToken, requireAdmin, async (req, res) => { // Use imported middleware
    try {
        const { id } = req.params;
        const { role, nickname } = req.body; // 允许管理员修改角色和昵称
        const userIdToUpdate = parseInt(id);

        // 基本验证
        if (!role || !['admin', 'user'].includes(role)) {
            return res.status(400).json({ error: '无效的用户角色' });
        }
        // 昵称可以是空字符串或 null

        // 防止管理员修改自己的角色 (如果需要此限制)
        // if (req.user.id === userIdToUpdate && req.user.role === 'admin' && role !== 'admin') {
        //     return res.status(403).json({ error: '管理员不能修改自己的角色' });
        // }

        const connection = await pool.getConnection();
        try {
            // 检查用户是否存在
            const [users] = await connection.query('SELECT id, role FROM users WHERE id = ?', [userIdToUpdate]);
            if (users.length === 0) {
                connection.release();
                return res.status(404).json({ error: '未找到该用户' });
            }

            // 检查是否是最后一个管理员 (如果尝试将最后一个管理员降级)
            if (users[0].role === 'admin' && role === 'user') {
                 const [[{ adminCount }]] = await connection.query('SELECT COUNT(*) as adminCount FROM users WHERE role = ?', ['admin']);
                 if (adminCount <= 1) {
                     connection.release();
                     return res.status(400).json({ error: '不能移除最后一个管理员的角色' });
                 }
            }


            // 更新用户信息
            await connection.query(
                'UPDATE users SET role = ?, nickname = ? WHERE id = ?',
                [role, nickname || null, userIdToUpdate]
            );

            // 获取更新后的用户信息 (排除密码)
            const [updatedUsers] = await connection.query(
                'SELECT id, username, email, role, nickname, avatar_url, created_at FROM users WHERE id = ?',
                [userIdToUpdate]
            );
            connection.release();

            res.json({ message: '用户信息更新成功', user: updatedUsers[0] });

        } catch (dbError) {
            connection.release();
            throw dbError;
        }
    } catch (error) {
        console.error(`管理员更新用户 ${req.params.id} 信息错误:`, error);
        res.status(500).json({ error: '更新用户信息失败', details: error.message });
    }
});

// 删除用户 (需要管理员权限)
app.delete('/api/admin/users/:id', authenticateToken, requireAdmin, async (req, res) => { // Use imported middleware
    try {
        const { id } = req.params;
        const userIdToDelete = parseInt(id);

        // 不能删除自己
        if (req.user.id === userIdToDelete) {
            return res.status(403).json({ error: '不能删除自己' });
        }

        const connection = await pool.getConnection();
        await connection.beginTransaction();
        try {
            // 检查用户是否存在以及是否是管理员
            const [users] = await connection.query('SELECT id, role, avatar_url FROM users WHERE id = ?', [userIdToDelete]);
            if (users.length === 0) {
                 await connection.rollback();
                 connection.release();
                return res.status(404).json({ error: '未找到该用户' });
            }
            const userToDelete = users[0];

            // 防止删除最后一个管理员
            if (userToDelete.role === 'admin') {
                 const [[{ adminCount }]] = await connection.query('SELECT COUNT(*) as adminCount FROM users WHERE role = ?', ['admin']);
                 if (adminCount <= 1) {
                     await connection.rollback();
                     connection.release();
                     return res.status(400).json({ error: '不能删除最后一个管理员' });
                 }
            }

            // 1. 处理关联的 prompt_examples (将 author_id 设为 NULL)
            console.log(`将用户 ${userIdToDelete} 创建的案例作者设为 NULL`);
            await connection.query('UPDATE prompt_examples SET author_id = NULL WHERE author_id = ?', [userIdToDelete]);

            // 2. 删除关联的点赞记录 (prompt_likes)
            console.log(`删除用户 ${userIdToDelete} 的点赞记录`);
            await connection.query('DELETE FROM prompt_likes WHERE user_id = ?', [userIdToDelete]);

            // 3. 删除关联的密码重置记录 (password_resets)
            console.log(`删除用户 ${userIdToDelete} 的密码重置记录`);
            await connection.query('DELETE FROM password_resets WHERE user_id = ?', [userIdToDelete]);

            // 4. 删除关联的邮箱验证记录 (email_verifications)
            console.log(`删除用户 ${userIdToDelete} 的邮箱验证记录`);
            await connection.query('DELETE FROM email_verifications WHERE user_id = ?', [userIdToDelete]);
            console.log(`[Email Change] Deleted old verification tokens for user ${userIdToDelete}.`); // <-- Added log

            // 5. 删除用户记录
            console.log(`删除用户 ${userIdToDelete} 的记录`);
            await connection.query('DELETE FROM users WHERE id = ?', [userIdToDelete]);

            // 6. 删除用户的头像文件 (如果存在)
            if (userToDelete.avatar_url) {
                const avatarPath = path.join(uploadPath, userToDelete.avatar_url);
                console.log(`尝试删除用户 ${userIdToDelete} 的头像文件: ${avatarPath}`);
                if (fs.existsSync(avatarPath)) {
                    try {
                        fs.unlinkSync(avatarPath);
                        console.log('用户头像文件已删除');
                    } catch (unlinkErr) {
                        console.error(`删除用户 ${userIdToDelete} 头像文件失败:`, unlinkErr);
                        // 记录错误，但继续完成删除流程
                    }
                } else {
                    console.log('用户头像文件不存在，无需删除');
                }
            }

            await connection.commit();
            connection.release();
            console.log(`用户 ${userIdToDelete} 已成功删除`);
            res.json({ success: true, message: '用户已成功删除' });

        } catch (dbError) {
            await connection.rollback();
            connection.release();
            console.error(`数据库操作错误 (删除用户 ${userIdToDelete}):`, dbError);
            throw dbError; // 重新抛出以便外层捕获
        }
    } catch (error) {
        console.error(`管理员删除用户 ${req.params.id} 错误:`, error);
        res.status(500).json({ error: '删除用户失败', details: error.message });
    }
});

// --- 结束 管理员功能 ---

// --- 新增：获取插件统计信息 ---
app.get('/api/plugin/stats', async (req, res) => {
    console.log('请求插件统计信息');
    let connection;
    try {
        connection = await pool.getConnection();
        // 获取下载次数
        const [[downloadCountRow]] = await connection.query(
            "SELECT stat_value FROM plugin_stats WHERE stat_key = 'download_count'"
        );
        const downloadCount = downloadCountRow ? parseInt(downloadCountRow.stat_value || '0', 10) : 0;

        connection.release();
        console.log(`返回插件下载次数: ${downloadCount}`);
        res.json({ downloadCount });
    } catch (error) {
        if (connection) connection.release();
        console.error('获取插件统计信息失败:', error);
        // 即使失败也返回 0，避免前端出错
        res.status(500).json({ downloadCount: 0, error: '获取统计信息失败' });
    }
});

// --- 新增：处理插件下载请求 ---
app.get('/api/plugin/download', async (req, res) => {
    console.log('处理插件下载请求');
    let connection;
    try {
        connection = await pool.getConnection();
        await connection.beginTransaction();

        // 1. 增加下载计数 (原子操作)
        // 使用 INSERT ... ON DUPLICATE KEY UPDATE 来确保 stat_key 存在
        await connection.query(`
            INSERT INTO plugin_stats (stat_key, stat_value)
            VALUES ('download_count', '1')
            ON DUPLICATE KEY UPDATE stat_value = CAST(stat_value AS UNSIGNED) + 1;
        `);
        console.log('下载计数已增加');

        // 2. 获取实际下载 URL
        const [[downloadUrlRow]] = await connection.query(
            "SELECT stat_value FROM plugin_stats WHERE stat_key = 'download_url'"
        );

        if (!downloadUrlRow || !downloadUrlRow.stat_value) {
            throw new Error('未配置插件下载 URL');
        }
        const downloadUrl = downloadUrlRow.stat_value;
        console.log(`获取到插件下载 URL: ${downloadUrl}`);

        await connection.commit();
        connection.release();

        // 3. 重定向到实际下载地址
        console.log(`重定向到: ${downloadUrl}`);
        res.redirect(302, downloadUrl);

    } catch (error) {
        if (connection) {
            await connection.rollback();
            connection.release();
        }
        console.error('处理插件下载失败:', error);
        res.status(500).send('下载处理失败，请稍后重试或联系管理员。');
    }
});

// --- 结束 管理员功能 ---

// --- 导入所有路由 ---
const aiGenerateRoutes = require('./routes/ai-generate');
// const promptRoutes = require('./routes/prompts'); // 文件不存在，注释掉
// const authRoutes = require('./routes/auth'); // 认证路由直接在 index.js 定义，注释掉
const creditRoutes = require('./routes/credits');
// const categoryRoutes = require('./routes/categories'); // 分类路由直接在 index.js 定义，注释掉 
//const tagRoutes = require('./routes/tags'); 
const historyRoutes = require('./routes/ai-history');
const adminUserManagementRoutes = require('./routes/admin/userManagement');
const authResetRoutes = require('./routes/authReset'); // <-- ADDED
const creativeUpscaleRoutes = require('./routes/creative-upscale'); // <-- Import new route
const upscaleHistoryRoutes = require('./routes/upscale-history'); // <-- Import history route
const featureCostsAdminRoutes = require('./routes/admin/featureCosts'); // <-- Import feature costs admin route
const imageUtilRoutes = require('./routes/imageUtils'); // <--- 导入图片工具路由
const removeBackgroundHistoryRoutes = require('./routes/removeBackgroundHistory'); // <--- 导入历史路由
const vectorizeHistoryRoutes = require('./routes/vectorizeHistory'); // <--- 导入 SVG 历史路由
const imageTo3dRoutes = require('./routes/image-to-3d'); // <--- 挂载图片转3D路由
const imageToVideoRoutes = require('./routes/image-to-video'); // <--- 挂载图生视频路由
const commentRoutes = require('./routes/comments'); // <--- 新增这行
const gpt4oEditRoutes = require('./routes/gpt4o-edit'); // <-- 导入 GPT-4o 编辑路由
const removeAnythingHistoryRouter = require('./routes/removeAnythingHistory');
const removeAnythingRunRouter = require('./routes/removeAnythingRun');
const examplesRoutes = require('./routes/examples'); // <--- 在这里添加这一行
const jaazProxyRoutes = require('./routes/jaaz-proxy'); // <--- 新增：导入Jaaz代理路由
const imageProxyRoutes = require('./routes/image-proxy'); // <--- 新增：导入图片代理路由
const notificationsRoutes = require('./routes/notifications'); // <--- 新增：导入通知路由
const patternExtractionRoutes = require('./routes/pattern-extraction'); // <--- 新增：导入印花提取路由
const ecommerceToolsRouter = require('./routes/ecommerce-tools');

// 引入Midjourney路由
const mjGenerateRouter = require('./routes/mj-generate');
const uiComponentsRoutes = require('./routes/uiComponents'); // <-- 新增：导入UI组件路由

// --- 挂载路由 ---
app.use('/api/ai', aiGenerateRoutes);
// app.use('/api/prompts', promptRoutes); // 文件不存在，注释掉
// app.use('/api/auth', authRoutes); // 认证路由直接在 index.js 定义，注释掉
app.use('/api/credits', creditRoutes);
// app.use('/api/categories', categoryRoutes); // 分类路由直接在 index.js 定义，注释掉
//app.use('/api/tags', tagRoutes);
app.use('/api/ai/history', historyRoutes); // <--- 修改这里
app.use('/api/admin', adminUserManagementRoutes);
app.use('/api/auth', authResetRoutes); // <-- ADDED to mount reset routes under /api/auth
app.use('/api/creative-upscale', creativeUpscaleRoutes); // <-- Mount new route
app.use('/api/upscale-history', upscaleHistoryRoutes); // <-- Mount history route
app.use('/api/admin/feature-costs', featureCostsAdminRoutes); // <-- Mount feature costs admin route
app.use('/api/images', imageUtilRoutes);   // <--- 使用图片工具路由
app.use('/api/remove-background', removeBackgroundHistoryRoutes); // <--- 挂载历史路由
app.use('/api/vectorize', vectorizeHistoryRoutes); // <--- 挂载 SVG 历史路由
app.use('/api/image-to-3d', imageTo3dRoutes); // <--- 挂载图片转3D路由
app.use('/api/image-to-video', imageToVideoRoutes); // <--- 挂载图生视频路由
app.use('/api/gpt4o-edit', gpt4oEditRoutes); // <-- 修改：挂载 GPT-4o 编辑路由到 /api 下 (完整路径为 /api/gpt4o-edit 和 /api/gpt4o-edit/history)
app.use('/api/remove-anything', removeAnythingHistoryRouter);
app.use('/api/remove-anything', removeAnythingRunRouter);
app.use('/api/examples', examplesRoutes); // <--- 推荐这样修改
app.use('/', jaazProxyRoutes); // <--- 新增：挂载Jaaz代理路由（注意：挂载在根路径，因为代理路由已包含/api/jaaz前缀）
app.use('/', imageProxyRoutes); // <--- 新增：挂载图片代理路由
app.use('/api/notifications', notificationsRoutes); // <--- 新增：挂载通知路由
app.use('/api/pattern-extraction', patternExtractionRoutes); // <--- 新增：挂载印花提取路由
app.use('/api/ecommerce-tools', ecommerceToolsRouter);
app.use('/api/mj', mjGenerateRouter);
app.use('/api/ui-components', uiComponentsRoutes); // <-- 新增：挂载UI组件路由

// 使用非参数化路径挂载评论路由，避免Express路由参数解析问题
console.log('[API] 挂载评论路由: /api/examples/comment-routes');
// app.use('/api/examples/comment-routes', commentRoutes); // 删除这一行
app.use('/api/examples/comment-routes', commentRoutes); // 取消注释，恢复挂载

// 保持向后兼容性：仍然保留原始路由挂载
console.log('[API] 挂载评论路由: /api/examples/:exampleId/comments');
app.use('/api/examples/:exampleId/comments', commentRoutes);

// --- 错误处理中间件 ---
app.use((err, req, res, next) => {
    console.error("全局错误处理:", err.stack);
    res.status(500).send('服务器发生错误！');
});

// 启动服务器
app.listen(PORT, async () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);

  // 启动时测试数据库连接
  try {
    console.log('正在测试数据库连接...');
    const connection = await pool.getConnection(); // Use imported pool
    console.log('数据库连接成功!');

    // 测试查询
    try {
      console.log('正在测试数据库查询...');
      const [tables] = await connection.query('SHOW TABLES');
      console.log('数据库表:', tables.map(t => Object.values(t)[0]).join(', '));

      // 检查必要的表是否存在
      const requiredTables = ['prompt_examples', 'tags', 'prompt_tags', 'users', 'categories', 'password_resets', 'email_verifications', 'prompt_likes', 'plugin_stats', 'ai_generation_history']; // Added ai_generation_history
      const existingTables = tables.map(t => Object.values(t)[0]);
      const missingTables = requiredTables.filter(table => !existingTables.includes(table));

      if (missingTables.length > 0) {
        console.error('警告: 缺少必要的数据库表:', missingTables.join(', '));
        console.error('请检查数据库结构是否完整');
      } else {
        console.log('所有必要的表都存在');
        // ... existing checks ...
      }
    } catch (queryError) {
      console.error('数据库查询测试失败:', queryError);
    }

    connection.release();
  } catch (dbError) {
    console.error('数据库连接测试失败:', dbError);
    console.error('请检查 .env 文件中的数据库配置是否正确');
    // ... existing error logging ...
  }
});

// --- 导出需要在其他模块中使用的函数和变量 ---
// module.exports = { pool, authenticateToken }; // REMOVED

// 在 server/index.js 顶部附近
const aiChatHistoryRoutes = require('./routes/ai-chat-history');

// 在 server/index.js 合适的位置 (例如其他 app.use 之后)
app.use('/api/ai-chat', aiChatHistoryRoutes);

// In prompt-examples/server/index.js
// ...

// ...
app.use('/api/examples/:exampleId/comments', commentRoutes);
// ...

console.log('准备挂载 /api/remove-anything 路由');
app.use('/api/remove-anything', removeAnythingHistoryRouter);
console.log('/api/remove-anything 路由已挂载');

app.use('/api/voice-clone', require('./routes/voice-clone'));

// 在其他路由注册之后添加
const fluxRoutes = require('./routes/flux-routes');
app.use('/api/flux', fluxRoutes);

// 添加AI闪光灯路由
const flashLightUtils = require('./routes/flashLightUtils');
const flashLightHistory = require('./routes/flashLightHistory');
app.use('/api/images', flashLightUtils); // 处理闪光灯图片上传和处理
app.use('/api/flash-light', flashLightHistory); // 处理闪光灯历史记录
app.use('/api/flash-beauty', flashLightUtils); // 使用已有的flashLightUtils处理API路由
app.use('/api/images/flash-beauty', flashLightUtils); // 处理闪光灯和美颜功能

// 添加激活账户API
app.post('/api/auth/activate', async (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      return res.status(400).json({ error: '激活令牌不能为空' });
    }
    
    // 验证激活令牌
    const result = await verifyActivationToken(token);
    
    if (!result.success) {
      return res.status(400).json({ error: result.message });
    }
    
    res.json({
      message: result.message,
      email: result.email
    });
  } catch (error) {
    console.error('激活账户错误:', error);
    res.status(500).json({ error: '激活账户失败，请稍后重试' });
  }
});

// 添加重新发送激活邮件API
app.post('/api/auth/resend-activation', async (req, res) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return res.status(400).json({ error: '邮箱不能为空' });
    }
    
    // 重新发送激活邮件
    const result = await resendActivationEmail(email, mailer);
    
    if (!result.success) {
      return res.status(400).json({ error: result.message });
    }
    
    res.json({
      message: '激活邮件已重新发送，请查收'
    });
  } catch (error) {
    console.error('重发激活邮件错误:', error);
    res.status(500).json({ error: '重发激活邮件失败，请稍后重试' });
  }
});

// SSO 相关路由
// 生成跳转到JAAZ系统的SSO Token
app.post('/api/sso/generate-token', authenticateToken, async (req, res) => {
  try {
    const user = req.user;
    const { target_system = 'jaaz' } = req.body;
    
    // 生成SSO Token
    const ssoToken = SSOAuth.generateSSOToken(user, target_system);
    
    // 构建跳转URL - 使用反向代理地址
    const jaazBaseUrl = process.env.JAAZ_BASE_URL || 'https://figma123.yzycolour.top';
    const redirectUrl = `${jaazBaseUrl}/sso/login?token=${encodeURIComponent(ssoToken)}`;
    
    res.json({
      success: true,
      sso_token: ssoToken,
      redirect_url: redirectUrl,
      message: '跳转令牌生成成功'
    });
  } catch (error) {
    console.error('生成SSO Token失败:', error);
    res.status(500).json({
      success: false,
      message: '生成跳转令牌失败'
    });
  }
});

// 验证SSO Token（供JAAZ系统调用）
app.post('/api/sso/verify-token', async (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Token不能为空'
      });
    }
    
    // 验证SSO Token
    const user = await SSOAuth.verifySSOToken(token);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Token无效或已过期'
      });
    }
    
    // 获取用户积分信息
    const creditsInfo = await SSOAuth.getUserCredits(user.id);
    
    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        nickname: user.nickname,
        avatar_url: user.avatar_url,
        is_activated: user.is_activated,
        credits: creditsInfo.credits
      },
      message: 'Token验证成功'
    });
  } catch (error) {
    console.error('验证SSO Token失败:', error);
    res.status(500).json({
      success: false,
      message: '验证Token失败'
    });
  }
});

// 获取用户积分（供JAAZ系统调用）
app.get('/api/sso/user/:userId/credits', async (req, res) => {
  try {
    const { userId } = req.params;
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '需要授权Token'
      });
    }
    
    // 验证SSO Token
    const user = await SSOAuth.verifySSOToken(token);
    if (!user || user.id != userId) {
      return res.status(403).json({
        success: false,
        message: '无权访问此用户的积分信息'
      });
    }
    
    // 获取积分信息
    const creditsInfo = await SSOAuth.getUserCredits(userId);
    
    res.json({
      success: true,
      credits: creditsInfo.credits
    });
  } catch (error) {
    console.error('获取用户积分失败:', error);
    res.status(500).json({
      success: false,
      message: '获取积分信息失败'
    });
  }
});

// 扣除用户积分（供JAAZ系统调用）
app.post('/api/sso/user/:userId/deduct-credits', async (req, res) => {
  try {
    const { userId } = req.params;
    const { amount, reason = 'JAAZ系统使用' } = req.body;
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '需要授权Token'
      });
    }
    
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: '扣除数量必须大于0'
      });
    }
    
    // 验证SSO Token
    const user = await SSOAuth.verifySSOToken(token);
    if (!user || user.id != userId) {
      return res.status(403).json({
        success: false,
        message: '无权操作此用户的积分'
      });
    }
    
    // 扣除积分
    await SSOAuth.deductCredits(userId, amount, reason);
    
    // 获取最新积分
    const creditsInfo = await SSOAuth.getUserCredits(userId);
    
    res.json({
      success: true,
      message: `成功扣除${amount}积分`,
      remaining_credits: creditsInfo.credits
    });
  } catch (error) {
    console.error('扣除用户积分失败:', error);
    const message = error.message === '积分余额不足' ? error.message : '扣除积分失败';
    res.status(error.message === '积分余额不足' ? 400 : 500).json({
      success: false,
      message
    });
  }
});

// 增加用户积分（供管理系统调用）
app.post('/api/sso/user/:userId/add-credits', async (req, res) => {
  try {
    const { userId } = req.params;
    const { amount, reason = '系统奖励' } = req.body;
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '需要授权Token'
      });
    }
    
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: '增加数量必须大于0'
      });
    }
    
    // 验证SSO Token
    const user = await SSOAuth.verifySSOToken(token);
    if (!user) {
      return res.status(403).json({
        success: false,
        message: 'Token无效'
      });
    }
    
    // 只有管理员可以给其他用户增加积分，或者用户只能给自己增加
    if (user.role !== 'admin' && user.id != userId) {
      return res.status(403).json({
        success: false,
        message: '无权操作此用户的积分'
      });
    }
    
    // 增加积分
    await SSOAuth.addCredits(userId, amount, reason);
    
    // 获取最新积分
    const creditsInfo = await SSOAuth.getUserCredits(userId);
    
    res.json({
      success: true,
      message: `成功增加${amount}积分`,
      current_credits: creditsInfo.credits
    });
  } catch (error) {
    console.error('增加用户积分失败:', error);
    res.status(500).json({
      success: false,
      message: '增加积分失败'
    });
  }
});

// 恢复原来的管理员路由
app.use('/api/admin/feature-costs', require('./routes/admin/featureCosts'));

// 添加公共访问路由
app.use('/api/feature-costs', require('./routes/featureCosts'));

// 添加Flux API路由
app.use('/api/flux', require('./routes/flux'));

// 添加Flux Kontext专用API路由(不扣积分版本)
app.use('/api/flux-kontext', require('./routes/flux-kontext'));
