<!DOCTYPE html>
<html lang="zh" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>更新日志 - 提示词案例管理系统</title>
    <link href="../libs/css/bootstrap.min.css" rel="stylesheet">
    <link href="../libs/css/bootstrap-icons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        /* Basic Timeline Styles */
        .timeline {
            position: relative;
            padding: 20px 0;
            list-style: none;
            max-width: 800px;
            margin: 30px auto;
        }

        .timeline::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 3px;
            background: rgba(255, 255, 255, 0.15); /* Timeline line color */
            left: 40px; /* Adjust based on icon/marker size */
            margin-left: -1.5px;
        }

        .timeline-item {
            margin: 0 0 40px 0; /* Spacing between items */
            padding-left: 90px; /* 增加左内边距，给日期更多空间 */
            position: relative;
        }

        .timeline-marker {
            position: absolute;
            left: 40px;
            top: 0;
            transform: translateX(-50%);
            background-color: var(--modal-bg-color); /* Use modal background */
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
        }

        .timeline-marker i {
            font-size: 12px;
            color: #adb5bd; /* Icon color */
        }

        .timeline-content {
            background: var(--card-bg-color); /* Use card background */
            border-radius: 8px;
            padding: 15px 20px;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
        }

         .timeline-content::before { /* Arrow pointing to timeline */
            content: '';
            position: absolute;
            top: 8px;
            left: -10px; /* Adjust position */
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-right: 10px solid rgba(255, 255, 255, 0.1); /* Match border color */
        }
         .timeline-content::after { /* Cover the border line behind arrow */
             content: '';
             position: absolute;
             top: 9px; /* Offset slightly */
             left: -8px; /* Offset slightly */
             width: 0;
             height: 0;
             border-top: 7px solid transparent;
             border-bottom: 7px solid transparent;
             border-right: 9px solid var(--card-bg-color); /* Match background color */
         }


        .timeline-content h5 {
            margin-top: 0;
            margin-bottom: 5px;
            font-size: 1.1rem;
            color: #e0e0e0;
        }
        .timeline-content p {
            margin-bottom: 5px;
            font-size: 0.9rem;
            color: #adb5bd;
        }
         .timeline-content ul {
             padding-left: 20px;
             margin-top: 10px;
             margin-bottom: 0;
             font-size: 0.9rem;
             color: #adb5bd;
         }
         .timeline-content ul li {
             margin-bottom: 3px;
         }
        .timeline-date {
            font-size: 0.8rem;
            color: #6c757d;
            position: absolute;
            top: 3px;
            left: 0; /* Position relative to the item's left padding */
            width: 95px; /* 稍微增加宽度 */
            text-align: right;
            margin-left: -85px; /* 调整负边距以适应新的 padding-left */
        }
        /* Adjust left position for smaller screens if needed */
        @media (max-width: 768px) {
            .timeline::before { left: 20px; }
            .timeline-item { padding-left: 45px; }
            .timeline-marker { left: 20px; width: 20px; height: 20px; }
            .timeline-marker i { font-size: 10px; }
            .timeline-date { display: block; position: static; margin-left: 0; text-align: left; margin-bottom: 5px; }
            .timeline-content::before, .timeline-content::after { display: none; } /* Hide arrow on mobile */
        }
    </style>
</head>
<body>
    <div class="grid-mask"></div>
    <div class="container">
        <div class="page-header" style="justify-content: center;">
             <h1><i class="bi bi-clock-history me-2"></i>更新日志</h1>
        </div>

        <ul class="timeline">
            <!-- 新增：Flux Kontext Pro 图片处理功能 -->
            <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-palette-fill text-primary"></i></div>
                <span class="timeline-date">2025-06-15</span>
                <div class="timeline-content">
                    <h5>新功能：Flux Kontext Pro 图片处理</h5>
                    <p>推出全新的 Flux Kontext Pro 图片处理功能，利用 AI 技术实现高质量图像生成与处理：</p>
                    <ul>
                        <li><strong>纯文本生成图片：</strong> 输入文本提示词，选择比例和格式，通过 AI 生成精美图像。</li>
                        <li><strong>图生图模式：</strong> 上传参考图片与文本提示词结合，生成风格一致的创意图像。</li>
                        <li><strong>智能图像扩展：</strong> 专业的扩图功能，能够智能扩展图片边界，实现无缝衔接。</li>
                        <li><strong>直观编辑器：</strong> 扩图模式下提供可视化编辑器，可拖拽调整扩展区域、预览效果。</li>
                        <li><strong>比例智能匹配：</strong> 提供多种常用比例预设，轻松调整扩展区域到指定比例。</li>
                        <li><strong>图像操作增强：</strong> 支持图片缩放、平移、多角度查看，更精确控制扩展效果。</li>
                        <li><strong>多语言提示词支持：</strong> 自动检测中文提示词并进行翻译处理，优化生成效果。</li>
                        <li><strong>历史记录功能：</strong> 保存处理结果历史，便于查看、下载和再次使用。</li>
                    </ul>
                </div>
            </li>
            
            <!-- 新增：AI提示词助手功能 -->
            <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-robot text-primary"></i></div>
                <span class="timeline-date">2025-05-26</span>
                <div class="timeline-content">
                    <h5>新功能：AI提示词助手</h5>
                    <p>新增智能提示词助手功能，帮助优化和完善您的AI绘图提示词：</p>
                    <ul>
                        <li><strong>便捷访问：</strong> 在AI图片生成的提示词输入框旁增加"提示词助手"按钮，一键唤起助手对话窗口。</li>
                        <li><strong>智能对话：</strong> 基于智普GLM-4的AI助手，可以帮您优化、扩写或翻译提示词。</li>
                        <li><strong>多轮交互：</strong> 支持多轮对话，逐步精确调整您的提示词。</li>
                        <li><strong>一键填入：</strong> 满意后可直接将优化后的提示词一键填入主输入框，无需手动复制粘贴。</li>
                        <li><strong>交互体验：</strong> 优化了中文输入法的兼容性，解决了输入中文时回车键冲突问题。</li>
                    </ul>
                </div>
            </li>
            <!-- 新增：Midjourney生图功能 -->
            <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-palette text-success"></i></div>
                <span class="timeline-date">2025-05-22</span>
                <div class="timeline-content">
                    <h5>新功能：Midjourney生图支持</h5>
                    <p>在原有的超级生图和高级生图之外，新增对Midjourney模型的支持：</p>
                    <ul>
                        <li><strong>模型选择：</strong> 在AI图片生成区域的模型选择中，新增Midjourney选项。</li>
                        <li><strong>专属参数：</strong> 选择Midjourney后，界面会自动调整为对应的参数设置，包括模式选择(Fast/Relax)。</li>
                        <li><strong>垫图功能：</strong> 支持上传参考图片作为生成基础（Imagine专用）。</li>
                        <li><strong>命令提示：</strong> 提供Midjourney特有参数的使用提示，如--ar、--s、--v等命令。</li>
                        <li><strong>任务追踪：</strong> 支持异步生成任务追踪，完成后自动显示结果。</li>
                        <li><strong>多图结果：</strong> 展示Midjourney的网格结果，支持对网格中的单个图片进行放大(U)、变体(V)等操作。</li>
                    </ul>
                </div>
            </li>
            <!-- 新增：一键同款智能匹配比例 -->
            <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-magic text-primary"></i></div> <!-- 使用魔法棒或尺寸相关图标 -->
                <span class="timeline-date">2025-05-18</span> <!-- 使用当前或更新的日期 -->
                <div class="timeline-content">
                    <h5>优化："一键同款"智能匹配图片比例</h5>
                    <p>为了让"一键同款"功能更加便捷，我们进行了以下优化：</p>
                    <ul>
                        <li><strong>智能识别比例：</strong> 当您从案例详情页点击"一键同款"时，系统会自动识别原案例图片的宽高比例。</li>
                        <li><strong>自动预设参数：</strong> 在跳转到 AI 图片生成区域后，系统会根据识别到的图片比例，为您自动选择最接近的预设生成尺寸比例。</li>
                        <li><strong>体验更流畅：</strong> 无需手动调整图片比例，直接就能开始生成与原案例风格一致的图片，操作更省心。</li>
                    </ul>
                </div>
            </li>
            <!-- 新增：首尾帧图生视频 -->
            <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-images text-info"></i></div> <!-- 使用图片组图标 -->
                <span class="timeline-date">2025-05-12</span> <!-- 使用最新的日期 -->
                <div class="timeline-content">
                    <h5>增强：图生视频 - 支持首尾帧模式</h5>
                    <p>在原有图生视频功能基础上，新增了通过指定首、尾两张图片来生成过渡视频的模式：</p>
                    <ul>
                        <li><strong>模式切换:</strong> 在"图生视频"标签页增加了"首尾帧模式"开关。</li>
                        <li><strong>双图上传:</strong> 启用该模式后，需要分别上传首帧和尾帧图片。</li>
                        <li><strong>专用模型:</strong> 此模式目前使用特定的 `wanx2.1-kf2v-plus` 模型进行处理。</li>
                        <li><strong>参数设置:</strong> 仍然需要输入提示词并选择分辨率。</li>
                        <li><strong>平滑过渡:</strong> AI 会根据首尾帧和提示词生成中间的过渡动画。</li>
                        <li><strong>积分消耗:</strong> 首尾帧模式有单独的积分消耗配置。</li>
                        <li><strong>UI 健壮性修复:</strong> 修复了任务完成后 UI 可能卡在加载状态的问题，增加了超时保护和状态恢复机制。</li>
                    </ul>
                </div>
            </li>
             <!-- 新增：近期优化与修复 -->
            <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-tools text-info"></i></div>
                <span class="timeline-date">2025-05-12</span> <!-- 使用一个更新的日期 -->
                <div class="timeline-content">
                    <h5>界面优化与功能修复</h5>
                    <p>我们进行了一系列优化和修复，以提升您的使用体验：</p>
                    <ul>
                        <li><strong>背景移除功能增强:</strong>
                            <ul>
                                <li>修复了处理过程中加载提示有时位置不正确或不显示的问题。</li>
                                <li>优化了处理完成前的加载动画效果，使其更平滑。</li>
                                <li>统一了"抠图引擎"和"移除模式"选择框的样式，改善了在不同操作系统和主题下的视觉一致性。</li>
                            </ul>
                        </li>
                        <li><strong>案例详情与浏览体验优化:</strong>
                            <ul>
                                <li>改进了案例详情弹窗在手机等小屏幕设备上的显示效果，确保所有内容都能完整、顺畅地查看。</li>
                                <li>修正了"一键同款"按钮的显示逻辑，确保它在正确的场景下出现。</li>
                                <li>修复了打开案例详情时可能发生的内部错误，提升了稳定性。</li>
                                <li>当鼠标移动到案例卡片上时，会显示可点击的手型光标，交互更清晰。</li>
                            </ul>
                        </li>
                        <li><strong>系统稳定性与后端优化:</strong> 对图片处理服务和相关任务流程进行了优化，提升了整体的稳定性和效率。</li>
                    </ul>
                </div>
            </li>
            <!-- 新增：图生视频 -->
            <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-film text-success"></i></div>
                <span class="timeline-date">2025-05-08</span> <!-- 使用一个合适的日期 -->
                <div class="timeline-content">
                    <h5>新功能：图生视频 (Image to Video)</h5>
                    <p>新增根据静态图片和提示词生成动态视频的功能：</p>
                    <ul>
                        <li><strong>专用 Tab 页:</strong> 新增 "图生视频" 标签页。</li>
                        <li><strong>图片上传:</strong> 支持拖拽、点击或粘贴上传 PNG/JPG/WEBP 图片 (最大 5MB)。</li>
                        <li><strong>提示词与模型:</strong> 需要输入提示词，并可选择不同的生成模型 (Turbo/Plus) 及支持的分辨率。</li>
                        <li><strong>异步任务:</strong> 视频生成在后台处理，用户提交任务后可关闭页面，稍后返回查看结果。</li>
                        <li><strong>状态轮询与结果:</strong> 前端会自动轮询任务状态，完成后在右侧展示生成的视频，并提供下载链接。</li>
                        <li><strong>历史记录:</strong> (待实现) 保留最近的图生视频历史。</li>
                        <li><strong>积分消耗:</strong> 此功能会根据所选模型消耗用户积分 (具体数值由管理员在后台配置)。</li>
                    </ul>
                </div>
            </li>
            <!-- 新增：AI 聊天助手后端存储 -->
            <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-database-down text-info"></i></div>
                <span class="timeline-date">2025-05-07</span> <!-- 使用今天的日期或相关日期 -->
                <div class="timeline-content">
                    <h5>增强：AI 聊天助手后端存储</h5>
                    <p>将 AI 聊天助手的历史记录存储从浏览器 LocalStorage 迁移至后端数据库：</p>
                    <ul>
                        <li><strong>数据持久化:</strong> 聊天记录现在与用户账号关联，存储在服务器数据库中，不再受浏览器缓存限制，更加安全可靠。</li>
                        <li><strong>后端 API:</strong> 开发了用于管理会话和消息的 CRUD API 接口。</li>
                        <li><strong>前端改造:</strong> 重构了前端 JavaScript 代码，使其通过调用新 API 来加载、保存、删除聊天记录。</li>
                        <li><strong>用户隔离:</strong> 每个用户的聊天记录现在是独立的。</li>
                        <li><strong>(未来基础):</strong> 为跨设备同步聊天记录等功能奠定了基础。</li>
                    </ul>
                </div>
            </li>
            <!-- 新增：悬浮图标优化 -->
            <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-hand-index-thumb"></i></div>
                <span class="timeline-date">2025-05-06</span> <!-- 使用今天的日期或相关日期 -->
                <div class="timeline-content">
                    <h5>优化：AI 生成历史悬浮操作</h5>
                    <p>为 AI 生成历史记录中的图片增加了悬浮操作按钮，方便快速流转：</p>
                    <ul>
                        <li><strong>悬浮按钮:</strong> 鼠标悬停在历史图片上时，会显示一组操作图标。</li>
                        <li><strong>快捷操作:</strong> 提供 "发送到清晰放大"、"发送到背景移除"、"发送到位图转 SVG" 按钮，点击后可将该图片直接发送到对应功能标签页进行处理。</li>
                        <li><strong>样式统一:</strong> 调整了悬浮图标的样式和间距，使其更美观协调。</li>
                    </ul>
                </div>
            </li>
            <!-- 新增：位图转 SVG 功能 -->
            <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-bounding-box-circles text-primary"></i></div> <!-- 使用 SVG 图标 -->
                <span class="timeline-date">2025-05-06</span> <!-- 使用今天的日期或相关日期 -->
                <div class="timeline-content">
                    <h5>新功能：位图转 SVG (Vectorize Image)</h5>
                    <p>新增位图转 SVG 功能，可以将普通图片转换为矢量格式：</p>
                    <ul>
                        <li><strong>专用 Tab 页:</strong> 新增 "位图转 SVG" 标签页。</li>
                        <li><strong>图片上传:</strong> 支持拖拽、点击或粘贴上传 PNG/JPG/WEBP 图片 (最大 5MB)。</li>
                        <li><strong>SVG 转换:</strong> 点击按钮开始处理，结果将在右侧显示。</li>
                        <li><strong>结果预览与下载:</strong> 处理完成后，可预览生成的 SVG 图像，并提供下载链接。</li>
                        <li><strong>历史记录:</strong> 保留最近的 SVG 转换历史，方便查看和再次下载。</li>
                         <li><strong>积分消耗:</strong> 此功能会消耗用户积分 (具体数值由管理员在后台配置)。</li>
                    </ul>
                </div>
            </li>
             <!-- 新增：背景移除功能 -->
            <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-eraser-fill text-danger"></i></div> <!-- 使用橡皮擦图标 -->
                <span class="timeline-date">2025-05-05</span> <!-- 使用一个合适的日期 -->
                <div class="timeline-content">
                    <h5>新功能：背景移除 (Background Removal)</h5>
                    <p>新增图像背景移除功能：</p>
                    <ul>
                        <li><strong>专用 Tab 页:</strong> 新增 "背景移除" 标签页。</li>
                        <li><strong>图片上传:</strong> 支持拖拽、点击或粘贴上传 PNG/JPG/WEBP 图片 (最大 5MB)。</li>
                        <li><strong>移除处理:</strong> 点击按钮开始处理，结果将在右侧显示。</li>
                        <li><strong>结果展示与下载:</strong> 处理完成后，可预览移除背景后的图片 (透明背景 PNG)，并提供下载链接。</li>
                        <li><strong>历史记录:</strong> 保留最近的背景移除历史，方便查看和再次下载。</li>
                        <li><strong>积分消耗:</strong> 此功能会消耗用户积分 (具体数值由管理员在后台配置)。</li>
                    </ul>
                </div>
            </li>
            <!-- 新增：清晰放大功能 -->
            <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-arrows-angle-expand text-info"></i></div> <!-- 使用放大图标 -->
                <span class="timeline-date">2025-05-05</span> <!-- 使用一个合适的日期 -->
                <div class="timeline-content">
                    <h5>新功能：清晰放大 (Creative Upscale)</h5>
                    <p>新增图像清晰放大功能，提升图片分辨率：</p>
                    <ul>
                        <li><strong>专用 Tab 页:</strong> 新增"清晰放大"标签页。</li>
                        <li><strong>图片上传:</strong> 支持拖拽、点击或粘贴上传 PNG/JPG/WEBP 图片 (最大 5MB, 4MP)。</li>
                        <li><strong>放大处理:</strong> 点击按钮开始处理，结果将在右侧显示。</li>
                        <li><strong>结果展示与下载:</strong> 处理完成后，可预览放大后的图片，并提供下载链接。</li>
                        <li><strong>历史记录:</strong> 保留最近的放大历史，方便查看和再次下载。</li>
                    </ul>
                </div>
            </li>
            <!-- 新增：积分系统 -->
            <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-coin text-success"></i></div> <!-- 使用硬币图标 -->
                <span class="timeline-date">2025-05-04</span> <!-- 更新为当前日期或相关日期 -->
                <div class="timeline-content">
                    <h5>新功能：用户积分系统</h5>
                    <p>引入了用户积分系统，用于未来的功能扩展和资源管理：</p>
                    <ul>
                        <li><strong>用户积分显示:</strong> 用户现在可以在页面顶部看到自己的积分余额。</li>
                        <li><strong>管理员积分管理:</strong>
                            <ul>
                                <li>在"管理中心"新增"用户积分管理"面板。</li>
                                <li>管理员可以查看所有用户的积分余额。</li>
                                <li>管理员可以直接修改用户的积分。</li>
                                <li>所有积分修改操作都会被记录。</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </li>
            <!-- 新增：AI 图片生成功能 -->
            <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-stars text-warning"></i></div> <!-- 使用星星图标 -->
                <span class="timeline-date">2025-05-03</span> <!-- 使用一个合适的日期 -->
                <div class="timeline-content">
                    <h5>新功能：AI 图片生成 & 一键入库</h5>
                    <p>集成 AI 图片生成能力，并打通了从生成到加入案例库的流程：</p>
                    <ul>
                        <li><strong>AI 图片生成 Tab:</strong> 新增专属标签页，可输入提示词、选择模型 (即梦 V3.0/V2.1)、尺寸进行图片生成。</li>
                        <li><strong>生成历史与复用:</strong> 结果与历史记录合并展示，支持分页，提供 "使用提示词" 功能。</li>
                        <li><strong>图片详情查看:</strong> 点击图片可查看大图及详细参数（模型、尺寸、时间）。</li>
                        <li><strong>一键上传案例库:</strong> 详情页提供上传按钮，点击后自动：
                            <ul>
                                <li>打开"添加案例"弹窗。</li>
                                <li>预填充提示词、模型、图片预览。</li>
                                <li>调用 AI 分析图片，自动填充建议的标题和标签。</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </li>
            <!-- Timeline Item Template -->
             <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-shield-gear"></i></div>
                <span class="timeline-date">2025-05-01</span>
                <div class="timeline-content">
                    <h5>新功能：管理中心增强</h5>
                    <p>为了方便管理员操作，我们对后台管理功能进行了整合与增强：</p>
                    <ul>
                       <li><strong>统一管理入口:</strong> 新增"管理中心"标签页 (仅管理员可见)，集中管理标签、分类和用户。</li>
                       <li><strong>标签管理完善:</strong> 管理员现在可以在管理中心内直接 <strong>更新</strong> 和 <strong>删除</strong> 标签 (如果标签未被使用)。</li>
                       <li><strong>分类管理整合:</strong> 将原有的分类管理功能移入管理中心，提供更一致的操作体验。</li>
                       <li><strong>用户管理上线:</strong> 管理员现在可以：
                           <ul>
                               <li>查看所有用户列表，支持搜索和分页。</li>
                               <li>编辑用户的昵称和角色 (User/Admin)。</li>
                               <li>删除用户 (不能删除自己或最后一个管理员)，删除时会自动处理关联数据。</li>
                           </ul>
                       </li>
                    </ul>
                </div>
            </li>
             <li class="timeline-item">
                 <div class="timeline-marker"><i class="bi bi-heart-fill text-danger"></i></div>
                 <span class="timeline-date">2025-04-30</span>
                 <div class="timeline-content">
                     <h5>新功能：我点赞的案例</h5>
                     <ul>
                        <li>现在可以在用户菜单中找到"我点赞的案例"选项。</li>
                        <li>点开后会弹出一个窗口，展示你所有点赞过的案例。</li>
                        <li>采用三列网格布局，滚动到底部会自动加载更多。</li>
                        <li>如果你是案例的作者或是管理员，会显示编辑和删除按钮。</li>
                     </ul>
                 </div>
             </li>
             <li class="timeline-item">
                 <div class="timeline-marker"><i class="bi bi-person-badge"></i></div>
                 <span class="timeline-date">2025-04-29</span>
                 <div class="timeline-content">
                     <h5>新功能：我的案例</h5>
                      <ul>
                         <li>在用户菜单中增加了"我的案例"选项，方便查看自己上传的案例。</li>
                         <li>展示方式从之前的 Tab 页改为更方便的弹出窗口。</li>
                         <li>修复了弹出窗口中卡片布局和样式显示不正确的问题。</li>
                         <li>采用三列网格布局，支持无限滚动加载。</li>
                      </ul>
                 </div>
             </li>
            <li class="timeline-item">
                <div class="timeline-marker"><i class="bi bi-tools"></i></div>
                <span class="timeline-date">2025-04-28</span>
                <div class="timeline-content">
                    <h5>功能优化与 Bug 修复</h5>
                    <ul>
                        <li>优化了部分内部代码，提升了运行效率。</li>
                        <li>修复了在"我的案例"中图片无法正常加载的问题。</li>
                        <li>调整了弹出窗口的宽度，使其在不同屏幕上显示更佳。</li>
                    </ul>
                </div>
            </li>
             <li class="timeline-item">
                 <div class="timeline-marker"><i class="bi bi-check-circle-fill text-success"></i></div>
                 <span class="timeline-date">2025-04-27</span>
                 <div class="timeline-content">
                     <h5>系统上线</h5>
                     <p>提示词工具箱初步上线，提供案例管理（增删改查）、分类与标签、用户登录注册、点赞等基础功能。</p>
                 </div>
             </li>
        </ul>

        <div class="text-center mt-4 mb-5">
            <a href="../index.html" class="btn btn-outline-secondary"><i class="bi bi-arrow-left me-1"></i>返回主页</a>
        </div>
    </div>

    <script src="../libs/js/bootstrap.bundle.min.js"></script>
    <script>
        const gridMask = document.querySelector('.grid-mask');
        if (gridMask) {
            document.addEventListener('mousemove', (e) => {
                gridMask.style.setProperty('--mouse-x', e.clientX + 'px');
                gridMask.style.setProperty('--mouse-y', e.clientY + 'px');
                gridMask.style.opacity = '1';
            });
            document.addEventListener('mouseleave', () => {
                gridMask.style.opacity = '0';
            });
        }
    </script>
</body>
</html> 