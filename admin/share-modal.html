<!-- Share Modal -->
<div class="modal fade" id="shareModal" tabindex="-1" aria-labelledby="shareModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="shareModalLabel">分享提示词案例</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="sharePosterContent"> <!-- Flex container: row -->
                 <!-- Left Column: Image (70%) -->
                <div class="share-image-left">
                    <div id="shareImageContainer" class="share-image-container">
                        <img src="" alt="案例图片" id="shareExampleImage" style="display: none;">
                    </div>
                </div>
                <!-- Right Column: Content (30%) -->
                <div class="share-content-right">
                    <!-- Author Info & QR Code Wrapper -->
                    <div class="share-header-wrapper d-flex justify-content-between align-items-center mb-3">
                        <!-- Author Info -->
                        <div class="share-author-info">
                            <img src="images/default-avatar.png" alt="作者头像" id="shareAuthorAvatar" class="share-author-avatar">
                            <div class="share-author-details">
                                <span id="shareAuthorName" class="share-author-name">加载中...</span>
                                <!-- Optional: <span class="share-author-meta">128 粉丝</span> -->
                            </div>
                            <!-- Optional: <button class="btn btn-sm btn-outline-secondary ms-auto">+ 关注</button> -->
                        </div>
                        <!-- QR Code Container (Moved Here) -->
                        <div class="qr-code-wrapper text-center">
                            <div id="shareQrCodeContainer"></div>
                        </div>
                    </div>
                    <!-- Title -->
                    <h6 id="shareExampleTitle" class="share-title mb-2">加载中...</h6>
                    <!-- Meta Info -->
                    <div class="share-meta-info mb-3">
                        <span id="shareExampleDate"></span>
                        <!-- Optional: <span id="shareExampleUsage"></span> -->
                    </div>
                    <!-- Prompt Section -->
                    <div class="share-prompt-section mb-3">
                        <label class="share-section-label">提示词</label>
                        <pre id="sharePromptDisplay" class="share-prompt-display">加载中...</pre>
                        <!-- Model Info Moved Inside -->
                         <div class="share-detail-item mt-2 pt-2 border-top border-secondary border-opacity-25"> <!-- Add spacing and separator -->
                            <label class="share-section-label">模型</label>
                            <span id="shareModelName">-</span>
                        </div>
                    </div>
                     <!-- Details Section (Now Empty or Removed) -->
                     <div class="share-details-section">
                         <!-- Removed model info -->
                    </div>
                    <!-- Action Buttons -->
                    <div class="share-actions mt-3">
                         <button type="button" class="btn btn-secondary" id="copyLinkBtn">复制分享链接</button>
                         <button type="button" class="btn btn-primary" id="copyPosterBtn">复制分享海报</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 