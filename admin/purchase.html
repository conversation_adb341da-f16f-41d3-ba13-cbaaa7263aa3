<!DOCTYPE html>
<html lang="zh" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>积分充值 - 提示词案例管理系统</title>
    <!-- Assuming libs and css are relative to the prompt-examples directory -->
    <link href="libs/css/bootstrap.min.css" rel="stylesheet">
    <link href="libs/css/bootstrap-icons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* Removed purple theme variables and related styles */
        :root {
            /* --custom-purple: #8a2be2; */ /* Removed */
            /* --custom-purple-hover: #7b1fa2; */ /* Removed */
            /* --bs-primary-rgb: 138, 43, 226; */ /* Removed */
            /* --bs-primary: var(--custom-purple); */ /* Removed */
        }

        .card {
            /* Keep card base styles if they differ from style.css, otherwise remove */
            /* Assuming style.css already handles card base styling well */
            /* border: 1px solid rgba(255, 255, 255, 0.15); */ /* Likely redundant */
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out, border-color 0.3s ease; /* Add border-color transition */
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3); /* Keep specific hover or let style.css handle */
        }
        .card-title {
            font-weight: bold;
            /* color: var(--custom-purple); */ /* Removed purple, inherit or set to white/light grey */
             color: #e0e0e0; /* Example: Use a light grey */
        }
        .price {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .points {
            font-size: 1.2rem;
            /* color: var(--bs-success); */ /* Keep success color for points */
            color: #8a2be2; /* Change points color back to purple */
            margin-bottom: 1rem;
        }

        /* New styles for selected card */
        .card.card-selected {
            border-color: rgba(255, 255, 255, 0.5); /* Example: Brighter border for selected */
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4); /* Slightly stronger shadow */
            transform: translateY(-3px); /* Keep slight lift */
        }

        /* Removed .btn-purchase styles, use standard buttons from style.css */
        /* .btn-purchase { ... } */
        /* .btn-purchase:hover { ... } */

        /* Removed styles overriding .btn-primary and .border-primary */
        /* .border-primary { ... } */
        /* .btn-primary { ... } */
        /* .btn-primary:hover { ... } */
        /* .badge.bg-primary { ... } */

    </style>
</head>
<body>
    <div class="grid-mask"></div>
    <div class="container mt-4 mb-5">
        <div class="page-header" style="justify-content: center;">
             <h1><i class="bi bi-gem me-2"></i>积分充值</h1> <!-- Removed inline style -->
        </div>

        <p class="text-center text-muted mb-4">选择一个套餐进行充值，积分可用于 AI 图片生成等服务 (当前生成一次消耗 2 积分)。</p>

        <div class="row justify-content-center g-4">
            <!-- 尝鲜包 -->
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="card text-center h-100 shadow-sm"> <!-- Use default card styling from style.css -->
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title mb-3">尝鲜包</h5>
                        <div class="price">￥10</div>
                        <div class="points">40 积分</div>
                        <!-- Use btn-outline-secondary for standard purchase -->
                        <button class="btn btn-outline-secondary mt-auto w-100" data-package="starter" data-amount="10" data-points="40">立即充值</button>
                    </div>
                </div>
            </div>

            <!-- 实用包 -->
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="card text-center h-100 shadow-sm"> <!-- Use default card styling -->
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title mb-3">实用包</h5>
                        <div class="price">￥30</div>
                        <div class="points">135 积分</div>
                        <!-- Use btn-outline-secondary -->
                        <button class="btn btn-outline-secondary mt-auto w-100" data-package="standard" data-amount="30" data-points="135">立即充值</button>
                    </div>
                </div>
            </div>

            <!-- 畅玩包 -->
            <div class="col-lg-3 col-md-4 col-sm-6">
                 <!-- Removed border-primary border-2, let style.css handle card borders -->
                <div class="card text-center h-100 shadow-sm">
                     <div class="position-absolute top-0 end-0 mt-2 me-2">
                         <!-- Changed bg-primary to bg-secondary for the badge -->
                         <span class="badge bg-secondary">推荐</span>
                     </div>
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title mb-3">畅玩包</h5>
                        <div class="price">￥50</div>
                        <div class="points">250 积分</div>
                        <!-- Use standard btn-primary from style.css -->
                        <button class="btn btn-primary mt-auto w-100" data-package="premium" data-amount="50" data-points="250">立即充值</button>
                    </div>
                </div>
            </div>

            <!-- 囤货包 -->
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="card text-center h-100 shadow-sm"> <!-- Use default card styling -->
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title mb-3">囤货包</h5>
                        <div class="price">￥100</div>
                        <div class="points">550 积分</div>
                        <!-- Use btn-outline-secondary -->
                        <button class="btn btn-outline-secondary mt-auto w-100" data-package="ultimate" data-amount="100" data-points="550">立即充值</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-5">
            <!-- Assuming index.html is in the same directory -->
            <a href="index.html" class="btn btn-outline-secondary"><i class="bi bi-arrow-left me-1"></i>返回主页</a>
        </div>
    </div>

    <!-- Assuming libs are relative to the prompt-examples directory -->
    <script src="libs/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Grid mask effect (copied from changelog)
            const gridMask = document.querySelector('.grid-mask');
            if (gridMask) {
                document.addEventListener('mousemove', (e) => {
                    gridMask.style.setProperty('--mouse-x', e.clientX + 'px');
                    gridMask.style.setProperty('--mouse-y', e.clientY + 'px');
                    gridMask.style.opacity = '1';
                });
                document.addEventListener('mouseleave', () => {
                    gridMask.style.opacity = '0';
                });
            }

            // Card selection logic
            const purchaseCards = document.querySelectorAll('.row.justify-content-center .card.h-100'); // Select cards directly
            purchaseCards.forEach(card => {
                card.addEventListener('click', function(event) {
                    // Prevent card selection when clicking the button inside
                    if (event.target.closest('button')) {
                        return;
                    }
                    // Remove selected class from all cards
                    purchaseCards.forEach(c => c.classList.remove('card-selected'));
                    // Add selected class to the clicked card
                    this.classList.add('card-selected');
                });
            });

            // Payment Modal Logic
            const paymentModalElement = document.getElementById('paymentModal');
            // Check if the modal element exists before initializing
            if (paymentModalElement) {
                const paymentModal = new bootstrap.Modal(paymentModalElement);
                const qrCodeImage = document.getElementById('qrCodeImage');
                const paymentAmountText = document.getElementById('paymentAmountText');
                const paymentPointsText = document.getElementById('paymentPointsText');

                // Button click handler to show modal
                document.querySelectorAll('.btn-outline-secondary, .btn-primary').forEach(button => {
                    button.addEventListener('click', function() {
                        const cardElement = this.closest('.card');
                        const packageName = cardElement.querySelector('.card-title').textContent;
                        const amount = this.dataset.amount;
                        const points = this.dataset.points;

                        // Update modal content
                        if (qrCodeImage) qrCodeImage.src = `images/${amount}.jpg`; // Construct image path
                        if (paymentAmountText) paymentAmountText.textContent = amount; // Update amount display
                        if (paymentPointsText) paymentPointsText.textContent = points; // Update points display
                        if (paymentModalElement) paymentModalElement.querySelector('.modal-title').textContent = `扫码支付 - ${packageName}`; // Update title

                        // Show the modal
                        paymentModal.show();
                    });
                });
            } else {
                console.error('Payment modal element (#paymentModal) not found.');
            }
        });
    </script>

    <!-- Payment QR Code Modal -->
    <div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentModalLabel">扫码支付</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <p class="lead">请使用微信或支付宝扫描下方二维码支付</p>
                    <h3 class="mb-3"><strong id="paymentAmountText">--</strong> 元</h3>
                    <img id="qrCodeImage" src="" alt="收款二维码" class="img-fluid mb-3" style="max-width: 250px; border: 1px solid #ccc;">
                    <p class="text-muted small">
                        支付成功后可获得 <strong id="paymentPointsText">--</strong> 积分。<br>
                        <strong>重要：</strong>请在支付的时候 (<span class="text-warning">添加您的账号到备注中</span>) 以便为您充值积分。
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

</body>
</html> 