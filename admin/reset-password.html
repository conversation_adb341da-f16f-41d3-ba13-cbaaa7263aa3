<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置密码 - 提示词案例管理系统</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.2.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-icons/1.10.3/font/bootstrap-icons.min.css" rel="stylesheet">
    <style>
        :root {
            --gradient-bg: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
            --card-bg-color: rgba(20, 20, 20, 0.7);
            --blur-intensity: 15px;
        }

        body {
            min-height: 100vh;
            background-color: #0a0a0a !important;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        body::before {
            content: '';
            position: fixed;
            inset: 0;
            background-image:
                linear-gradient(rgba(255, 255, 255, 0.2) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.2) 1px, transparent 1px);
            background-size: 40px 40px;
            z-index: 0;
            opacity: 0.3;
            pointer-events: none;
        }

        .reset-container {
            width: 100%;
            max-width: 420px;
            padding: 30px;
            background: var(--card-bg-color);
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(var(--blur-intensity));
            -webkit-backdrop-filter: blur(var(--blur-intensity));
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        .reset-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .reset-header h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
            background: linear-gradient(to bottom, #e0e0e0 0%, #ffffff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 600;
        }

        .form-control {
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.15);
            color: #e9ecef;
            border-radius: 8px;
            padding: 0.6rem 0.8rem;
            margin-bottom: 15px;
        }

        .form-control:focus {
            background-color: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 255, 255, 0.3);
            color: #fff;
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
        }

        .btn {
            padding: 10px 24px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-primary {
            background: linear-gradient(45deg, #4a4a4a, #2a2a2a);
            border: 1px solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3), inset 0 1px 1px rgba(255, 255, 255, 0.05);
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #5a5a5a, #3a3a3a);
            border-color: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }

        .alert {
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-header">
            <h1>重置密码</h1>
        </div>

        <div class="alert alert-danger" id="errorAlert" role="alert"></div>
        <div class="alert alert-success" id="successAlert" role="alert"></div>

        <form id="resetPasswordForm">
            <div class="mb-3">
                <input type="password" class="form-control" id="newPassword" placeholder="输入新密码" required>
            </div>
            <div class="mb-3">
                <input type="password" class="form-control" id="confirmPassword" placeholder="确认新密码" required>
            </div>
            <button type="submit" class="btn btn-primary w-100" id="submitBtn">
                确认重置密码
            </button>
            <div class="text-center mt-3">
                <a href="login.html" style="color: #0d6efd; text-decoration: none;">返回登录</a>
            </div>
        </form>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.2.3/js/bootstrap.bundle.min.js"></script>
    <script>
        // 配置
        const API_URL = 'https://caca.yzycolour.top/api'; // 确保与 login.html 中的 API 地址一致
        // const API_URL = 'http://localhost:32009/api';

        // DOM 元素
        const resetPasswordForm = document.getElementById('resetPasswordForm');
        const newPasswordInput = document.getElementById('newPassword');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        const submitBtn = document.getElementById('submitBtn');
        const errorAlert = document.getElementById('errorAlert');
        const successAlert = document.getElementById('successAlert');

        let resetToken = '';

        // 页面加载时获取 token
        document.addEventListener('DOMContentLoaded', () => {
            const urlParams = new URLSearchParams(window.location.search);
            resetToken = urlParams.get('token');

            if (!resetToken) {
                showError('无效的重置链接，请重新从邮件中点击链接或返回登录页面请求新的链接。');
                submitBtn.disabled = true; // 禁用提交按钮
            } else {
                 console.log('获取到的 Token:', resetToken); // 调试用
            }
        });

        // 表单提交
        resetPasswordForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            hideAlerts();

            if (!resetToken) {
                showError('无法重置密码，缺少必要的令牌信息。');
                return;
            }

            const newPassword = newPasswordInput.value;
            const confirmPassword = confirmPasswordInput.value;

            if (!newPassword || !confirmPassword) {
                showError('请填写新密码和确认密码');
                return;
            }

            if (newPassword !== confirmPassword) {
                showError('两次输入的密码不一致');
                return;
            }

            // 添加密码强度校验 (可选，但建议)
            if (newPassword.length < 6) {
                 showError('密码长度不能少于6位');
                 return;
            }

            // 禁用提交按钮
            submitBtn.disabled = true;
            const originalBtnText = submitBtn.textContent;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';

            try {
                const response = await fetch(`${API_URL}/auth/reset-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        token: resetToken,
                        password: newPassword
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '密码重置失败，可能是链接已过期或无效，请重试。');
                }

                // 密码重置成功
                showSuccess('密码重置成功！正在跳转到登录页面...');
                resetPasswordForm.reset(); // 清空表单
                setTimeout(() => {
                    window.location.href = 'login.html'; // 跳转到登录页
                }, 2000); // 延迟跳转，让用户看到成功消息

            } catch (error) {
                showError(error.message);
            } finally {
                // 恢复提交按钮状态 (如果不需要跳转的话)
                 if (successAlert.style.display !== 'block') { // 仅在非成功跳转时恢复
                    submitBtn.disabled = false;
                    submitBtn.textContent = originalBtnText;
                 }
            }
        });

        // 显示错误消息
        function showError(message) {
            errorAlert.textContent = message;
            errorAlert.style.display = 'block';
            successAlert.style.display = 'none';
        }

        // 显示成功消息
        function showSuccess(message) {
            successAlert.textContent = message;
            successAlert.style.display = 'block';
            errorAlert.style.display = 'none';
        }

        // 隐藏所有提示
        function hideAlerts() {
            errorAlert.style.display = 'none';
            successAlert.style.display = 'none';
        }
    </script>
</body>
</html> 