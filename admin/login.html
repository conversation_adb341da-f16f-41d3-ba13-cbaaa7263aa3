<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 像素星云AI助手</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.2.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-icons/1.10.3/font/bootstrap-icons.min.css" rel="stylesheet">
    <style>
        :root {
            --gradient-bg: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
            --card-bg-color: rgba(20, 20, 20, 0.7);
            --blur-intensity: 15px;
        }

        body {
            min-height: 100vh;
            background-color: #0a0a0a !important;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        body::before {
            content: '';
            position: fixed;
            inset: 0; /* Restore inset */
            background-image:
                linear-gradient(rgba(255, 255, 255, 0.2) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.2) 1px, transparent 1px);
            background-size: 40px 40px;
            z-index: 0;
            opacity: 0.3; /* Restore original opacity */
            pointer-events: none;
            /* Remove animation from grid */
        }

        /* Remove the ::after rule as it's replaced by the div */
        /* body::after { ... } */

        /* Spotlight Styles for the new divs */
        .spotlight {
            content: ''; /* Content not needed for divs */
            position: fixed;
            top: 0;
            width: 60vmax; /* Adjust size as needed */
            height: 60vmax; /* Adjust size as needed */
            background: radial-gradient(circle at center, rgba(106, 13, 173, 0.15), transparent 70%); /* Slightly stronger purple light */
            z-index: 0; /* Behind login container, same level as grid */
            pointer-events: none;
            opacity: 0.7;
            animation: pulseLight 8s infinite alternate ease-in-out;
            /* Remove transform from base class */
        }

        .spotlight.top-left {
            left: 0;
            transform: translate(-40%, -40%); /* Position off-screen */
            animation-delay: -4s; /* Offset animation start */
        }

        .spotlight.top-right {
            right: 0;
            transform: translate(40%, -40%); /* Position off-screen */
             /* Inherits animation */
        }

        /* Keep the keyframes animation */
        @keyframes pulseLight {
            0% {
                opacity: 0.5;
                 /* Animation now only affects opacity and scale if needed, position is static per class */
                 transform: translate(-40%, -40%) scale(0.95); /* Example for top-left animation */
            }
            100% {
                opacity: 0.8;
                 transform: translate(-35%, -35%) scale(1); /* Example for top-left animation */
            }
        }
        /* Ensure keyframes animation applies correctly to both top-left and top-right */
        /* The position transform needs to be slightly adjusted in the keyframes if we want movement */
         @keyframes pulseLight {
            0% {
                opacity: 0.5;
                /* Scale can be animated */
                /* transform: scale(0.95); */ /* Apply scale universally if desired */
            }
            100% {
                opacity: 0.8;
                 /* transform: scale(1); */
            }
        }
        /* Simplify animation just for opacity pulsing */
         @keyframes pulseLight {
            from { opacity: 0.5; }
            to { opacity: 0.8; }
        }

        /* End Spotlight Effects */

        .login-container {
            width: 100%;
            max-width: 420px;
            padding: 30px;
            background: var(--card-bg-color);
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(var(--blur-intensity));
            -webkit-backdrop-filter: blur(var(--blur-intensity));
            /* Restore the original subtle border */
            border: 1px solid rgba(255, 255, 255, 0.1);
            /* Remove border-image properties */
            /* border-image-source: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)); */
            /* border-image-slice: 1; */
            position: relative; /* Needed for pseudo-element positioning */
            z-index: 1;
            overflow: hidden; /* Needed to clip the pseudo-element */
        }

        /* Add pseudo-element for gradient border effect */
        .login-container::before {
            content: '';
            position: absolute;
            inset: 0; /* Cover the entire container */
            border-radius: inherit; /* Inherit the parent's border-radius */
            padding: 1px; /* Create space for the border effect */
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
            -webkit-mask:
                linear-gradient(#fff 0 0) content-box, /* Keep the content area transparent */
                linear-gradient(#fff 0 0); /* Apply gradient to the border area */
            mask:
                linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor; /* Punch out the content area */
            mask-composite: exclude; /* Standard property */
            pointer-events: none; /* Allow interaction with the container */
            z-index: 0; /* Position behind content but above background potentially */
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
            background: linear-gradient(to bottom, #e0e0e0 0%, #ffffff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 600;
        }

        .form-control {
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.15);
            color: #e9ecef;
            border-radius: 8px;
            padding: 0.6rem 0.8rem;
            margin-bottom: 15px;
        }

        .form-control:focus {
            background-color: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 255, 255, 0.3);
            color: #fff;
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
        }

        .btn {
            padding: 10px 24px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-primary {
            background: linear-gradient(45deg, #4a4a4a, #2a2a2a);
            border: 1px solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3), inset 0 1px 1px rgba(255, 255, 255, 0.05);
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #5a5a5a, #3a3a3a);
            border-color: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }

        .alert {
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .form-switch {
            padding-left: 2.5em;
            margin-bottom: 15px;
        }

        .form-switch .form-check-input {
            width: 2em;
            margin-left: -2.5em;
            background-color: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .form-switch .form-check-input:checked {
            background-color: #6A0DAD;
            border-color: #6A0DAD;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba(255,255,255,1.0)'/%3e%3c/svg%3e");
        }

        .form-check-label {
            color: #adb5bd;
        }

        .toggle-form {
            color: #7B68EE;
            text-decoration: none;
            cursor: pointer;
        }

        .toggle-form:hover {
            text-decoration: underline;
            color: #A084E8;
        }

        @media (max-width: 576px) {
            .login-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Spotlight Elements -->
    <div class="spotlight top-left"></div>
    <div class="spotlight top-right"></div>
    <!-- End Spotlight Elements -->

    <div class="login-container">
        <div class="login-header">
            <h1>像素星云AI助手</h1>
            <p class="text-muted" id="formTitle">登录账号</p>
        </div>

        <div class="alert alert-danger" id="errorAlert" role="alert"></div>
        <div class="alert alert-success" id="successAlert" role="alert"></div>

        <form id="loginForm">
            <div class="mb-3">
                <input type="text" class="form-control" id="identifier" placeholder="邮箱或用户名" required>
            </div>
            <div class="mb-3 position-relative">
                <input type="password" class="form-control" id="password" placeholder="密码" required>
                <button type="button" class="btn btn-link position-absolute end-0 top-0 text-decoration-none" style="padding: 0.6rem 0.8rem; color: #adb5bd;" id="togglePassword">
                    <i class="bi bi-eye-slash"></i>
                </button>
            </div>
            <div class="mb-3 position-relative" id="confirmPasswordDiv" style="display: none;">
                <input type="password" class="form-control" id="confirmPassword" placeholder="确认密码" required>
                <button type="button" class="btn btn-link position-absolute end-0 top-0 text-decoration-none" style="padding: 0.6rem 0.8rem; color: #adb5bd;" id="toggleConfirmPassword">
                    <i class="bi bi-eye-slash"></i>
                </button>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="rememberMe">
                    <label class="form-check-label" for="rememberMe">记住我</label>
                </div>
                <a class="toggle-form small" id="toggleForgotPassword">忘记密码？</a>
            </div>
            <button type="submit" class="btn btn-primary w-100" id="submitBtn">
                登录
            </button>
            <div class="text-center mt-3">
                <span class="text-muted" id="toggleText">还没有账号？</span>
                <a class="toggle-form" id="toggleForm">立即注册</a>
            </div>
        </form>

        <form id="forgotPasswordForm" style="display: none;">
             <div class="mb-3">
                <input type="email" class="form-control" id="forgotEmail" placeholder="请输入注册邮箱" required>
            </div>
            <button type="submit" class="btn btn-primary w-100" id="forgotSubmitBtn">
                发送重置链接
            </button>
            <div class="text-center mt-3">
                <a class="toggle-form" id="backToLogin">返回登录</a>
            </div>
        </form>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.2.3/js/bootstrap.bundle.min.js"></script>
    <script>
        // 配置
        const API_URL = 'https://caca.yzycolour.top/api';
        // const API_URL = 'http://localhost:32009/api';

        // DOM 元素
        const loginForm = document.getElementById('loginForm');
        const forgotPasswordForm = document.getElementById('forgotPasswordForm');
        const formTitle = document.getElementById('formTitle');
        const identifierInput = document.getElementById('identifier');
        const passwordInput = document.getElementById('password');
        const confirmPasswordDiv = document.getElementById('confirmPasswordDiv');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        const togglePasswordBtn = document.getElementById('togglePassword');
        const toggleConfirmPasswordBtn = document.getElementById('toggleConfirmPassword');
        const rememberMeCheckbox = document.getElementById('rememberMe');
        const submitBtn = document.getElementById('submitBtn');
        const toggleFormBtn = document.getElementById('toggleForm');
        const toggleForgotPasswordBtn = document.getElementById('toggleForgotPassword');
        const backToLoginBtn = document.getElementById('backToLogin');
        const forgotEmailInput = document.getElementById('forgotEmail');
        const forgotSubmitBtn = document.getElementById('forgotSubmitBtn');
        const toggleText = document.getElementById('toggleText');
        const errorAlert = document.getElementById('errorAlert');
        const successAlert = document.getElementById('successAlert');

        let currentForm = 'login'; // 'login', 'register', 'forgot'

        // 密码可见性切换
        togglePasswordBtn.addEventListener('click', () => {
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                togglePasswordBtn.innerHTML = '<i class="bi bi-eye"></i>';
            } else {
                passwordInput.type = 'password';
                togglePasswordBtn.innerHTML = '<i class="bi bi-eye-slash"></i>';
            }
        });
        
        // 确认密码可见性切换
        toggleConfirmPasswordBtn.addEventListener('click', () => {
            if (confirmPasswordInput.type === 'password') {
                confirmPasswordInput.type = 'text';
                toggleConfirmPasswordBtn.innerHTML = '<i class="bi bi-eye"></i>';
            } else {
                confirmPasswordInput.type = 'password';
                toggleConfirmPasswordBtn.innerHTML = '<i class="bi bi-eye-slash"></i>';
            }
        });

        // 检查是否有保存的登录信息
        const savedIdentifier = localStorage.getItem('identifier');
        const savedRememberMe = localStorage.getItem('rememberMe') === 'true';
        if (savedIdentifier && savedRememberMe) {
            identifierInput.value = savedIdentifier;
            rememberMeCheckbox.checked = true;
        }

        // 检查是否有通过URL激活账户的token
        window.addEventListener('DOMContentLoaded', () => {
            // 从URL中获取激活token
            const params = new URLSearchParams(window.location.search);
            const activationToken = params.get('activation_token');
            
            if (activationToken) {
                // 显示激活中的状态
                hideAlerts();
                showInfo('正在验证激活链接，请稍候...');
                
                // 调用激活账户API
                activateAccount(activationToken);
            }
        });
        
        // 激活账户函数
        async function activateAccount(token) {
            try {
                const response = await fetch(`${API_URL}/auth/activate`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({token: token})
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || '激活失败，请重试或联系管理员。');
                }
                
                // 成功激活
                showSuccess('账户激活成功！现在您可以登录了。');
                
                // 确保处于登录模式
                currentForm = 'login';
                updateFormDisplay();
                
                // 如果有保存的邮箱，自动填入
                if (data.email) {
                    identifierInput.value = data.email;
                }
                
                // 移除URL中的参数，避免刷新页面重复激活
                window.history.replaceState({}, document.title, window.location.pathname);
            } catch (error) {
                console.error('账户激活错误:', error);
                showError(error.message);
            }
        }

        // 更新表单显示状态
        function updateFormDisplay() {
            hideAlerts();
            loginForm.style.display = (currentForm === 'login' || currentForm === 'register') ? 'block' : 'none';
            forgotPasswordForm.style.display = currentForm === 'forgot' ? 'block' : 'none';

            if (currentForm === 'login') {
                formTitle.textContent = '登录账号';
                submitBtn.textContent = '登录';
                toggleText.textContent = '还没有账号？ ';
                toggleFormBtn.textContent = '立即注册';
                // 确保登录时输入框是 text 类型
                identifierInput.type = 'text';
                identifierInput.placeholder = '邮箱或用户名';
                // 隐藏确认密码框
                confirmPasswordDiv.style.display = 'none';
                confirmPasswordInput.removeAttribute('required');
            } else if (currentForm === 'register') {
                formTitle.textContent = '注册账号';
                submitBtn.textContent = '注册';
                toggleText.textContent = '已有账号？ ';
                toggleFormBtn.textContent = '立即登录';
                // 注册时强制为 email 类型
                identifierInput.type = 'email';
                identifierInput.placeholder = '请输入注册邮箱';
                // 显示确认密码框
                confirmPasswordDiv.style.display = 'block';
                confirmPasswordInput.setAttribute('required', 'required');
            } else { // forgot
                formTitle.textContent = '找回密码';
            }
        }

        // 切换登录/注册表单
        toggleFormBtn.addEventListener('click', () => {
            currentForm = (currentForm === 'login' || currentForm === 'forgot') ? 'register' : 'login'; // 从忘记密码也切换到注册
            updateFormDisplay();
        });

        // 切换到忘记密码表单
        toggleForgotPasswordBtn.addEventListener('click', () => {
            currentForm = 'forgot';
            updateFormDisplay();
        });

        // 从忘记密码返回登录
        backToLoginBtn.addEventListener('click', () => {
            currentForm = 'login';
            updateFormDisplay();
        });


        // 登录/注册表单提交
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            hideAlerts();

            const identifierValue = identifierInput.value.trim();
            const password = passwordInput.value;

            if (!identifierValue || !password) {
                 showError(currentForm === 'register' ? '请填写邮箱和密码' : '请填写邮箱/用户名和密码');
                return;
            }
            
            // 检查注册时两次密码是否一致
            if (currentForm === 'register') {
                const confirmPassword = confirmPasswordInput.value;
                if (password !== confirmPassword) {
                    showError('两次输入的密码不一致，请重新输入');
                    return;
                }
                
                if (password.length < 6) {
                    showError('密码长度不能少于6位');
                    return;
                }
            }

            // 禁用提交按钮
            submitBtn.disabled = true;
            const originalBtnText = submitBtn.textContent;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';

            // 将 isLoginMode 的声明移到这里
            const isLoginMode = (currentForm === 'login');

            try {
                const endpoint = isLoginMode ? '/auth/login' : '/auth/register';
                let requestBody = {};

                if (isLoginMode) {
                    requestBody = { identifier: identifierValue, password }; // 登录时发送 identifier
                } else {
                    // 注册时，需要验证是否是合法的邮箱格式
                    if (!validateEmail(identifierValue)) {
                         showError('请输入有效的邮箱地址');
                         submitBtn.disabled = false; // 恢复按钮
                         submitBtn.textContent = originalBtnText;
                         return;
                    }
                    requestBody = { email: identifierValue, password }; // 注册时发送 email
                }

                const response = await fetch(`${API_URL}${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody) // 使用构建的请求体
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '操作失败');
                }

                // 保存登录信息 (仅登录成功时)
                if (isLoginMode) {
                    if (rememberMeCheckbox.checked) {
                        localStorage.setItem('identifier', identifierValue); // 保存 identifier
                        localStorage.setItem('rememberMe', 'true');
                    } else {
                        localStorage.removeItem('identifier');
                        localStorage.removeItem('rememberMe');
                    }
                    // 保存 token 和用户信息
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user));
                    
                    // 如果有激活提示消息，保存到 localStorage
                    if (data.activation_message) {
                        localStorage.setItem('activation_message', data.activation_message);
                    }
                    
                    // 登录成功，跳转到管理页面
                    showSuccess('登录成功，正在跳转...');
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);
                } else {
                    // 注册成功，提示用户需要激活
                    showSuccess(`注册成功！我们已向 ${identifierValue} 发送了一封激活邮件，请在24小时内点击邮件中的链接完成账户激活。未激活账号将无法获得每日免费积分。`);
                    setTimeout(() => {
                        currentForm = 'login';
                        updateFormDisplay();
                        loginForm.reset(); // 清空表单
                        identifierInput.value = identifierValue; // 保留邮箱地址以便登录
                    }, 2000); // 延长显示时间，让用户有时间阅读提示
                }
            } catch (error) {
                showError(error.message);
            } finally {
                // 现在这里的 isLoginMode 应该是可访问的了
                if (!isLoginMode || errorAlert.style.display === 'block') {
                   submitBtn.disabled = false;
                   submitBtn.textContent = originalBtnText;
                }
            }
        });

        // 忘记密码表单提交
        forgotPasswordForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            hideAlerts();

            const email = forgotEmailInput.value.trim();
            if (!email) {
                showError('请输入您的注册邮箱');
                return;
            }

            // 禁用提交按钮
            forgotSubmitBtn.disabled = true;
            const originalBtnText = forgotSubmitBtn.textContent;
            forgotSubmitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 发送中...';

            try {
                // --- 后端 API 调用占位符 ---
                console.log(`向 ${email} 发送密码重置请求`);
                const response = await fetch(`${API_URL}/auth/forgot-password`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email })
                });
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '请求失败，请稍后再试');
                }
                // --- 结束 API 调用 ---

                showSuccess('如果邮箱地址存在，密码重置链接已发送到您的邮箱，请查收。');
                forgotPasswordForm.reset(); // 清空表单

            } catch (error) {
                // 为了安全，即使邮箱不存在，也显示通用成功消息，防止探测账户是否存在
                // 但在开发阶段或明确需要时，可以显示具体错误
                // showError(error.message);
                 showSuccess('如果邮箱地址存在，密码重置链接已发送到您的邮箱，请查收。');
                 console.error("Forgot password error:", error);
            } finally {
                // 恢复提交按钮
                forgotSubmitBtn.disabled = false;
                forgotSubmitBtn.textContent = originalBtnText;
            }
        });


        // 显示错误消息
        function showError(message) {
            errorAlert.textContent = message;
            errorAlert.style.display = 'block';
            successAlert.style.display = 'none';
        }

        // 显示成功消息
        function showSuccess(message) {
            successAlert.textContent = message;
            successAlert.style.display = 'block';
            errorAlert.style.display = 'none';
        }

        // 显示提示信息（使用成功提示样式但区分内容）
        function showInfo(message) {
            successAlert.textContent = message;
            successAlert.style.display = 'block';
            successAlert.className = 'alert alert-info';
            errorAlert.style.display = 'none';
        }

        // 隐藏所有提示
        function hideAlerts() {
            errorAlert.style.display = 'none';
            successAlert.style.display = 'none';
            successAlert.className = 'alert alert-success'; // 重置为默认样式
        }

        // 邮箱格式验证函数
        function validateEmail(email) {
            const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            return re.test(String(email).toLowerCase());
        }

        // 初始化表单显示
        updateFormDisplay();
    </script>
</body>
</html> 