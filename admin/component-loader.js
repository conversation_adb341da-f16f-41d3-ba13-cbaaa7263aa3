document.addEventListener('DOMContentLoaded', () => {
    // 获取当前用户权限和配置
    let userRole = localStorage.getItem('userRole') || 'user';
    
    // 加载全局组件
    loadGlobalComponents();
    
    // 根据用户权限初始化导航
    initializeNavigation(userRole);
    
    // 为每个Tab注册组件加载事件
    registerTabComponentLoaders();
});

/**
 * 加载全局组件（如头部、侧边栏等）
 */
function loadGlobalComponents() {
    // 如果你有全局组件，这里可以加载它们
}

/**
 * 根据用户权限初始化导航
 */
function initializeNavigation(userRole) {
    // 根据权限显示或隐藏某些导航项
    const adminTabs = document.querySelectorAll('.admin-only-tab');
    adminTabs.forEach(tab => {
        if (userRole === 'admin') {
            tab.style.display = 'block';
        } else {
            tab.style.display = 'none';
        }
    });
}

/**
 * 为每个Tab注册组件加载事件
 */
function registerTabComponentLoaders() {
    // AI生成Tab的组件加载
    document.getElementById('ai-generate-tab').addEventListener('shown.bs.tab', () => {
        loadAiGenerateComponents();
    });
    
    // 其他Tab的组件加载...
}

/**
 * 加载AI生成Tab的组件
 */
async function loadAiGenerateComponents() {
    // 检查是否已经加载过
    if (document.querySelector('#ai-generate-params-container').children.length > 0) {
        return; // 已加载，不重复加载
    }
    
    try {
        // 从API加载组件配置并渲染
        const components = await loadAndRenderComponentsFromAPI('ai_generate', 'ai-generate-params-container');
        
        // 注册组件事件处理
        if (components) {
            document.addEventListener('ai_generate.model_selector.change', (e) => {
                console.log('模型选择已更改:', e.detail);
                handleModelChange(e.detail.value);
            });
            
            document.addEventListener('ai_generate.generate_button.click', () => {
                console.log('生成按钮点击');
                startGeneration();
            });
        }
        
    } catch (error) {
        console.error('加载AI生成组件失败:', error);
    }
}

/**
 * 处理模型切换逻辑
 */
function handleModelChange(modelValue) {
    // 这里可以放模型切换的逻辑，例如显示或隐藏特定参数
}

/**
 * 开始生成逻辑
 */
function startGeneration() {
    // 这里可以放生成逻辑
}