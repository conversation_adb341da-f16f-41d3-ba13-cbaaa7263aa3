/* Example Detail Modal Specific Styles */

/* --- Theme Base (Inspired by ai-chat.css) --- */
:root {
    --modal-bg: #202123; /* Deep gray, common dark theme bg */
    --modal-bg-backdrop: rgba(32, 33, 35, 0.85); /* Semi-transparent for backdrop filter */
    --modal-card-bg: rgba(42, 43, 46, 0.85); /* Slightly lighter for cards/sections, also semi-transparent */
    --modal-text-primary: #e0e0e0; /* Primary text color */
    --modal-text-secondary: #adb5bd; /* Secondary text (muted) */
    --modal-border-color: rgba(255, 255, 255, 0.1);
    --modal-input-bg: rgba(255, 255, 255, 0.05);
    
    /* Adjusted Button Colors for Black/White/Gray Theme */
    --modal-btn-outline-text: #c0c0c0;
    --modal-btn-outline-border: rgba(255, 255, 255, 0.2);
    --modal-btn-outline-hover-bg: rgba(255, 255, 255, 0.1);
    --modal-btn-outline-hover-text: #ffffff;

    --modal-btn-secondary-bg: #48494a; /* Darker gray for primary action, e.g., Generate Same */
    --modal-btn-secondary-border: #5a5b5c;
    --modal-btn-secondary-text: #e0e0e0;
    --modal-btn-secondary-hover-bg: #5a5b5c;

    --modal-btn-liked-bg: #333333; /* Liked button background */
    --modal-btn-liked-text: #00ff00; /* Example: Green check or brighter text for liked */
    --modal-btn-liked-icon: #00dd00;

    --modal-tag-bg: rgba(255, 255, 255, 0.1);
    --modal-tag-text: #d0d0d0;
}

/* Modal Dialog Size */
#exampleDetailModal .modal-dialog {
    max-width: 90%;
    height: 90vh;
    margin: 5vh auto;
}

#exampleDetailModal .modal-content {
    background-color: var(--modal-bg-backdrop);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: var(--modal-text-primary);
    border: 1px solid var(--modal-border-color);
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Modal Header */
#exampleDetailModal .modal-header {
    border-bottom: 1px solid var(--modal-border-color);
    background-color: rgba(0,0,0,0.1);
    padding: 0.75rem 1rem;
    flex-shrink: 0;
}

/* Modal Body - 主内容区 */
#exampleDetailModal .modal-body {
    padding: 1rem;
    flex: 1;
    overflow: hidden;
    min-height: 0;
}

/* 确保容器和行占满高度 */
#exampleDetailModal .modal-body .container-fluid,
#exampleDetailModal .modal-body .row {
    height: 100%;
}

/* 左侧栏 - 图片区域 */
#exampleDetailModal .modal-body .col-lg-7 {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
}

/* 右侧栏 - 详情区域 */
#exampleDetailModal .modal-body .col-lg-5 {
    padding: 0 8px 0 15px;
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
    max-height: 100%;
}

/* 强制所有右侧元素使用紧凑布局 */
#exampleDetailModal .modal-body .col-lg-5 > * {
    margin-bottom: 8px !important;
}

/* 重置flex布局元素 */
#exampleDetailModal .modal-body .col-lg-5 .d-flex {
    margin-bottom: 8px !important;
}

#exampleDetailModal hr {
    margin: 8px 0;
}

#detailTitle {
    margin-top: 0;
    margin-bottom: 8px;
}

/* 调整标签页容器高度 */
#detailTabs {
    margin-bottom: 0;
}

/* 确保标签内容容器正确显示 */
#detailTabsContent {
    display: block;
    margin-bottom: 0;
    position: relative; /* 添加定位上下文 */
}

/* 确保两个标签页面板占用相同的空间 */
#detailTabsContent .tab-pane {
    position: relative;
    padding: 10px;
    margin-bottom: 0;
    font-size: 0.9rem;
    background-color: var(--modal-card-bg);
    border: 1px solid var(--modal-border-color);
    border-top: none;
    color: var(--modal-text-primary);
}

/* 强制评论标签页与详情标签页共享相同的显示区域 */
#comments-tab-pane {
    width: 100%; 
    display: none;
}
#comments-tab-pane.show.active {
    display: block;
}

/* 强制详情标签页与评论标签页共享相同的显示区域 */
#details-tab-pane {
    width: 100%;
    display: none;
}
#details-tab-pane.show.active {
    display: block;
}

/* 确保评论区域容器正确显示 */
.comments-section {
    display: block;
    width: 100%;
}

/* 各个区块间距调整 */
#detailTabsContent h6 {
    margin-top: 16px;
    margin-bottom: 12px;
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--modal-text-primary);
}

#detailTabsContent h6:first-of-type {
    margin-top: 8px;
}

/* 确保预览文本区域不占用过多空间 */
#detailTabsContent .prompt-text-container {
    margin-bottom: 12px;
}

#detailTabsContent .prompt-text-container pre {
    max-height: 100px; /* 减小预览区高度 */
}

/* 详情页元素间距 */
#details-tab-pane .mb-2 {
    margin-bottom: 12px !important;
}

/* 调整提示词区域间距 */
.prompt-text-container {
    margin-bottom: 12px;
}

/* 调整标签区域间距 */
#detailTagsContainer {
    margin-bottom: 12px;
}

/* 调整复制按钮间距 */
#detailCopyPromptBtn {
    margin-top: 8px;
    margin-bottom: 16px;
}

/* 标签页面板内容区域间距 */
#detailTabsContent .tab-pane {
    display: none;
    padding: 16px;
    background-color: var(--modal-card-bg);
    border: none; /* 完全移除边框 */
    color: var(--modal-text-primary);
    border-radius: 0 0 4px 4px; /* 保留圆角 */
}

/* 覆盖Bootstrap的边框样式 */
#detailTabsContent .tab-pane.border {
    border: none !important; /* 完全移除边框 */
}

/* 覆盖Bootstrap的所有边框相关类 */
.border, .border-top, .border-end, .border-bottom, .border-start {
    border: none !important;
}

/* 调整创意详情区上间距 */
.detail-tab-container {
    display: flex;
    flex-direction: column;
    height: auto;
    margin-top: 16px;
    margin-bottom: 16px;
}

/* 调整面板组整体间距 */
#exampleDetailModal .modal-body .col-lg-5 > * {
    margin-bottom: 12px !important;
}

/* 调整评论区间距 */
#detailCommentsList {
    max-height: 150px;
    overflow-y: auto;
    margin-bottom: 12px;
}

#detailCommentForm textarea {
    margin-bottom: 10px;
    min-height: 60px;
}

/* 调整标签区域标签间距 */
#detailTagsContainer .tag {
    margin-right: 8px;
    margin-bottom: 8px;
    padding: 4px 8px;
}

/* 左侧图片容器 */
.col-lg-7 .mb-3.text-center {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    overflow: hidden;
}

/* 主图片尺寸 */
#detailMainImage {
    max-height: 70vh;
    max-width: 100%;
    object-fit: contain;
    border-radius: 0.25rem;
}

/* 恢复评论部分的字体大小 */
#detailCommentsList {
    max-height: 180px;
    overflow-y: auto;
    font-size: 0.85rem;
    margin-bottom: 10px;
}

/* 确保内容容器有足够的填充空间 */
#exampleDetailModal .modal-body {
    padding: 1rem;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 非常重要，确保flex布局下滚动正常工作 */
}

/* 确保右侧滚动 - 关键修复 */
#exampleDetailModal .modal-body .col-lg-5 {
    max-height: 100%;
    height: 100%;
    overflow-y: auto !important; /* 强制启用滚动 */
    overflow-x: hidden;
    padding-right: 10px;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.15) rgba(255, 255, 255, 0.03);
    padding-top: 0;
    padding-bottom: 0;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

/* 右侧滚动条样式 */
#exampleDetailModal .modal-body .col-lg-5::-webkit-scrollbar {
    width: 6px;
}

#exampleDetailModal .modal-body .col-lg-5::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.03);
}

#exampleDetailModal .modal-body .col-lg-5::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 3px;
}

#exampleDetailModal .modal-body .col-lg-5::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.25);
}

#exampleDetailModal .modal-header {
    border-bottom: 1px solid var(--modal-border-color);
    background-color: rgba(0,0,0,0.1); /* Optional: Slightly different header bg for depth */
}

#exampleDetailModal .modal-header .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%); /* Make close button white */
}

/* --- Right Column General --- */
.modal-dialog-xl .modal-content .col-lg-5 {
    display: flex;
    flex-direction: column;
    gap: 0.8rem; /* Add some general gap between direct children blocks */
}

/* --- User Info Block --- */
#exampleDetailModal .d-flex.align-items-center.justify-content-between.mb-3 {
    margin-bottom: 0.5rem !important;
}

#detailAuthorAvatar {
    width: 48px !important;
    height: 48px !important;
    border: 1px solid var(--modal-border-color);
}

#detailAuthorName {
    font-size: 1rem;
    font-weight: 600;
    color: var(--modal-text-primary);
    /* Ensure it aligns well if no sub-text */
    line-height: 1.2; 
}

#detailAuthor作品, #detailAuthor粉丝 {
    color: var(--modal-text-secondary);
    font-size: 0.8rem;
}

#detailFollowBtn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    /* Applied via JS now, but good default */
    background-color: transparent;
    color: var(--modal-btn-outline-text);
    border: 1px solid var(--modal-btn-outline-border);
}
#detailFollowBtn:hover {
    background-color: var(--modal-btn-outline-hover-bg);
}
#detailFollowBtn.btn-primary { /* For "已关注" state */
    background-color: var(--modal-btn-primary-bg);
    color: var(--modal-btn-primary-text);
    border-color: var(--modal-btn-primary-bg);
}

/* New Share Button Style in User Info */
#detailShareBtn {
    /* font-size: 0.8rem; */ /* Handled by btn-sm from HTML or adjust here */
    padding: 0.25rem 0.6rem; 
    background-color: transparent;
    color: var(--modal-btn-outline-text);
    border: 1px solid var(--modal-btn-outline-border);
}
#detailShareBtn:hover {
    background-color: var(--modal-btn-outline-hover-bg);
    color: var(--modal-text-primary);
}
#detailShareBtn i {
    font-size: 0.9em; /* Adjust icon size within button if needed */
}

/* --- Action Icons (Like, Share) --- */
.detail-actions {
    /* margin-bottom: 0.5rem !important; */ /* Controlled by parent gap now or adjust if needed */
     margin-top: 0.2rem; /* Add a bit of space above actions */
}
.detail-actions .btn-icon {
    padding: 0.375rem 0.75rem;
    color: var(--modal-text-secondary);
    border: 1px solid transparent; /* Or var(--modal-border-color) for subtle border */
}
.detail-actions .btn-icon:hover {
    color: var(--modal-text-primary);
    background-color: var(--modal-btn-outline-hover-bg);
}
.detail-actions .like-btn.liked i {
    color: var(--bs-danger); /* Keep liked red, or use a theme-specific liked color */
}
.detail-actions .like-count {
    font-size: 0.9em;
    color: var(--modal-text-secondary);
}

/* --- Separator --- */
#exampleDetailModal hr {
    border-top: 1px solid var(--modal-border-color);
}

/* --- Work Info --- */
#detailTitle {
    font-size: 1.25rem; /* Keep as is or slightly reduce if still too large */
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--modal-text-primary);
}

#detailWorkMeta {
    font-size: 0.85rem;
    color: var(--modal-text-secondary);
}

/* --- Tabs --- */
.nav-tabs {
    border-bottom-color: var(--modal-border-color);
    margin-top: 0.5rem;
    margin-bottom: 0;
}
.nav-tabs .nav-link {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    color: var(--modal-text-secondary);
    border-color: transparent transparent var(--modal-border-color) transparent;
}
.nav-tabs .nav-link:hover {
    border-color: transparent transparent var(--modal-border-color) transparent;
    color: var(--modal-text-primary);
    background-color: var(--modal-btn-outline-hover-bg);
}
.nav-tabs .nav-link.active {
    font-weight: 500;
    color: var(--modal-text-primary);
    /* background-color: var(--modal-card-bg); */ /* Active tab has card-like bg */
    background-color: transparent; /* With backdrop, active tab might look better transparent or slightly different */
    border-color: var(--modal-border-color) var(--modal-border-color) transparent var(--modal-border-color);
}

#detailTabsContent .tab-pane {
    font-size: 0.9rem;
    background-color: var(--modal-card-bg);
    padding: 1rem;
    border: 1px solid var(--modal-border-color);
    border-top: none;
    color: var(--modal-text-primary);
    overflow: visible;
    display: block; /* 使用block而非flex */
    height: auto; /* 自动高度 */
}

#detailTabsContent h6 {
    font-size: 0.95rem;
    font-weight: 600;
    margin-top: 1.2rem; /* Increased top margin for section titles */
    margin-bottom: 0.6rem; /* Increased bottom margin for section titles */
    color: var(--modal-text-primary);
}
#detailTabsContent h6:first-of-type {
    margin-top: 0.5rem; /* Less top margin for the very first title in tab content */
}

/* --- Badges (Model, Category) --- */
.badge-container div {
    margin-bottom: 0.6rem; /* Add space between Model and Category lines */
}
#detailModel, #detailCategory {
    background-color: var(--modal-tag-bg) !important; 
    color: var(--modal-tag-text) !important;
    padding: 0.3em 0.6em;
    font-size: 0.75rem; 
    border-radius: 4px;
    /* margin-bottom: 0.3rem; */ /* Handled by parent .badge-container div */
}

/* --- Prompt Area --- */
#detailTabsContent .prompt-text-container {
    margin-bottom: 0.8rem; /* Space below prompt text before copy button */
}
#detailTabsContent .prompt-text-container pre {
    max-height: 130px;
    overflow-y: auto;
    background-color: var(--modal-input-bg);
    padding: 10px;
    border-radius: 4px;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-size: 0.85rem;
    color: var(--modal-text-primary);
    border: 1px solid var(--modal-border-color);
}

#detailCopyPromptBtn {
    background-color: transparent;
    color: var(--modal-btn-outline-text);
    border: 1px solid var(--modal-btn-outline-border);
    font-size: 0.8rem;
    margin-bottom: 1rem; /* Increased space before next section (tags) */
}
#detailCopyPromptBtn:hover {
    background-color: var(--modal-btn-outline-hover-bg);
    color: var(--modal-btn-outline-hover-text);
}

/* --- Tags --- */
#detailTagsContainer {
    margin-bottom: 1rem; /* Space below tags (was before generate button, now it's end of this section) */
}
#detailTagsContainer .tag {
    display: inline-block;
    background-color: var(--modal-tag-bg);
    color: var(--modal-tag-text);
    padding: 0.2em 0.6em;
    margin-right: 5px;
    margin-bottom: 5px;
    border-radius: 10px; /* Or 4px like badges for consistency */
    font-size: 0.8rem;
}

/* --- Specific Action Buttons (Upscale, HD, Generate Same) --- */
#detailUpscaleBtn, #detailHD2KBtn {
    font-size: 0.85rem;
    background-color: transparent;
    border: 1px solid var(--modal-btn-outline-border);
    color: var(--modal-btn-outline-text);
}
#detailUpscaleBtn:hover, #detailHD2KBtn:hover {
    background-color: var(--modal-btn-outline-hover-bg);
}

#detailGenerateSameBtn {
    font-size: 0.95rem;
    padding: 0.5rem 1rem;
    background-color: var(--bs-success); 
    border-color: var(--bs-success);
    color: white;
    margin-top: 0.5rem; /* Add some space above the main button */
}
#detailGenerateSameBtn:hover {
    opacity: 0.9;
}

/* --- Comments Section --- */
#detailCommentsList {
    max-height: 180px;
    overflow-y: auto;
}
.comment-item {
    padding: 8px 0;
    border-bottom: 1px solid var(--modal-border-color);
    color: var(--modal-text-primary);
}
.comment-item:last-child {
    border-bottom: none;
}
.comment-author {
    font-weight: 600;
    font-size: 0.8rem;
    color: var(--modal-text-primary);
}
.comment-date {
    font-size: 0.75rem;
    color: var(--modal-text-secondary);
    margin-left: 8px;
}
.comment-text {
    margin-top: 4px;
    margin-bottom: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

#detailCommentForm textarea {
    font-size: 0.9rem;
    background-color: var(--modal-input-bg);
    color: var(--modal-text-primary);
    border: 1px solid var(--modal-border-color);
    border-radius: 4px;
}
#detailCommentForm textarea::placeholder {
    color: var(--modal-text-secondary);
}

#detailCommentForm button {
    font-size: 0.9rem;
    background-color: var(--modal-btn-primary-bg);
    border-color: var(--modal-btn-primary-bg);
    color: var(--modal-btn-primary-text);
}
#detailCommentForm button:hover {
    opacity: 0.9;
}

/* --- Image Placeholders (already dark, minor tune if needed) --- */
.example-detail-placeholder-main-image,
.example-detail-placeholder-secondary-image {
    /* background-color: var(--modal-card-bg); */ /* Consistent card bg */
    background-color: rgba(42, 43, 46, 0.7); /* Ensure placeholders are also semi-transparent if needed */
    color: var(--modal-text-secondary);
    border: 1px dashed var(--modal-border-color); /* Dashed for placeholder */
}

/* --- GPT-4O Edit Images Section (already fine for dark theme layout) --- */
#detailGpt4oEditImagesSection {
    margin-top: 16px;
    gap: 15px;
}
#detailGpt4oEditImagesSection > div {
    flex: 1;
}
#detailGpt4oEditImagesSection img,
#detailGpt4oEditImagesSection .example-detail-placeholder-secondary-image {
    max-height: 150px;
    object-fit: contain;
}

/* --- Scrollbar (from ai-chat.css, adapted) --- */
#detailTabsContent .tab-pane::-webkit-scrollbar, 
#detailCommentsList::-webkit-scrollbar, 
#detailTabsContent .prompt-text-container pre::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}
#detailTabsContent .tab-pane::-webkit-scrollbar-track, 
#detailCommentsList::-webkit-scrollbar-track, 
#detailTabsContent .prompt-text-container pre::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 4px;
}
#detailTabsContent .tab-pane::-webkit-scrollbar-thumb, 
#detailCommentsList::-webkit-scrollbar-thumb, 
#detailTabsContent .prompt-text-container pre::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}
#detailTabsContent .tab-pane::-webkit-scrollbar-thumb:hover, 
#detailCommentsList::-webkit-scrollbar-thumb:hover, 
#detailTabsContent .prompt-text-container pre::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.25);
}

#detailTabsContent .tab-pane, 
#detailCommentsList, 
#detailTabsContent .prompt-text-container pre {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.15) rgba(255, 255, 255, 0.03);
}

/* Fallback for general text color if not specifically set */
#exampleDetailModal ::placeholder {
    color: var(--modal-text-secondary);
}
#exampleDetailModal strong { /* e.g., "模型:", "分类:" */
    color: var(--modal-text-secondary);
}

/* 如果希望选项卡内容区域有固定高度和滚动条 */
/*
#detailTabsContent .tab-pane {
    max-height: 400px; // 根据需要调整
    overflow-y: auto;
}
*/

/* Make Bootstrap modal backdrop transparent for better blur effect on modal content */
.modal-backdrop {
    background-color: transparent !important; 
}

/* Source/Reference Image Size (if they are used) */
#detailSourceImage, #detailReferenceImage {
    max-height: 120px; /* Example: Reduced from 150px, adjust as needed */
    object-fit: contain;
    border-radius: 0.25rem;
}

/* --- Footer Buttons --- */
#detailLikeBtn {
    padding: 0.4rem 0.8rem;
    background-color: transparent;
    color: var(--modal-btn-outline-text);
    border: 1px solid var(--modal-btn-outline-border);
}
#detailLikeBtn:hover {
    background-color: var(--modal-btn-outline-hover-bg);
    color: var(--modal-btn-outline-hover-text);
}

#detailLikeBtn .like-count {
    font-size: 0.9em; 
    color: var(--modal-btn-outline-text); /* Match button text */
}
#detailLikeBtn:hover .like-count {
    color: var(--modal-btn-outline-hover-text);
}

#detailLikeBtn.liked {
    background-color: var(--modal-btn-liked-bg) !important;
    border-color: var(--modal-btn-liked-bg) !important; /* Or a slightly darker border */
    color: #e74c3c;
}
#detailLikeBtn.liked .like-count {
    color: #e74c3c;
}
#detailLikeBtn.liked i.bi-heart-fill {
    color: #e74c3c;
}
#detailLikeBtn i.bi-heart {
     color: var(--modal-btn-outline-text);
}
#detailLikeBtn:hover i.bi-heart {
     color: var(--modal-btn-outline-hover-text);
}

#detailGenerateSameBtn_footer { /* Updated ID */
    font-size: 0.95rem; 
    padding: 0.4rem 0.8rem; 
    background-color: var(--modal-btn-secondary-bg); 
    border: 1px solid var(--modal-btn-secondary-border);
    color: var(--modal-btn-secondary-text);
    /* margin-top: 0.5rem; */ /* Removed as it's in footer with other buttons */
}
#detailGenerateSameBtn_footer:hover {
    background-color: var(--modal-btn-secondary-hover-bg);
    opacity: 1; /* Ensure hover is not transparent if base was */
}

/* 确保在评论Tab之间切换时高度一致 */
#detailTabsContent {
    flex: 1;
    display: flex;
    flex-direction: column;
}

#detailTabsContent .tab-content {
    flex: 1;
}

/* 确保tab内容区域占满高度并正确滚动 */
#details-tab-pane, #comments-tab-pane {
    display: block;
    height: auto;
}

/* 修复评论区域布局 */
.comments-section {
    display: block;
    height: auto;
}

/* 恢复container和row样式 */
#exampleDetailModal .modal-body .container-fluid,
#exampleDetailModal .modal-body .row {
    height: 100%;
    flex: 1;
    min-height: 0; /* 确保flex嵌套中滚动正常 */
}

/* 恢复左侧栏样式 */
#exampleDetailModal .modal-body .col-lg-7 {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* 修复右侧滚动的最终优化 */
#exampleDetailModal .modal-body .col-lg-5 {
    max-height: 100%;
    height: 100%;
    overflow-y: auto !important; /* 强制启用滚动 */
    overflow-x: hidden;
    padding-right: 10px;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.15) rgba(255, 255, 255, 0.03);
    padding-top: 0;
    padding-bottom: 0;
}

/* 确保提示词区域不打断右侧内容的滚动 */
#detailTabsContent .prompt-text-container pre {
    max-height: 148px;
    overflow-y: auto;
    background-color: var(--modal-input-bg);
    padding: 10px;
    border-radius: 4px;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-size: 0.85rem;
    color: var(--modal-text-primary);
    border: 1px solid var(--modal-border-color);
}

/* 确保Tab内容区没有多余空间 */
#detailTabsContent {
    display: block;
}

/* Modal Footer */
#exampleDetailModal .modal-footer {
    border-top: 1px solid var(--modal-border-color);
    background-color: rgba(0,0,0,0.1);
    padding: 0.75rem 1rem;
    flex-shrink: 0;
}

/* 标签页容器样式 */
.detail-tab-container {
    display: flex;
    flex-direction: column;
    height: auto;
    margin-top: 10px;
}

/* 标签页样式 */
.nav-tabs {
    border-bottom-color: var(--modal-border-color);
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    color: var(--modal-text-secondary);
    border-color: transparent transparent var(--modal-border-color) transparent;
    cursor: pointer;
}

.nav-tabs .nav-link:hover {
    border-color: transparent transparent var(--modal-border-color) transparent;
    color: var(--modal-text-primary);
    background-color: var(--modal-btn-outline-hover-bg);
}

.nav-tabs .nav-link.active {
    font-weight: 500;
    color: var(--modal-text-primary);
    background-color: transparent;
    border-color: var(--modal-border-color) var(--modal-border-color) transparent var(--modal-border-color);
}

/* 标签内容样式 */
#detailTabsContent {
    flex: 1;
    display: block;
    margin-bottom: 10px;
}

/* 标签面板样式 */
#detailTabsContent .tab-pane {
    display: none;
    padding: 10px;
    background-color: var(--modal-card-bg);
    border: 1px solid var(--modal-border-color);
    border-top: none;
    color: var(--modal-text-primary);
}

#detailTabsContent .tab-pane.show.active {
    display: block;
}

/* 评论区样式 */
#detailCommentsList {
    max-height: 150px;
    overflow-y: auto;
    margin-bottom: 8px;
}

.comments-section {
    display: block;
    width: 100%;
}

#detailCommentForm textarea {
    margin-bottom: 5px;
    min-height: 40px;
}

/* 模型与分类容器样式 */
.model-container, .category-container {
    margin-bottom: 12px;
}

/* 模型和分类标签样式 */
#detailModel, #detailCategory {
    display: inline-block;
    padding: 4px 10px;
    font-size: 0.85rem;
    font-weight: normal;
    border-radius: 4px;
}

/* 模型标签样式 */
#detailModel {
    background-color: var(--modal-tag-bg) !important;
    color: var(--modal-tag-text) !important;
    border: none;
}

/* 分类标签样式 */
#detailCategory {
    background-color: rgba(13, 110, 253, 0.15) !important;
    color: #8bb9fe !important;
    border: none;
}

/* 详情信息网格布局 */
.detail-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin: 24px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 16px;
}

/* 详情信息项样式 */
.detail-info-item {
    display: flex;
    flex-direction: column;
}

/* 详情信息标签样式 */
.detail-info-label {
    color: var(--modal-text-secondary);
    font-size: 0.85rem;
    margin-bottom: 4px;
    opacity: 0.8;
}

/* 详情信息值样式 */
.detail-info-value {
    color: var(--modal-text-primary);
    font-size: 1rem;
    font-weight: 600;
}

/* 移除旧的模型和分类样式 */
.model-container, .category-container {
    display: none;
}

#detailModel, #detailCategory {
    background: none !important;
    border: none !important;
    padding: 0 !important;
    color: var(--modal-text-primary) !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    display: block !important;
}

/* 评论区样式 */
.comments-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--modal-text-primary);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 8px;
}

.comments-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 20px;
    padding-right: 5px;
}

/* 评论项样式 */
.comment-item {
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    margin-bottom: 10px;
}

.comment-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.comment-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.comment-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
    object-fit: cover;
}

.comment-author {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--modal-text-primary);
    margin-right: 8px;
}

.comment-date {
    font-size: 0.75rem;
    color: var(--modal-text-secondary);
}

.comment-text {
    font-size: 0.9rem;
    margin: 0;
    color: var(--modal-text-primary);
    white-space: pre-wrap;
    word-break: break-word;
}

/* 评论表单样式 */
.comment-form-container {
    background-color: rgba(255, 255, 255, 0.03);
    border-radius: 6px;
    padding: 15px;
}

#detailCommentText {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--modal-text-primary);
    border-radius: 4px;
    resize: none;
    font-size: 0.9rem;
    padding-right: 45px; /* 为字数计数腾出空间 */
}

.comment-text-count {
    position: absolute;
    bottom: 8px;
    right: 10px;
    color: var(--modal-text-secondary);
    font-size: 0.8rem;
}

#commentSubmitBtn {
    background-color: #007bff;
    border-color: #007bff;
}

#commentSubmitBtn:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

#commentSubmitBtn:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
}

#commentSubmitError {
    font-size: 0.8rem;
    padding: 8px;
}

/* 加载和错误状态 */
#detailCommentsLoading, #detailCommentsError {
    min-height: 150px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

#retryCommentsBtn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

.comment-actions {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}
.comment-edit-icon i,
.comment-save-icon i,
.comment-cancel-icon i {
    color: #aaa;
    font-size: 1.1em;
    cursor: pointer;
    transition: color 0.2s;
}
.comment-edit-icon i:hover,
.comment-save-icon i:hover {
    color: #666;
}
.comment-delete-icon i {
    color: #e74c3c;
    font-size: 1.1em;
    cursor: pointer;
    transition: color 0.2s;
}
.comment-delete-icon i:hover {
    color: #ff4d4f;
}
.comment-save-icon i {
    color: #4caf50;
}
.comment-save-icon i:hover {
    color: #388e3c;
}
.comment-cancel-icon i {
    color: #aaa;
}
.comment-cancel-icon i:hover {
    color: #888;
}

/* 多图切换区样式 */
.detail-images-gallery {
    width: 100%;
    margin-bottom: 1.5rem;
}
.detail-images-gallery .main-image-area {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 340px;
    /* max-height: 55vh; */
    overflow: hidden;
}
#detailGalleryMainImage {
    max-height: 50vh;
    max-width: 100%;
    object-fit: contain;
    border-radius: 0.25rem;
    box-shadow: 0 2px 12px rgba(0,0,0,0.10);
}
#detailGalleryMainImagePlaceholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: #888;
}
.detail-images-gallery .thumbnails {
    margin-top: 0.5rem;
    gap: 0.8rem;
}
.gallery-thumb {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    border-radius: 8px;
    padding: 2px 4px;
    border: 2px solid transparent;
    transition: border-color 0.2s, box-shadow 0.2s;
}
.gallery-thumb.active {
    border-color: #4caf50;
    box-shadow: 0 0 0 2px #4caf5040;
}
.gallery-thumb img {
    width: 48px;
    height: 48px;
    object-fit: contain;
    border-radius: 6px;
    border: 1.5px solid #333;
    background: #222;
}
.gallery-thumb div {
    font-size: 0.75em;
    color: #aaa;
    margin-top: 2px;
    text-align: center;
}

/* --- Responsiveness for Mobile --- */
@media (max-width: 991.98px) { /* Bootstrap LG breakpoint - for tablets and larger phones */
    #exampleDetailModal .modal-dialog {
        max-width: 95%;
        height: 95vh; /* Allow a bit more height coverage */
        margin: 2.5vh auto;
    }

    #exampleDetailModal .modal-body {
        /* Ensure the modal body itself can scroll if its content (the stacked row) overflows, 
           though ideally inner elements handle their own scrolling. 
           This acts as a fallback if the stacked layout itself becomes too tall for the viewport. */
        overflow-y: auto; /* Changed from hidden to auto for the body itself in mobile */
        display: flex; /* Already set, but ensure it's here */
        flex-direction: column; /* Already set */
    }

    /* Stack columns and adjust padding/height */
    #exampleDetailModal .modal-body .row {
        flex-direction: column;
        height: auto; /* Allow row to grow with content */
        min-height: 0; /* For flex context */
        display: flex; /* Make the row a flex container for its children */
        flex: 1; /* Allow the row to take up available space in modal-body */
    }

    #exampleDetailModal .modal-body .col-lg-7,
    #exampleDetailModal .modal-body .col-lg-5 {
        width: 100%;
        max-width: 100%;
        flex-basis: auto; /* Reset basis */
        padding-left: 0.5rem; /* Reduce padding */
        padding-right: 0.5rem;
        /* overflow-y: visible; REMOVED - let children handle their scroll */
    }

    #exampleDetailModal .modal-body .col-lg-7 { /* Image Area */
        margin-bottom: 1rem; 
        max-height: 50vh; 
        overflow-y: auto; 
        flex-shrink: 0; /* Prevent image area from shrinking if content below is large */
    }
    
    #exampleDetailModal .modal-body .col-lg-5 { /* Details Area */
        /* max-height: none; REMOVED - let flex control this */
        overflow-y: auto; 
        flex-grow: 1; /* Allow details area to take remaining space */
        min-height: 0; /* CRITICAL for allowing a flex item to scroll its own content */
        display: flex; /* To make its direct children (like tab container) also behave well */
        flex-direction: column; 
    }

    /* Ensure tab content within details area can also grow and scroll if needed */
    #exampleDetailModal .modal-body .col-lg-5 .detail-tab-container {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
    }
    #exampleDetailModal .modal-body .col-lg-5 #detailTabsContent {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
        overflow-y: auto; /* Allow tab content itself to scroll if very long */
    }
     #exampleDetailModal .modal-body .col-lg-5 #detailTabsContent .tab-pane {
        flex-grow: 1;
        overflow-y: auto; /* Ensure individual tab panes can scroll */
        min-height: 0; /* For scrolling within flex child */
     }


    #detailGalleryMainImage {
        max-height: 40vh; /* Adjust main image height in stacked view */
    }

    #detailGpt4oEditImagesSection img,
    #detailGpt4oEditImagesSection .example-detail-placeholder-secondary-image {
        max-height: 120px; /* Smaller secondary images on mobile */
    }

    #detailTabsContent .prompt-text-container pre {
        max-height: 100px; /* Shorter prompt preview on mobile */
    }

    #detailCommentsList {
        max-height: 200px; /* Adjust comment list height */
    }
}

@media (max-width: 575.98px) { /* Bootstrap SM breakpoint - for smaller phones */
    #exampleDetailModal .modal-dialog {
        max-width: 100%;
        height: 100%;
        margin: 0;
        border-radius: 0;
    }

    #exampleDetailModal .modal-content {
        border-radius: 0;
        height: 100%;
    }

    #exampleDetailModal .modal-header,
    #exampleDetailModal .modal-body,
    #exampleDetailModal .modal-footer {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    #detailTitle {
        font-size: 1.1rem;
    }

    .nav-tabs .nav-link {
        padding: 0.5rem 0.75rem; /* Smaller tab padding */
        font-size: 0.85rem;
    }

    #detailGalleryMainImage {
        max-height: 35vh; 
    }

    .gallery-thumb img {
        width: 40px;
        height: 40px;
    }
    .gallery-thumb div {
        font-size: 0.7em;
    }

    /* Further reduce padding for very small screens if necessary */
    #exampleDetailModal .modal-body .col-lg-5 {
        padding-left: 0.25rem;
        padding-right: 0.25rem;
    }
} 