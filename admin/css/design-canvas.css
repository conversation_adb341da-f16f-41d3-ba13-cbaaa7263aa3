/* 设计画布页面样式 */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

/* 工具按钮 */
.btn-tool {
    width: 36px;
    height: 36px;
    border-radius: 4px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border: none;
}

.btn-tool:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.btn-tool.active {
    background-color: rgba(13, 110, 253, 0.15);
    color: #0d6efd;
}

/* 画布容器 */
.canvas-container {
    background-color: #f5f5f5;
    overflow: hidden;
}

#designCanvas {
    border: 1px solid #ddd;
    background-color: white;
}

/* 聊天容器样式 */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 300px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    background-color: #f8f9fa;
}

.chat-input-container {
    display: flex;
    padding: 8px;
    border-top: 1px solid #dee2e6;
    background-color: #ffffff;
}

.chat-input-container textarea {
    resize: none;
    height: 40px;
    margin-right: 8px;
    flex-grow: 1;
}

/* 聊天消息样式 */
.chat-message {
    margin-bottom: 10px;
    max-width: 85%;
}

.user-message {
    margin-left: auto;
}

.ai-message {
    margin-right: auto;
}

.message-content {
    padding: 8px 12px;
    border-radius: 12px;
}

.user-message .message-content {
    background-color: #007bff;
    color: #ffffff;
    border-top-right-radius: 0;
}

.ai-message .message-content {
    background-color: #e9ecef;
    border-top-left-radius: 0;
}

/* 属性面板 */
#propertiesPanel {
    max-height: calc(100vh - 450px);
    overflow-y: auto;
}

/* 响应式调整 */
@media (max-width: 992px) {
    .sidebar {
        width: 250px !important;
    }
}

@media (max-width: 768px) {
    .tools-sidebar {
        width: 40px !important;
    }
    
    .sidebar {
        width: 200px !important;
    }
}
