/* Flux Kontext Pro 图片处理功能样式 */

/* 历史记录列表样式 */
.history-list-flex {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

/* 通用样式 */
.cursor-pointer {
    cursor: pointer;
}

/* 处理按钮样式 */
#startFluxProcessingBtn {
    position: relative;
    overflow: hidden;
    background-color: #343a40;
    border: none;
    color: white;
    transition: all 0.3s ease;
}

#startFluxProcessingBtn:hover {
    background-color: #212529;
}

/* 历史记录项样式 */
#fluxHistoryList .history-item {
    border-radius: 10px;
    padding: 12px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: calc(33.33% - 10px);
    position: relative;
    overflow: hidden;
}

#fluxHistoryList .history-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.2);
}

#fluxHistoryList .history-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 0;
    pointer-events: none;
}

#fluxHistoryList .history-item-content {
    margin-top: 10px;
    position: relative;
    z-index: 1;
}

#fluxHistoryList .history-item-title {
    font-size: 0.9rem;
    margin: 0 0 5px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #fff;
}

#fluxHistoryList .history-item-date {
    font-size: 0.75rem;
    color: #aaa;
    display: block;
}

#fluxHistoryList .history-images {
    position: relative;
    z-index: 1;
    border-radius: 6px;
    overflow: hidden;
    text-align: center;
    height: 180px;
    background-color: #212529;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

#fluxHistoryList .history-images img {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    display: block;
    transition: transform 0.3s ease;
}

#fluxHistoryList .history-images img:hover {
    transform: scale(1.03);
}

#fluxHistoryList .history-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
    opacity: 0;
    transition: opacity 0.3s ease;
}

#fluxHistoryList .history-item:hover .history-actions {
    opacity: 1;
}

#fluxHistoryList .history-actions button {
    padding: 4px 8px;
    font-size: 0.8rem;
    background-color: rgba(0, 0, 0, 0.5);
}

/* 响应式布局 */
@media (max-width: 1200px) {
    #fluxHistoryList .history-item {
        width: calc(50% - 8px);
    }
}

@media (max-width: 768px) {
    #fluxHistoryList .history-item {
        width: 100%;
    }
}

/* 按钮悬停效果 */
.btn-hover-glow:hover {
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

/* 加载动画样式 */
.loading-dots {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
}

.loading-dots span {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #6c757d;
    margin: 0 5px;
    animation: pulse 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes pulse {
    0%, 80%, 100% { transform: scale(0); opacity: 0.3; }
    40% { transform: scale(1); opacity: 1; }
}

/* 修复历史记录中的空白状态 */
#fluxHistoryList .text-muted {
    width: 100%;
    text-align: center;
    padding: 20px 0;
}

/* 自定义滚动条 */
#flux-kontext-pane ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

#flux-kontext-pane ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

#flux-kontext-pane ::-webkit-scrollbar-thumb {
    background: rgba(100, 100, 100, 0.5);
    border-radius: 4px;
}

#flux-kontext-pane ::-webkit-scrollbar-thumb:hover {
    background: rgba(100, 100, 100, 0.8);
}

/* 图片预览样式优化 */
#fluxResultImage {
    max-height: 300px;
    object-fit: contain;
}

/* 简化的扩图编辑器样式 */
#fluxExpandModal {
    padding: 0 !important;
}

#fluxExpandModal .modal-dialog {
    margin: 0;
    max-width: 100%;
    width: 100%;
    height: 100%;
}

#fluxExpandModal .modal-content {
    background-color: #1a1a1a;
    color: #fff;
    border-radius: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

#fluxExpandModal .modal-body {
    flex: 1;
    overflow: auto;
    padding: 15px;
}

#fluxExpandModal .modal-header,
#fluxExpandModal .modal-footer {
    background-color: #222;
    border-color: #333;
}

/* 优化缩放和居中样式 */
.image-editor-container {
    background-color: #111;
    border-radius: 8px;
    overflow: hidden;
    height: calc(100vh - 180px);
    position: relative;
}

.editor-viewport {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    padding: 50px; /* 确保有足够的边距 */
}

.zoomable-canvas {
    position: absolute;
    transform-origin: center center;
    transition: transform 0.2s ease;
    display: flex;
    justify-content: center;
    align-items: center;
}

#fluxExpandEditorContainer {
    position: relative;
    padding: 20px;
    background-color: transparent;
}

#fluxExpandImageWrapper {
    position: relative;
    display: inline-block;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

/* 确保图片大小适中 */
#fluxExpandEditorImg {
    display: block;
    max-width: 100%;
    max-height: 100%;
}

/* 空格键按下时的光标样式 */
.editor-viewport.space-down {
    cursor: grab;
}

/* 拖动时的光标样式 */
.editor-viewport.dragging {
    cursor: grabbing !important;
}

/* 控制点样式 - 即梦风格 */

/* 角落L形控制点 */
.corner-handle {
    position: absolute;
    width: 20px;
    height: 20px;
    pointer-events: auto;
    z-index: 100;
}

.corner-handle-h, .corner-handle-v {
    position: absolute;
    background-color: white;
}

.corner-handle-h {
    height: 2px;
    width: 20px;
}

.corner-handle-v {
    width: 2px;
    height: 20px;
}

/* 顶部左侧角落控制点 */
.top-left-handle {
    top: 0;
    left: 0;
    transform: translate(-10px, -10px);
    cursor: nwse-resize;
}

.top-left-handle .corner-handle-h {
    top: 0;
    left: 0;
}

.top-left-handle .corner-handle-v {
    top: 0;
    left: 0;
}

/* 顶部右侧角落控制点 */
.top-right-handle {
    top: 0;
    right: 0;
    transform: translate(10px, -10px);
    cursor: nesw-resize;
}

.top-right-handle .corner-handle-h {
    top: 0;
    right: 0;
}

.top-right-handle .corner-handle-v {
    top: 0;
    right: 0;
}

/* 底部左侧角落控制点 */
.bottom-left-handle {
    bottom: 0;
    left: 0;
    transform: translate(-10px, 10px);
    cursor: nesw-resize;
}

.bottom-left-handle .corner-handle-h {
    bottom: 0;
    left: 0;
}

.bottom-left-handle .corner-handle-v {
    bottom: 0;
    left: 0;
}

/* 底部右侧角落控制点 */
.bottom-right-handle {
    bottom: 0;
    right: 0;
    transform: translate(10px, 10px);
    cursor: nwse-resize;
}

.bottom-right-handle .corner-handle-h {
    bottom: 0;
    right: 0;
}

.bottom-right-handle .corner-handle-v {
    bottom: 0;
    right: 0;
}

/* 边缘控制点 */
.edge-handle {
    position: absolute;
    pointer-events: auto;
    z-index: 100;
}

.edge-handle-bar {
    position: absolute;
    background-color: white;
}

/* 顶部边缘控制点 */
.top-handle {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 10px;
    cursor: ns-resize;
}

.top-handle .edge-handle-bar {
    top: 0;
    left: 0;
    width: 30px;
    height: 2px;
}

/* 底部边缘控制点 */
.bottom-handle {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 10px;
    cursor: ns-resize;
}

.bottom-handle .edge-handle-bar {
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
}

/* 左侧边缘控制点 */
.left-handle {
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 10px;
    height: 30px;
    cursor: ew-resize;
}

.left-handle .edge-handle-bar {
    left: 0;
    top: 0;
    width: 2px;
    height: 30px;
}

/* 右侧边缘控制点 */
.right-handle {
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 10px;
    height: 30px;
    cursor: ew-resize;
}

.right-handle .edge-handle-bar {
    right: 0;
    top: 0;
    width: 2px;
    height: 30px;
}

/* 控制点悬停效果 */
.corner-handle:hover .corner-handle-h,
.corner-handle:hover .corner-handle-v,
.edge-handle:hover .edge-handle-bar {
    background-color: #007bff;
}

/* 扩展区域样式 */
.expand-area {
    position: absolute;
    background-color: rgba(0, 123, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

/* 确保扩展区域边框在暗色背景上可见 */
.top-expand-area {
    left: 0;
    top: 0;
    width: 100%;
    height: 0;
    transform: translateY(-100%);
}

.bottom-expand-area {
    left: 0;
    bottom: 0;
    width: 100%;
    height: 0;
    transform: translateY(100%);
}

.left-expand-area {
    top: 0;
    left: 0;
    height: 100%;
    width: 0;
    transform: translateX(-100%);
}

.right-expand-area {
    top: 0;
    right: 0;
    height: 100%;
    width: 0;
    transform: translateX(100%);
}

/* 确保输入框在暗色背景下可见 */
#fluxExpandModal input,
#fluxExpandModal select,
#fluxExpandModal textarea {
    background-color: #333;
    color: white;
    border-color: #444;
}

#fluxExpandModal .input-group-text {
    background-color: #444;
    color: white;
    border-color: #444;
}

/* 比例选择按钮样式 - 修改选中和悬浮状态 */
.ratio-selector {
    margin-bottom: 15px;
}

.ratio-btn {
    min-width: 60px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: transparent;
    color: white;
    transition: all 0.2s ease;
}

/* 选中状态 - 白色背景，黑色文字 */
.ratio-btn.active {
    background-color: white;
    border-color: white;
    color: black;
    font-weight: 500;
}

/* 悬浮状态 - 浅白色背景，黑色文字 */
.ratio-btn:hover:not(.active) {
    background-color: rgba(255, 255, 255, 0.7);
    color: black;
}

/* 确保按钮在暗色背景上更加醒目 */
.ratio-btn:focus {
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.25);
}

/* 按钮之间的间距 */
.ratio-selector .gap-2 {
    gap: 0.5rem !important;
}

/* 圆角样式，使按钮更美观 */
.ratio-btn {
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
}

/* 增加特异性，确保样式覆盖Bootstrap默认样式 */
#fluxExpandModal .ratio-btn.active {
    background-color: white !important;
    border-color: white !important;
    color: black !important;
    font-weight: 500;
}

#fluxExpandModal .ratio-btn:hover:not(.active) {
    background-color: rgba(255, 255, 255, 0.7) !important;
    color: black !important;
}

/* 确保按钮在暗色模式下仍然可见 */
#fluxExpandModal .ratio-btn {
    min-width: 60px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: transparent;
    color: white;
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
}

/* Flux案例展示样式 */
#fluxExamplesModal .bi-arrow-right {
    color: #6c757d;
}

#fluxExamplesModal .card {
    transition: transform 0.2s;
    border: 1px solid rgba(0,0,0,.125);
    display: flex;
    flex-direction: column;
}

#fluxExamplesModal .card:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 8px rgba(0,0,0,.1);
}

#fluxExamplesModal .card-body {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-grow: 1;
}

#fluxExamplesModal .card-img-fixed-height {
    height: 180px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
}

#fluxExamplesModal .card-img-fixed-height img {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
}

#fluxExamplesModal .small.fst-italic {
    color: #495057;
    max-width: 150px;
    margin: 0 auto;
}

/* 查看案例按钮样式 */
#showFluxExamplesBtn {
    background-color: white;
    border: 1px solid #dee2e6;
    transition: background-color 0.2s ease;
}

#showFluxExamplesBtn:hover {
    background-color: rgba(255, 255, 255, 0.7); /* 降低白色透明度 */
}

/* 如果您希望按钮和"开始处理"按钮保持相同高度 */
#showFluxExamplesBtn {
    display: flex;
    align-items: center;
    justify-content: center;
} 