/* 历史记录项的基础样式 */
.history-item {
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    margin-bottom: 16px;
    padding: 16px;
    transition: opacity 0.3s ease, transform 0.3s ease, max-height 0.5s ease;
}

/* 删除按钮样式 */
.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}

.btn-outline-danger:hover {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

/* 确认删除对话框样式 */
#deleteHistoryConfirmModal .modal-content {
    border-radius: 8px;
}

#deleteHistoryConfirmModal .modal-header {
    border-bottom: 1px solid #555555;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

#deleteHistoryConfirmModal .modal-footer {
    border-top: 1px solid #555555;
}

/* 新生成的历史记录项动画 */
.newly-generated-item {
    animation: fadeInUp 0.5s ease-out forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 删除动画 */
.history-item.deleting {
    opacity: 0;
    transform: translateY(-20px);
    max-height: 0;
    overflow: hidden;
    margin: 0;
    padding-top: 0;
    padding-bottom: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .history-item-actions {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .history-item-actions .btn {
        margin-bottom: 8px;
        width: 100%;
    }
}

/* Add styles for Midjourney buttons container */
.mj-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

/* Style for individual MJ action buttons */
.mj-action-button {
    padding: 4px 10px;
    font-size: 0.85rem;
    border-radius: 4px;
    min-width: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.mj-action-button:hover {
    background-color: rgba(13, 110, 253, 0.1);
    transform: translateY(-1px);
}

/* MJ grid image */
.mj-grid-image {
    max-width: 100%;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.mj-grid-image:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* AI提示词助手按钮样式（白底黑字，黑色边框，圆角，hover高亮） */
.ai-helper-btn {
    position: absolute; /* 保持绝对定位 */
    bottom: 12px; /* 距离文本域底部固定距离 */
    right: 12px; /* 距离文本域右侧固定距离 */
    background: #222; /* 白色背景 */
    color: #fff; /* 黑色文字 */
    border: 1px solid #333; /* 1px 黑色边框 */
    border-radius: 8px;
    padding: 6px 12px; /* 调整padding更紧凑 */
    font-size: 14px; /* 稍小字体 */
    font-weight: 500;
    box-shadow: none;
    outline: none;
    transition: background 0.2s, color 0.2s, border-color 0.2s;
    display: inline-flex;
    align-items: center;
    z-index: 10; /* 确保按钮在其他元素之上 */
}
.ai-helper-btn:hover, .ai-helper-btn:focus {
    background: #333; /* hover时浅灰色背景 */
    color: #fff; /* 文字颜色不变 */
    border-color: #666; /* 边框颜色可略微加深 */
}
.ai-helper-btn i {
    margin-right: 6px;
}

/* --- 新增：AI 图片生成结果区域样式 --- */
.ai-generation-result-container {
    /* 可以添加一些外边距或内边距 */
}

.prompt-display-area {
    background-color: rgba(255, 255, 255, 0.03);
    padding: 10px 15px;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.08);
    font-size: 0.9rem;
}

.image-grid {
    display: grid;
    /* grid-template-columns: repeat(2, 1fr); */ /* 创建一个 2x2 网格 */
    grid-template-columns: repeat(4, 1fr); /* 改为 4 列 */
    gap: 10px; /* 可以适当减小间距 */
}

.generated-image-thumbnail {
    width: 100%;
    height: auto; /* 保持图片比例 */
    /* aspect-ratio: 1 / 1; */ /* 移除强制1:1比例 */
    /* object-fit: cover; */ /* 移除裁剪 */
    border-radius: 6px; /* 可以稍微减小圆角 */
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.generated-image-thumbnail:hover {
    transform: scale(1.03);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}
/* --- 结束：AI 图片生成结果区域样式 --- */

/* AI Generation History Styles */
#aiHistoryList .history-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1.5rem; /* 增大条目间距 */
    background-color: var(--card-bg-color);
    /* display: flex; */ /* 移除 flex */
    /* flex-direction: column; */ /* 移除 */
    /* gap: 0.8rem; */ /* 移除 */
}

/* 新增：历史记录项头部 */
#aiHistoryList .history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem; /* 头部和提示词间距 */
}

#aiHistoryList .history-item-title {
    font-weight: 600;
    color: #e0e0e0;
    font-size: 0.95rem;
    display: flex; /* 让图标和文字对齐 */
    align-items: center;
    gap: 0.4rem; /* 图标和文字间距 */
}

#aiHistoryList .history-item-date {
    font-size: 0.8rem;
    color: #888; /* 日期颜色更暗 */
}

/* 修改：提示词样式 */
#aiHistoryList .history-prompt {
    font-weight: normal; /* 取消加粗 */
    margin-bottom: 0.75rem; /* 提示词和图片间距 */
    word-break: break-word;
    color: #ccc; /* 提示词颜色稍暗 */
    line-height: 1.5;
}

/* 新增：提示词后面的小标签样式 */
#aiHistoryList .history-prompt .badge {
    font-size: 0.7em; /* 标签字体更小 */
    padding: 0.2em 0.5em;
    margin-left: 0.5rem; /* 与提示词或其他标签的间距 */
    vertical-align: middle; /* 垂直居中 */
    background-color: rgba(255, 255, 255, 0.15) !important; /* 使用浅灰背景，覆盖默认 */
    color: #adb5bd !important; /* 标签文字颜色 */
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: normal;
}

/* 图片网格 */
#aiHistoryList .history-images {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
    margin-bottom: 0.8rem;
}

#aiHistoryList .history-images img {
    width: 100%;
    height: auto;
    aspect-ratio: 1 / 1;
    object-fit: cover;
    border-radius: 6px;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform 0.2s ease;
}

#aiHistoryList .history-images img:hover {
    transform: scale(1.05);
}

/* 移除旧的 info 和 details 样式 */
/* #aiHistoryList .history-info { ... } */
/* #aiHistoryList .history-details { ... } */

/* 操作按钮 */
#aiHistoryList .history-actions {
    margin-top: 0.5rem; /* 调整与图片间距 */
}

#aiHistoryList .history-actions button {
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
}

/* 分页 */
#aiHistoryPagination button {
    margin: 0 0.25rem;
}

/* Image Detail Modal Styles */
#imageDetailModal .modal-body img {
    background-color: rgba(0,0,0,0.2); /* Add a subtle background in case of transparency */
}

#modalPromptDisplay {
    max-height: 100px; /* Limit prompt display height */
    overflow-y: auto; /* Allow scrolling for long prompts */
    white-space: pre-wrap; /* Preserve line breaks */
    word-break: break-word;
    color: #ccc;
}

/* AI 生成历史记录项样式 */
.ai-history-item {
    border: 1px solid var(--bs-border-color-translucent);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: rgba(var(--bs-body-bg-rgb), 0.5);
    display: flex;
    flex-direction: column;
}

.ai-history-item .image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); /* 调整minmax适应 */
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.ai-history-item .image-container {
    position: relative;
    overflow: hidden;
    border-radius: 0.375rem; /* Slightly smaller radius for grid items */
    cursor: pointer;
    aspect-ratio: 1 / 1; /* 保持图片容器为方形 */
}

.ai-history-item .image-container img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover; /* 裁剪以填充容器 */
    transition: transform 0.3s ease;
}

.ai-history-item .image-container:hover img {
    transform: scale(1.05);
}

.ai-history-item .history-prompt {
    font-size: 0.85rem;
    color: var(--bs-secondary-color);
    margin-bottom: 0.5rem;
    white-space: pre-wrap; /* Allow wrapping */
    word-break: break-word; /* Break long words */
    max-height: 6em; /* Limit initial height */
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4; /* Show max 4 lines */
    -webkit-box-orient: vertical;
}

.ai-history-item .history-meta {
    font-size: 0.75rem;
    color: var(--bs-tertiary-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-history-item .history-actions button {
    font-size: 0.75rem;
    padding: 0.1rem 0.4rem; /* Smaller padding */
}

/* 图片详情模态框 (#imageDetailModal) 样式 */
#imageDetailModal .modal-xl {
    --bs-modal-width: 80%; /* 让模态框更宽 */
    max-width: 1400px; /* 限制最大宽度 */
}

#imageDetailModal .modal-body {
    padding: 0; /* 移除 modal-body 的默认 padding */
}

#imageDetailModal .image-detail-left {
    display: flex;
    align-items: center; /* 垂直居中图片 */
    justify-content: center; /* 水平居中图片 */
    background-color: #1e1e20; /* 给图片区域一个深色背景 */
    padding: 1rem;
    /* 确保图片侧边栏在小屏幕上也合理 */
    max-height: 80vh;
    overflow-y: auto; /* 如果图片太高允许滚动 */
    border-right: 1px solid #444; /* 新增：添加右边框作为分割线 */
}

#imageDetailModal #modalImageView {
    max-width: 100%;
    max-height: 75vh; /* 限制图片最大高度 */
    object-fit: contain; /* 保持图片比例 */
    border-radius: 0.5rem; /* 图片圆角 */
}

#imageDetailModal .image-detail-right {
    padding: 1.5rem; /* 给右侧内容添加内边距 */
    display: flex;
    flex-direction: column;
    max-height: 80vh; /* 限制最大高度 */
    overflow-y: auto; /* 如果内容太多允许滚动 */
    background-color: #2a2a2e; /* 新增：设置右侧背景色，与分享弹窗一致 */
}

/* 新增：单个参数项（标签左，值右） */
.parameter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.image-detail-label {
    font-weight: 600;
    color: var(--bs-secondary-color);
    /* margin-bottom: 0.25rem; */ /* 移除 bottom margin，因为 flex 会处理对齐 */
    /* display: block; */ /* 不再需要 block */
    font-size: 0.8rem; /* 标签字号稍小 */
    text-transform: uppercase; /* 标签大写 */
    margin-right: 1rem; /* 标签和值之间的间距 */
}

.image-detail-value {
    font-size: 0.95rem;
    color: #e0e0e0; /* 新增：使用一个更亮的颜色 */
    text-align: right; /* 值靠右对齐 */
}

#modalPromptDisplay.image-detail-prompt {
    font-family: var(--bs-font-monospace); /* 使用等宽字体显示提示词 */
    font-size: 0.9rem;
    white-space: pre-wrap; /* 保留换行和空格 */
    word-break: break-word;
    background-color: rgba(var(--bs-tertiary-bg-rgb), 0.5); /* 浅色背景 */
    padding: 0.75rem;
    border-radius: 0.375rem;
    max-height: 30vh; /* 限制提示词区域最大高度 */
    overflow-y: auto; /* 内容过多时滚动 */
    border: 1px solid var(--bs-border-color-translucent);
}

/* 调整模态框 footer */
#imageDetailModal .modal-footer {
    border-top: 1px solid var(--bs-border-color-translucent);
}

/* 新增：右侧信息卡片样式 */
.detail-info-card {
    background-color: rgba(0, 0, 0, 0.2); /* 类似分享弹窗内部卡片背景 */
    border: 1px solid #444; /* 类似分享弹窗内部卡片边框 */
    border-radius: 6px; /* 内部卡片圆角 */
    padding: 1rem; /* 内部卡片内边距 */
    margin-bottom: 1.5rem; /* 与下方隐藏输入框或其他元素的间距 */
}

/* 新增：信息卡片内部的分割线样式 */
.detail-separator {
    border-top: 1px solid rgba(255, 255, 255, 0.1); /* 更细更浅的颜色 */
    margin-top: 1rem; /* 分割线与上方提示词的间距 */
    margin-bottom: 1rem; /* 分割线与下方参数行的间距 */
    opacity: 0.5; /* 让线条更不明显 */
}

/* 新增：参数行样式 (可选，如果需要微调间距) */
.detail-parameter-row .col-4 {
    /* 可以添加额外的样式，比如 text-align: center; 如果需要 */
}

/* --- AI Generation Loading Animation --- */
#aiGenerationLoading {
    display: none; /* Hidden by default */
    /* Mimic history item structure for size/spacing */
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    background-color: var(--card-bg-color);
    min-height: 200px; /* Give it some height */
    position: relative;
    overflow: hidden; /* Important for shimmer */
    display: flex; /* Use flex to center content */
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

#aiGenerationLoading::before { /* Shimmer effect */
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.08),
        transparent
    );
    animation: shimmer 1.8s infinite linear;
    z-index: 1;
}

#aiGenerationLoading p {
    color: #adb5bd;
    font-weight: 500;
    margin-top: 10px;
    z-index: 2; /* Ensure text is above shimmer */
}

/* Optional: Add some simple animated dots */
.loading-dots span {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    margin: 0 3px;
    animation: pulse 1.4s infinite ease-in-out both;
    z-index: 2;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes shimmer {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; } /* Hold at the end before looping */
}

@keyframes pulse {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1.0); }
}
/* --- End AI Generation Loading --- */

/* --- AI Generation & Upscale Loading Animation --- */ /* <-- Renamed Section */
/* General Loading Card Style */
.loading-shimmer-card {
    display: none; /* Hidden by default */
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    background-color: var(--card-bg-color);
    min-height: 200px; /* Give it some height */
    position: relative;
    overflow: hidden; /* Important for shimmer */
    display: flex; /* Use flex to center content */
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

/* Apply base style to specific loading elements */
#aiGenerationLoading,
#upscaleResultLoading { /* <-- Added Upscale ID */
    /* Inherits from .loading-shimmer-card */
}


.loading-shimmer-card::before { /* Shimmer effect */
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.08),
        transparent
    );
    animation: shimmer 1.8s infinite linear;
    z-index: 1;
}

/* 新增：为 loading-shimmer-card 添加内部扫描线 */
.loading-shimmer-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px; /* 扫描线的高度/粗细 */
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(150, 180, 255, 0.6) 40%,
        rgba(180, 220, 255, 0.8) 50%, /* 扫描线核心颜色，亮蓝色 */
        rgba(150, 180, 255, 0.6) 60%,
        transparent 100%
    );
    filter: blur(1px);
    animation: innerScan 2.5s infinite ease-in-out;
    z-index: 2; /* 确保在 shimmer 之上，但在文字和点之下 */
}

@keyframes innerScan {
    0% {
        top: 10%;
        opacity: 0.7;
    }
    50% {
        top: 90%;
        opacity: 0.3;
    }
    100% {
        top: 10%;
        opacity: 0.7;
    }
}
/* 结束：为 loading-shimmer-card 添加内部扫描线 */

.loading-shimmer-card p { /* Style for text within the loading card */
    color: #adb5bd;
    font-weight: 500;
    margin-top: 10px;
    z-index: 3; /* 提高层级，确保在扫描线之上 */
}

/* Optional: Add some simple animated dots */
.loading-dots span {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    margin: 0 3px;
    animation: pulse 1.4s infinite ease-in-out both;
    z-index: 3; /* 提高层级，确保在扫描线之上 */
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes shimmer {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; } /* Hold at the end before looping */
}

@keyframes pulse {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1.0); }
}

/* --- End Loading Animation --- */

/* --- 新增：更酷的卡片生成动画 --- */
@keyframes coolGenerateAppear {
    0% {
        opacity: 0;
        transform: translateY(15px) scale(0.97); /* 从下方轻微缩小开始 */
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1); /* 恢复正常大小和位置 */
    }
}

@keyframes cardScanLineOnce {
    0% {
        left: -20%; /* 从卡片左侧外部开始 */
        opacity: 0;
    }
    10% { /* 光效快速出现 */
        opacity: 0.7; /* 光效最大不透明度 */
    }
    90% { /* 光效在划过时保持可见 */
        opacity: 0.7;
    }
    100% {
        left: 120%; /* 划过到卡片右侧外部 */
        opacity: 0;
    }
}

.newly-generated-item {
    animation: coolGenerateAppear 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    position: relative; /* 为 ::after 伪元素定位 */
    overflow: hidden;   /* 限制 ::after 伪元素在此元素内 */
}

.newly-generated-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: -20%; /* 初始位置，匹配动画起始 */
    width: 15px; /* 光束宽度 */
    height: 100%;
    background: linear-gradient(
        180deg, /* 光束内部的垂直渐变，使其看起来有厚度感 */
        rgba(180, 180, 220, 0) 0%,       /* 顶部透明 */
        rgba(220, 220, 255, 0.5) 30%,   /* 中间部分较亮，略带蓝色 */
        rgba(220, 220, 255, 0.7) 50%,   /* 核心最亮 */
        rgba(220, 220, 255, 0.5) 70%,
        rgba(180, 180, 220, 0) 100%    /* 底部透明 */
    );
    filter: blur(2px); /* 轻微模糊使光束柔和 */
    animation: cardScanLineOnce 0.6s ease-out forwards; /* 动画时长和缓动 */
    animation-delay: 0.05s; /* 主动画开始后一小段时间再开始光效 */
    z-index: 1; /* 确保光效在卡片内容之上，如果需要光效在内容之下，可设为 -1 并确保卡片有背景色 */
    pointer-events: none; /* 确保光效不影响鼠标交互 */
}
/* --- 结束：更酷的卡片生成动画 --- */

/* --- 新增：图片比例选择器样式 --- */
#aiRatioSelector .ratio-button {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 60px;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.1s ease;
}

#aiRatioSelector .ratio-button.active{
    background: rgba(255, 255, 255,1);
    border-color: rgba(0, 0, 0, 0.5);
    color: #333;
}

/* 选中状态下的比例图标样式 */
#aiRatioSelector .ratio-button.active .ratio-icon {
    border: 1px solid #000000 !important; /* 黑色边框 */
    background: transparent !important; /* 透明背景 */
}

/* 比例图标样式 */
.ratio-icon {
    display: inline-block;
    width: 24px;
    height: 18px;
    margin-right: 6px;
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: 2px;
    position: relative;
}

/* 各种比例的形状 */
.ratio-21-9 {
    width: 21px;
    height: 9px;
}

.ratio-16-9 {
    width: 20px;
    height: 11px;
}

.ratio-3-2 {
    width: 18px;
    height: 12px;
}

.ratio-4-3 {
    width: 16px;
    height: 12px;
}

.ratio-1-1 {
    width: 16px;
    height: 16px;
}

.ratio-3-4 {
    width: 12px;
    height: 16px;
}

.ratio-2-3 {
    width: 12px;
    height: 18px;
}

.ratio-9-16 {
    width: 11px;
    height: 20px;
}

/* 按钮悬停效果 */
#aiRatioSelector .ratio-button:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* 新增：Midjourney操作按钮样式 */
.mj-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 8px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.mj-buttons-container .btn {
    min-width: 40px;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
}

.mj-buttons-container .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* --- AI History Item Actions --- */
.ai-history-image-wrapper {
    position: relative; /* Allows absolute positioning for children */
    display: inline-block; /* Or block, depending on desired layout */
    vertical-align: top; /* Align wrappers nicely if inline-block */
}

.ai-history-actions {
    position: absolute;
    bottom: 5px;
    left: 40px;
    right: 5px;
    background-color: rgba(15, 17, 21, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border-radius: 6px;
    padding: 4px 1px; /* Further reduced left/right padding */
    display: none;
    justify-content: center;
    align-items: center;
    gap: 5px; /* Increased space between icons */
    transition: opacity 0.2s ease-in-out;
    opacity: 0;
    z-index: 10; /* Ensure it's above the image */
}

/* Show actions when hovering over the image wrapper */
.ai-history-image-wrapper:hover .ai-history-actions {
    display: flex; /* Use flex to arrange icons */
    opacity: 1; /* Fade in */
}

.ai-history-actions .btn-action {
    background: none; /* Transparent background */
    border: none;
    color: rgba(255, 255, 255, 0.8); /* Light icon color */
    padding: 2px 4px; /* Smaller padding */
    font-size: 0.9rem; /* Slightly smaller icon */
    line-height: 1; /* Ensure icon vertical alignment */
    cursor: pointer;
    transition: color 0.2s ease;
}

.ai-history-actions .btn-action:hover {
    color: white; /* Brighter color on hover */
}

/* Optional: Style for the history image itself */
.ai-history-image {
    display: block; /* Ensure image behaves like a block */
    max-width: 100%; /* Prevent overflow */
    height: auto; /* Maintain aspect ratio */
    /* Add other styles like border-radius, border if needed */
}