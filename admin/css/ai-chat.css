/* --- AI 聊天助手样式 --- */
#aiChatMainRow {
    /* min-height: 75vh; */ /* 移除或注释 */
    /* max-height: 85vh; */ /* 移除或注释 */
}

/* 让左右两栏的卡片都撑满父行的高度，并设置一个固定的总高度 */
#aiChatMainRow > div > .card {
    height: 80vh; /* 例如：视口高度的80%，您可以根据需要调整这个值 */
    /* 或者使用固定像素值，如 height: 700px; */
    display: flex; 
    flex-direction: column;
}

/* 左侧聊天卡片的 card-body，即 aiChatMessagesContainer 的直接父级 */
.ai-chat-card .card-body {
    flex-grow: 1; /* 占据卡片内剩余垂直空间 */
    overflow-y: auto; /* 当内容溢出时，显示垂直滚动条 */
    min-height: 0; /* flexbox技巧，确保flex-grow能正确计算高度，防止被内容撑开 */
    padding: 0; /* 移除内边距，让aiChatMessagesContainer自己控制 */
    display: flex; /* 新增，使其子元素aiChatMessagesContainer能正确布局 */
    flex-direction: column; /* 新增 */
    margin-top: 2rem;
}

/* 聊天消息实际容器 */
.ai-chat-messages-container {
    flex-grow: 1; /* 填充其父级(.card-body)的空间 */
    padding: 1rem; /* 内部留白，之前在.card-body上，现在移到这里 */
    /* overflow-y: auto; */ /* 移除，滚动由父级 .card-body 处理 */
    /* height, min-height, max-height 都不需要在这里设置固定值 */
}


/* 右侧设置卡片的 card-body */
#aiChatRightPanelCollapse .card .card-body {
    flex-grow: 1; /* 占据卡片内剩余垂直空间 */
    overflow-y: auto; /* 当内容溢出时，显示垂直滚动条 */
    min-height: 0; /* flexbox技巧 */
    padding-top: 1.5rem; /* 新增或修改：为右侧面板内容添加顶部内边距 */
}


.ai-chat-input-area {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem;
    flex-shrink: 0; /* 防止输入区域被压缩 */
}

.ai-chat-input-area textarea {
    resize: none; 
    max-height: 150px; 
    min-height: 40px; 
    line-height: 1.5;
}

.ai-chat-input-area .btn {
    min-height: 40px; 
}

/* 聊天消息气泡 */
.chat-message-bubble {
    display: flex;
    margin-bottom: 1.5rem;
    max-width: 85%; 
    position: relative; /* Make bubble the positioning context */
}

.chat-message-bubble .avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    margin-right: 10px;
    background-color: rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    flex-shrink: 0; 
}

.chat-message-bubble .message-content {
    padding: 0.75rem 1rem;
    border-radius: 12px;
    position: relative; 
    word-break: break-word; 
}

/* 用户消息 */
.chat-message-bubble.user-message {
    margin-left: auto; 
    flex-direction: row-reverse; 
    margin-right: 2rem; /* 新增：用户消息气泡距离右侧边缘的额外间距 */
}

.chat-message-bubble.user-message .avatar {
    margin-right: 0;
    margin-left: 10px;
}

.chat-message-bubble.user-message .message-content {
    background-color: #007bff; 
    color: white;
    border-top-right-radius: 4px; 
}

/* AI 助手消息 */
.chat-message-bubble.assistant-message {
    margin-right: auto; 
    margin-left: 2rem; /* 新增：AI助手消息气泡距离左侧边缘的额外间距 */
}

.chat-message-bubble.assistant-message .message-content {
    background-color: rgba(32, 33, 35, 0.5);
    color: #e0e0e0; 
    border-top-left-radius: 4px; 
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
}

/* 消息内容中的代码块样式 */
.chat-message-bubble .message-content pre {
    background-color: rgba(0, 0, 0, 0.3) !important; 
    color: #f8f8f2 !important; 
    padding: 1em !important;
    /* margin: 0.5em 0 !important; */ /* Margin is handled by wrapper now */
    overflow: auto !important;
    border-radius: 6px !important;
    border: 1px solid rgba(255,255,255,0.1);
    font-size: 0.85em;
    line-height: 1.4;
}

.chat-message-bubble .message-content pre code {
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
    white-space: pre-wrap !important; 
    word-break: break-all !important; 
}

/* AI 助手消息加载中的打字效果 */
.typing-indicator span {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin: 0 2px;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    animation: typing-blink 1.4s infinite both;
}

.typing-indicator span:nth-child(1) { animation-delay: 0s; }
.typing-indicator span:nth-child(2) { animation-delay: 0.2s; }
.typing-indicator span:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing-blink {
    0% { opacity: 0.2; }
    20% { opacity: 1; }
    100% { opacity: 0.2; }
}

/* 右侧设置与历史记录区域 */
#aiChatSettingsTabs .nav-link {
    font-size: 0.9rem;
    padding: 0.6rem 1rem;
}

/* 历史记录项样式 */
#aiChatHistoryList .list-group-item {
    cursor: pointer;
    font-size: 0.85rem;
    padding: 0.6rem 1rem;
    background-color: rgba(255, 255, 255, 0.03);
    border-color: rgba(255, 255, 255, 0.08);
    color: var(--bs-secondary-color);
    transition: background-color 0.2s ease, color 0.2s ease;
    border-radius: 8px; 
    margin-bottom: 8px; 
    border: 1px solid rgba(109, 101, 101, 0.1);
}

#aiChatHistoryList .list-group-item:last-child {
    margin-bottom: 0; 
}

#aiChatHistoryList .list-group-item:hover {
    background-color: rgba(255, 255, 255, 0.07);
    color: #f8f9fa;
}

#aiChatHistoryList .list-group-item.active {
    background-color: rgba(255, 255, 255, 0.12); 
    border-color: rgba(255, 255, 255, 0.2);    
    color: #f8f9fa; 
}

#aiChatHistoryList .history-item-title {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 0.25rem;
}

#aiChatHistoryList .history-item-date {
    font-size: 0.75rem;
    color: var(--bs-tertiary-color);
}

#aiChatHistoryList .history-item-actions {
    float: right; 
}

#aiChatHistoryList .btn-delete-history-item {
    padding: 0.1rem 0.3rem;
    font-size: 0.8rem;
    line-height: 1;
    color: var(--bs-danger);
    opacity: 0.6;
}
#aiChatHistoryList .btn-delete-history-item:hover {
    opacity: 1;
}

/* Token 计数样式 */
#aiChatTokenInfo {
    font-size: 0.75rem;
}

/* --- 新增：DeepSeek-R1 推理过程样式 --- */
.reasoning-container {
    margin-bottom: 0.75rem; 
    border: 1px solid rgba(255, 255, 255, 0.15); 
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.03); 
}

.thinking-label {
    display: flex;
    align-items: center;
    justify-content: space-between; 
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    font-size: 0.8rem;
    font-weight: 500;
    color: var(--bs-secondary-color);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1); 
    transition: background-color 0.2s ease;
}

.thinking-label:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.thinking-label .toggle-arrow {
    font-size: 1rem; 
    transition: transform 0.3s ease-out;
}

.reasoning-text {
    padding: 0.75rem;
    font-size: 0.85em;
    color: #adb5bd; 
    line-height: 1.5;
    white-space: pre-wrap; 
    word-break: break-word;
    max-height: 500px; 
    overflow-y: auto;
    transition: max-height 0.35s ease-out, padding 0.35s ease-out, opacity 0.3s ease-out;
    opacity: 1;
}

.reasoning-text.collapsed {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
    opacity: 0;
    overflow: hidden; 
}

.thinking-label .toggle-arrow.bi-chevron-up {
    transform: rotate(0deg);
}
.thinking-label .toggle-arrow.bi-chevron-down {
    transform: rotate(180deg);
}

.message-content > .reasoning-container + div { 
    padding-top: 0.75rem;
}

/* --- 新增：图片上传与预览样式 --- */
.ai-chat-image-preview {
    position: relative;
    display: inline-block; 
    max-width: 120px; 
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 4px;
    background-color: rgba(255, 255, 255, 0.05);
}

#aiChatPreviewImage {
    display: block;
    max-width: 100%;
    height: auto;
    max-height: 100px; 
    border-radius: 4px;
}

#aiChatRemoveImageBtn {
    position: absolute;
    top: -8px; 
    right: -8px;
    width: 20px;
    height: 20px;
    padding: 0;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    opacity: 0.8;
    transition: opacity 0.2s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

#aiChatRemoveImageBtn:hover {
    opacity: 1;
}

#aiChatUploadImageBtn {
    min-height: 40px; 
}

/* --- 结束：AI 聊天助手样式 --- */

/* --- 代码块复制代码按钮样式 --- */
.code-block-wrapper {
    position: relative; 
    margin: 0.5em 0; 
}
.code-block-wrapper pre {
    margin: 0; 
    padding: 1em; 
    padding-top: 2.5em; 
}
.copy-code-btn { /* Base style for ALL copy buttons */
    position: absolute;
    opacity: 0; 
    transition: opacity 0.2s ease-in-out, background-color 0.2s ease, border-color 0.2s ease; /* Added transitions */
    padding: 0.2rem 0.4rem; 
    font-size: 0.8rem; 
    line-height: 1;
    background-color: rgba(80, 80, 80, 0.7); 
    border-color: rgba(120, 120, 120, 0.8);
    color: #fff;
    border-radius: 0.2rem; 
    border-width: 1px;
    border-style: solid;
    z-index: 1; /* Ensure button is clickable */
    cursor: pointer; /* Indicate it's clickable */
}
/* Positioning for code block button (top-right inside wrapper) */
.code-block-wrapper .copy-code-btn {
    top: 0.5em; 
    right: 0.5em; 
}
/* Show code block button on wrapper hover */
.code-block-wrapper:hover .copy-code-btn {
    opacity: 1; 
}
/* Copied state styling (common for all copy buttons) */
.copy-code-btn:hover {
     background-color: rgba(100, 100, 100, 0.8);
     border-color: rgba(150, 150, 150, 0.9);
}
.copy-code-btn.btn-success {
    background-color: var(--bs-success);
    border-color: var(--bs-success);
    color: white;
    opacity: 1 !important; /* Ensure feedback is visible */
}
.copy-code-btn.btn-danger {
     background-color: var(--bs-danger);
    border-color: var(--bs-danger);
    color: white;
    opacity: 1 !important; /* Ensure feedback is visible */
} 

/* --- 气泡文本复制按钮特定样式 --- */
.chat-message-bubble {
    position: relative; /* Make bubble the positioning context */
    /* padding-bottom: 15px; */ /* Optional: Add padding if button overlaps too much */
}

.copy-bubble-btn {
    /* Inherits base styles from .copy-code-btn */
    position: absolute; /* Position relative to .chat-message-bubble */
    bottom: -10px; /* Position below the bubble */
    /* Remove inherited top/right from .copy-code-btn */
    top: auto;
    right: auto;
    left: auto; /* Reset left as well */
}

/* AI 助手消息的气泡复制按钮 (右下角) */
.assistant-message .copy-bubble-btn {
    right: -36px; /* Position to the right, outside */
    bottom: 8px; /* Position below the bubble */
}

/* 用户消息的气泡复制按钮 (左下角) */
.user-message .copy-bubble-btn {
    left: -36px; /* Position to the left, outside */
    bottom: 8px; /* Position below the bubble */
}

/* 在鼠标悬停在整个气泡上时显示按钮 */
.chat-message-bubble:hover .copy-bubble-btn {
    opacity: 1;
}

/* --- 自定义滚动条样式 (适用于 WebKit/Blink 浏览器) --- */

/* 应用于需要自定义滚动条的元素 */
.ai-chat-card .card-body::-webkit-scrollbar,
#aiChatRightPanelCollapse .card .card-body::-webkit-scrollbar {
    width: 8px; /* 滚动条宽度 */
    height: 8px; /* 水平滚动条高度 (如果需要) */
}

/* 滚动条轨道 */
.ai-chat-card .card-body::-webkit-scrollbar-track,
#aiChatRightPanelCollapse .card .card-body::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05); /* 轨道背景色，稍亮于主背景 */
    border-radius: 4px; /* 轨道圆角 */
    margin: 2px 0; /* 可选：给轨道上下一点边距 */
}

/* 滚动条滑块 (thumb) */
.ai-chat-card .card-body::-webkit-scrollbar-thumb,
#aiChatRightPanelCollapse .card .card-body::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.25); /* 滑块颜色 */
    border-radius: 4px; /* 滑块圆角 */
    border: 1px solid rgba(255, 255, 255, 0.1); /* 可选：给滑块加个细边框 */
}

/* 鼠标悬停在滑块上 */
.ai-chat-card .card-body::-webkit-scrollbar-thumb:hover,
#aiChatRightPanelCollapse .card .card-body::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.4); /* 悬停时更亮 */
}

/* --- Firefox 滚动条样式 --- */
/* 应用于需要自定义滚动条的元素 */
.ai-chat-card .card-body,
#aiChatRightPanelCollapse .card .card-body {
    scrollbar-width: thin; /* 设置滚动条宽度为 'thin' 或 'auto' */
    scrollbar-color: rgba(255, 255, 255, 0.25) rgba(255, 255, 255, 0.05); /* 设置滑块颜色和轨道颜色 */
} 