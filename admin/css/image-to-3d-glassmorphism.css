/* 毛玻璃效果样式 */
.badge.glassmorphism {
    background: rgba(40, 40, 40, 0.5) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    color: white;
    padding: 0.5em 1em;
    border-radius: 50px;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.badge.glassmorphism.bg-success {
    background: rgba(40, 167, 69, 0.6) !important;
    border-color: rgba(40, 167, 69, 0.8);
}

.badge.glassmorphism.bg-danger {
    background: rgba(220, 53, 69, 0.6) !important;
    border-color: rgba(220, 53, 69, 0.8);
}

.badge.glassmorphism.bg-info {
    background: rgba(23, 162, 184, 0.6) !important;
    border-color: rgba(23, 162, 184, 0.8);
}

.badge.glassmorphism.bg-secondary {
    background: rgba(108, 117, 125, 0.6) !important;
    border-color: rgba(108, 117, 125, 0.8);
}

.badge.glassmorphism:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 14px rgba(0, 0, 0, 0.2);
}

/* 毛玻璃按钮样式 */
.glassmorphism-btn {
    background: rgba(40, 40, 40, 0.5) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18) !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    color: white !important;
    transition: all 0.3s ease;
}

.glassmorphism-btn:hover {
    background: rgba(60, 60, 60, 0.7) !important;
    transform: translateY(-2px);
    box-shadow: 0 7px 14px rgba(0, 0, 0, 0.2);
}
