/* --- Core Variables & Reset --- */
:root {
    --gradient-border-anim: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
    --gradient-border-static: linear-gradient(90deg, #6c757d, #adb5bd, #6c757d);
    --gradient-bg: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
    --checkerboard-color1: rgba(255, 255, 255, 0.03);
    --checkerboard-size-sm: 80px;
    --checkerboard-size-lg: 200px;
    --card-bg-color: rgba(20, 20, 20, 0.7); /* Darker card background */
    --modal-bg-color: rgba(30, 30, 30, 0.8);
    --blur-intensity: 15px;
}

html, body {
    margin: 0;
    padding: 0;
    scroll-behavior: smooth;
    overflow-x: hidden;
    width: 100%;
    min-height: 100vh;
    background-color: #0a0a0a !important; /* Force the dark background */
    color: #e9ecef;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    position: relative; /* Needed for z-index stacking context with pseudo-elements */
    overflow-y: overlay;
    padding-top: 20px;
}

body {
    position: relative;
    overflow-y: overlay; /* Allows content scrolling over scrollbar */
    padding-top: 20px;
}

/* --- Background Grids --- */
body::before {
    content: '';
    position: fixed;
    inset: 0;
    background-image:
        linear-gradient(rgba(255, 255, 255, 0.2) 1px, transparent 1px), /* Keep adjusted alpha */
        linear-gradient(90deg, rgba(255, 255, 255, 0.2) 1px, transparent 1px);
    background-size: var(--checkerboard-size-sm) var(--checkerboard-size-sm); /* 40px */
    z-index: 0; /* Keep z-index 0 */
    opacity: 0.3; /* Restore subtle opacity */
    pointer-events: none; /* Keep this */
}

body::after {
     content: '';
     position: fixed;
     inset: 0;
     background-image:
         linear-gradient(rgba(255, 255, 255, 0.15) 2px, transparent 2px), /* Keep adjusted alpha, keep 2px thickness */
         linear-gradient(90deg, rgba(255, 255, 255, 0.15) 2px, transparent 2px);
     background-size: var(--checkerboard-size-lg) var(--checkerboard-size-lg); /* 100px */
     z-index: 0; /* Keep z-index 0 */
     opacity: 0.3; /* Restore subtle opacity */
     display: block; 
     pointer-events: none; 
}

/* --- Mouse Light Effect Mask --- */
.grid-mask {
    position: fixed;
    inset: 0;
    pointer-events: none;
    z-index: -1; /* Behind content, above background grids */
    opacity: 0; /* Initially hidden, controlled by JS */
    transition: opacity 0.3s;
    background: radial-gradient(
        600px circle at var(--mouse-x) var(--mouse-y),
        rgba(255, 255, 255, 0.08), /* Subtle light effect */
        transparent 40%
    );
}

/* --- Container --- */
.container {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
    position: relative;
    z-index: 1;
    opacity: 0;
    animation: fadeIn 0.5s 0.2s ease-in forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* --- Header --- */
.page-header { /* Targeting the div containing h1 and buttons */
     display: flex;
     justify-content: space-between;
     align-items: center;
     margin-bottom: 40px; /* Increased margin */
     padding-bottom: 20px;
     border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 新增：包裹用户区域和管理按钮的容器 */
.page-header .header-actions {
    display: flex;
    align-items: center;
}

/* 调整用户下拉菜单触发器 */
.page-header .dropdown .dropdown-toggle {
    padding: 5px 10px;
    border-radius: 12px;
    transition: background-color 0.3s ease;
    border: 1px solid transparent; /* 准备 hover 边框 */
}

.page-header .dropdown .dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

.page-header .dropdown .dropdown-toggle strong {
    font-weight: 500;
}

/* 美化下拉菜单 */
.dropdown-menu[aria-labelledby="dropdownUser"] {
    background: var(--modal-bg-color); /* 使用模态框背景 */
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px); /* 轻微模糊 */
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
    padding: 8px 0;
    margin-top: 8px !important; /* 增加与触发器的距离 */
}

.dropdown-menu[aria-labelledby="dropdownUser"] .dropdown-item {
    color: #adb5bd;
    padding: 8px 15px;
    font-size: 14px;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.dropdown-menu[aria-labelledby="dropdownUser"] .dropdown-item i {
    opacity: 0.7;
    margin-right: 8px; /* 图标和文字间距 */
}

.dropdown-menu[aria-labelledby="dropdownUser"] .dropdown-item:hover,
.dropdown-menu[aria-labelledby="dropdownUser"] .dropdown-item:focus {
    background-color: rgba(255, 255, 255, 0.08);
    color: #fff;
}

.dropdown-menu[aria-labelledby="dropdownUser"] .dropdown-divider {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin: 8px 0;
}

/* 调整管理按钮组 */
.page-header #managementActions {
    display: flex;
    gap: 10px; /* 按钮之间的间距 */
}

.page-header h1 {
    font-size: 2.5rem; /* Slightly larger */
    margin-bottom: 0;
    background: linear-gradient(to bottom, #e0e0e0 0%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 600;
    letter-spacing: -0.02em;
    position: relative;
    /* Optional: Add subtle animation */
    /* animation: titleFloat 3s ease-in-out infinite; */
}

@keyframes titleFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
}

/* 新增：特定调整导航栏头像大小和边框 */
.page-header #userAvatar {
    width: 48px; /* 从 36px 增大到 48px */
    height: 48px;
    border: 1px solid rgba(255, 255, 255, 0.2); /* 添加细微的白色描边 */
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.1); /* 可选：添加一点点光晕 */
    object-fit: cover; /* 新增：填充容器，保持比例，裁剪多余 */
}

/* --- General Card Style (e.g., Filter Card) --- */
.card {
    position: relative;
    border-radius: 16px; /* Consistent rounded corners */
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    margin-bottom: 30px;
    overflow: hidden;
    background: var(--card-bg-color);
    backdrop-filter: blur(var(--blur-intensity));
    -webkit-backdrop-filter: blur(var(--blur-intensity));
    border: 1px solid transparent; /* Prepare for gradient border */
    background-clip: padding-box;
    transition: all 0.3s ease;
}

.card::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px; /* Border thickness */
    background: var(--gradient-border-anim);
    -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    z-index: -1;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.08)); /* Subtle gradient by default */
    pointer-events: none; /* Add this line to allow clicks to pass through */
}

.card:hover {
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.5);
     transform: translateY(-3px);
}

@keyframes moveGradient {
    0% { background-position: 0% 0%; }
    100% { background-position: 300% 0%; }
}

.card-header {
    background: linear-gradient(rgba(255, 255, 255, 0.03), transparent); /* Subtle gradient */
    color: #fff;
    font-weight: 500;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.card-body {
    padding: 20px;
}

/* --- Example Cards Grid --- */
 .examples-container { /* This applies to the main management tab */
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); /* Responsive grid */
    gap: 24px;
    position: relative; /* Needed if using absolute positioning for hidden cards */
 }

/* --- Add specific styles for My Examples Modal --- */
#myExamplesModal .my-examples-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* Change to 3 columns */
    gap: 20px; /* Adjust gap as needed */
    padding: 10px; /* Add some padding inside the modal body */
}

/* Ensure loading/empty states inside modal span all columns */
#myExamplesModal .my-examples-container .loading,
#myExamplesModal .my-examples-container .empty-state {
    grid-column: 1 / -1; /* Span all columns */
}

/* --- Add specific styles for Liked Examples Modal (Mirroring My Examples) --- */
#likedExamplesModal .liked-examples-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* Change to 3 columns */
    gap: 20px;
    padding: 10px;
}

#likedExamplesModal .liked-examples-container .loading,
#likedExamplesModal .liked-examples-container .empty-state {
    grid-column: 1 / -1; /* Span all columns */
}

/* --- Example Card Item Style (Mimicking index_副本.css .card-item) --- */
.example-card {
    position: relative;
    background: var(--card-bg-color);
    border-radius: 16px;
    text-decoration: none;
    color: #fff;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(var(--blur-intensity));
    -webkit-backdrop-filter: blur(var(--blur-intensity));
    overflow: hidden;
    /* transform-style: preserve-3d; */ /* Temporarily disable for debugging */
    /* transform: perspective(1000px); */ /* Temporarily disable for debugging */
    z-index: 1;
    border: none; /* Remove default card border */
    padding: 0; /* Remove default padding, control internally */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    min-height: 420px; /* Ensure minimum height for content */
    margin-bottom: 0; /* Remove default margin */
    display: flex; /* Make card itself a flex container */
    flex-direction: column; /* Stack image and body vertically */
    cursor: pointer; /* 新增：为整个卡片添加手形光标 */
}

/* --- 新增：为相关案例卡片重置最小高度 --- */
.example-card.relevant-example-card {
    min-height: auto; /* 覆盖通用的 min-height，允许卡片高度自适应 */
}

.example-card::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: var(--gradient-border-anim);
    -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.08)); /* Subtle gradient by default */
    z-index: 0; /* Behind content */
}

/* Apply animation and animated background only on hover */
.example-card:hover::before {
    background: var(--gradient-border-anim); /* Switch to animated gradient on hover */
    background-size: 300% 100%; /* Add background size needed for animation */
    animation: moveGradient 3s linear infinite;
}

/* Hover Glow Effect */
.example-card::after {
    content: '';
    position: absolute;
    inset: -2px; /* Slightly larger than card */
    border-radius: inherit;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
    opacity: 0;
    filter: blur(16px);
    transition: opacity 0.4s ease;
    z-index: -1; /* Behind border */
    pointer-events: none;
}

.example-card:hover {
    /* transform: perspective(1000px) translateY(-8px) translateZ(20px); */ /* Simplified hover */
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.example-card:hover::before {
    animation-duration: 3s; /* Faster gradient on hover */
}

.example-card:hover::after {
    opacity: 0.15;
}

/* Internal structure adjustment */
.example-card .card-body {
    padding: 20px;
    position: relative;
    z-index: 2; /* Above image */
    /* transform-style: preserve-3d; */
    /* transform: translateZ(40px); */ /* Removed Z transform */
    display: flex;
    flex-direction: column;
    flex-grow: 1; /* Allow card-body to grow and fill remaining space */
}

.example-img {
    width: calc(100% + 40px);
    margin: -20px -20px 15px -20px;
    height: 180px;
    object-fit: contain;
    border-radius: 16px 16px 0 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform 0.4s ease;
    position: relative; /* Needed for z-index */
    z-index: 1; /* Below card-body, above card background */
}
 .example-card:hover .example-img {
     /* transform: translateZ(30px) scale(1.03); */
     transform: scale(1.03); /* Simplified hover */
 }

.example-card h5, 
.example-card p, 
.example-card .tags-container, 
.example-card .actions { 
    /* transform: translateZ(Xpx); */ /* Removed Z transforms */
    position: relative; /* Ensure proper stacking within card-body */
}

.example-card h5 { /* Card Title */
    font-size: 1.2rem;
    margin-bottom: 10px;
    font-weight: 600;
    color: #ffffff;
}

.example-card p { /* Card Prompt Snippet */
    color: #adb5bd;
    font-size: 0.85rem;
    line-height: 1.5;
    margin-bottom: 15px;
    flex-grow: 1; /* This makes the paragraph take available space */
    /* Add max-height as a fallback if flex layout isn't enough */
     max-height: calc(0.85rem * 1.5 * 3); /* approx 3 lines */
    overflow: hidden;
    /* display: -webkit-box; */ /* Rely on max-height, simpler */
    /* -webkit-line-clamp: 3; */
    /* -webkit-box-orient: vertical; */
     position: relative;
}

.example-card .tags-container {
    margin-bottom: 15px;
}
.example-card .actions { /* Container for edit/delete buttons */
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: auto; /* Push to bottom */
}

/* --- 新增：点赞按钮和计数样式 --- */
.like-btn {
    /* 默认样式 */
    color: #adb5bd; /* 默认灰色 */
    transition: color 0.2s ease, transform 0.2s ease; /* 添加过渡 */
}

.like-btn .bi-heart, /* 图标 */
.like-btn .like-count { /* 计数 */
    vertical-align: middle; /* 垂直居中对齐 */
}

.like-btn .like-count {
    font-size: 0.8em; /* 计数稍微小一点 */
}

.like-btn:hover {
    color: #ff8a8a; /* 悬停时浅红色 */
    /* transform: scale(1.1); /* 可选：悬停放大效果 */
}

/* 已点赞状态 */
.like-btn.liked {
    color: #e74c3c; /* 已点赞为红色 */
}

.like-btn.liked:hover {
    color: #c0392b; /* 已点赞悬停时深红色 */
}

/* 新增：点赞动画 */
@keyframes heartBeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.3); }
  100% { transform: scale(1); }
}

/* 当按钮处于动画状态时，对其内部的图标应用动画 */
.like-btn.animating .bi {
  animation: heartBeat 0.35s ease-in-out;
}

/* --- 新增：为卡片内的下拉菜单添加毛玻璃效果 --- */
.example-card .actions .dropdown-menu {
    background-color: rgba(40, 40, 40, 0.8) !important; /* 使用半透明背景，!important 确保覆盖默认 BS 样式 */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15); /* 可选：添加细边框 */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); /* 可选：添加阴影 */
    border-radius: 8px; /* 确保圆角 */
}
/* 确保下拉菜单项在毛玻璃背景上清晰可见 */
.example-card .actions .dropdown-menu .dropdown-item {
    color: #e0e0e0; /* 确保文字颜色足够亮 */
}
.example-card .actions .dropdown-menu .dropdown-item:hover,
.example-card .actions .dropdown-menu .dropdown-item:focus {
    background-color: rgba(255, 255, 255, 0.1); /* 调整悬停背景 */
    color: #fff;
}
.example-card .actions .dropdown-menu .dropdown-item.text-danger {
    color: #ff8a8a !important; /* 让删除选项的红色更亮 */
}
.example-card .actions .dropdown-menu .dropdown-item.text-danger:hover {
    background-color: rgba(255, 80, 80, 0.2); /* 删除选项悬停背景 */
    color: #ffcccc !important;
}
/* --- 结束：卡片内下拉菜单样式 --- */


/* --- Tags --- */
.tag {
    display: inline-block;
    background: rgba(255, 255, 255, 0.15); /* Increased background opacity */
    border: 1px solid rgba(255, 255, 255, 0.3); /* Increased border opacity */
    color: #adb5bd;
    border-radius: 12px; /* Slightly less round */
    padding: 4px 10px;
    font-size: 11px;
    margin: 2px;
    transition: all 0.2s ease;
    cursor: default;
}
.tag:hover {
    background: rgba(255, 255, 255, 0.25); /* Adjusted hover background */
    color: #f0f0f0;
    border-color: rgba(255, 255, 255, 0.4); /* Adjusted hover border */
}

/* --- Buttons (Tech Style) --- */
.btn {
    position: relative;
    padding: 10px 24px; /* Adjusted padding */
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    border: 1px solid transparent;
    transform-style: preserve-3d;
    /* transform: translateZ(0); */ /* Base Z for perspective */
}

.btn span, .btn i { /* Ensure text/icons are above effects */
    position: relative;
    z-index: 1;
}

/* Primary Button - 纯白底黑字简洁样式 */
.btn-primary {
    background: #ffffff !important; /* 纯白色背景，使用!important确保优先级 */
    color: #333333 !important; /* 深灰色文字 */
    border: 1px solid #cccccc !important; /* 浅灰色边框 */
    box-shadow: none !important; /* 完全移除阴影 */
}
.btn-primary:hover {
    background: #f5f5f5 !important; /* 悬停时浅灰色背景 */
    color: #333333 !important; /* 保持深灰色文字 */
    border-color: #999999 !important; /* 悬停时深一点的边框 */
    box-shadow: none !important; /* 移除阴影 */
    transform: none !important; /* 移除位移效果 */
}
.btn-primary:active, .btn-primary:focus {
    background: #e5e5e5 !important; /* 点击/聚焦时更深的灰色 */
    color: #333333 !important;
    border-color: #666666 !important;
    transform: none !important; /* 移除位移效果 */
    box-shadow: none !important; /* 移除阴影 */
}

/* Outline Primary Button */
.btn-outline-primary {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
}
.btn-outline-primary:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border-color: rgba(255, 255, 255, 0.3);
     box-shadow: 0 0 10px rgba(255,255,255,0.05);
     transform: translateY(-2px);
}
 .btn-outline-primary:active {
     transform: translateY(0px);
     background: rgba(255, 255, 255, 0.03);
 }

/* --- Add styles for Outline Secondary Button to match Primary --- */
.btn-outline-secondary {
    
    color: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
}
.btn-outline-secondary:hover {
    background: #000000 !important;
    color:  rgba(255, 255, 255, 0.7);
    border-color: rgba(255, 255, 255, 0.3);
    
     transform: translateY(-2px);
}
 .btn-outline-secondary:active {
     transform: translateY(0px);
     background: rgba(255, 255, 255, 0.03);
 }

/* Danger Button */
 .btn-danger {
     background: linear-gradient(45deg, #6b2128, #4d181d); /* Dark red */
     color: rgba(255, 255, 255, 0.9);
     border: 1px solid rgba(255, 100, 100, 0.2);
     box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3), inset 0 1px 1px rgba(255, 255, 255, 0.05);
 }
 .btn-danger:hover {
    background: linear-gradient(45deg, #852a33, #6b2128);
    color: #ffffff;
    border-color: rgba(255, 100, 100, 0.3);
    box-shadow: 0 6px 15px rgba(80, 0, 0, 0.5), inset 0 1px 1px rgba(255, 255, 255, 0.08), 0 0 15px rgba(255,100,100,0.1);
    transform: translateY(-2px);
 }
 .btn-danger:active {
     transform: translateY(0px);
     box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3), inset 0 1px 2px rgba(0, 0, 0, 0.1);
 }

.btn-light {
    background: transparent !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    transition: all 0.3s ease !important;
}

.btn-light:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.btn-light:active, .btn-light:focus {
    background: rgba(255, 255, 255, 0.15) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.4) !important;
    box-shadow: none !important;
}

/* Icon Button Style - 缩小按钮尺寸 */
.btn-icon {
    padding: 0.25rem 0.5rem !important; /* 减小内边距 */
    font-size: 0.8rem !important; /* 减小字体大小 */
    line-height: 1.2 !important;
    min-width: auto !important;
}

/* 隐藏复制按钮 - 只隐藏提示词管理页面的复制按钮，不影响反推提示词页面 */
.copy-prompt-btn,
.btn[onclick*="copyPrompt"],
.btn[title="复制提示词"] {
    display: none !important;
}

/* 隐藏提示词管理页面的剪贴板图标，但不影响反推提示词页面 */
#management-tab-pane .btn .bi-clipboard {
    display: none !important;
}

#management-tab-pane .btn:has(.bi-clipboard) {
    display: none !important;
}

/* 确保反推提示词页面的复制按钮可以正常显示 */
#reverse-prompt-tab-pane .copy-btn {
    display: inline-block !important;
}

#reverse-prompt-tab-pane .copy-btn .bi-clipboard {
    display: inline !important;
}

/* 新增：自定义案例上传插件按钮样式 */
.btn-plugin-upload {
    background: rgba(138, 43, 226, 0.1); /* 淡紫色背景 */
    color: rgba(200, 160, 255, 0.9); /* 紫色系文字 */
    border: 1px solid rgba(255, 255, 255, 0.2); /* 默认灰色描边 */
    backdrop-filter: blur(5px);
}
.btn-plugin-upload:hover {
    background: #7A49A5; /* 悬停时实心紫色 (调暗) */
    color: #ffffff; /* 悬停时白色文字 */
    border-color: transparent; /* 悬停时无描边 */
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4); /* 悬停时添加通用阴影 */
    transform: translateY(-2px);
}
.btn-plugin-upload:active {
    transform: translateY(0px);
    background: rgba(138, 43, 226, 0.05); /* 点击时更淡的背景 */
    color: rgba(200, 160, 255, 0.7);
}
/* --- 结束：自定义按钮样式 --- */


/* --- Modal Style --- */
.modal-dialog {
    max-width: 700px;
    margin: 50px auto; /* Add top margin */
}
.modal-content {
    position: relative;
    background: var(--modal-bg-color);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(var(--blur-intensity));
    -webkit-backdrop-filter: blur(var(--blur-intensity));
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4), inset 0 0 0 1px rgba(255, 255, 255, 0.05);
    overflow: hidden;
    color: #e9ecef;
}

/* Internal Modal Light Effect */
.modal-content::before {
    content: '';
    position: absolute;
    top: -50%; /* Position effect higher */
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(
        circle at center,
        rgba(255, 255, 255, 0.08) 0%, /* More subtle effect */
        transparent 50%
    );
    animation: rotateEffect 15s linear infinite;
    z-index: 0;
    opacity: 0.7;
}

@keyframes rotateEffect {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Ensure content is above effect */
.modal-header, .modal-body, .modal-footer {
    position: relative;
    z-index: 1;
}

.modal-header {
    background: rgba(255, 255, 255, 0.03);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
}
.modal-header .modal-title {
    font-weight: 600;
    color: #fff;
}
.modal-header .btn-close {
     filter: invert(1) grayscale(100%) brightness(200%); /* Make close button white */
     opacity: 0.7;
}
 .modal-header .btn-close:hover {
     opacity: 1;
 }

.modal-body {
     padding: 1.5rem;
     background: rgba(0,0,0,0.1); /* Slight contrast */
}
.modal-footer {
    background: rgba(255, 255, 255, 0.03);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
}

/* --- Form Elements --- */
label {
    color: #adb5bd;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}
.form-control, .form-select {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: #e9ecef;
    border-radius: 8px;
    padding: 0.6rem 0.8rem;
    transition: all 0.3s ease;
}
.form-control:focus, .form-select:focus {
    background-color: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.3);
    color: #fff;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}
.form-control::placeholder {
    color: #6c757d;
}
textarea.form-control {
     min-height: 100px;
}

/* 新增：调整 Input Group 图标背景 */
.input-group-text {
    background-color: rgba(255, 255, 255, 0.05); /* 与输入框背景一致 */
    border: 1px solid rgba(255, 255, 255, 0.15); /* 与输入框边框一致 */
    border-left: none; /* 移除与输入框相接的左边框 */
    color: #adb5bd; /* 图标颜色 */
}

/* 新增：确保禁用状态的输入框也使用暗色主题 */
.form-control:disabled, .form-control[readonly] {
    background-color: rgba(50, 50, 50, 0.5); /* 使用一个稍暗或不同的灰色背景表示禁用 */
    border-color: rgba(255, 255, 255, 0.1);
    color: #6c757d; /* 使用较深的灰色文字表示禁用 */
    opacity: 1; /* 确保不透明度不会影响背景 */
    cursor: not-allowed;
}

/* --- Enhanced Form Select for Dark Mode Consistency --- */
.form-select {
    /* Existing styles */
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: #e9ecef;
    border-radius: 8px;
    padding: 0.6rem 0.8rem;
    transition: all 0.3s ease;

    /* Overrides for Windows/Browser defaults */
    appearance: none;
    -webkit-appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23adb5bd' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"); /* Simple gray arrow SVG */
    background-repeat: no-repeat;
    background-position: right 0.75rem center; /* Position arrow */
    background-size: 16px 12px; /* Arrow size */
    padding-right: 2.5rem; /* Ensure text doesn't overlap arrow */
}

.form-select:focus {
    /* Existing focus styles */
    background-color: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.3);
    color: #fff;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
    /* Ensure custom arrow remains on focus */
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23adb5bd' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
}

/* Optional: Style for disabled state if needed */
.form-select:disabled {
    background-color: rgba(50, 50, 50, 0.5);
    border-color: rgba(255, 255, 255, 0.1);
    color: #6c757d;
    opacity: 1;
    /* Change arrow color for disabled state */
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%236c757d' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"); 
}

/* --- Tagify Dark Theme --- */
.tagify {
    --tags-bg: rgba(255, 255, 255, 0.05);
    --tags-hover-bg: rgba(255, 255, 255, 0.1);
    --tags-text-color: #adb5bd;
    --tag-bg: rgba(255, 255, 255, 0.1);
    --tag-hover: rgba(255, 255, 255, 0.2);
    --tag-text-color: #e0e0e0; /* Lighter tag text */
    --tag-remove-btn-color: #adb5bd;
    --tag-remove-btn-bg: transparent;
    --tag-remove-btn-bg--hover: rgba(220, 53, 69, 0.8);
    --tag-inset-shadow-size: 0;
    border-color: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
}
.tagify__input { color: #e9ecef; }
.tagify__tag { border-radius: 6px; background: rgba(75, 75, 75, 0.85);} /* Match form control */
.tagify__tag > div::before { box-shadow: none; }
.tagify__tag__removeBtn { border-radius: 50%; width: 16px; height: 16px; font-size: 10px; }
.tagify__dropdown {
    background: rgba(40, 40, 40, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 20px rgba(0,0,0,0.4);
     backdrop-filter: blur(5px);
     border-radius: 8px;
}
.tagify__dropdown__item { color: #adb5bd; padding: 0.5rem 0.8rem; }
.tagify__dropdown__item--active { background: rgba(255, 255, 255, 0.1); color: #f8f9fa; }
.tagify__dropdown__item:hover { background: rgba(255, 255, 255, 0.05); color: #f8f9fa; }

/* --- Image Drop Zone --- */
.image-drop-zone {
    border: 2px dashed #444; /* 保持您原有的边框样式 */
    padding: 20px;          /* 保持您原有的内边距 */
    border-radius: 8px;     /* 保持您原有的圆角 */
    cursor: pointer;        /* 保持您原有的光标样式 */
    transition: background-color 0.2s ease; /* 保持您原有的过渡效果 */

    /* 新增的 Flexbox 居中样式 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    min-height: 150px; /* 给予一个最小高度，确保垂直居中有效，您可以根据需要调整 */
}

.image-drop-zone:hover, .image-drop-zone.dragging {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

.image-drop-zone i {
    font-size: 3rem; /* 示例大小，您可以调整 */
    margin-bottom: 10px; /* 图标下方的间距 */
}

.image-drop-zone p {
    margin-bottom: 5px; /* 主要文字下方的间距 */
    font-size: 1rem;    /* 示例大小 */
}

.image-drop-zone small {
    font-size: 0.875rem; /* 示例大小 */
}

/* 粘贴高亮样式 */
.paste-highlight {
    animation: paste-flash 0.5s ease;
    border-color: #007bff !important;
    background-color: rgba(0, 123, 255, 0.1);
}

@keyframes paste-flash {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.5);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0.2);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

/* 图片预览容器统一样式 */
.preview-container {
    text-align: center;
    display: none;
}

.preview-img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
}

/* --- Table Styles --- */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
}
.table {
    --bs-table-color: #e0e0e0;
    --bs-table-bg: transparent; /* Use container background */
    --bs-table-border-color: rgba(255, 255, 255, 0.1);
    --bs-table-striped-color: #e9ecef;
    --bs-table-striped-bg: rgba(255, 255, 255, 0.03);
    --bs-table-hover-color: #fff;
    --bs-table-hover-bg: rgba(255, 255, 255, 0.05);
    margin-bottom: 0; /* Remove margin inside responsive container */
}
.table th {
    font-weight: 600;
    color: #fff;
    background-color: rgba(255, 255, 255, 0.04);
}
.table td {
    vertical-align: middle;
}
.table-hover > tbody > tr:hover > * {
     color: var(--bs-table-hover-color);
     background-color: var(--bs-table-hover-bg);
}


/* --- Loading & Empty States --- */
.loading, .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    color: #6c757d;
    width: 100%; /* Ensure it spans grid width */
    grid-column: 1 / -1; /* Span all grid columns */
    text-align: center;
    flex-direction: column;
    gap: 15px;
}
.spinner-border {
    width: 2.5rem;
    height: 2.5rem;
    color: #888;
    border-right-color: transparent;
}
.empty-state i {
     font-size: 3rem;
     margin-bottom: 1rem;
}


/* --- Utility & Hidden --- */
.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* --- Figma Plugin Floating Action Button --- */
.plugin-fab {
    position: fixed;
    bottom: 30px;
    right: 30px;
    /* width: 56px; */ /* Remove fixed width, let padding control size */
    /* height: 56px; */ /* Remove fixed height */
    padding: 12px 22px; /* Adjust padding for text */
    /* background-color: #0d6efd; */ /* Remove single color */
    background: linear-gradient(135deg, #282c34, #1c1e22); /* Dark gradient */
    color: #e0e0e0; /* Light text color */
    border-radius: 28px; /* Pill shape */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px; /* Adjust font size for text */
    font-weight: 600; /* Make text bold */
    text-decoration: none;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    z-index: 1040;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out, background 0.3s ease;
    /* border: 2px solid rgba(255, 255, 255, 0.3); */ /* Remove or change border */
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden; /* Important for clipping the animation */
    cursor: pointer;
}

/* Add pseudo-element for the streaming light */
.plugin-fab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%; /* Start off-screen to the left */
    width: 50%; /* Width of the light beam */
    height: 100%;
    background: linear-gradient(
        to right,
        transparent 0%,
        rgba(255, 255, 255, 0.25) 50%,
        transparent 100%
    );
    transform: skewX(-25deg); /* Skew the light beam */
    animation: streaming-light 3s linear infinite; /* Apply animation */
    z-index: 1; /* Above background, below text */
}

.plugin-fab span { /* Ensure text is above the light effect */
    position: relative;
    z-index: 2;
}

.plugin-fab:hover {
    transform: scale(1.05); /* Slightly less scaling */
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
    color: #fff; /* Ensure color remains white on hover */
    /* background-color: #0b5ed7; */ /* Remove single color hover */
    background: linear-gradient(135deg, #3a3f47, #2a2c30); /* Slightly lighter dark gradient on hover */
    border-color: rgba(255, 255, 255, 0.2);
}

/* Remove the specific rule for <i> if it exists, or ensure it doesn't conflict */
/* .plugin-fab i {
    line-height: 1; 
} */

/* Keyframes for the streaming light animation */
@keyframes streaming-light {
    0% {
        left: -100%;
    }
    50% { /* Optional: Pause briefly in the middle */
        left: 150%;
    }
    100% {
        left: 150%; /* End off-screen to the right */
    }
}

/* --- Responsiveness --- */
 @media (max-width: 992px) {
     .examples-container {
         grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
         gap: 20px;
     }
     .page-header h1 { font-size: 2rem; }
 }

 @media (max-width: 768px) {
     .container { padding: 20px 15px; }
     .page-header { flex-direction: column; align-items: flex-start; gap: 15px; }
     .examples-container {
         grid-template-columns: 1fr; /* Single column on smaller screens */
         gap: 20px;
     }
     .modal-dialog { margin: 20px auto; max-width: 95%;}
     .card { border-radius: 12px;}
     .example-card { border-radius: 12px; }
 }

 @media (max-width: 576px) {
    .page-header h1 { font-size: 1.8rem; }
    .btn { padding: 8px 18px; font-size: 13px; }
    .form-control, .form-select { padding: 0.5rem 0.7rem; }
 }

/* --- Category Badge Styling --- */
.example-card .category-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 3; /* Ensure it's above image and body content */
    /* background: linear-gradient(45deg, #8a2be2, #6f42c1); */ /* Original Purple gradient */
    /* background: linear-gradient(45deg, rgba(138, 43, 226, 0.55), rgba(111, 66, 193, 0.85)); /* Adjusted with alpha */
    color: #fff;
    padding: 5px 12px;
    font-size: 16px; /* Increased font size */
    font-weight: 500;
    border-radius: 8px;
    /* box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3); */ /* Removed shadow */
    /* border: ... set by JS ... */
    border: 1px solid transparent; /* Keep border structure, color set by JS */
    transition: all 0.3s ease;
    /* backdrop-filter: blur(3px); */ /* Original blur value, updating */
    backdrop-filter: blur(5px); /* 毛玻璃效果 */
    -webkit-backdrop-filter: blur(5px); /* 毛玻璃效果 for Safari */
}
.example-card:hover .category-badge {
     /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4); */ /* Removed hover shadow */
     transform: translateY(-2px);
}

/* --- 新增：模型标签样式 --- */
.example-card .model-badge {
    position: absolute;
    top: 15px;        /* 与 category-badge 对齐 */
    left: 15px;       /* 放在左侧 */
    z-index: 3;
    background: rgba(0, 123, 255, 0.7); /* 使用蓝色调 */
    color: #fff;
    padding: 5px 10px;
    font-size: 11px;  /* 稍小一点 */
    font-weight: 500;
    border-radius: 6px;
    border: 1px solid rgba(0, 123, 255, 0.9);
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    transition: all 0.3s ease;
    text-transform: uppercase; /* 可选：让文字大写 */
    letter-spacing: 0.5px; /* 可选：增加字母间距 */
}

.example-card:hover .model-badge {
     transform: translateY(-2px);
     background: rgba(0, 123, 255, 0.85); /* 悬停时更明显 */
     border-color: rgba(0, 123, 255, 1);
}

/* --- Tab Navigation Style Override --- */
.nav-tabs {
    border-bottom: 1px solid rgba(255, 255, 255, 0.15); /* Darker border */
}

.nav-tabs .nav-item {
    margin-bottom: -1px; /* Overlap border */
}

.nav-tabs .nav-link {
    background: none;
    border: 1px solid transparent;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    color: #adb5bd; /* Default gray text */
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

.nav-tabs .nav-link:hover {
    border-color: rgba(255, 255, 255, 0.1);
    color: #e0e0e0; /* Lighter gray on hover */
    isolation: isolate; /* Needed for z-index */
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
    color: #ffffff; /* White text for active */
    /* Use card background or slightly lighter */
    background-color: var(--card-bg-color); 
    /* background-color: rgba(35, 35, 35, 0.8); */ /* Slightly lighter alternative */
    border-color: rgba(255, 255, 255, 0.15) rgba(255, 255, 255, 0.15) transparent; /* Top and side borders */
    border-bottom-color: transparent; /* Remove bottom border to merge with pane */
    font-weight: 600;
}

/* Optional: Remove focus outline if needed, but consider accessibility */
/* .nav-tabs .nav-link:focus {
    outline: none;
    box-shadow: none;
} */


/* --- 新增：优化插件推广弹窗列表样式 --- */
#pluginPromoModal .modal-body ul {
    list-style: none; /* 移除默认的圆点 */
    padding-left: 0; /* 移除默认的左侧内边距 */
    margin-top: 1rem; /* 列表与上方段落的间距 */
}

#pluginPromoModal .modal-body ul li {
    margin-bottom: 0.8rem; /* 列表项之间的间距 */
    padding-left: 2rem; /* 为图标留出空间 */
    position: relative; /* 用于图标定位 */
    color: #e0e0e0; /* 列表项文字颜色 */
    line-height: 1.5;
}

#pluginPromoModal .modal-body ul li::before {
    content: ''; /* Bootstrap Icons 需要 content */
    font-family: 'bootstrap-icons'; /* 使用 Bootstrap Icons 字体 */
    position: absolute;
    left: 0;
    top: 0.1em; /* 微调图标垂直位置 */
    font-size: 1.2em; /* 图标大小 */
    color: #adb5bd; /* 图标颜色 (改为浅灰色) */
    /* 可以为每个列表项设置不同的图标 */
}

/* 为每个列表项设置特定图标 (示例) */
#pluginPromoModal .modal-body ul li:nth-child(1)::before {
    content: '\F4E8'; /* bi-chat-left-dots */
}
#pluginPromoModal .modal-body ul li:nth-child(2)::before {
    content: '\F5D2'; /* bi-sliders */
}
#pluginPromoModal .modal-body ul li:nth-child(3)::before {
    content: '\F4A0'; /* bi-lightbulb */
}
/* --- End: 优化插件推广弹窗列表样式 --- */


/* --- 浮动插件推广提示样式 --- */
.floating-plugin-promo {
    position: fixed;
    bottom: 30px;
    left: 50%;
    z-index: 1040; /* Below modals (1050+), above toasts */
    background: linear-gradient(135deg, rgba(50, 50, 60, 0.85), rgba(30, 30, 40, 0.95)); /* Darker, slightly bluish gradient */
    color: #e0e0e0;
    padding: 15px 20px 15px 15px; /* More padding top/bottom */
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: none; /* Initially hidden, controlled by JS */
    align-items: center;
    gap: 8px;
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
    transition: opacity 0.4s ease-out, transform 0.4s ease-out;
    max-width: 350px; /* Limit width */
}

/* 当 JS 添加 display:flex/block 时触发 */
/* .floating-plugin-promo[style*="display: flex"],
.floating-plugin-promo[style*="display: block"] {
    opacity: 1;
    transform: translateY(0);
} */
/* Note: The opacity/transform for visibility is now handled directly in the JS function showFloatingPluginPromo */


.floating-plugin-promo .promo-icon {
    font-size: 1.4rem;
    line-height: 1;
}

.floating-plugin-promo .promo-link {
    color: #a0c4ff; /* Brighter link color */
    text-decoration: none;
    font-weight: 500;
    border-bottom: 1px solid transparent;
    transition: all 0.2s ease;
}

.floating-plugin-promo .promo-link:hover {
    color: #c0d8ff;
    border-bottom-color: #a0c4ff;
}

.floating-plugin-promo .btn-close {
    position: absolute;
    top: 8px;
    right: 8px;
    filter: invert(80%) grayscale(100%) brightness(150%); /* Adjust close button color */
    opacity: 0.6;
    padding: 0.25rem; /* Smaller padding */
    background-size: 0.8em; /* Smaller icon */
}

.floating-plugin-promo .btn-close:hover {
    opacity: 1;
}

/* 微调模态框按钮 */
#pluginPromoModal .modal-body .btn-lg {
    padding: 12px 30px; /* Make button larger */
    font-size: 1.1rem;
}

/* 新增：个人资料头像预览区域样式 */
.avatar-preview-container {
    position: relative; /* Needed for overlay positioning */
    display: inline-block; /* Fit content size */
    border-radius: 50%; /* Ensure container matches image shape */
    overflow: hidden; /* Hide overlay overflow */
    border: 2px solid rgba(255, 255, 255, 0.1); /* Add a subtle border to the container */
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.avatar-preview-container:hover {
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.15);
}

.avatar-preview-container img {
    display: block; /* Remove extra space below image */
    width: 100px; /* Ensure dimensions are set for object-fit */
    height: 100px;
    object-fit: cover; /* 新增：填充容器，保持比例，裁剪多余 */
}

.avatar-edit-overlay {
    position: absolute;
    /* inset: 0; */ /* 不再覆盖整个区域 */
    bottom: 0px; /* 定位到右下角，稍微偏移 */
    right: 0px;
    width: 30px; /* 固定大小 */
    height: 30px;
    background-color: rgba(0, 0, 0, 0.6); /* 深色圆形背景 */
    color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem; /* 调整图标大小 */
    opacity: 0; /* 默认隐藏 */
    transition: opacity 0.3s ease;
    border-radius: 50%; /* 圆形 */
    border: 1px solid rgba(255, 255, 255, 0.2); /* 添加细边框 */
    pointer-events: none; /* 让点击穿透到下面的容器 */
}

.avatar-preview-container:hover .avatar-edit-overlay {
    opacity: 1; /* 悬停时显示 */
}

/* 移除移除按钮的样式 */
/* #removeAvatarBtn {
    margin: 10px auto 0 auto; 
    display: none; 
} */


.page-header .dropdown .dropdown-toggle strong {
    font-weight: 500;
}

/* --- 新增：卡片作者信息样式 --- */
.example-card .author-info {
    position: absolute; /* 定位在卡片内 */
    bottom: 15px;      /* 距离底部 */
    left: 20px;       /* 距离左侧 */
    display: flex;
    align-items: center;
    gap: 8px;         /* 头像和名字间距 */
    z-index: 3;       /* 确保在内容之上 */
    opacity: 0.8;     /* 稍微降低透明度 */
    transition: opacity 0.3s ease;
}

.example-card:hover .author-info {
    opacity: 1; /* 悬停时完全显示 */
}

.example-card .author-avatar {
    width: 28px;      /* 小头像尺寸 */
    height: 28px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid rgba(255, 255, 255, 0.2); /* 轻微描边 */
}

.example-card .author-info span {
    font-size: 12px;  /* 作者名字体大小 */
    color: #adb5bd; /* 作者名字颜色 */
    font-weight: 500;
}

/* --- 新增：插件推广弹窗图片样式 --- */
#pluginPromoModal .promo-image-container {
    text-align: center; /* Center the image block */
}

#pluginPromoModal .promo-image-container img {
    max-width: 100%;
    height: auto; /* Maintain aspect ratio */
    border: 1px solid rgba(255, 255, 255, 0.1); /* Optional subtle border */
    box-shadow: 0 4px 10px rgba(0,0,0,0.2); /* Add some depth */
}
/* --- End: 插件推广弹窗图片样式 --- */

/* --- 新增：分享模态框样式 (即梦风格) --- */
#shareModal .modal-dialog.modal-xl { /* Target modal-xl specifically */
    /* Ensure XL modal width takes effect */
    /* max-width: 1140px; */ /* Explicitly set if needed, though modal-xl should handle it */
    max-width: 1300px; /* Increased max-width */
}

#shareModal .modal-content {
    background-color: #2a2a2e; /* Slightly different dark background */
    border: 1px solid #444; 
    /* min-height: 600px; */ /* Remove min-height */
    max-height: 95vh; /* Further increased max-height */
    display: flex; /* Ensure content itself is flex for body to grow */
    flex-direction: column; /* Stack header, body, footer(if any) */
}

#shareModal .modal-header {
     border-bottom: 1px solid #444; 
}

#shareModal .modal-body {
    padding: 0; /* Remove padding from body, handle in columns */
    display: flex; 
    flex-direction: row; 
    /* gap: 0; */ /* Remove gap, handle padding internally */ /* Keep default gap or remove */
    align-items: stretch; /* Make columns equal height */
    /* overflow: hidden; */ /* Prevent body itself from scrolling, let columns scroll */ /* Removed overflow: hidden */
    flex-grow: 1; /* Allow body to take up space within content max-height */
}

/* Left column (Image - 70%) */
.share-image-left {
    flex: 0 0 70%; 
    display: flex;
    /* align-items: center; */ /* Vertically centers the container inside the column */ /* Changed to flex-start */
    align-items: flex-start; /* Let container align to top */
    justify-content: center;
    background-color: #1e1e20; /* Darker background for image area */
    padding: 20px; /* Add padding around image container */
    border-right: 1px solid #444; /* Separator line */
}
.share-image-left .share-image-container {
    max-width: 100%; 
    /* max-height: 100%; */ /* Allow container to use full height */ /* Removed max-height */
    overflow: hidden; 
}
#shareModal #shareExampleImage {
    display: block; 
    width: 100%; /* Let width fill container */
    /* height: 100%; */ /* Let height fill container */ /* Removed height: 100% */
    height: auto; /* Added height: auto */
    max-height: 510px; /* Remove max-height, rely on container */
    object-fit: contain; /* Ensure image scales down/up to fit container */
    border-radius: 8px; 
    box-shadow: none; /* Remove shadow */
    border: none; /* Remove border */
}

/* Right column (Content - 30%) */
.share-content-right {
    flex: 1; 
    display: flex;
    flex-direction: column; 
    overflow-y: auto; /* Allow vertical scrolling only for this column */
    min-height: 0; /* Added to potentially fix flex height calculation */
    /* max-height: 550px; */ /* Remove fixed max-height, rely on flex stretch */
    padding: 16px; /* Padding inside content area */
    background-color: #2a2a2e; /* Added explicit background color */
    /* position: relative; */ /* Not needed for QR anymore */
}

/* Author Info */
.share-author-info {
    display: flex;
    align-items: center;
    gap: 10px;
}
.share-author-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}
.share-author-details {
    display: flex;
    flex-direction: column;
}
.share-author-name {
    font-weight: 600;
    color: #e0e0e0;
}
.share-author-meta {
    font-size: 0.8rem;
    color: #888;
}

/* Title */
.share-title {
    color: #fff;
    font-weight: 600;
    font-size: 1.1rem;
}

/* Meta Info (Date/Usage) */
.share-meta-info {
    font-size: 0.75rem;
    color: #888;
}

/* Section Label */
.share-section-label {
    display: block;
    font-size: 0.8rem;
    color: #aaa;
    margin-bottom: 5px;
    font-weight: 500;
}

/* Prompt Display */
#shareModal .share-prompt-display {
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 12px;
    font-size: 0.75rem; /* Reduced font size */
    color: #ccc;
    overflow-y: auto; /* Allow scrolling within prompt box if needed */
    white-space: pre-wrap; 
    word-break: break-word;
    width: 100%; 
    text-align: left; 
    box-shadow: none;
    /* flex-grow: 1; */ /* Remove flex-grow */
    min-height: 100px; /* Give it some minimum height */
}

/* Prompt Section now includes Model */
.share-prompt-section {
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px; 
}

#shareModal .share-prompt-section .share-prompt-display {
    background-color: transparent; /* Make inner pre transparent */
    border: none;
    padding: 0;
    margin-bottom: 0;
    min-height: auto;
    max-height: 250px; /* Limit prompt height within card */
    overflow-y: auto; /* Allow scroll */
    color: #ccc; 
}

/* Style for the detail item moved inside prompt section */
.share-prompt-section .share-detail-item {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    /* Ensure it inherits border-top from HTML class */
    /* border-top: 1px solid rgba(255, 255, 255, 0.1); */ 
    /* margin-top: 10px; */ /* Use HTML class mt-2 */
    /* padding-top: 10px; */ /* Use HTML class pt-2 */
}
.share-prompt-section .share-detail-item .share-section-label {
    margin-bottom: 0;
    color: #aaa;
}
.share-prompt-section .share-detail-item span {
    color: #ccc;
    text-align: right;
}

/* Details Section (Model/Ratio) - Remove or comment out */
/* 
.share-details-section {
     margin-top: 15px; 
}
.share-detail-item {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    margin-bottom: 5px;
}
.share-detail-item .share-section-label {
    margin-bottom: 0;
    color: #aaa;
}
.share-detail-item span {
    color: #ccc;
    text-align: right;
}
*/

/* Action Buttons Container */
.share-actions {
    display: flex;
    justify-content: flex-end; /* Align buttons right */
    gap: 10px;
    margin-top: auto; /* Push actions to bottom */
    padding-top: 20px; /* Space above buttons */
    border-top: 1px solid #444; /* Separator line above buttons */
    margin-left: -25px; /* Extend separator across padding */
    margin-right: -25px; /* Extend separator across padding */
    padding-left: 25px; /* Restore padding for buttons */
    padding-right: 25px; /* Restore padding for buttons */
}
.btn-icon-text {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Remove QR Code Styles */
/* #shareModal .qr-code-container { ... } */

/* Remove Footer Styles */
/* #shareModal .modal-footer { ... } */

/* --- 新增：分享模态框二维码样式 --- */
#shareQrCodeContainer {
    /* padding: 10px; */ /* Removed padding */
    /* background-color: rgba(0, 0, 0, 0.15); */ /* Removed background */
    /* border-radius: 6px; */ /* Removed radius */
    /* border: 1px solid #444; */ /* Removed border */
    /* margin-bottom: 15px; */ /* Removed margin-bottom */
    flex-shrink: 0; /* Prevent QR code from shrinking */
}

#shareQrCodeContainer img {
    display: block; /* Remove extra space below image */
    /* margin: 0 auto; */ /* Centering handled by parent */
    background-color: #2a2a2e; /* Ensure background matches modal */
    /* Small padding around the QR code - Reduced */
    border-radius: 4px; /* Slight rounding */
}
/* --- End: 分享模态框二维码样式 --- */

/* --- 新增：自定义级联模型选择器下拉菜单毛玻璃效果 --- */
#cascadeModelSelectMenu {
    background-color: rgba(40, 40, 45, 0.85) !important; /* 半透明深色背景 */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15); /* 细边框 */
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4); /* 添加阴影 */
    border-radius: 8px; /* 圆角 */
}

/* 确保下拉项文本清晰 */
#cascadeModelSelectMenu .dropdown-item {
    color: #e0e0e0;
}

#cascadeModelSelectMenu .dropdown-item:hover,
#cascadeModelSelectMenu .dropdown-item:focus {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
}

/* 返回按钮图标颜色 */
#cascadeModelSelectMenu .dropdown-item[data-action="back"] i {
    color: #adb5bd; /* 可以调整返回图标颜色 */
}

/* --- End: 分享模态框样式 --- */ 

/* --- Specific Modal Width Override --- */
#myExamplesModal .modal-dialog.modal-xl,
#likedExamplesModal .modal-dialog.modal-xl { /* Apply to both modals */
    max-width: 90%; /* Override Bootstrap's max-width for modal-xl */
} 

.timeline-date {
    font-size: 0.8rem;
    color: #6c757d;
    position: absolute;
    top: 3px;
    left: 0; /* Position relative to the item's left padding */
    width: 85px; /* Increased width for the date */
    text-align: right;
    margin-left: -95px; /* Adjusted margin to pull date left */
}
/* Adjust left position for smaller screens if needed */
@media (max-width: 768px) {
    .timeline-date {
        margin-left: -105px; /* Adjusted margin for smaller screens */
    }
} 

/* --- 新增：管理中心样式 --- */
.admin-nav .list-group-item {
    border-radius: 8px;
    margin-bottom: 8px;
    background-color: rgba(255, 255, 255, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #adb5bd;
}

.admin-nav .list-group-item:last-child {
    margin-bottom: 0;
}

.admin-nav .list-group-item.active {
    background: linear-gradient(45deg, #4a4a4a, #2a2a2a); /* Match primary button */
    border-color: rgba(255, 255, 255, 0.15);
    color: #fff;
    font-weight: 600;
}

.admin-nav .list-group-item:hover {
    background-color: rgba(255, 255, 255, 0.08);
    color: #e0e0e0;
}

.admin-table th,
.admin-table td {
    font-size: 0.9rem;
    white-space: nowrap; /* Prevent wrapping in table cells initially */
}

.admin-table td {
     max-width: 200px; /* Limit max width for content like email */
     overflow: hidden;
     text-overflow: ellipsis;
     white-space: nowrap; /* Ensure ellipsis works */
}

/* Allow wrapping for specific columns if needed */
/* .admin-table td:nth-child(X) { white-space: normal; } */

.admin-table .btn-sm {
    padding: 0.2rem 0.5rem; /* Smaller padding for action buttons */
    font-size: 0.8rem;
}

#adminUsersPagination .page-link {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #adb5bd;
}
#adminUsersPagination .page-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
}
#adminUsersPagination .page-item.active .page-link {
    background-color: #333; /* Or match primary button gradient */
    border-color: rgba(255, 255, 255, 0.15);
    color: #fff;
}
#adminUsersPagination .page-item.disabled .page-link {
    background-color: rgba(255, 255, 255, 0.02);
    color: #6c757d;
    border-color: rgba(255, 255, 255, 0.08);
}
/* --- End: 管理中心样式 --- */ 

/* --- 新增：AI 图片生成结果区域样式 --- */
.ai-generation-result-container {
    /* 可以添加一些外边距或内边距 */
}

.prompt-display-area {
    background-color: rgba(255, 255, 255, 0.03);
    padding: 10px 15px;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.08);
    font-size: 0.9rem;
}

.image-grid {
    display: grid;
    /* grid-template-columns: repeat(2, 1fr); */ /* 创建一个 2x2 网格 */
    grid-template-columns: repeat(4, 1fr); /* 改为 4 列 */
    gap: 10px; /* 可以适当减小间距 */
}

.generated-image-thumbnail {
    width: 100%;
    height: auto; /* 保持图片比例 */
    /* aspect-ratio: 1 / 1; */ /* 移除强制1:1比例 */
    /* object-fit: cover; */ /* 移除裁剪 */
    border-radius: 6px; /* 可以稍微减小圆角 */
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.generated-image-thumbnail:hover {
    transform: scale(1.03);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}
/* --- 结束：AI 图片生成结果区域样式 --- */

/* AI Generation History Styles */
#aiHistoryList .history-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1.5rem; /* 增大条目间距 */
    background-color: var(--card-bg-color);
    /* display: flex; */ /* 移除 flex */
    /* flex-direction: column; */ /* 移除 */
    /* gap: 0.8rem; */ /* 移除 */
}

/* 新增：历史记录项头部 */
#aiHistoryList .history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem; /* 头部和提示词间距 */
}

#aiHistoryList .history-item-title {
    font-weight: 600;
    color: #e0e0e0;
    font-size: 0.95rem;
    display: flex; /* 让图标和文字对齐 */
    align-items: center;
    gap: 0.4rem; /* 图标和文字间距 */
}

#aiHistoryList .history-item-date {
    font-size: 0.8rem;
    color: #888; /* 日期颜色更暗 */
}

/* 修改：提示词样式 */
#aiHistoryList .history-prompt {
    font-weight: normal; /* 取消加粗 */
    margin-bottom: 0.75rem; /* 提示词和图片间距 */
    word-break: break-word;
    color: #ccc; /* 提示词颜色稍暗 */
    line-height: 1.5;
}

/* 新增：提示词后面的小标签样式 */
#aiHistoryList .history-prompt .badge {
    font-size: 0.7em; /* 标签字体更小 */
    padding: 0.2em 0.5em;
    margin-left: 0.5rem; /* 与提示词或其他标签的间距 */
    vertical-align: middle; /* 垂直居中 */
    background-color: rgba(255, 255, 255, 0.15) !important; /* 使用浅灰背景，覆盖默认 */
    color: #adb5bd !important; /* 标签文字颜色 */
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: normal;
}

/* 图片网格 */
#aiHistoryList .history-images {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
    margin-bottom: 0.8rem;
}

#aiHistoryList .history-images img {
    width: 100%;
    height: auto;
    aspect-ratio: 1 / 1;
    object-fit: cover;
    border-radius: 6px;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform 0.2s ease;
}

#aiHistoryList .history-images img:hover {
    transform: scale(1.05);
}

/* 移除旧的 info 和 details 样式 */
/* #aiHistoryList .history-info { ... } */
/* #aiHistoryList .history-details { ... } */

/* 操作按钮 */
#aiHistoryList .history-actions {
    margin-top: 0.5rem; /* 调整与图片间距 */
}

#aiHistoryList .history-actions button {
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
}

/* 分页 */
#aiHistoryPagination button {
    margin: 0 0.25rem;
}

/* Image Detail Modal Styles */
#imageDetailModal .modal-body img {
    background-color: rgba(0,0,0,0.2); /* Add a subtle background in case of transparency */
}

#modalPromptDisplay {
    max-height: 100px; /* Limit prompt display height */
    overflow-y: auto; /* Allow scrolling for long prompts */
    white-space: pre-wrap; /* Preserve line breaks */
    word-break: break-word;
    color: #ccc;
}

/* AI 生成历史记录项样式 */
.ai-history-item {
    border: 1px solid var(--bs-border-color-translucent);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: rgba(var(--bs-body-bg-rgb), 0.5);
    display: flex;
    flex-direction: column;
}

.ai-history-item .image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); /* 调整minmax适应 */
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.ai-history-item .image-container {
    position: relative;
    overflow: hidden;
    border-radius: 0.375rem; /* Slightly smaller radius for grid items */
    cursor: pointer;
    aspect-ratio: 1 / 1; /* 保持图片容器为方形 */
}

.ai-history-item .image-container img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover; /* 裁剪以填充容器 */
    transition: transform 0.3s ease;
}

.ai-history-item .image-container:hover img {
    transform: scale(1.05);
}

.ai-history-item .history-prompt {
    font-size: 0.85rem;
    color: var(--bs-secondary-color);
    margin-bottom: 0.5rem;
    white-space: pre-wrap; /* Allow wrapping */
    word-break: break-word; /* Break long words */
    max-height: 6em; /* Limit initial height */
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4; /* Show max 4 lines */
    -webkit-box-orient: vertical;
}

.ai-history-item .history-meta {
    font-size: 0.75rem;
    color: var(--bs-tertiary-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-history-item .history-actions button {
    font-size: 0.75rem;
    padding: 0.1rem 0.4rem; /* Smaller padding */
}

/* 图片详情模态框 (#imageDetailModal) 样式 */
#imageDetailModal .modal-xl {
    --bs-modal-width: 80%; /* 让模态框更宽 */
    max-width: 1400px; /* 限制最大宽度 */
}

#imageDetailModal .modal-body {
    padding: 0; /* 移除 modal-body 的默认 padding */
}

#imageDetailModal .image-detail-left {
    display: flex;
    align-items: center; /* 垂直居中图片 */
    justify-content: center; /* 水平居中图片 */
    background-color: #1e1e20; /* 给图片区域一个深色背景 */
    padding: 1rem;
    /* 确保图片侧边栏在小屏幕上也合理 */
    max-height: 80vh;
    overflow-y: auto; /* 如果图片太高允许滚动 */
    border-right: 1px solid #444; /* 新增：添加右边框作为分割线 */
}

#imageDetailModal #modalImageView {
    max-width: 100%;
    max-height: 75vh; /* 限制图片最大高度 */
    object-fit: contain; /* 保持图片比例 */
    border-radius: 0.5rem; /* 图片圆角 */
}

#imageDetailModal .image-detail-right {
    padding: 1.5rem; /* 给右侧内容添加内边距 */
    display: flex;
    flex-direction: column;
    max-height: 80vh; /* 限制最大高度 */
    overflow-y: auto; /* 如果内容太多允许滚动 */
    background-color: #2a2a2e; /* 新增：设置右侧背景色，与分享弹窗一致 */
}

/* 新增：参数行样式 (可选，如果需要微调间距) */
/* .detail-parameter-row .col-4 { ... } */ /* 移除之前的 col-4 规则 */

/* 新增：单个参数项（标签左，值右） */
.parameter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.image-detail-label {
    font-weight: 600;
    color: var(--bs-secondary-color);
    /* margin-bottom: 0.25rem; */ /* 移除 bottom margin，因为 flex 会处理对齐 */
    /* display: block; */ /* 不再需要 block */
    font-size: 0.8rem; /* 标签字号稍小 */
    text-transform: uppercase; /* 标签大写 */
    margin-right: 1rem; /* 标签和值之间的间距 */
}

.image-detail-value {
    font-size: 0.95rem;
    color: #e0e0e0; /* 新增：使用一个更亮的颜色 */
    text-align: right; /* 值靠右对齐 */
}

#modalPromptDisplay.image-detail-prompt {
    font-family: var(--bs-font-monospace); /* 使用等宽字体显示提示词 */
    font-size: 0.9rem;
    white-space: pre-wrap; /* 保留换行和空格 */
    word-break: break-word;
    background-color: rgba(var(--bs-tertiary-bg-rgb), 0.5); /* 浅色背景 */
    padding: 0.75rem;
    border-radius: 0.375rem;
    max-height: 30vh; /* 限制提示词区域最大高度 */
    overflow-y: auto; /* 内容过多时滚动 */
    border: 1px solid var(--bs-border-color-translucent);
}

/* 调整模态框 footer */
#imageDetailModal .modal-footer {
    border-top: 1px solid var(--bs-border-color-translucent);
}

/* 新增：右侧信息卡片样式 */
.detail-info-card {
    background-color: rgba(0, 0, 0, 0.2); /* 类似分享弹窗内部卡片背景 */
    border: 1px solid #444; /* 类似分享弹窗内部卡片边框 */
    border-radius: 6px; /* 内部卡片圆角 */
    padding: 1rem; /* 内部卡片内边距 */
    margin-bottom: 1.5rem; /* 与下方隐藏输入框或其他元素的间距 */
}

/* 新增：信息卡片内部的分割线样式 */
.detail-separator {
    border-top: 1px solid rgba(255, 255, 255, 0.1); /* 更细更浅的颜色 */
    margin-top: 1rem; /* 分割线与上方提示词的间距 */
    margin-bottom: 1rem; /* 分割线与下方参数行的间距 */
    opacity: 0.5; /* 让线条更不明显 */
}

/* 新增：参数行样式 (可选，如果需要微调间距) */
.detail-parameter-row .col-4 {
    /* 可以添加额外的样式，比如 text-align: center; 如果需要 */
}

/* --- 新增：用户积分/免费次数标签样式 --- */
.user-credits-tags {
    display: flex;
    align-items: center;
}

.credit-tag {
    display: inline-flex; /* 使用 inline-flex 让图标和文字对齐 */
    align-items: center;
    padding: 0.25rem 0.6rem; /* 调整内边距 */
    font-size: 0.8rem; /* 稍小字体 */
    font-weight: 500;
    line-height: 1;
    color: var(--bs-light); /* 浅色文字 */
    background-color: rgba(var(--bs-secondary-rgb), 0.3); /* 半透明次要背景色 */
    border: 1px solid rgba(var(--bs-secondary-rgb), 0.5);
    border-radius: 0.375rem; /* 圆角 */
    margin-left: 0.5rem; /* 标签间距 */
    white-space: nowrap; /* 防止换行 */
    cursor: default; /* 默认光标 */
}

.credit-tag .bi {
    margin-right: 0.3rem; /* 图标和文字间距 */
    font-size: 0.9em; /* 图标大小 */
}

.credit-tag.free-tag {
    background-color: rgba(var(--bs-success-rgb), 0.3); /* 免费次数用成功色 */
    border-color: rgba(var(--bs-success-rgb), 0.5);
}

/* 可选：悬停时稍微变亮 */
.credit-tag:hover {
   filter: brightness(1.1);
}

/* 当加载中时，显示省略号 */
#credits-balance:empty::before,
#free-generations:empty::before,
#free-generations-per-day:empty::before {
    content: "...";
    opacity: 0.6;
}

/* --- 结束：用户积分/免费次数标签样式 --- */

/* --- AI History Item Actions --- */
.ai-history-image-wrapper {
    position: relative; /* Allows absolute positioning for children */
    display: inline-block; /* Or block, depending on desired layout */
    vertical-align: top; /* Align wrappers nicely if inline-block */
}

.ai-history-actions {
    position: absolute;
    bottom: 5px;
    left: 40px;
    right: 5px;
    background-color: rgba(15, 17, 21, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border-radius: 6px;
    padding: 4px 1px; /* Further reduced left/right padding */
    display: none;
    justify-content: center;
    align-items: center;
    gap: 5px; /* Increased space between icons */
    transition: opacity 0.2s ease-in-out;
    opacity: 0;
    z-index: 10; /* Ensure it's above the image */
}

/* Show actions when hovering over the image wrapper */
.ai-history-image-wrapper:hover .ai-history-actions {
    display: flex; /* Use flex to arrange icons */
    opacity: 1; /* Fade in */
}

.ai-history-actions .btn-action {
    background: none; /* Transparent background */
    border: none;
    color: rgba(255, 255, 255, 0.8); /* Light icon color */
    padding: 2px 4px; /* Smaller padding */
    font-size: 0.9rem; /* Slightly smaller icon */
    line-height: 1; /* Ensure icon vertical alignment */
    cursor: pointer;
    transition: color 0.2s ease;
}

.ai-history-actions .btn-action:hover {
    color: white; /* Brighter color on hover */
}

/* Optional: Style for the history image itself */
.ai-history-image {
    display: block; /* Ensure image behaves like a block */
    max-width: 100%; /* Prevent overflow */
    height: auto; /* Maintain aspect ratio */
    /* Add other styles like border-radius, border if needed */
}

/* --- AI Generation Loading Animation --- */
#aiGenerationLoading {
    display: none; /* Hidden by default */
    /* Mimic history item structure for size/spacing */
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    background-color: var(--card-bg-color);
    min-height: 200px; /* Give it some height */
    position: relative;
    overflow: hidden; /* Important for shimmer */
    display: flex; /* Use flex to center content */
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

#aiGenerationLoading::before { /* Shimmer effect */
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.08),
        transparent
    );
    animation: shimmer 1.8s infinite linear;
    z-index: 1;
}

#aiGenerationLoading p {
    color: #adb5bd;
    font-weight: 500;
    margin-top: 10px;
    z-index: 2; /* Ensure text is above shimmer */
}

/* Optional: Add some simple animated dots */
.loading-dots span {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    margin: 0 3px;
    animation: pulse 1.4s infinite ease-in-out both;
    z-index: 2;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes shimmer {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; } /* Hold at the end before looping */
}

@keyframes pulse {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1.0); }
}

/* --- End AI Generation Loading --- */

/* --- AI Generation & Upscale Loading Animation --- */ /* <-- Renamed Section */
/* General Loading Card Style */
.loading-shimmer-card {
    display: none; /* Hidden by default */
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    background-color: var(--card-bg-color);
    min-height: 200px; /* Give it some height */
    position: relative;
    overflow: hidden; /* Important for shimmer */
    display: flex; /* Use flex to center content */
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

/* Apply base style to specific loading elements */
#aiGenerationLoading,
#upscaleResultLoading { /* <-- Added Upscale ID */
    /* Inherits from .loading-shimmer-card */
}


.loading-shimmer-card::before { /* Shimmer effect */
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.08),
        transparent
    );
    animation: shimmer 1.8s infinite linear;
    z-index: 1;
}

/* 新增：为 loading-shimmer-card 添加内部扫描线 */
.loading-shimmer-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px; /* 扫描线的高度/粗细 */
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(150, 180, 255, 0.6) 40%,
        rgba(180, 220, 255, 0.8) 50%, /* 扫描线核心颜色，亮蓝色 */
        rgba(150, 180, 255, 0.6) 60%,
        transparent 100%
    );
    filter: blur(1px);
    animation: innerScan 2.5s infinite ease-in-out;
    z-index: 2; /* 确保在 shimmer 之上，但在文字和点之下 */
}

@keyframes innerScan {
    0% {
        top: 10%;
        opacity: 0.7;
    }
    50% {
        top: 90%;
        opacity: 0.3;
    }
    100% {
        top: 10%;
        opacity: 0.7;
    }
}
/* 结束：为 loading-shimmer-card 添加内部扫描线 */

.loading-shimmer-card p { /* Style for text within the loading card */
    color: #adb5bd;
    font-weight: 500;
    margin-top: 10px;
    z-index: 3; /* 提高层级，确保在扫描线之上 */
}

/* Optional: Add some simple animated dots */
.loading-dots span {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    margin: 0 3px;
    animation: pulse 1.4s infinite ease-in-out both;
    z-index: 3; /* 提高层级，确保在扫描线之上 */
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes shimmer {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; } /* Hold at the end before looping */
}

@keyframes pulse {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1.0); }
}

/* --- End Loading Animation --- */

/* --- Fix for Custom Filter Dropdowns --- */
/* Ensure dropdown menu can overflow card body */
.card-body {
    /* overflow: visible; */ /* Potentially needed if card-body clips, but test without first */
}

/* --- NEW: Allow filter card to overflow --- */
/* Target the specific card containing the filters if possible, otherwise use a general approach */
#management-tab-pane > .card {
    overflow: visible !important; /* Force overflow visible */
    position: relative; /* Needed for z-index */
    z-index: 5; /* Increase stacking order */
}

/* Style the specific model filter dropdown */
.custom-filter-dropdown { /* <-- Use the common class */
    /* --- 毛玻璃背景 --- */
    background-color: rgba(40, 40, 45, 0.85) !important; /* Use !important to override BS if needed */
    backdrop-filter: blur(12px); /* Increased blur value */
    -webkit-backdrop-filter: blur(12px); /* Increased blur value for Safari */
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);

    /* --- 解决裁切问题 --- */
    max-height: 250px; /* Limit height and enable scroll */
    overflow-y: auto;

    /* --- 提升层级 --- */
    position: absolute; /* z-index requires position other than static */
    z-index: 10; /* Ensure it appears above other content */
}

/* --- 黑白灰悬浮/选中效果 --- */
.custom-filter-dropdown .dropdown-item { /* <-- Use the common class */
    color: #adb5bd; /* Default text color */
    transition: background-color 0.2s ease, color 0.2s ease;
}

.custom-filter-dropdown .dropdown-item:hover,
.custom-filter-dropdown .dropdown-item:focus {
    background-color: rgba(255, 255, 255, 0.08); /* Light gray background on hover */
    color: #f8f9fa; /* White text on hover */
}

.custom-filter-dropdown .dropdown-item.active,
.custom-filter-dropdown .dropdown-item:active { /* Style active state similarly to hover */
    background-color: rgba(255, 255, 255, 0.15); /* Slightly darker gray for active */
    color: #ffffff;
}

/* --- End Custom Filter Dropdowns --- */

/* --- NEW: Hide caret on custom dropdown buttons --- */
.form-select.dropdown-toggle::after {
    display: none !important; /* Hide the default Bootstrap caret */
}
/* --- End Hide caret --- */

/* --- 新增：更酷的卡片生成动画 --- */
@keyframes coolGenerateAppear {
    0% {
        opacity: 0;
        transform: translateY(15px) scale(0.97); /* 从下方轻微缩小开始 */
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1); /* 恢复正常大小和位置 */
    }
}

@keyframes cardScanLineOnce {
    0% {
        left: -20%; /* 从卡片左侧外部开始 */
        opacity: 0;
    }
    10% { /* 光效快速出现 */
        opacity: 0.7; /* 光效最大不透明度 */
    }
    90% { /* 光效在划过时保持可见 */
        opacity: 0.7;
    }
    100% {
        left: 120%; /* 划过到卡片右侧外部 */
        opacity: 0;
    }
}

.newly-generated-item {
    animation: coolGenerateAppear 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    position: relative; /* 为 ::after 伪元素定位 */
    overflow: hidden;   /* 限制 ::after 伪元素在此元素内 */
}

.newly-generated-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: -20%; /* 初始位置，匹配动画起始 */
    width: 15px; /* 光束宽度 */
    height: 100%;
    background: linear-gradient(
        180deg, /* 光束内部的垂直渐变，使其看起来有厚度感 */
        rgba(180, 180, 220, 0) 0%,       /* 顶部透明 */
        rgba(220, 220, 255, 0.5) 30%,   /* 中间部分较亮，略带蓝色 */
        rgba(220, 220, 255, 0.7) 50%,   /* 核心最亮 */
        rgba(220, 220, 255, 0.5) 70%,
        rgba(180, 180, 220, 0) 100%    /* 底部透明 */
    );
    filter: blur(2px); /* 轻微模糊使光束柔和 */
    animation: cardScanLineOnce 0.6s ease-out forwards; /* 动画时长和缓动 */
    animation-delay: 0.05s; /* 主动画开始后一小段时间再开始光效 */
    z-index: 1; /* 确保光效在卡片内容之上，如果需要光效在内容之下，可设为 -1 并确保卡片有背景色 */
    pointer-events: none; /* 确保光效不影响鼠标交互 */
}
/* --- 结束：更酷的卡片生成动画 --- */

/* --- 新增：AI 图片生成结果区域样式 --- */

/* 图生视频功能的样式 */

/* 首尾帧模式下的样式调整 */
#i2vKf2vModeContainer .drop-zone {
    min-height: 120px;
}

/* 图生视频功能现在统一使用image-drop-zone样式，样式已在上面定义 */

/* 确保图片预览显示正确 */
#i2vPreviewContainer, 
#i2vFirstFramePreviewContainer, 
#i2vLastFramePreviewContainer {
    text-align: center;
    margin-bottom: 15px;
    display: none; /* 默认隐藏，由JS控制显示 */
}

#i2vPreviewImg, 
#i2vFirstFramePreviewImg, 
#i2vLastFramePreviewImg {
    max-height: 200px;
    max-width: 100%;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#aiExamplesList .example-card {
    width: calc(50% - 15px); /* 假设 gutter 是 15px，这里每边减去一半的 gutter，或者根据实际情况调整 */
    margin-bottom: 15px; /* 也可以在这里设置底部间距，替代 Masonry 的 gutter 或与之配合 */
}

/* 在小屏幕上，可能希望是单列 */
@media (max-width: 768px) { /* 这是一个示例断点，根据你的需求调整 */
    #aiExamplesList .example-card {
        width: 100%;
    }
}

.relevant-author-avatar {
    width: 24px; /* 或其他合适的大小 */
    height: 24px;
    border-radius: 50%;
    object-fit: cover; /* 确保图片不变形 */
}
.relevant-author-avatar-placeholder {
    font-size: 24px; /* 匹配头像大小 */
    color: #6c757d; /* Bootstrap secondary color */
}
.relevant-author-name {
    font-size: 0.8rem;
    color: #adb5bd; /* Bootstrap muted color or similar */
}
.relevant-card-actions { /* 确保页脚内边距合适 */
    padding: 0.5rem 0.75rem;
}

/* admin/css/style.css (或你的主样式文件) */
.dimension-badge {
    position: absolute;
    top: 60px; /* 根据你的 category-badge 和 model-badge 位置调整 */
    right: 10px;
    background-color: rgba(108, 117, 125, 0.8); /* 灰色背景 */
    color: white;
    padding: 0.25em 0.5em;
    font-size: 0.75em;
    border-radius: 0.25rem;
    border: 1px solid rgba(108, 117, 125, 1);
}

/* 新增：图片转3D历史记录卡片样式 */
.history-item-3d {
    position: relative;
    border-radius: 16px;
    margin-bottom: 1.5rem;
    padding: 1.2rem;
    background: transparent;
    border: 1px solid transparent;
    background-clip: padding-box;
    transition: all 0.3s ease;
    /* overflow: hidden; */ /* 移除此行以解决下拉菜单裁切问题 */
}

.history-item-3d::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px; /* Border thickness */
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.08));
    -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    z-index: -1;
    pointer-events: none;
}

.processing-animation {
  width: 150px; /* 你可以根据需要调整 */
  height: 60px;  /* 你可以根据需要调整 */
  display: block;
  margin: 10px auto; /* 居中并添加一些边距 */
}

.processing-animation .conveyor-belt-track {
  stroke: #4A4A5E; /* 深色主题的传送带轨道颜色 */
  stroke-dasharray: 4;
  animation: conveyorDash 1s linear infinite;
}

.processing-animation .conveyor-rollers circle {
  fill: #3A3A4C; /* 滚轮颜色 */
  animation: roll 0.5s linear infinite;
  transform-origin: center;
}

.processing-animation .package {
  /* 包裹颜色已通过SVG fill属性定义 (linearGradient) */
  /* filter: url(#glow); /* SVG中已定义 */
}

.processing-animation .sparkles circle {
   /* 火花/光效颜色已通过SVG fill属性定义 */
}

@keyframes conveyorDash {
  to {
    stroke-dashoffset: -8;
  }
}

@keyframes roll {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 用于包裹旧的加载文本和图标的容器的样式 (如果需要的话) */
.history-item .status-processing-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px;
    min-height: 100px; /* 给动画留出空间 */
}

.history-item .processing-text {
    font-size: 0.9em;
    color: #9FA2B4; /* 浅灰色文字，适合深色背景 */
    margin-top: 5px;
}

/* Tech Loader v2 Styles */
.history-loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;
    width: 100%; /* Ensure it takes full width of its container */
    min-height: 120px; /* Adjusted from 150px to better fit a shorter bar */
    grid-column: 1 / -1; /* Make this container span all columns of the parent grid */
}

.tech-loader-v2 {
    width: 60px;
    height: 60px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    /* animation: tech-loader-v2-rotate 2s linear infinite; */ /* Optional overall rotation */
}

.tech-loader-v2::before {
    content: '';
    width: 30px;
    height: 30px;
    background-color: transparent;
    border: 3px solid #00c6ff; /* Light blue for the diamond */
    transform: rotate(45deg);
    position: absolute;
    animation: tech-loader-v2-pulse 1.5s ease-in-out infinite;
    box-shadow: 0 0 8px rgba(0, 198, 255, 0.5);
}

.tech-loader-v2-bracket {
    width: 15px;
    height: 15px;
    border-color: #29d9d5; /* Teal color for brackets */
    border-style: solid;
    position: absolute;
    opacity: 0.8;
}

.tech-loader-v2-bracket.top-left {
    border-width: 3px 0 0 3px;
    top: 0;
    left: 0;
    animation: tech-loader-v2-bracket-tl 1.5s ease-in-out infinite alternate;
}

.tech-loader-v2-bracket.top-right {
    border-width: 3px 3px 0 0;
    top: 0;
    right: 0;
    animation: tech-loader-v2-bracket-tr 1.5s ease-in-out infinite alternate;
}

.tech-loader-v2-bracket.bottom-left {
    border-width: 0 0 3px 3px;
    bottom: 0;
    left: 0;
    animation: tech-loader-v2-bracket-bl 1.5s ease-in-out infinite alternate;
}

.tech-loader-v2-bracket.bottom-right {
    border-width: 0 3px 3px 0;
    bottom: 0;
    right: 0;
    animation: tech-loader-v2-bracket-br 1.5s ease-in-out infinite alternate;
}

.loading-text-primary {
    color: #e0e0e0; /* Light gray for primary text */
    font-size: 0.9rem;
    margin-top: 15px;
}

.loading-text-secondary {
    color: #9e9e9e; /* Darker gray for secondary text */
    font-size: 0.75rem;
}

/* Keyframe Animations */
@keyframes tech-loader-v2-rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes tech-loader-v2-pulse {
    0%, 100% { transform: rotate(45deg) scale(0.9); opacity: 0.7; border-width: 3px; }
    50% { transform: rotate(45deg) scale(1.1); opacity: 1; border-width: 4px; }
}

@keyframes tech-loader-v2-bracket-tl {
    0%, 100% { top: 0; left: 0; }
    50% { top: -3px; left: -3px; }
}

@keyframes tech-loader-v2-bracket-tr {
    0%, 100% { top: 0; right: 0; }
    50% { top: -3px; right: -3px; }
}

@keyframes tech-loader-v2-bracket-bl {
    0%, 100% { bottom: 0; left: 0; }
    50% { bottom: -3px; left: -3px; }
}

@keyframes tech-loader-v2-bracket-br {
    0%, 100% { bottom: 0; right: 0; }
    50% { bottom: -3px; right: -3px; }
}

/* Streaming Light Card Loader Styles */
.streaming-light-card-loader {
    position: relative;
    width: 100%; /* Occupy full width of its parent (.history-loading-indicator) */
    min-height: 80px; /* Adjusted for a rectangular bar, was 120px */
    /* aspect-ratio: 1 / 1; */ /* REMOVED to make it rectangular */
    background-color: rgba(30, 30, 35, 0.7); /* Dark card background */
    border-radius: 8px; /* Match image border-radius */
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 15px;
    overflow: hidden; /* Crucial for the ::after pseudo-element animation */
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.streaming-light-card-loader::after {
    content: '';
    position: absolute;
    top: 0;
    left: -150%; /* Start off-screen to the left */
    width: 70%;  /* Width of the light beam, adjust for desired effect */
    height: 100%;
    background: linear-gradient(
        to right,
        transparent 0%,
        rgba(255, 255, 255, 0.25) 40%, /* White light, semi-transparent */
        rgba(255, 255, 255, 0.4) 50%, /* Brighter white core */
        rgba(255, 255, 255, 0.25) 60%,
        transparent 100%
    );
    transform: skewX(-25deg); /* Angle the light beam */
    animation: streaming-light-effect 2.5s linear infinite;
    filter: blur(2px); /* Soften the light edges */
}

/* Text inside the loader needs to be above the light effect */
.streaming-light-card-loader .loading-text-primary,
.streaming-light-card-loader .loading-text-secondary {
    position: relative; /* Ensure z-index works */
    z-index: 1;
    color: #e0e0e0;
}

.streaming-light-card-loader .loading-text-primary {
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.streaming-light-card-loader .loading-text-secondary {
    font-size: 0.75rem;
    color: #9e9e9e;
}

@keyframes streaming-light-effect {
    0% {
        left: -150%;
        opacity: 0.5;
    }
    50% {
        opacity: 0.8; /* Light becomes more prominent mid-way */
    }
    100% {
        left: 150%; /* End off-screen to the right */
        opacity: 0.5;
    }
}

/* 新增：图片比例选择器样式 */
#aiRatioSelector .ratio-button {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 60px;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.1s ease;
}

#aiRatioSelector .ratio-button.active{
    background: rgba(255, 255, 255,1);
    border-color: rgba(0, 0, 0, 0.5);
    color: #333;
}

/* 选中状态下的比例图标样式 */
#aiRatioSelector .ratio-button.active .ratio-icon {
    border: 1px solid #000000 !important; /* 黑色边框 */
    background: transparent !important; /* 透明背景 */
}

/* 比例图标样式 */
.ratio-icon {
    display: inline-block;
    width: 24px;
    height: 18px;
    margin-right: 6px;
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: 2px;
    position: relative;
}

/* 各种比例的形状 */
.ratio-21-9 {
    width: 21px;
    height: 9px;
}

.ratio-16-9 {
    width: 20px;
    height: 11px;
}

.ratio-3-2 {
    width: 18px;
    height: 12px;
}

.ratio-4-3 {
    width: 16px;
    height: 12px;
}

.ratio-1-1 {
    width: 16px;
    height: 16px;
}

.ratio-3-4 {
    width: 12px;
    height: 16px;
}

.ratio-2-3 {
    width: 12px;
    height: 18px;
}

.ratio-9-16 {
    width: 11px;
    height: 20px;
}

/* 按钮悬停效果 */
#aiRatioSelector .ratio-button:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* 新增：Midjourney操作按钮样式 */
.mj-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 8px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.mj-buttons-container .btn {
    min-width: 40px;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
}

.mj-buttons-container .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 可点击的积分标签特殊样式 */
#credit-tag-clickable:hover {
    filter: brightness(1.2);
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

#credit-tag-clickable:active {
    transform: translateY(0);
}


.btn-secondary {
    background-color: transparent !important; /* 黑色背景 */
    border-color: #808080;    /* 灰色边框 */
    color: #FFFFFF;           /* 白色文字，如果需要的话 */
}

.btn-secondary:hover {
    background-color: #1a1a1a; /* 鼠标悬停时略微变亮的黑色背景 */
    border-color: #909090;    /* 鼠标悬停时略微变亮的灰色边框 */
    color: #FFFFFF;
}

/* 隐藏通知图标的下拉三角形 */
#notificationDropdown.dropdown-toggle::after {
    display: none !important;
}
/* 自定义深色下拉菜单样式 */
.custom-dropdown-dark {
    background-color: #121212; /* 背景色 - 可以调整为你想要的颜色 */
    border-color: rgba(255, 255, 255, 0.15);
    color: #fff; /* 文字颜色 */
  }
  
  .custom-dropdown-dark .dropdown-item {
    color: #ddd; /* 下拉项文字颜色 */
  }
  
  .custom-dropdown-dark .dropdown-item:hover,
  .custom-dropdown-dark .dropdown-item:focus {
    background-color: #444; /* 悬停时的背景色 */
    color: #fff; /* 悬停时的文字颜色 */
  }
  
  .custom-dropdown-dark .dropdown-divider {
    border-color: rgba(255, 255, 255, 0.1); /* 分隔线颜色 */
  }
/* 只针对评论通知的样式 */
#commentsNotificationModalBody .list-group-item.list-group-item-action {
    background-color: transparent !important; /* 透明背景 */
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #fff !important;
    margin-bottom: 5px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

#commentsNotificationModalBody .list-group-item.list-group-item-action:hover {
    background-color: rgba(0, 0, 0, 0.1) !important; /* 悬停时半透明白色背景 */
    border-color: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* 印花提取历史记录项样式 */
#patternExtractionHistoryList .history-item {
    border-radius: 10px;
    padding: 12px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: calc(33.33% - 10px);
    position: relative;
    overflow: hidden;
    display: inline-block;
}

#patternExtractionHistoryList .history-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.2);
}

#patternExtractionHistoryList .history-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 0;
    pointer-events: none;
}

#patternExtractionHistoryList .history-images {
    width: 100%;
    height: 180px;
    overflow: hidden;
    border-radius: 6px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.2);
}

#patternExtractionHistoryList .history-images img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
}

#patternExtractionHistoryList .history-item:hover .history-images img {
    transform: scale(1.05);
}

#patternExtractionHistoryList .history-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 4px;
    max-width: 100%;
}

#patternExtractionHistoryList .history-item:hover .history-actions {
    opacity: 1;
}

#patternExtractionHistoryList .history-actions button,
#patternExtractionHistoryList .history-actions a {
    padding: 4px 8px;
    font-size: 0.8rem;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    margin: 0 2px;
    transition: all 0.2s ease;
}

#patternExtractionHistoryList .history-actions button:hover,
#patternExtractionHistoryList .history-actions a:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    background-color: rgba(0, 0, 0, 0.7);
}

/* 响应式布局 */
@media (max-width: 1200px) {
    #patternExtractionHistoryList .history-item {
        width: calc(50% - 8px);
    }
}

@media (max-width: 768px) {
    #patternExtractionHistoryList .history-item {
        width: 100%;
    }
}

/* 电商工具卡片悬停效果 */
.ecommerce-tool-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    cursor: pointer;
}

#patternExtractionHistoryList .history-item-content {
    position: relative;
    z-index: 1;
    padding: 0 5px;
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 0 0 5px 5px;
}

#patternExtractionHistoryList .history-item-title {
    margin: 0;
    padding: 8px 0 2px;
    font-size: 0.9rem;
    font-weight: 500;
    color: #fff;
}

#patternExtractionHistoryList .history-item-date {
    display: block;
    padding-bottom: 5px;
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
}

/* 确保Tab内容不会重叠显示 */
.tab-pane:not(.active) {
    display: none !important;
}

/* 强制处理闪光灯和Flux智能编辑的冲突 */
#mainTabContent #flash-light-pane,
#mainTabContent #flux-kontext-pane {
    position: relative;
    z-index: 1;
}

/* 当Flux Tab激活时，确保闪光灯内容不可见 */
#flux-kontext-tab.active + li #flash-light-tab:not(.active) ~ #mainTabContent #flash-light-pane {
    display: none !important;
    z-index: -1;
}

/* 当闪光灯Tab激活时，确保Flux内容不可见 */
#flash-light-tab.active ~ #mainTabContent #flux-kontext-pane {
    display: none !important;
    z-index: -1;
}