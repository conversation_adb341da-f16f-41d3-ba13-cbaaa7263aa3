/* 毛玻璃效果样式 */
.badge.glassmorphism {
    background: rgba(40, 40, 40, 0.5) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    color: white;
    padding: 0.5em 1em;
    border-radius: 50px;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.badge.glassmorphism.bg-success {
    background: rgba(40, 167, 69, 0.6) !important;
    border-color: rgba(40, 167, 69, 0.8);
}

.badge.glassmorphism.bg-danger {
    background: rgba(220, 53, 69, 0.6) !important;
    border-color: rgba(220, 53, 69, 0.8);
}

.badge.glassmorphism.bg-info {
    background: rgba(23, 162, 184, 0.6) !important;
    border-color: rgba(23, 162, 184, 0.8);
}

.badge.glassmorphism.bg-warning {
    background: rgba(255, 193, 7, 0.6) !important;
    border-color: rgba(255, 193, 7, 0.8);
}

.badge.glassmorphism.bg-secondary {
    background: rgba(108, 117, 125, 0.6) !important;
    border-color: rgba(108, 117, 125, 0.8);
}

.badge.glassmorphism:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 14px rgba(0, 0, 0, 0.2);
}

/* 毛玻璃按钮样式 */
.glassmorphism-btn {
    background: rgba(40, 40, 40, 0.5) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18) !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    color: white !important;
    transition: all 0.3s ease;
}

.glassmorphism-btn:hover {
    background: rgba(60, 60, 60, 0.7) !important;
    transform: translateY(-2px);
    box-shadow: 0 7px 14px rgba(0, 0, 0, 0.2);
}

/* 历史记录项毛玻璃效果 */
.i2v-history-item {
    position: relative;
    border-radius: 16px;
    margin-bottom: 1.5rem;
    padding: 1.2rem;
    background: transparent;
    border: 1px solid transparent;
    background-clip: padding-box;
    transition: all 0.3s ease;
}

.i2v-history-item::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px; /* Border thickness */
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.08));
    -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    z-index: -1;
    pointer-events: none;
}

.i2v-history-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* 缩略图样式 */
.i2v-history-item img, 
.i2v-history-item .thumbnail-placeholder {
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.i2v-history-item:hover img,
.i2v-history-item:hover .thumbnail-placeholder {
    border-color: rgba(255, 255, 255, 0.25) !important;
    transform: scale(1.02);
}

/* 提示文本样式 */
.prompt-text {
    white-space: normal;
    word-break: break-word;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3; /* 最多显示3行 */
    -webkit-box-orient: vertical;
    line-height: 1.4;
    max-height: 4.2em; /* 3行的高度 */
    color: #ccc;
    transition: max-height 0.3s ease;
}

.i2v-history-item:hover .prompt-text {
    -webkit-line-clamp: 6; /* 悬停时显示更多行 */
    max-height: 8.4em; /* 6行的高度 */
} 