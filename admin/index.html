<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="dark">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>像素星云AI助手</title>
        <!-- Local Bootstrap CSS -->
        <link href="libs/css/bootstrap.min.css" rel="stylesheet">
        <!-- Local Bootstrap Icons CSS -->
        <link href="libs/css/bootstrap-icons.min.css" rel="stylesheet">
        <!-- Local Tagify CSS -->
        <link href="libs/css/tagify.min.css" rel="stylesheet">
        <link href="css/style.css?=1.56" rel="stylesheet">
        <link href="css/ai-chat.css?=1.05" rel="stylesheet"> <!-- 新增：AI 聊天助手 CSS -->
        <link href="css/example-detail-modal.css?=1.05" rel="stylesheet"> <!-- 新增：案例详情模态框 CSS -->
        <link href="css/gpt4o-image-edit.css" rel="stylesheet"> <!-- 新增：GPT-4o 图片编辑 CSS -->
        <link href="css/ai-generate.css?=1.06" rel="stylesheet"> <!-- 新增：AI 图片生成和历史记录删除功能 CSS -->
        <!-- 在head标签内添加 -->
        <link rel="stylesheet" href="css/image-to-3d-glassmorphism.css">
        <link rel="stylesheet" href="css/image-to-video-glassmorphism.css">
        <!-- 在head中添加CSS引用，放在其他CSS引用后面 -->
        <link href="css/flux-kontext.css?=1.04" rel="stylesheet"> <!-- 新增：Flux图片处理 CSS -->
    <!-- --------------------------------------- -->
    <!-- 添加图片压缩库 -->
    <!--<script src="js/debug-protection.js"></script>-->
    <script src="libs/js/browser-image-compression.min.js"></script>
</head>
<body>
    <div class="grid-mask"></div> <!-- Add mouse effect mask -->
    <div class="container">
        <div class="page-header">
            <h1>像素星云</h1>
            <div class="header-actions">
                <!-- 新增：积分和免费次数显示区域 -->
                <div id="user-credits-tooltip-wrapper" 
                     class="user-credits-tags me-3" 
                     data-bs-toggle="tooltip" 
                     data-bs-placement="bottom" 
                     title="加载中..." 
                     data-bs-trigger="hover">
                    <span class="credit-tag" id="credit-tag-clickable" style="cursor: pointer;" title="点击购买积分">
                        <i class="bi bi-coin"></i> <span id="credits-balance">...</span>
                    </span>
                </div>
                <!-- 通知中心 -->
                
                <div class="dropdown me-3">
                    <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" id="dropdownUser" data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="images/default-avatar.png" alt="用户头像" width="32" height="32" class="rounded-circle me-2" id="userAvatar">
                        <strong id="userDisplayName">用户名</strong>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark text-small shadow" aria-labelledby="dropdownUser">
                        <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#profileModal"><i class="bi bi-person-circle me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#myExamplesModal"><i class="bi bi-collection me-2"></i>我的案例</a></li>
                        <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#likedExamplesModal"><i class="bi bi-heart-fill me-2"></i>我点赞的案例</a></li>
                        
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()"><i class="bi bi-box-arrow-right me-2"></i>退出</a></li>
                    </ul>
                </div>
                <div class="dropdown me-3">
                    <a href="#" class="d-block link-light text-decoration-none position-relative dropdown-toggle" id="notificationDropdown" data-bs-toggle="dropdown" aria-expanded="false" title="通知">
                        <i class="bi bi-bell fs-5"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notification-badge" style="display: none;">
                            0
                        </span>
                    </a>
                    <ul class="dropdown-menu custom-dropdown-dark text-small shadow" aria-labelledby="notificationDropdown" id="notificationMenuList">
                        <li><a class="dropdown-item d-flex justify-content-between align-items-center" href="#" id="showCommentsNotifications">
                            <span>
                                <i class="bi bi-chat-left-text-fill me-2"></i>
                                收到的评论
                            </span>
                            <span class="badge bg-primary rounded-pill" id="comment-notification-count" style="display: none;"></span>
                        </a></li>
                        <li><a class="dropdown-item" href="changelog/changelog.html">
                            <i class="bi bi-journal-text me-2"></i>
                            更新日志
                        </a></li>
                    </ul>
                </div>
                <div id="managementActions" style="display: block;">
                
                    <!-- 新增加群按钮 -->
                    <button type="button" class="btn btn-outline-success me-2" data-bs-toggle="modal" data-bs-target="#joinGroupModal" title="加入官方社群获取帮助">
                        <i class="bi bi-wechat"></i> 
                    </button>
                    <!-- 新增：跳转到JAAZ画布系统的按钮 -->
                    <button id="jump-to-ai-canvas" class="btn btn-primary">AI画布</button>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addExampleModal">
                        <i class="bi bi-plus-lg"></i> 添加案例
                    </button>
                </div>
            </div>
        </div>

        <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="management-tab" data-bs-toggle="tab" data-bs-target="#management-tab-pane" type="button" role="tab" aria-controls="management-tab-pane" aria-selected="true">
                    <i class="bi bi-collection me-1"></i> 案例管理
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="reverse-prompt-tab" data-bs-toggle="tab" data-bs-target="#reverse-prompt-tab-pane" type="button" role="tab" aria-controls="reverse-prompt-tab-pane" aria-selected="false">
                    <i class="bi bi-image-alt me-1"></i> 提示词反推 (图生文)
                </button>
            </li>
            <!-- 新增：AI 图片生成 Tab -->
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="ai-generate-tab" data-bs-toggle="tab" data-bs-target="#ai-generate-tab-pane" type="button" role="tab" aria-controls="ai-generate-tab-pane" aria-selected="false">
                    <i class="bi bi-stars me-1"></i> AI 图片生成
                </button>
            </li>
            <!-- 新增：管理中心 Tab (初始隐藏) -->
            <li class="nav-item" role="presentation" id="adminManagementTabLink" style="display: none;">
                <button class="nav-link" id="admin-management-tab" data-bs-toggle="tab" data-bs-target="#admin-management-tab-pane" type="button" role="tab" aria-controls="admin-management-tab-pane" aria-selected="false">
                    <i class="bi bi-shield-lock me-1"></i> 管理中心
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="creative-upscale-tab" data-bs-toggle="tab" data-bs-target="#creative-upscale-pane" type="button" role="tab" aria-controls="creative-upscale-pane" aria-selected="false">
                    <i class="bi bi-arrows-angle-expand me-1"></i>清晰放大
                </button>
            </li>
            <!-- 新增：背景移除 Tab -->
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="remove-background-tab" data-bs-toggle="tab" data-bs-target="#remove-background-pane" type="button" role="tab" aria-controls="remove-background-pane" aria-selected="false">
                    <i class="bi bi-eraser-fill me-1"></i> 背景移除
                </button>
            </li>
            <!-- 新增：位图转 SVG Tab -->
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="vectorize-tab" data-bs-toggle="tab" data-bs-target="#vectorize-pane" type="button" role="tab" aria-controls="vectorize-pane" aria-selected="false">
                    <i class="bi bi-bounding-box-circles me-1"></i> 位图转 SVG
                </button>
            </li>
            <!-- 新增：图片转3D模型 Tab -->
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="image-to-3d-tab" data-bs-toggle="tab" data-bs-target="#image-to-3d-pane" type="button" role="tab" aria-controls="image-to-3d-pane" aria-selected="false">
                    <i class="bi bi-cube me-1"></i> 图片转3D
                </button>
            </li>
            <!-- 新增：图生视频 Tab -->
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="image-to-video-tab" data-bs-toggle="tab" data-bs-target="#image-to-video-pane" type="button" role="tab" aria-controls="image-to-video-pane" aria-selected="false">
                    <i class="bi bi-film me-1"></i> 图生视频
                </button>
            </li>
            <!-- 新增：GPT-4o 图片编辑 Tab -->
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="gpt4o-image-edit-tab" data-bs-toggle="tab" data-bs-target="#gpt4o-image-edit-pane" type="button" role="tab" aria-controls="gpt4o-image-edit-pane" aria-selected="false">
                    <i class="bi bi-pencil-square me-1"></i> GPT-4o 图片编辑
                </button>
            </li>
            <!-- 新增：AI 聊天助手 Tab -->
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="ai-chat-assistant-tab" data-bs-toggle="tab" data-bs-target="#ai-chat-assistant-pane" type="button" role="tab" aria-controls="ai-chat-assistant-pane" aria-selected="false">
                    <i class="bi bi-chat-dots-fill me-1"></i> AI 聊天助手
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="remove-anything-tab" data-bs-toggle="tab" data-bs-target="#remove-anything-pane" type="button" role="tab" aria-controls="remove-anything-pane" aria-selected="false">
                    <i class="bi bi-scissors me-1"></i> 移除万物
                </button>
            </li>
            <!-- 在现有的导航标签之后添加Flux图片处理标签 -->
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="flux-kontext-tab" data-bs-toggle="tab" data-bs-target="#flux-kontext-pane" type="button" role="tab" aria-controls="flux-kontext-pane" aria-selected="false">
                    <i class="bi bi-palette-fill me-1"></i> Flux智能编辑图片
                </button>
            </li>
            <!-- 新增：电商专栏标签 -->
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="ecommerce-tab" data-bs-toggle="tab" data-bs-target="#ecommerce-pane" type="button" role="tab" aria-controls="ecommerce-pane" aria-selected="false">
                    <i class="bi bi-shop me-1"></i> 电商专栏
                </button>
            </li>
            <!-- 新增：闪光灯标签 -->
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="flash-light-tab" data-bs-toggle="tab" data-bs-target="#flash-light-pane" type="button" role="tab" aria-controls="flash-light-pane" aria-selected="false">
                    <i class="bi bi-lightning me-1"></i> AI补光&美颜
                </button>
            </li>
        </ul>

        <div class="tab-content" id="mainTabContent">

            <div class="tab-pane fade show active" id="management-tab-pane" role="tabpanel" aria-labelledby="management-tab" tabindex="0">
                <div class="card mb-4">
                    <div class="card-header">
                        筛选器
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="categoryFilter" class="form-label">分类筛选</label>
                                <!-- 新增：分类筛选 Bootstrap Dropdown -->
                                <div class="dropdown">
                                    <button class="form-select text-start dropdown-toggle" type="button" id="categoryFilterBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                        全部分类
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="categoryFilterBtn" id="categoryFilterMenu">
                                        <!-- 分类选项将由 JS 填充 -->
                                        <li><a class="dropdown-item active" href="#" data-value="">加载中...</a></li>
                                    </ul>
                                    <input type="hidden" id="categoryFilterValue" value=""> <!-- 隐藏 input 存储值 -->
                                </div>
                                <!-- 结束：分类筛选 -->
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="modelFilter" class="form-label">模型筛选</label>
                                <!-- 新增：Bootstrap Dropdown 结构 -->
                                <div class="dropdown">
                                    <button class="form-select text-start dropdown-toggle" type="button" id="modelFilterBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                        全部模型 <!-- 默认显示文本 -->
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="modelFilterBtn" id="modelFilterMenu">
                                        <li><a class="dropdown-item active" href="#" data-value="">全部模型</a></li> <!-- 默认选中 -->
                                        <li><a class="dropdown-item" href="#" data-value="MJ">MJ</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="FLUX">FLUX</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="FLUX-多图">FLUX-多图</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="SDXL">SDXL</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="超级生图">超级生图</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="高级生图">高级生图</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="GPT4O">GPT4O</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="GPT4O-编辑">GPT4O-编辑</a></li> <!-- 新增 -->
                                        <li><a class="dropdown-item" href="#" data-value="其他">其他</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="_NULL_">未指定</a></li>
                                    </ul>
                                    <input type="hidden" id="modelFilterValue" value=""> <!-- 隐藏 input 存储值 -->
                                </div>
                                <!-- 结束：Bootstrap Dropdown 结构 -->
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="sortFilter" class="form-label">排序方式</label>
                                <!-- 新增：排序方式 Bootstrap Dropdown -->
                                <div class="dropdown">
                                    <button class="form-select text-start dropdown-toggle" type="button" id="sortFilterBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                        最新发布
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="sortFilterBtn" id="sortFilterMenu">
                                        <li><a class="dropdown-item active" href="#" data-value="date_desc">最新发布</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="likes_desc">最多点赞</a></li>
                                    </ul>
                                    <input type="hidden" id="sortFilterValue" value="date_desc"> <!-- 隐藏 input 存储值 -->
                                </div>
                                <!-- 结束：排序方式 -->
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <div style="display: none;">
                                    <input type="text" name="fake_username_for_autocomplete_prevention" autocomplete="username">
                                    <input type="password" name="fake_password_for_autocomplete_prevention" autocomplete="current-password">
                                </div>
                                <label for="searchInput" class="form-label">搜索</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchInput" placeholder="输入标题、提示词或标签进行搜索" autocomplete="new-password">
                                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="examples-container">
                    <div class="loading"> <!-- Initial Loading State -->
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <span>加载案例中...</span>
                    </div>
                    <!-- Example cards will be dynamically added here -->
                    <!-- Placeholder for empty state -->
                    <div class="empty-state" style="display: none;">
                        <i class="bi bi-journal-x"></i>
                        <span>没有找到符合条件的案例。</span>
                    </div>
                </div>
            </div>

            <div class="tab-pane fade" id="reverse-prompt-tab-pane" role="tabpanel" aria-labelledby="reverse-prompt-tab" tabindex="0">
                <div class="row">
                    <div class="col-md-5 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <span>上传图片并选择模型</span> <!-- Wrap title in span for flex alignment -->
                                <div class="d-flex align-items-center"> <!-- Wrapper for key icon and dropdown -->
                                    <!-- API Key Modal Trigger Button -->
                                    <button class="btn btn-icon btn-sm btn-outline-secondary me-2" type="button" data-bs-toggle="modal" data-bs-target="#apiKeyModal" title="设置模型 API Key">
                                        <i class="bi bi-key-fill"></i>
                                    </button>
                                    <!-- Custom Cascade Model Selector -->
                                    <div class="dropdown"> 
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="cascadeModelSelectBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                            选择模型 
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="cascadeModelSelectBtn" id="cascadeModelSelectMenu">
                                            <!-- Dropdown items will be populated by JS -->
                                            <li><a class="dropdown-item" href="#">加载中...</a></li>
                                        </ul>
                                        <input type="hidden" id="selectedModelValueInput"> <!-- Hidden input to store value -->
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- 图片上传区域 -->
                                <div class="image-drop-zone mb-3" id="reverseImageDropZone">
                                    <i class="bi bi-cloud-arrow-up"></i>
                                    <p>将图片拖拽到此处，或点击选择文件</p>
                                    <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
                                </div>
                                <input class="visually-hidden" type="file" id="reverseImageInput" accept="image/*">
                                <div class="preview-container mt-2" id="reverseImagePreview" style="display: none;">
                                    <img src="" class="preview-img" id="reversePreviewImg">
                                    <div class="mt-2 text-center">
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="removeReverseImage">
                                            <i class="bi bi-trash"></i> 移除图片
                                        </button>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <!-- 选择生成的提示词类型 (恢复) -->
                                <div class="mb-3">
                                    <label class="form-label fw-bold">选择生成的提示词类型:</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="general" id="targetTypeGeneral" checked>
                                        <label class="form-check-label" for="targetTypeGeneral">
                                            通用详细描述
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="mj" id="targetTypeMJ" checked>
                                        <label class="form-check-label" for="targetTypeMJ">
                                            Midjourney 风格
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="sd" id="targetTypeSD" checked>
                                        <label class="form-check-label" for="targetTypeSD">
                                            Stable Diffusion 风格
                                        </label>
                                    </div>
                                </div>

                                <!-- 生成按钮 -->
                                <div class="d-grid mt-4">
                                    <button class="btn btn-primary btn-lg" id="generateReversePromptBtn" disabled>
                                        <i class="bi bi-magic me-2"></i> 开始生成提示词
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-7">
                        <div class="card">
                            <div class="card-header">生成结果</div>
                            <div class="card-body">
                                <div id="reversePromptLoading" style="display: none;">
                                    <div class="loading">
                                        <div class="spinner-border" role="status"></div>
                                        <span>AI 正在分析图片并生成提示词...</span>
                                    </div>
                                </div>
                                <div id="reversePromptError" class="alert alert-danger" style="display: none;" role="alert">
                                    生成失败，请稍后重试或检查图片。
                                </div>
                                <div id="reversePromptResults">
                                    <p class="text-muted text-center" id="resultsPlaceholder">上传图片并点击生成按钮</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 新增：AI 图片生成 Tab 内容 -->
            <div class="tab-pane fade" id="ai-generate-tab-pane" role="tabpanel" aria-labelledby="ai-generate-tab" tabindex="0">
                <div class="row">
                    <div class="col-md-5 mb-4"> <!-- 左侧输入区域 -->
                        <div class="card">
                            <div class="card-header">
                                <!-- 新增：Tab 导航 -->
                                <ul class="nav nav-tabs card-header-tabs" id="aiGenerateLeftPanelTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="generate-params-tab" data-bs-toggle="tab" data-bs-target="#generate-params-pane" type="button" role="tab" aria-controls="generate-params-pane" aria-selected="true">生成参数</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="relevant-examples-tab" data-bs-toggle="tab" data-bs-target="#relevant-examples-pane" type="button" role="tab" aria-controls="relevant-examples-pane" aria-selected="false">相关案例</button>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <!-- 新增：Tab 内容 -->
                                <div class="tab-content" id="aiGenerateLeftPanelTabContent">
                                    <!-- Tab 1: 生成参数 -->
                                    <div class="tab-pane fade show active" id="generate-params-pane" role="tabpanel" aria-labelledby="generate-params-tab" tabindex="0">
                                        <div class="mb-3">
                                            <label for="aiPromptInput" class="form-label">提示词 (Prompt)</label>
                                            <div style="position: relative;"> <!-- 添加相对定位的容器 -->
                                                <textarea class="form-control" id="aiPromptInput" rows="4" placeholder="输入你想要生成的图片描述..." style="padding-right: 100px;"></textarea>
                                                <button class="ai-helper-btn" type="button" id="aiPromptHelperBtn" title="提示词助手">
                                                    提示词助手
                                                </button>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="aiModelSelectBtn" class="form-label">模型</label>
                                                <div class="dropdown">
                                                    <button class="form-select text-start dropdown-toggle" type="button" id="aiModelSelectBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                                        超级生图 V3.0
                                                    </button>
                                                    <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="aiModelSelectBtn" id="aiModelSelectMenu">
                                                        <!-- 选项由JS动态填充 -->
                                                        <li><a class="dropdown-item active" href="#" data-value="super_image_3">超级生图 V3.0</a></li>
                                                        <li><a class="dropdown-item" href="#" data-value="comfyui_imagefx_advanced">高级生图</a></li>
                                                        <li><a class="dropdown-item" href="#" data-value="midjourney">Midjourney</a></li>
                                                        <li><a class="dropdown-item" href="#" data-value="super_image_2">超级生图 V2.1</a></li>
                                                    </ul>
                                                    <input type="hidden" id="aiModelSelectValue" value="super_image_3">
                                                </div>
                                            </div>
                                        </div>
                                        <div id="midjourneyParamsGroup" class="mb-3" style="display: none;">
                                            <div class="mb-3">
                                                <label for="mjModeSelect" class="form-label">Midjourney 模式</label>
                                                <select class="form-select" id="mjModeSelect">
                                                    <option value="fast" selected>Fast</option>
                                                    <option value="relax">Relax</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">垫图 (可选, Imagine专用)</label>
                                                <div class="image-drop-zone mb-2" id="mjImagineBaseImageDropZone">
                                                    <i class="bi bi-cloud-arrow-up"></i>
                                                    <p>将图片拖拽到此处，或点击选择文件</p>
                                                    <small class="text-muted d-block mt-1">支持 PNG, JPG, WEBP. 建议尺寸与生成目标接近。</small>
                                                </div>
                                                <input class="visually-hidden" type="file" id="mjImagineBaseImageInput" accept="image/png, image/jpeg, image/webp">
                                                <div class="preview-container mt-1" id="mjImagineBaseImagePreviewContainer" style="display: none;">
                                                    <img src="" class="preview-img" id="mjImagineBaseImagePreviewImg">
                                                    <div class="mt-2 text-center">
                                                        <button type="button" class="btn btn-sm btn-outline-danger" id="mjRemoveImagineBaseImageBtn">
                                                            <i class="bi bi-trash"></i> 移除垫图
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-text mb-2">
                                                提示：Midjourney 的图片宽高比、尺寸、风格化等参数通常在提示词中通过 <code>--ar</code>, <code>--s</code>, <code>--v</code> 等命令控制。
                                            </div>
                                        </div>
                                        <div id="comfyAspectRatioGroup" class="mb-3" style="display: none;"> <!-- Initially hidden -->
                                            <label for="comfyAspectRatioBtn" class="form-label">画面比例 (高级生图)</label>
                                            <div class="dropdown">
                                                <button class="form-select text-start dropdown-toggle" type="button" id="comfyAspectRatioBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                                    16:9 (Landscape)
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="comfyAspectRatioBtn" id="comfyAspectRatioMenu">
                                                    <li><a class="dropdown-item" href="javascript:void(0);" data-value="1:1 (Square)">1:1 (Square)</a></li>
                                                    <li><a class="dropdown-item active" href="javascript:void(0);" data-value="16:9 (Landscape)">16:9 (Landscape)</a></li>
                                                    <li><a class="dropdown-item" href="javascript:void(0);" data-value="9:16 (Portrait)">9:16 (Portrait)</a></li>
                                                    <li><a class="dropdown-item" href="javascript:void(0);" data-value="3:2 (Landscape)">3:2 (Landscape)</a></li>
                                                    <li><a class="dropdown-item" href="javascript:void(0);" data-value="2:3 (Portrait)">2:3 (Portrait)</a></li>
                                                    <li><a class="dropdown-item" href="javascript:void(0);" data-value="4:3 (Landscape)">4:3 (Landscape)</a></li>
                                                    <li><a class="dropdown-item" href="javascript:void(0);" data-value="3:4 (Portrait)">3:4 (Portrait)</a></li>
                                                    <li><a class="dropdown-item" href="javascript:void(0);" data-value="21:9 (Landscape)">21:9 (Landscape)</a></li>
                                                    <li><a class="dropdown-item" href="javascript:void(0);" data-value="9:21 (Portrait)">9:21 (Portrait)</a></li>
                                                </ul>
                                                <input type="hidden" id="comfyAspectRatioValue" value="16:9 (Landscape)">
                                            </div>
                                        </div>
                                        <!-- +++ 新增：高级生图的图片数量选择器 +++ -->
                                        <div id="comfyNumImagesGroup" class="mb-3" style="display: none;"> <!-- Initially hidden -->
                                            <label for="comfyNumImagesInput" class="form-label">图片数量 (高级生图)</label>
                                            <input type="number" class="form-control" id="comfyNumImagesInput" value="1" min="1" max="4">
                                            <div class="form-text">ComfyUI支持生成1-4张图片。</div>
                                        </div>
                                        <!-- +++ 新增代码结束 +++ -->
                                        <div class="row">
                                            <div class="mb-3">
                                                <label class="form-label">图片比例</label>
                                                <div id="aiRatioSelector" class="btn-group flex-wrap" role="group" aria-label="Image Ratios">
                                                    <!-- 比例按钮，例如: -->
                                                    <button type="button" class="btn btn-outline-secondary ratio-button m-1" data-ratio="21:9">
                                                        <span class="ratio-icon ratio-21-9"></span> <!-- 可选的图标 -->
                                                        21:9
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary ratio-button m-1" data-ratio="16:9">
                                                         <span class="ratio-icon ratio-16-9"></span>16:9
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary ratio-button m-1" data-ratio="3:2">
                                                        <span class="ratio-icon ratio-3-2"></span>3:2
                                                    </button>
                                                     <button type="button" class="btn btn-outline-secondary ratio-button m-1" data-ratio="4:3">
                                                         <span class="ratio-icon ratio-4-3"></span>4:3
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary ratio-button m-1 active" data-ratio="1:1"> <!-- 默认选中 -->
                                                         <span class="ratio-icon ratio-1-1"></span>1:1
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary ratio-button m-1" data-ratio="3:4">
                                                         <span class="ratio-icon ratio-3-4"></span>3:4
                                                    </button>
                                                     <button type="button" class="btn btn-outline-secondary ratio-button m-1" data-ratio="2:3">
                                                         <span class="ratio-icon ratio-2-3"></span>2:3
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary ratio-button m-1" data-ratio="9:16">
                                                         <span class="ratio-icon ratio-9-16"></span>9:16
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row g-2 mb-3 align-items-center" id="aiImageDimensionsRow">
                                            <label class="col-sm-2 col-form-label">图片尺寸</label>
                                            <div class="col-sm-10">
                                               <div class="row g-2 align-items-center">
                                                   <div class="col">
                                                       <div class="input-group">
                                                           <span class="input-group-text">W</span>
                                                           <input type="number" class="form-control" id="aiImageWidth" placeholder="宽度" value="1328" step="8"> <!-- 初始值 & 步长 -->
                                                       </div>
                                                   </div>
                                                   <div class="col-auto">
                                                       <button type="button" id="aiRatioLockBtn" class="btn btn-outline-secondary active" title="比例已锁定"> <!-- 初始锁定 -->
                                                           <i class="bi bi-link-45deg"></i>
                                                       </button>
                                                   </div>
                                                   <div class="col">
                                                       <div class="input-group">
                                                           <span class="input-group-text">H</span>
                                                           <input type="number" class="form-control" id="aiImageHeight" placeholder="高度" value="1328" step="8"> <!-- 初始值 & 步长 -->
                                                       </div>
                                                   </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-grid mt-4">
                                            <button class="btn btn-primary btn-lg" id="startAIGenerationBtn">
                                                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true" style="display: none;"></span>
                                                <i class="bi bi-stars me-2"></i> 开始生成
                                            </button>
                                        </div>
                                    </div>
                                    <!-- Tab 2: 相关案例 -->
                                    <div class="tab-pane fade" id="relevant-examples-pane" role="tabpanel" aria-labelledby="relevant-examples-tab" tabindex="0">
                                        
                                        <!-- 新增：相关案例专属搜索框 -->
                                        <div class="mb-3">
                                            <input type="search" class="form-control form-control-sm" id="relevantExamplesSearchInput" placeholder="搜索相关案例...">
                                        </div>

                                        <div class="row g-3 row-cols-md-2 relevant-examples-scroll-container" id="aiGenerateRelevantExamplesContainer" style="max-height: 480px; overflow-y: auto;">
                                            <!-- 相关案例卡片将动态加载到这里 -->
                                            <p class="text-muted text-center w-100 relevant-examples-placeholder">输入提示词或选择模型以查看相关案例。</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-7"> <!-- 右侧结果区域 -->
                        <div class="card">
                            <div class="card-header">生成结果与历史</div>
                            <div class="card-body" style="height: 560px; overflow-y: auto; overflow-x: hidden;"> <!-- 添加固定高度和滚动样式 -->
                                 <div id="aiGenerationLoading" style="display: none;">
                                    <div class="loading-dots">
                                        <span></span><span></span><span></span>
                                    </div>
                                    <p>正在生成图片...</p>
                                </div>
                                <div id="aiGenerationError" class="alert alert-danger" style="display: none;" role="alert">
                                    生成失败，请检查输入或稍后重试。
                                </div>

                                <div id="aiHistoryList" class="mb-3"> <!-- 历史记录列表 -->
                                    <p class="text-muted text-center py-3" id="historyPlaceholder">在这里查看生成结果与历史记录...</p>
                                </div>
                                <!-- 加载更多指示器将由 JS 添加到 card-body 的末尾 -->

                            </div>
                        </div>
                    </div>
                </div>
            </div> <!-- 结束：AI 图片生成 Tab 内容 -->

            <!-- 新增：管理中心 Tab 内容 -->
            <div class="tab-pane fade" id="admin-management-tab-pane" role="tabpanel" aria-labelledby="admin-management-tab" tabindex="0">
                <div class="row">
                    <!-- 左侧导航 -->
                    <div class="col-md-3 mb-4">
                        <div class="list-group admin-nav">
                            <a href="#admin-tags-section" class="list-group-item list-group-item-action active" data-bs-toggle="pill" aria-current="true">
                                <i class="bi bi-tags-fill me-2"></i> 标签管理
                            </a>
                            <a href="#admin-categories-section" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                <i class="bi bi-bookmark-star-fill me-2"></i> 分类管理
                            </a>
                            <a href="#admin-users-section" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                <i class="bi bi-people-fill me-2"></i> 用户管理
                            </a>
                            <!-- 移动并修改用户积分管理导航 -->
                            <a href="#user-credit-management-section" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                <i class="bi bi-currency-bitcoin me-2"></i> 用户积分管理 <!-- 可以换个更合适的图标 -->
                            </a>
                            <a href="#admin-feature-costs-section" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                <i class="bi bi-sliders me-2"></i> 功能成本管理
                            </a>
                        </div>
                    </div>

                    <!-- 右侧内容区域 -->
                    <div class="col-md-9">
                        <div class="tab-content">
                            <!-- 标签管理区域 -->
                            <div class="tab-pane fade show active" id="admin-tags-section" role="tabpanel">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <span>标签列表</span>
                                        <button class="btn btn-success btn-sm" id="adminAddTagBtn">
                                            <i class="bi bi-plus-lg"></i> 添加标签
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover admin-table">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th>名称</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="adminTagsTableBody">
                                                    <!-- 标签数据将由 JS 填充 -->
                                                    <tr><td colspan="3" class="text-center">加载中...</td></tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 新增：分类管理区域 -->
                            <div class="tab-pane fade" id="admin-categories-section" role="tabpanel">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <span>分类列表</span>
                                        <button class="btn btn-success btn-sm" id="adminAddCategoryBtn">
                                            <i class="bi bi-plus-lg"></i> 添加分类
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover admin-table">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th>名称</th>
                                                        <th>标识符 (Slug)</th>
                                                        <th>描述</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="adminCategoriesTableBody">
                                                    <!-- 分类数据将由 JS 填充 -->
                                                    <tr><td colspan="5" class="text-center">加载中...</td></tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 用户管理区域 -->
                            <div class="tab-pane fade" id="admin-users-section" role="tabpanel">
                                <div class="card">
                                    <div class="card-header">
                                        <span>用户列表</span>
                                    </div>
                                    <div class="card-body">
                                        <!-- 搜索框 -->
                                        <div class="mb-3">
                                             <div class="input-group">
                                                <input type="text" class="form-control" id="adminUserSearchInput" placeholder="按用户名、邮箱或昵称搜索...">
                                                <button class="btn btn-outline-secondary" type="button" id="adminUserSearchBtn"><i class="bi bi-search"></i></button>
                                            </div>
                                        </div>

                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover admin-table">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th>用户名</th>
                                                        <th>邮箱</th>
                                                        <th>昵称</th>
                                                        <th>角色</th>
                                                        <th>注册时间</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="adminUsersTableBody">
                                                    <!-- 用户数据将由 JS 填充 -->
                                                    <tr><td colspan="7" class="text-center">加载中...</td></tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <!-- 分页 -->
                                        <nav aria-label="用户分页" class="mt-3 d-flex justify-content-center">
                                            <ul class="pagination" id="adminUsersPagination"></ul>
                                        </nav>
                                    </div>
                                </div>
                            </div>

                            <!-- 修改用户积分管理区域为 tab-pane -->
                            <div class="tab-pane fade" id="user-credit-management-section" role="tabpanel">
                                <!-- 新增 Card 结构 -->
                                <div class="card">
                                    <div class="card-header">
                                         <h2><i class="bi bi-currency-bitcoin me-2"></i> 用户积分管理</h2>
                                    </div>
                                    <div class="card-body">
                                        <!-- 新增：搜索框 -->
                                        <div class="mb-3">
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="userCreditSearchInput" placeholder="按用户名或邮箱搜索...">
                                                <button class="btn btn-outline-secondary" type="button" id="userCreditSearchBtn"><i class="bi bi-search"></i></button>
                                            </div>
                                        </div>
                                        <div class="table-responsive">
                                            <div id="user-credits-loading" class="text-center my-3" style="display: none;">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">加载中...</span>
                                                </div>
                                                <p>正在加载用户列表...</p>
                                            </div>
                                            <div id="user-credits-error" class="alert alert-danger" role="alert" style="display: none;">
                                                加载用户列表失败，请稍后重试。
                                            </div>
                                            <table class="table table-striped table-hover align-middle">
                                                <thead>
                                                    <tr>
                                                        <th scope="col">ID</th>
                                                        <th scope="col">用户名</th>
                                                        <th scope="col">邮箱</th>
                                                        <th scope="col">积分余额</th>
                                                        <th scope="col" style="width: 200px;">操作</th> <!-- 给操作列固定宽度 -->
                                                    </tr>
                                                </thead>
                                                <tbody id="user-credits-table-body">
                                                    <!-- 用户数据将由 JavaScript 动态填充 -->
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- 新增：分页控件容器 -->
                                        <nav aria-label="用户积分分页" class="mt-3 d-flex justify-content-center">
                                            <ul class="pagination" id="user-credits-pagination">
                                                <!-- 分页按钮将由 JS 动态生成 -->
                                            </ul>
                                        </nav>
                                        <!-- 结束：分页控件容器 -->

                                    </div>
                                </div>
                            </div>

                            <!-- 新增：功能成本管理区域 -->
                            <div class="tab-pane fade" id="admin-feature-costs-section" role="tabpanel">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <span><i class="bi bi-sliders me-2"></i>功能成本设置</span>
                                        <button class="btn btn-success btn-sm" id="saveFeatureCostsBtn" disabled>
                                            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true" style="display: none;"></span>
                                            保存更改
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div id="feature-costs-loading" class="text-center my-3">
                                            <div class="spinner-border text-primary" role="status"></div> <p>加载成本设置...</p>
                                        </div>
                                        <div id="feature-costs-error" class="alert alert-danger" style="display: none;"></div>
                                        <form id="featureCostsForm">
                                            <!-- 功能成本表单将由 JS 动态填充 -->
                                            <div id="featureCostsList" class="mb-3">
                                                <!-- Example Entry (JS will generate these):
                                                <div class="row mb-2 align-items-center">
                                                    <label class="col-sm-4 col-form-label">AI 图片生成 (ai_generate)</label>
                                                    <div class="col-sm-4">
                                                        <input type="number" class="form-control form-control-sm" data-feature-key="ai_generate" value="2" min="0">
                                                    </div>
                                                    <div class="col-sm-4">
                                                        <small class="text-muted">每次生成消耗的积分</small>
                                                    </div>
                                                </div>
                                                -->
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <!-- 结束：功能成本管理区域 -->

                        </div>
                    </div>
                </div>
            </div>
            <!-- 结束：管理中心 Tab 内容 -->

            <!-- Creative Upscale Tab Pane -->
            <div class="tab-pane fade" id="creative-upscale-pane" role="tabpanel" aria-labelledby="creative-upscale-tab" tabindex="0">
                <div class="row">
                    <div class="col-md-5 mb-4">
                        <div class="card">
                            <div class="card-header">
                                上传与操作
                            </div>
                            <div class="card-body">
                                <h6>1. 上传图片</h6>
                                <p class="text-muted small">选择 PNG, JPG, 或 WEBP。最大 5MB, 分辨率不超过 4MP，尺寸 32x32 到 4096x4096。</p>
                                <div class="image-drop-zone mb-3" id="upscaleImageDropZone">
                                    <i class="bi bi-cloud-arrow-up"></i>
                                    <p>将图片拖拽到此处，或点击选择文件</p>
                                    <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
                                </div>
                                <input class="visually-hidden" type="file" id="upscaleImageInput" accept="image/png, image/jpeg, image/webp">
                                <div class="preview-container mt-2" id="upscalePreviewContainer" style="display: none;">
                                    <img src="" class="preview-img" id="upscalePreviewImg">
                                    <div class="mt-2 text-center">
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="removeUpscaleImage">
                                            <i class="bi bi-trash"></i> 移除图片
                                        </button>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <h6>2. 开始放大</h6>
                                <p class="text-muted small">点击按钮开始处理。请注意，这可能需要一些时间。</p>
                                <div class="d-grid">
                                    <button class="btn btn-primary btn-lg" id="startUpscaleBtn" disabled>
                                        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true" style="display: none;"></span>
                                        <i class="bi bi-aspect-ratio me-1"></i>开始清晰放大
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-7">
                        <div class="card">
                            <div class="card-header">
                                清晰放大结果
                            </div>
                            <div class="card-body">
                                <div id="upscaleResultContainer" style="display: none;">
                                    <div id="upscaleResultLoading" class="loading-shimmer-card" style="display: none;"> <!-- 添加 loading-shimmer-card 类 -->
                                        <div class="loading-dots">
                                            <span></span><span></span><span></span>
                                        </div>
                                        <p>正在生成清晰放大图片...</p> <!-- 可以自定义文字 -->
                                    </div>
                                    <img id="upscaleResultImage" src="#" alt="Upscaled image" class="img-fluid rounded border mb-2" style="background-color: rgba(0,0,0,0.2);">
                                    <a id="upscaleResultLink" href="#" target="_blank" class="btn btn-outline-secondary btn-sm w-100 mt-2">
                                        <i class="bi bi-download me-1"></i> 在新标签页打开或下载
                                    </a>
                                </div>
                                <div id="upscaleErrorAlert" class="alert alert-danger mt-3" role="alert" style="display: none;">
                                     <!-- Error message will be set by JS -->
                                 </div>
                                <p class="text-muted text-center" id="upscaleResultsPlaceholder">在这里查看清晰放大结果...</p>

                            </div>
                        </div>

                        <!-- Upscale History Section - MOVED HERE -->
                        <div class="card mt-4">
                            <div class="card-header">
                                历史记录
                            </div>
                            <div class="card-body">
                                <div id="upscaleHistoryList">
                                    <!-- History items will be loaded here -->
                                    <p class="text-muted text-center" id="upscaleHistoryPlaceholder">加载历史记录中或暂无记录...</p>
                                </div>
                                <!-- Load More button will be inserted here by JS after the list container -->
                                <div id="upscaleHistoryPagination" class="d-flex justify-content-center mt-3" style="display: none !important;">
                                    <!-- Placeholder for old pagination, hidden forcefully -->
                                </div>
                            </div>
                        </div>
                        <!-- End Upscale History Section -->

                    </div>
                </div>
            </div>

            <!-- 新增：背景移除 Tab Pane -->
            <div class="tab-pane fade" id="remove-background-pane" role="tabpanel" aria-labelledby="remove-background-tab" tabindex="0">
                <div class="row">
                    <div class="col-md-5 mb-4"> <!-- 左侧上传与操作区域 -->
                        <div class="card">
                            <div class="card-header">上传图片并移除背景</div>
                            <div class="card-body">
                                <h6>1. 上传图片</h6>
                                <p class="text-muted small">选择 PNG, JPG, 或 WEBP 格式的图片。最大 5MB。</p>
                                <div class="image-drop-zone mb-3" id="rbImageDropZone">
                                    <i class="bi bi-cloud-arrow-up"></i>
                                    <p>将图片拖拽到此处，或点击选择文件</p>
                                    <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
                                </div>
                                <input class="visually-hidden" type="file" id="rbImageInput" accept="image/png, image/jpeg, image/webp">
                                <div class="preview-container mt-2" id="rbPreviewContainer" style="display: none;">
                                    <img src="" class="preview-img" id="rbPreviewImg">
                                    <div class="mt-2 text-center">
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="removeRbImage">
                                            <i class="bi bi-trash"></i> 移除图片
                                        </button>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <!-- 新增：抠图引擎选择 -->
                                <div class="mb-3">
                                    <label for="rbEngineSelect" class="form-label fw-bold">选择抠图引擎</label>
                                    <!-- <select class="form-select" id="rbEngineSelect">
                                        <option value="api" selected>API抠图 (默认)</option>
                                        <option value="comfyui">ComfyUI抠图</option>
                                    </select> -->
                                    <div class="dropdown">
                                        <button class="form-select text-start dropdown-toggle" type="button" id="rbEngineSelectBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                            API抠图 (默认)
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="rbEngineSelectBtn" id="rbEngineSelectMenu">
                                            <li><a class="dropdown-item active" href="#" data-value="api">API抠图 (默认)</a></li>
                                            <li><a class="dropdown-item" href="#" data-value="comfyui">ComfyUI抠图</a></li>
                                        </ul>
                                        <input type="hidden" id="rbEngineSelectValue" value="api">
                                    </div>
                                </div>

                                <!-- 新增：ComfyUI 移除模式选择 (初始隐藏) -->
                                <div class="mb-3" id="comfyuiRemModeSection" style="display: none;">
                                    <label for="comfyuiRemModeSelect" class="form-label fw-bold">选择移除模式 (ComfyUI)</label>
                                    <!-- <select class="form-select" id="comfyuiRemModeSelect">
                                        <option value="BEN2" selected>BEN2 (默认)</option>
                                        <option value="RMBG-2.0">RMBG-2.0</option>
                                        <option value="RMBG-1.4">RMBG-1.4</option>
                                        <option value="Inspyrenet">Inspyrenet</option>
                                    </select> -->
                                    <div class="dropdown">
                                        <button class="form-select text-start dropdown-toggle" type="button" id="comfyuiRemModeSelectBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                            BEN2 (默认)
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="comfyuiRemModeSelectBtn" id="comfyuiRemModeSelectMenu">
                                            <li><a class="dropdown-item active" href="#" data-value="BEN2">BEN2 (默认)</a></li>
                                            <li><a class="dropdown-item" href="#" data-value="RMBG-2.0">RMBG-2.0</a></li>
                                            <li><a class="dropdown-item" href="#" data-value="RMBG-1.4">RMBG-1.4</a></li>
                                            <li><a class="dropdown-item" href="#" data-value="Inspyrenet">Inspyrenet</a></li>
                                        </ul>
                                        <input type="hidden" id="comfyuiRemModeSelectValue" value="BEN2">
                                    </div>
                                </div>

                                <h6>2. 开始处理</h6>
                                <p class="text-muted small">点击按钮开始移除背景。</p>
                                <div class="d-grid">
                                    <button class="btn btn-primary btn-lg" id="startRbBtn" disabled>
                                        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true" style="display: none;"></span>
                                        <i class="bi bi-eraser me-1"></i>开始移除背景
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-7"> <!-- 右侧结果区域 -->
                        <div class="card">
                            <div class="card-header">处理结果</div>
                            <div class="card-body">
                                <div id="rbResultLoading" class="loading-shimmer-card" style="display: none;">
                                    <div class="loading-dots">
                                        <span></span><span></span><span></span>
                                    </div>
                                    <p>正在移除图片背景...</p>
                                </div>
                                <div id="rbResultContainer" style="display: none;">
                                    <img id="rbResultImage" src="#" alt="Removed background image" class="img-fluid rounded border mb-2" style="background-color: rgba(0,0,0,0.2); /* Optional background for transparent images */">
                                    <a id="rbResultLink" href="#" target="_blank" class="btn btn-outline-secondary btn-hover-glow btn-sm w-100 mt-2">
                                        <i class="bi bi-download me-1"></i> 在新标签页打开或下载
                                    </a>
                                    <!-- Optional: Add button to send to examples -->
                                    <!-- 
                                    <button type="button" class="btn btn-info btn-sm w-100 mt-2" id="sendRbImageToExamples">
                                        <i class="bi bi-send-plus me-1"></i> 发送到案例库
                                    </button> 
                                    -->
                                </div>
                                <div id="rbErrorAlert" class="alert alert-danger mt-3" role="alert" style="display: none;">
                                     <!-- Error message will be set by JS -->
                                 </div>
                                <p class="text-muted text-center" id="rbResultsPlaceholder">在这里查看移除背景后的图片...</p>
                            </div>
                        </div>

                        <!-- 新增：背景移除历史记录卡片 -->
                        <div class="card mt-4">
                            <div class="card-header">
                                历史记录
                            </div>
                            <div class="card-body">
                                <div id="rbHistoryList" class="history-list-flex"> <!-- 使用与放大一致的 flex 布局类 -->
                                    <!-- History items will be loaded here -->
                                    <p class="text-muted text-center w-100" id="rbHistoryPlaceholder">加载历史记录中或暂无记录...</p> <!-- w-100 确保占满宽度 -->
                                </div>
                                <!-- 加载更多按钮容器 -->
                                <div id="rbHistoryLoadMoreContainer" class="text-center mt-3" style="display: none;"> 
                                    <button class="btn btn-outline-secondary btn-sm" id="loadMoreRbHistoryBtn">加载更多</button>
                                </div>
                            </div>
                        </div>
                        <!-- 结束：背景移除历史记录卡片 -->
                    </div>
                </div>
            </div> <!-- 结束：背景移除 Tab Pane -->

            <!-- 新增：位图转 SVG Tab Pane -->
            <div class="tab-pane fade" id="vectorize-pane" role="tabpanel" aria-labelledby="vectorize-tab" tabindex="0">
                <div class="row">
                    <div class="col-md-5 mb-4"> <!-- 左侧上传与操作区域 -->
                        <div class="card">
                            <div class="card-header">上传图片并转换为 SVG</div>
                            <div class="card-body">
                                <h6>1. 上传图片</h6>
                                <p class="text-muted small">选择 PNG, JPG, 或 WEBP 格式的图片。最大 5MB。</p>
                                <div class="image-drop-zone mb-3" id="vectorizeImageDropZone"> <!-- New ID -->
                                    <i class="bi bi-cloud-arrow-up"></i>
                                    <p>将图片拖拽到此处，或点击选择文件</p>
                                    <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
                                </div>
                                <input class="visually-hidden" type="file" id="vectorizeImageInput" accept="image/png, image/jpeg, image/webp"> <!-- New ID -->
                                <div class="preview-container mt-2" id="vectorizePreviewContainer" style="display: none;"> <!-- New ID -->
                                    <img src="" class="preview-img" id="vectorizePreviewImg"> <!-- New ID -->
                                    <div class="mt-2 text-center">
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="removeVectorizeImage"> <!-- New ID -->
                                            <i class="bi bi-trash"></i> 移除图片
                                        </button>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <h6>2. 开始转换</h6>
                                <p class="text-muted small">点击按钮开始转换为 SVG。</p>
                                <div class="d-grid">
                                    <button class="btn btn-primary btn-lg" id="startVectorizeBtn" disabled> <!-- New ID -->
                                        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true" style="display: none;"></span>
                                        <i class="bi bi-vector-pen me-1"></i>开始转换 SVG
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-7"> <!-- 右侧结果区域 -->
                        <div class="card">
                            <div class="card-header">转换结果 (SVG)</div>
                            <div class="card-body">
                                <div id="vectorizeResultContainer" style="display: none;"> <!-- New ID -->
                                    <div id="vectorizeResultLoading" class="loading-shimmer-card" style="display: none;"> <!-- New ID -->
                                        <div class="loading-dots">
                                            <span></span><span></span><span></span>
                                        </div>
                                        <p>正在将图片转换为 SVG...</p>
                                    </div>
                                    <!-- SVG 结果 -->
                                    <div id="vectorizeResultContent" class="text-center"> <!-- Existing ID -->
                                        <!-- 新增：SVG 预览图片 -->
                                        <img id="vectorizeResultPreview" 
                                             class="img-fluid rounded border mb-3 mx-auto d-block"  alt="SVG Preview" 
                                             style="display: none; max-height: 300px; background-color: white;"> 
                                        <!-- 结束：SVG 预览图片 -->

                                        <p>SVG 转换成功！</p> 
                                        <a id="vectorizeResultLink" href="#" target="_blank" class="btn btn-primary btn-sm mt-2"> <!-- Changed btn-success to btn-primary -->
                                            <i class="bi bi-download me-1"></i> 点击下载 SVG
                                        </a>
                                    </div>
                                </div>
                                <div id="vectorizeErrorAlert" class="alert alert-danger mt-3" role="alert" style="display: none;"> <!-- New ID -->
                                     <!-- Error message will be set by JS -->
                                 </div>
                                <p class="text-muted text-center" id="vectorizeResultsPlaceholder">在这里查看转换后的 SVG 文件...</p> <!-- New ID -->
                            </div>
                        </div>
                        
                        <!-- 解除历史记录卡片的注释 -->
                        <div class="card mt-4">
                            <div class="card-header">历史记录</div>
                            <div class="card-body">
                                <div id="vectorizeHistoryList" class="history-list-flex"> <!-- New ID -->
                                    <p class="text-muted text-center w-100" id="vectorizeHistoryPlaceholder">加载历史记录中或暂无记录...</p> <!-- New ID -->
                                </div>
                                <div id="vectorizeHistoryLoadMoreContainer" class="text-center mt-3" style="display: none;"> <!-- New ID -->
                                    <button class="btn btn-outline-secondary btn-sm" id="loadMoreVectorizeHistoryBtn">加载更多</button> <!-- New ID -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> <!-- 结束：位图转 SVG Tab Pane -->

            <!-- 新增：图片转3D模型标签面板 -->
            <div class="tab-pane fade" id="image-to-3d-pane" role="tabpanel" aria-labelledby="image-to-3d-tab" tabindex="0">
                <div class="row">
                    <div class="col-md-5 mb-4"> <!-- 左侧上传与操作区域 -->
                        <div class="card">
                            <div class="card-header">上传图片并生成3D模型</div>
                            <div class="card-body">
                                <h6>1. 上传图片</h6>
                                <p class="text-muted small">选择 PNG, JPG, 或 WEBP 格式的图片。最大 5MB。</p>
                                <div class="image-drop-zone mb-3" id="image3dDropZone">
                                    <i class="bi bi-cloud-arrow-up"></i>
                                    <p>将图片拖拽到此处，或点击选择文件</p>
                                    <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
                                </div>
                                <input class="visually-hidden" type="file" id="image3dInput" accept="image/png, image/jpeg, image/webp">
                                <div class="preview-container mt-2" id="image3dPreviewContainer" style="display: none;">
                                    <img src="" class="preview-img" id="image3dPreviewImg">
                                    <div class="mt-2 text-center">
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="removeImage3d">
                                            <i class="bi bi-trash"></i> 移除图片
                                        </button>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <h6>2. 开始生成3D模型</h6>
                                <p class="text-muted small">点击按钮开始将图片转换为3D模型。可能需要等待几分钟。</p>
                                <div class="d-grid">
                                    <button class="btn btn-primary btn-lg" id="startImage3dBtn" disabled>
                                        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true" style="display: none;"></span>
                                        <i class="bi bi-cube me-1"></i>开始生成3D模型
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-7"> <!-- 右侧结果区域 -->
                        <div class="card">
                            <div class="card-header">生成进度与3D模型预览</div>
                            <div class="card-body">
                                <!-- 进度显示 -->
                                <div id="image3dStatusArea" style="display: none;">
                                    <h6 id="image3dStatusMessage">正在处理中...</h6>
                                    
                                    <!-- 进度条 -->
                                    <div id="image3dProgressContainer" style="display: none;" class="mb-3">
                                        <div class="d-flex justify-content-between mb-1">
                                            <span>生成进度:</span>
                                            <span id="image3dProgressPercentage">0%</span>
                                        </div>
                                        <div class="progress" style="height: 20px;">
                                            <div id="image3dProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                                 role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                        </div>
                                        <div class="mt-2 text-muted" id="image3dProgressDetail">正在排队中，请耐心等待...</div>
                                    </div>
                                    
                                    <!-- 错误消息 -->
                                    <div id="image3dErrorMessage" class="alert alert-danger mt-3" style="display: none;"></div>
                                </div>

                                <!-- 3D 模型查看器 -->
                                <div id="image3dModelContainer" style="display: none;">
                                    <h6 class="mb-3">3D 模型预览</h6>
                                    <div id="image3dModelViewer" style="width: 100%; height: 400px; background-color: #f8f9fa; border-radius: 8px;"></div>
                                    <div class="d-flex justify-content-between mt-3">
                                        <button id="image3dRotateBtn" class="btn btn-outline-secondary">
                                            <i class="bi bi-arrow-repeat me-1"></i>旋转
                                        </button>
                                        <button id="image3dResetBtn" class="btn btn-outline-secondary">
                                            <i class="bi bi-arrows-fullscreen me-1"></i>重置视角
                                        </button>
                                        <a id="image3dDownloadLink" href="#" target="_blank" class="btn btn-primary">
                                            <i class="bi bi-download me-1"></i>下载模型
                                        </a>
                                    </div>
                                </div>
                                
                                <p class="text-muted text-center" id="image3dResultsPlaceholder">上传图片并开始生成，查看3D模型预览...</p>
                            </div>
                        </div>

                        <!-- 历史记录卡片 -->
                        <div class="card mt-4">
                            <div class="card-header">
                                历史记录
                            </div>
                            <div class="card-body">
                                <div id="image3dHistoryList" class="history-list-flex">
                                    <p class="text-muted text-center w-100" id="image3dHistoryPlaceholder">加载历史记录中或暂无记录...</p>
                                </div>
                                <div id="image3dHistoryLoadMoreContainer" class="text-center mt-3" style="display: none;"> 
                                    <button class="btn btn-outline-secondary btn-sm" id="loadMoreImage3dHistoryBtn">加载更多</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> <!-- 结束：图片转3D模型标签面板 -->

            <!-- 新增：图生视频 Tab Pane -->
            <div class="tab-pane fade" id="image-to-video-pane" role="tabpanel" aria-labelledby="image-to-video-tab" tabindex="0">
                <div class="row">
                    <div class="col-md-5 mb-4"> <!-- 左侧上传与参数区域 -->
                        <div class="card">
                            <div class="card-header">上传图片并设置参数</div>
                            <div class="card-body">
                                <!-- 模式切换开关 -->
                                <div class="form-check form-switch mb-3">
                                  <input class="form-check-input" type="checkbox" id="i2vModeToggle">
                                  <label class="form-check-label" for="i2vModeToggle">首尾帧模式</label>
                                </div>

                                <!-- 标准模式容器 -->
                                <div id="i2vStandardModeContainer">
                                  <div class="mb-3">
                                    <label class="form-label">上传图片</label>
                                    <div class="image-drop-zone mb-3" id="i2vImageDropZone">
                                      <i class="bi bi-cloud-arrow-up"></i>
                                      <p>将图片拖拽到此处，或点击选择文件</p>
                                      <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
                                    </div>
                                    <input class="visually-hidden" type="file" id="i2vImageInput" accept="image/png,image/jpeg,image/webp">
                                    <div class="preview-container mt-2" id="i2vPreviewContainer" style="display:none">
                                      <img src="" class="preview-img" id="i2vPreviewImg">
                                      <div class="mt-2 text-center">
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="removeI2vImage">
                                          <i class="bi bi-trash"></i> 移除图片
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                  <small class="text-muted d-block mt-1">支持 PNG、JPG 和 WEBP 格式的图片。最大 5MB。</small>
                                </div>

                                <!-- 首尾帧模式容器 -->
                                <div id="i2vKf2vModeContainer" style="display:none">
                                  <div class="mb-3">
                                    <label class="form-label">上传首帧图片</label>
                                    <div class="image-drop-zone mb-3" id="i2vFirstFrameDropZone">
                                      <i class="bi bi-cloud-arrow-up"></i>
                                      <p>将首帧图片拖拽到此处，或点击选择图片</p>
                                      <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
                                    </div>
                                    <input class="visually-hidden" type="file" id="i2vFirstFrameInput" accept="image/png,image/jpeg,image/webp">
                                    <div class="preview-container mt-2" id="i2vFirstFramePreviewContainer" style="display:none">
                                      <img src="" class="preview-img" id="i2vFirstFramePreviewImg">
                                      <div class="mt-2 text-center">
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="removeI2vFirstFrame">
                                          <i class="bi bi-trash"></i> 移除图片
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="mb-3">
                                    <label class="form-label">上传尾帧图片</label>
                                    <div class="image-drop-zone mb-3" id="i2vLastFrameDropZone">
                                      <i class="bi bi-cloud-arrow-up"></i>
                                      <p>将尾帧图片拖拽到此处，或点击选择图片</p>
                                      <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
                                    </div>
                                    <input class="visually-hidden" type="file" id="i2vLastFrameInput" accept="image/png,image/jpeg,image/webp">
                                    <div class="preview-container mt-2" id="i2vLastFramePreviewContainer" style="display:none">
                                      <img src="" class="preview-img" id="i2vLastFramePreviewImg">
                                      <div class="mt-2 text-center">
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="removeI2vLastFrame">
                                          <i class="bi bi-trash"></i> 移除图片
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                  <small class="text-muted d-block mt-1">支持 PNG、JPG 和 WEBP 格式，每张图片最大 5MB。首帧和尾帧图片尺寸应当一致。</small>
                                </div>

                                <hr class="my-4">

                                <h6>2. 设置生成参数</h6>
                                <div class="mb-3">
                                    <label for="i2vPromptInput" class="form-label">视频提示词 (Prompt)</label>
                                    <textarea class="form-control" id="i2vPromptInput" rows="3" placeholder="描述视频内容，例如：一只猫在草地上奔跑"></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="i2vModelSelect" class="form-label">选择模型</label>
                                        <select class="form-select" id="i2vModelSelect">
                                            <option value="wanx2.1-i2v-turbo" selected>通用-Turbo (速度快)</option>
                                            <option value="wanx2.1-i2v-plus">通用-Plus (效果好)</option>
                                            <option value="wanx2.1-kf2v-plus">首尾帧专用 (KF2V)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="i2vResolutionSelect" class="form-label">视频分辨率 <span id="i2vModelCostDisplay_old" class="badge bg-secondary ms-2" style="display: none;"></span></label> 
                                        <select class="form-select" id="i2vResolutionSelect">
                                            <!-- Options will be populated by JS based on model -->
                                            <option value="720P">720P</option>
                                            <option value="480P">480P</option>
                                        </select>
                                    </div>
                                </div>
                                <!-- 阿里云 API 似乎没有太多其他可调参数给前端 -->

                                <hr class="my-4">

                                <h6>3. 开始生成视频</h6>
                                <p class="text-muted small">点击按钮开始生成。根据模型和排队情况，可能需要3-10分钟。</p>
                                <div class="d-grid">
                                    <button class="btn btn-primary btn-lg" id="startI2vGenerationBtn" disabled>
                                        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true" style="display: none;"></span>
                                        <i class="bi bi-film me-1"></i>
                                        <span id="startI2vGenerationBtnText">开始生成视频</span>
                                        <span id="i2vButtonCostDisplay" class="badge bg-warning text-dark ms-2" style="display: none;"></span> 
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-7"> <!-- 右侧结果与历史区域 -->
                        <div class="card">
                            <div class="card-header">生成结果与状态</div>
                            <div class="card-body">
                                <div id="i2vLoadingIndicator" class="text-center my-3" style="display: none;">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">处理中...</span>
                                    </div>
                                    <p id="i2vLoadingMessage" class="mt-2">正在处理，请稍候...</p>
                                </div>
                                
                                <div id="i2vErrorAlert" class="alert alert-danger" role="alert" style="display: none;">
                                    <!-- Error message will be set by JS -->
                                </div>

                                <div id="i2vResultContainer" style="display: none;">
                                    <h6 class="mb-3">视频结果:</h6>
                                    <video id="i2vResultVideo" controls class="img-fluid rounded border" style="width: 100%; background-color: #000;"></video>
                                    <a id="i2vDownloadLink" href="#" target="_blank" class="btn btn-outline-secondary btn-sm w-100 mt-2">
                                        <i class="bi bi-download me-1"></i> 在新标签页打开或下载视频
                                    </a>
                                </div>
                                <p class="text-muted text-center" id="i2vResultsPlaceholder">在这里查看生成状态和视频结果...</p>
                            </div>
                        </div>

                        <!-- 新增：图生视频历史记录卡片 -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <i class="bi bi-clock-history me-1"></i> 图生视频历史记录
                            </div>
                            <div class="card-body">
                                <div id="i2vHistoryListContainer">
                                    <!-- 加载提示 -->
                                    <div id="i2vHistoryLoading" class="text-center my-3" style="display: none;">
                                        <div class="spinner-border spinner-border-sm" role="status">
                                            <span class="visually-hidden">加载历史记录中...</span>
                                        </div>
                                        <span class="ms-2">加载历史记录中...</span>
                                    </div>
                                    <!-- 错误提示 -->
                                    <div id="i2vHistoryError" class="alert alert-warning" role="alert" style="display: none;">
                                        加载历史记录失败。
                                    </div>
                                    <!-- 列表容器 -->
                                    <div id="i2vHistoryList" class="list-group"> 
                                    </div>
                                    <!-- 空状态提示 -->
                                    <p id="i2vHistoryEmpty" class="text-muted text-center my-3" style="display: none;">
                                        暂无图生视频历史记录。
                                    </p>
                                </div>
                                
                                <!-- 加载更多按钮 -->
                                <div id="i2vHistoryLoadMoreContainer" class="text-center mt-3" style="display: none;">
                                    <button class="btn btn-outline-secondary btn-sm glassmorphism-btn" id="loadMoreI2VHistoryBtn">加载更多</button>
                                </div>
                                
                                <!-- 分页控件 -->
                                <nav aria-label="Image to Video History Pagination" class="mt-3" id="i2vHistoryPaginationNav" style="display: none;">
                                    <ul class="pagination justify-content-center" id="i2vHistoryPagination">
                                        <!-- 分页按钮将由JS动态填充 -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                        <!-- 结束：图生视频历史记录卡片 -->

                    </div>
                </div>
            </div>
            <!-- 结束：图生视频 Tab Pane -->

            <!-- 新增：GPT-4o 图片编辑 Tab Pane -->
            <div class="tab-pane fade" id="gpt4o-image-edit-pane" role="tabpanel" aria-labelledby="gpt4o-image-edit-tab" tabindex="0">
                <div class="row">
                    <div class="col-md-5 mb-4"> <!-- 左侧输入区域 -->
                        <div class="card">
                            <div class="card-header">编辑参数</div>
                            <div class="card-body">
                                <!-- 主图片上传 -->
                                <div class="mb-3">
                                    <label class="form-label fw-bold">1. 上传主图片 (必需)</label>
                                    <div class="image-drop-zone mb-2" id="gpt4oMainImageDropZone">
                                        <i class="bi bi-cloud-arrow-up"></i>
                                        <p>拖拽主图片或点击选择</p>
                                        <small class="text-muted d-block mt-1">支持 PNG, JPG, WEBP. 最大 5MB.</small>
                                    </div>
                                    <input class="visually-hidden" type="file" id="gpt4oMainImageInput" accept="image/png, image/jpeg, image/webp">
                                    <div class="preview-container mt-1" id="gpt4oMainImagePreviewContainer" style="display: none;">
                                        <img src="" class="preview-img" id="gpt4oMainImagePreviewImg">
                                        <div class="mt-2 text-center">
                                            <button type="button" class="btn btn-sm btn-outline-danger" id="removeGpt4oMainImageBtn">
                                                <i class="bi bi-trash"></i> 移除主图片
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 蒙版图片上传 (可选) -->
                                <div class="mb-3">
                                    <label class="form-label">2. 上传参考图片 1 (可选)</label>
                                    <p class="text-muted small">可用于风格参考等，具体效果取决于模型支持。</p>
                                    <div class="image-drop-zone mb-2" id="gpt4oRef1ImageDropZone">
                                        <i class="bi bi-cloud-arrow-up"></i>
                                        <p>拖拽参考图片1或点击选择</p>
                                    </div>
                                    <input class="visually-hidden" type="file" id="gpt4oRef1ImageInput" accept="image/png, image/jpeg, image/webp">
                                    <div class="preview-container mt-1" id="gpt4oRef1ImagePreviewContainer" style="display: none;">
                                        <img src="" class="preview-img" id="gpt4oRef1ImagePreviewImg">
                                        <div class="mt-2 text-center">
                                            <button type="button" class="btn btn-sm btn-outline-danger" id="removeGpt4oRef1ImageBtn">
                                                <i class="bi bi-trash"></i> 移除参考图1
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 参考图片上传 (可选) -->
                                <div class="mb-3">
                                    <label class="form-label">3. 上传参考图片 2 (可选)</label>
                                     <p class="text-muted small">可用于风格参考等，具体效果取决于模型支持。</p>
                                    <div class="image-drop-zone mb-2" id="gpt4oRef2ImageDropZone">
                                        <i class="bi bi-cloud-arrow-up"></i>
                                        <p>拖拽参考图片2或点击选择</p>
                                    </div>
                                    <input class="visually-hidden" type="file" id="gpt4oRef2ImageInput" accept="image/png, image/jpeg, image/webp">
                                    <div class="preview-container mt-1" id="gpt4oRef2ImagePreviewContainer" style="display: none;">
                                        <img src="" class="preview-img" id="gpt4oRef2ImagePreviewImg">
                                        <div class="mt-2 text-center">
                                            <button type="button" class="btn btn-sm btn-outline-danger" id="removeGpt4oRef2ImageBtn">
                                                <i class="bi bi-trash"></i> 移除参考图2
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <div class="mb-3">
                                    <label for="gpt4oEditPromptInput" class="form-label fw-bold">编辑指令 (Prompt)</label>
                                    <textarea class="form-control" id="gpt4oEditPromptInput" rows="4" placeholder="例如：将图1的风格变成图2的风格，造型不变"></textarea>
                                </div>

                                <!-- 新增：图片质量选择 -->
                                <div class="mb-3">
                                    <label for="gpt4oEditQualitySelect" class="form-label">图片质量</label>
                                    <select class="form-select" id="gpt4oEditQualitySelect">
                                        <option value="auto" selected>自动 (默认)</option>
                                        <option value="high">高 (high)</option>
                                        <option value="medium">中 (medium)</option>
                                        <option value="low">低 (low)</option>
                                    </select>
                                    <div class="form-text">选择生成图片的质量。"自动"将使用模型默认值。</div>
                                </div>

                                <!-- 新增：图片尺寸选择 -->
                                <div class="mb-3">
                                    <label for="gpt4oEditSizeSelect" class="form-label">图片尺寸</label>
                                    <select class="form-select" id="gpt4oEditSizeSelect">
                                        <option value="auto" selected>自动 (默认)</option>
                                        <option value="1024x1024">1024x1024</option>
                                        <option value="1536x1024">1536x1024</option>
                                        <option value="1024x1536">1024x1536</option>
                                    </select>
                                    <div class="form-text">选择生成图片的尺寸。"自动"将使用模型默认值。</div>
                                </div>

                                <!-- 新增：生成数量 -->
                                <div class="mb-3">
                                    <label for="gpt4oEditNSelect" class="form-label">生成数量 (n)</label>
                                    <input type="number" class="form-control form-control-sm" id="gpt4oEditNSelect" value="1" min="1" max="10" style="width: 100px;">
                                    <div class="form-text">图片生成数量 (1-10)。注意：部分模型（如 DALL-E 3 用于编辑时）可能仅支持 n=1。</div>
                                </div>

                                <div class="mb-3">
                                    <label for="numImagesAdvanced" class="form-label">图片数量:</label>
                                    <input type="number" class="form-control" id="numImagesAdvanced" value="1" min="1" max="10"> 
                                    <!-- 你可以根据需要调整 max 值 -->
                                </div>

                                <button type="button" class="btn btn-primary w-100" id="startGpt4oImageEditBtn" disabled>
                                    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true" style="display: none;"></span>
                                    <i class="bi bi-magic me-2"></i> 开始编辑
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-7"> <!-- 右侧结果区域 -->
                        <div class="card">
                            <div class="card-header">编辑结果</div> <!-- 修改标题，历史记录将有单独卡片 -->
                            <div class="card-body">
                                 <div id="gpt4oEditLoadingIndicator" style="display: none;" class="text-center my-3">
                                    <div class="loading-dots">
                                        <span></span><span></span><span></span>
                                    </div>
                                    <p>正在编辑图片，请稍候...</p>
                                </div>
                                <div id="gpt4oEditErrorAlert" class="alert alert-danger" style="display: none;" role="alert">
                                    编辑失败，请检查输入或稍后重试。
                                </div>

                                <div id="gpt4oEditResultContainer" style="display: none;">
                                    <h6 class="mb-2">编辑结果:</h6>
                                    <img id="gpt4oEditResultImage" src="#" alt="Edited image" class="img-fluid rounded border mb-2" style="background-color: rgba(0,0,0,0.2);">
                                    <a id="gpt4oEditResultDownloadLink" href="#" target="_blank" class="btn btn-outline-secondary btn-sm w-100 mt-2">
                                        <i class="bi bi-download me-1"></i> 下载编辑后的图片
                                    </a>
                                    <button type="button" class="btn btn-info btn-sm w-100 mt-2" id="sendGpt4oEditedImageToExamplesBtn" style="display:none;">
                                        <i class="bi bi-send-plus me-1"></i> 上传到案例库
                                    </button>
                                </div>
                                <p class="text-muted text-center" id="gpt4oEditResultsPlaceholder">在这里查看编辑结果...</p> 
                                <!-- 移除了此处错误的 hr 和 history list -->
                            </div>
                        </div>

                        <!-- 新增：GPT-4o 编辑历史记录卡片 -->
                        <div class="card mt-4">
                            <div class="card-header">
                                编辑历史
                            </div>
                            <div class="card-body">
                                <div id="gpt4oEditHistoryPlaceholder" class="text-center text-muted my-3" style="display: block;">
                                    加载历史记录中...
                                </div>
                                <div id="gpt4oEditHistoryList" class="row row-cols-2 row-cols-sm-3 row-cols-md-4 g-3 mb-3">
                                    <!-- 历史记录项会动态添加到这里 -->
                                </div>
                                <div id="gpt4oEditHistoryPagination" class="text-center">
                                    <!-- "加载更多"按钮会在这里创建 -->
                                </div>
                            </div>
                        </div>
                         <!-- 结束：GPT-4o 编辑历史记录卡片 -->
                    </div>
                </div>

                <!-- 移除了页面底部重复的 <hr>, <h5>编辑历史</h5> 和历史记录相关的 div -->
            </div>
            <!-- 结束：GPT-4o 图片编辑 Tab Pane -->

            <!-- 新增：AI 聊天助手 Tab Pane -->
            <div class="tab-pane fade" id="ai-chat-assistant-pane" role="tabpanel" aria-labelledby="ai-chat-assistant-tab" tabindex="0">
                <div class="row" id="aiChatMainRow"> <!-- 新增 ID -->
                    <!-- 左侧：聊天区域 -->
                    <div class="col-md-8" id="aiChatLeftPanel"> <!-- 新增 ID -->
                        <div class="card ai-chat-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <span>与 AI 助手对话</span>
                                <div class="ai-chat-header-actions d-flex align-items-center"> <!-- 新增 d-flex align-items-center -->
                                    <div class="text-muted small me-3" id="aiChatTokenInfo" style="display: none;"> <!-- 移动到此处，并添加 me-3 -->
                                        当前对话 Token: <span id="aiChatCurrentTokens">0</span>
                                    </div>
                                    <button class="btn btn-sm btn-outline-secondary me-2" id="aiChatNewConversationBtn" title="开始新对话">
                                        <i class="bi bi-plus-lg"></i> 新对话
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#aiChatRightPanelCollapse" aria-expanded="true" aria-controls="aiChatRightPanelCollapse" id="toggleAiChatRightPanelBtn" title="收起设置与历史面板">
                                        <i class="bi bi-layout-sidebar-inset-reverse"></i> <!-- 图标示例 -->
                                    </button>
                                </div>
                            </div>
                            <div class="card-body ai-chat-messages-container" id="aiChatMessagesContainer">
                                <!-- 消息将动态添加到这里 -->
                                <div class="text-muted text-center p-4" id="aiChatStartPlaceholder">
                                    <i class="bi bi-chat-quote-fill fs-2 mb-2"></i>
                                    <p>开始与 AI 聊天助手对话吧！<br>你可以在右侧配置 API Key 和模型参数。</p>
                                </div>
                            </div>
                            <div class="card-footer ai-chat-input-area">
                                <!-- 新增：图片预览区域 -->
                                <div class="ai-chat-image-preview mb-2" id="aiChatImagePreview" style="display: none;">
                                    <img src="" alt="Image preview" id="aiChatPreviewImage">
                                    <button type="button" class="btn-close btn-close-white" aria-label="Remove image" id="aiChatRemoveImageBtn"></button>
                                </div>
                                <!-- 结束：图片预览区域 -->
                                <div class="input-group">
                                    <!-- 新增：隐藏的文件输入 -->
                                    <input type="file" id="aiChatFileInput" accept="image/*" style="display: none;">
                                    <!-- 新增：图片上传按钮 -->
                                    <button class="btn btn-outline-secondary" type="button" id="aiChatUploadImageBtn" title="上传图片">
                                        <i class="bi bi-image"></i>
                                    </button>
                                    <textarea class="form-control" id="aiChatInput" placeholder="输入你的消息 (Shift + Enter 换行)" rows="1"></textarea>
                                    <button class="btn btn-primary" id="aiChatSendBtn" type="button">
                                        <i class="bi bi-send-fill"></i>
                                        <span class="ms-1">发送</span>
                                    </button>
                                </div>
                                <!-- Token 信息从这里移除 -->
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：设置与历史记录区域 -->
                    <div class="col-md-4 collapse show" id="aiChatRightPanelCollapse"> <!-- 修改：添加 collapse show 和 ID -->
                        <div class="card">
                            <div class="card-header">
                                <ul class="nav nav-tabs card-header-tabs" id="aiChatSettingsTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="ai-chat-settings-tab-btn" data-bs-toggle="tab" data-bs-target="#ai-chat-settings-pane-content" type="button" role="tab" aria-controls="ai-chat-settings-pane-content" aria-selected="true">
                                            <i class="bi bi-gear-fill me-1"></i> 设置
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="ai-chat-history-tab-btn" data-bs-toggle="tab" data-bs-target="#ai-chat-history-pane-content" type="button" role="tab" aria-controls="ai-chat-history-pane-content" aria-selected="false">
                                            <i class="bi bi-clock-history me-1"></i> 历史会话
                                        </button>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body tab-content" id="aiChatSettingsTabContent">
                                <!-- 设置 Tab 内容 -->
                                <div class="tab-pane fade show active" id="ai-chat-settings-pane-content" role="tabpanel" aria-labelledby="ai-chat-settings-tab-btn">
                                    <h6 class="mb-3">聊天参数设置</h6>

                                    <!-- New Platform Select and Dynamic API Key Input Area -->
                                    <div class="mb-3">
                                        <label for="aiChatApiPlatformSelectBtn" class="form-label">选择 AI 平台</label> <!-- Changed label 'for' attribute -->
                                        <!-- Modified Platform Select -->
                                        <div class="dropdown">
                                            <button class="form-select form-select-sm text-start dropdown-toggle" type="button" id="aiChatApiPlatformSelectBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                                加载中...
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="aiChatApiPlatformSelectBtn" id="aiChatApiPlatformSelectMenu">
                                                <!-- Options will be populated by JS -->
                                            </ul>
                                            <input type="hidden" id="aiChatApiPlatformSelectValue" value="">
                                        </div>
                                        <!-- End Modified Platform Select -->
                                    </div>

                                    <div id="dynamicApiKeyInputArea" class="mb-3">
                                        <!-- API Key input for the selected platform will be dynamically inserted here by JS -->
                                        <p class="text-muted small">请先从上方选择一个 AI 平台以配置其 API Key。</p>
                                    </div>
                                    <!-- End New Platform Select -->

                                    <div class="mb-3">
                                        <label for="aiChatModelSelect" class="form-label">选择模型</label>
                                        <!-- Modified Model Select -->
                                        <div class="dropdown">
                                            <button class="form-select form-select-sm text-start dropdown-toggle" type="button" id="aiChatModelSelectBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                                输入API Key并保存以加载模型...
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="aiChatModelSelectBtn" id="aiChatModelSelectMenu">
                                                <li><a class="dropdown-item" href="#" data-value="">输入API Key并保存以加载模型...</a></li>
                                            </ul>
                                            <input type="hidden" id="aiChatModelSelectValue" value="">
                                        </div>
                                        <!-- End Modified Model Select -->
                                    </div>
                                    <div class="mb-3">
                                        <label for="aiChatPersonaSelect" class="form-label">选择人设 (Persona):</label>
                                        <!-- Modified Persona Select -->
                                        <div class="dropdown">
                                            <button class="form-select form-select-sm text-start dropdown-toggle" type="button" id="aiChatPersonaSelectBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                                加载中...
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="aiChatPersonaSelectBtn" id="aiChatPersonaSelectMenu">
                                                <!-- Options will be populated by JavaScript -->
                                            </ul>
                                            <input type="hidden" id="aiChatPersonaSelectValue" value="">
                                        </div>
                                        <!-- End Modified Persona Select -->
                                    </div>
                                    <div id="aiChatCustomPersonaContainer" class="mb-3" style="display: none;">
                                        <label for="aiChatCustomPersonaTextarea" class="form-label">自定义人设:</label>
                                        <textarea id="aiChatCustomPersonaTextarea" class="form-control form-control-sm" rows="3" placeholder="在此输入你的自定义人设指令..."></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="aiChatTemperature" class="form-label">采样温度 (Temperature): <span id="aiChatTemperatureValueDisplay">0.7</span></label>
                                        <input type="range" class="form-range" id="aiChatTemperature" min="0" max="2" step="0.1" value="0.7">
                                    </div>
                                    <div class="mb-3">
                                        <label for="aiChatTopP" class="form-label">Top_P: <span id="aiChatTopPValueDisplay">1.0</span></label>
                                        <input type="range" class="form-range" id="aiChatTopP" min="0" max="1" step="0.01" value="1.0">
                                    </div>
                                    <div class="mb-3">
                                        <label for="aiChatMaxTokens" class="form-label">最大 Token 数 (Max Tokens)</label>
                                        <input type="number" class="form-control form-control-sm" id="aiChatMaxTokens" placeholder="默认自动">
                                        <small class="form-text text-muted">留空则使用模型默认值。</small>
                                    </div>
                                     <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="aiChatStreamResponse" checked>
                                        <label class="form-check-label" for="aiChatStreamResponse">
                                            开启流式响应
                                        </label>
                                    </div>
                                    <button class="btn btn-sm btn-primary w-100" id="aiChatSaveSettingsBtn">
                                        <i class="bi bi-check-circle-fill me-1"></i> 保存设置
                                    </button>
                                </div>
                                <!-- 历史会话 Tab 内容 -->
                                <div class="tab-pane fade" id="ai-chat-history-pane-content" role="tabpanel" aria-labelledby="ai-chat-history-tab-btn">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">历史会话</h6>
                                        <button class="btn btn-sm btn-outline-danger" id="aiChatClearHistoryBtn" style="display: none;">
                                            <i class="bi bi-trash3-fill me-1"></i> 清空全部
                                        </button>
                                    </div>
                                    <div id="aiChatHistoryList" class="list-group list-group-flush">
                                        <!-- 历史会话项将由 JS 动态填充 -->
                                        <p class="text-muted text-center small p-3" id="aiChatHistoryEmptyPlaceholder">暂无历史会话记录。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 结束：AI 聊天助手 Tab Pane -->

            <div class="tab-pane fade" id="remove-anything-pane" role="tabpanel" aria-labelledby="remove-anything-tab" tabindex="0">
                <div class="row">
                    <!-- 左侧：参数配置 -->
                    <div class="col-lg-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <i class="bi bi-sliders me-2"></i>参数配置
                            </div>
                            <div class="card-body">
                                <form id="removeAnythingForm">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">1. 上传主图片 <span class="text-danger">(必需)</span></label>
                                        <div class="image-drop-zone" id="removeAnythingImageDropZone">
                                            <i class="bi bi-cloud-arrow-up fs-1 text-muted"></i>
                                            <p class="mb-0">拖拽主图片或点击选择</p>
                                            <small class="text-muted">支持 PNG, JPG, WEBP, 最大 5MB</small>
                                        </div>
                                        <input type="file" class="form-control" id="removeImageInput" accept="image/png,image/jpeg,image/webp" style="display: none;">
                                        <div class="preview-container mt-2" id="removeAnythingImagePreviewContainer" style="display: none;">
                                            <img id="removeAnythingImagePreviewImg" class="preview-img img-fluid rounded" alt="主图片预览">
                                            <div class="mt-2 text-center">
                                                <button type="button" class="btn btn-sm btn-outline-primary" id="createMaskBtn">
                                                    <i class="bi bi-brush"></i> 涂抹蒙版
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger" id="removeAnythingMainImageBtn">
                                                    <i class="bi bi-trash"></i> 移除主图片
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold">2. Mask <span class="text-danger">(必需)</span></label>
                                        <div id="generatedMaskPreviewContainer" style="display: none; border: 1px dashed #6c757d; padding: 10px; text-align: center;">
                                            <img id="generatedMaskPreviewImg" class="img-fluid rounded mb-2" alt="绘制的蒙版预览" style="max-height: 200px;">
                                            <p class="text-muted small mb-0">已通过涂抹生成蒙版。</p>
                                        </div>
                                        <p id="noMaskGeneratedText" class="text-muted small">请先上传主图片，然后点击"涂抹蒙版"按钮生成蒙版。如果有影子蒙版必须涂上影子。</p>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="removePromptInput" class="form-label fw-bold">3. 提示词 <span class="text-danger">(必需)</span></label>
                                        <textarea class="form-control" id="removePromptInput" rows="3" placeholder="请输入要移除的物体，如人物：女孩、男孩，动物：猫、狗，物品：杯子、椅子" required></textarea>
                                    </div>

                                    <div class="row gx-2 mb-3">
                                        <div class="col-md-6">
                                            <label for="removeExpandInput" class="form-label">Mask扩展</label>
                                            <input type="number" class="form-control" id="removeExpandInput" value="50" min="0">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="removeStrengthInput" class="form-label">移除强度</label>
                                            <input type="number" class="form-control" id="removeStrengthInput" value="1.0" step="0.01" min="0" max="2">
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="removeOutputMPInput" class="form-label">输出分辨率 (0.5-1)</label>
                                        <input type="number" class="form-control" id="removeOutputMPInput" value="0.5" step="0.01" min="0.5" max="1">
                                    </div>

                                    <button type="submit" class="btn btn-primary w-100" id="startRemoveAnythingBtn">
                                        <i class="bi bi-magic me-2"></i>开始移除
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：结果与历史 -->
                    <div class="col-lg-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <i class="bi bi-image-fill me-2"></i>处理结果
                            </div>
                            <div class="card-body text-center">
                                <div id="removeAnythingResultPlaceholder" class="py-5 text-muted">
                                    <i class="bi bi-image-alt fs-1"></i>
                                    <p>在这里查看处理结果</p>
                                </div>
                                <div id="removeAnythingResultLoadingIndicator" class="loading-shimmer-card py-5" style="display: none;">
                                    <div class="text-center d-flex flex-column justify-content-center align-items-center" style="height: 100%;">
                                        <div>
                                            <div class="loading-dots">
                                                <span></span><span></span><span></span>
                                            </div>
                                            <p>正在处理图片，请稍候...</p>
                                        </div>
                                    </div>
                                </div>
                                <div id="removeAnythingResultContent" style="display: none;">
                                    <img id="removeAnythingResultImage" class="img-fluid rounded mb-3" alt="处理结果">
                                    <a id="removeAnythingResultDownloadLink" href="#" class="btn btn-primary btn-hover-glow w-100">
                                        <i class="bi bi-download me-2"></i>下载结果
                                    </a>
                                </div>
                                <div id="removeAnythingErrorAlert" class="alert alert-danger mt-3" style="display: none;"></div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <i class="bi bi-clock-history me-2"></i>历史记录
                            </div>
                            <div class="card-body">
                                <div id="removeAnythingHistoryList" class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-3">
                                    <!-- 历史记录卡片将由JS动态填充 -->
                                </div>
                                <div id="removeAnythingHistoryPagination" class="mt-3 text-center">
                                    <!-- 加载更多按钮将由JS动态添加 -->
                                </div>
                                <div id="removeAnythingHistoryPlaceholder" class="text-muted mt-3 text-center" style="display: none;">
                                    <!-- 占位符文本将由JS动态设置 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> <!-- 结束：移除万物 Tab Pane -->


            <div class="tab-pane fade" id="ecommerce-pane" role="tabpanel" aria-labelledby="ecommerce-tab" tabindex="0">
                <!-- 卡片网格 -->
                <div class="row row-cols-1 row-cols-md-3 g-4 mt-4" id="ecommerce-tools-grid">
                    <!-- 工具卡片将通过JavaScript动态加载 -->
                    <div id="ecommerce-tools-loading" class="col-12 text-center py-5">
                        <div class="spinner-border text-light" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-light">正在加载电商工具...</p>
                    </div>
                </div>
            </div>
                
                <!-- 工具内容区域 - 默认隐藏 -->
                <div id="ecommerce-tool-content" style="display: none;">
                    <!-- 印花提取工具内容 -->
                    <div id="pattern-extraction-content" class="ecommerce-tool-content-panel">
                        <div class="row">
                            <div class="col-12 mb-4">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h3>印花提取</h3>
                                    <button class="btn btn-outline-secondary" id="back-to-ecommerce-tools">
                                        <i class="bi bi-arrow-left me-1"></i>返回
                                    </button>
                                </div>
                                <p class="text-muted">从图片中提取印花图案，去除背景并优化图案细节</p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-5 mb-4">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <span>上传图片提取印花</span>
                                    </div>
                                    <div class="card-body">
                                        <div class="image-drop-zone mb-3" id="patternExtractionDropZone">
                                            <i class="bi bi-cloud-arrow-up"></i>
                                            <p>将图片拖拽到此处，或点击选择文件</p>
                                            <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
                                        </div>
                                        <input class="visually-hidden" type="file" id="patternExtractionInput" accept="image/*">
                                        <div class="preview-container mt-2" id="patternExtractionPreviewContainer" style="display: none;">
                                            <img src="" class="preview-img" id="patternExtractionPreviewImg">
                                            <div class="mt-2 text-center">
                                                <button type="button" class="btn btn-sm btn-outline-danger" id="removePatternExtractionImage">
                                                    <i class="bi bi-trash"></i> 移除图片
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <hr class="my-4">
                                        
                                        <div class="mb-3">
                                            <label class="form-label">提取选项</label>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="enhancePatternCheck" checked>
                                                <label class="form-check-label" for="enhancePatternCheck">
                                                    增强印花细节
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="removeBackgroundCheck" checked>
                                                <label class="form-check-label" for="removeBackgroundCheck">
                                                    去除背景
                                                </label>
                                            </div>
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="vectorizePatternCheck">
                                                <label class="form-check-label" for="vectorizePatternCheck">
                                                    转换为矢量图 (SVG)
                                                </label>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="patternSize" class="form-label">输出尺寸</label>
                                                <div class="dropdown">
                                                    <button class="form-select text-start dropdown-toggle" type="button" id="patternSizeBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                                        1024 x 1024
                                                    </button>
                                                    <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="patternSizeBtn" id="patternSizeMenu">
                                                        <li><a class="dropdown-item" href="#" data-value="512x512">512 x 512</a></li>
                                                        <li><a class="dropdown-item active" href="#" data-value="1024x1024">1024 x 1024</a></li>
                                                        <li><a class="dropdown-item" href="#" data-value="1280x1280">1280 x 1280</a></li>
                                                        <li><a class="dropdown-item" href="#" data-value="1536x1536">1536 x 1536</a></li>
                                                        <li><a class="dropdown-item" href="#" data-value="2048x2048">2048 x 2048</a></li>
                                                    </ul>
                                                    <input type="hidden" id="patternSize" value="1024x1024">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="d-grid mt-4">
                                            <button class="btn btn-primary btn-lg" id="extractPatternBtn" disabled>
                                                <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true" style="display: none;"></span>
                                                <i class="bi bi-palette2 me-1"></i>开始提取印花
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-7">
                                <div class="card">
                                    <div class="card-header">提取结果</div>
                                    <div class="card-body">
                                        <div id="patternExtractionLoading" style="display: none;">
                                            <div class="loading-dots">
                                                <span></span><span></span><span></span>
                                            </div>
                                            <p class="text-center progress-message">正在提取印花，请稍候...</p>
                                            <div class="progress mt-3">
                                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                                            </div>
                                        </div>
                                        <div id="patternExtractionResultContainer" style="display: none;">
                                            <div class="text-center">
                                                <img src="" id="patternExtractionResultImg" class="img-fluid rounded mb-3" alt="提取的印花">
                                                <div class="mt-3">
                                                    <a href="#" class="btn btn-primary" id="downloadPatternBtn" download>
                                                        <i class="bi bi-download me-1"></i>下载印花
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="patternExtractionError" class="alert alert-danger" style="display: none;">
                                            <!-- 错误信息将由JS设置 -->
                                        </div>
                                        <p class="text-muted text-center" id="patternExtractionPlaceholder">上传图片并点击提取按钮，查看提取的印花结果</p>
                                    </div>
                                </div>
                                
                                <!-- 历史记录卡片 -->
                                <div class="card mt-4">
                                    <div class="card-header">历史记录</div>
                                    <div class="card-body">
                                        <div id="patternExtractionHistoryList" class="history-list-flex">
                                            <!-- 历史记录将在这里加载 -->
                                            <p class="text-muted text-center w-100" id="patternExtractionHistoryPlaceholder">加载历史记录中或暂无记录...</p>
                                        </div>
                                        <!-- 加载更多按钮容器 -->
                                        <div id="patternExtractionHistoryLoadMoreContainer" class="text-center mt-3" style="display: none;">
                                            <button class="btn btn-outline-secondary btn-sm" id="loadMorePatternExtractionHistoryBtn">加载更多</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 新增：闪光灯 Tab Pane -->
            <div class="tab-pane fade" id="flash-light-pane" role="tabpanel" aria-labelledby="flash-light-tab" tabindex="0">
                <div class="row">
                    <div class="col-md-5 mb-4"> <!-- 左侧上传与操作区域 -->
                        <div class="card">
                            <div class="card-header">上传图片并选择功能</div>
                            <div class="card-body">
                                <!-- 新增：功能类型选择 -->
                                <div class="mb-3">
                                    <label for="flFunctionTypeSelectBtn" class="form-label">选择功能类型</label>
                                    <div class="dropdown">
                                        <button class="form-select text-start dropdown-toggle" type="button" id="flFunctionTypeSelectBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                            AI补光灯
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="flFunctionTypeSelectBtn" id="flFunctionTypeSelectMenu">
                                            <li><a class="dropdown-item active" href="#" data-value="flash_light">AI补光灯</a></li>
                                            <li><a class="dropdown-item" href="#" data-value="beauty">AI美颜</a></li>
                                        </ul>
                                        <input type="hidden" id="flFunctionTypeSelectValue" value="flash_light">
                                    </div>
                                </div>
                                
                                <!-- 美颜参数控制 - 初始隐藏 -->
                                <div id="beautyParamsContainer" style="display: none;">
                                    <div class="mb-3">
                                        <label for="beautyStrengthRange" class="form-label">美颜强度: <span id="beautyStrengthValue">1</span></label>
                                        <input type="range" class="form-range" id="beautyStrengthRange" min="0" max="1.5" step="0.1" value="1">
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="enhancedBeautyCheck">
                                        <label class="form-check-label" for="enhancedBeautyCheck">
                                            启用强化美颜(效果不好再开)
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="slimFaceCheck">
                                        <label class="form-check-label" for="slimFaceCheck">
                                            启用高P超瘦脸（效果不好再开）
                                        </label>
                                    </div>
                                </div>

                                <h6>1. 上传图片</h6>
                                <p class="text-muted small">选择 PNG, JPG, 或 WEBP 格式的图片。最大 5MB。</p>
                                <div class="image-drop-zone mb-3" id="flImageDropZone">
                                    <i class="bi bi-cloud-arrow-up"></i>
                                    <p>将图片拖拽到此处，或点击选择文件</p>
                                    <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
                                </div>
                                <input class="visually-hidden" type="file" id="flImageInput" accept="image/png, image/jpeg, image/webp">
                                <div class="preview-container mt-2" id="flPreviewContainer" style="display: none;">
                                    <img src="" class="preview-img" id="flPreviewImg">
                                    <div class="mt-2 text-center">
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="removeFlImage">
                                            <i class="bi bi-trash"></i> 移除图片
                                        </button>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <h6>2. 开始处理</h6>
                                <p class="text-muted small" id="flProcessDescription">开始补光，拯救废片。</p>
                                <div class="d-grid">
                                    <button class="btn btn-primary btn-lg" id="startFlBtn" disabled>
                                        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true" style="display: none;"></span>
                                        <i class="bi bi-lightning me-1"></i>开始补光，拯救废片
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-7"> <!-- 右侧结果区域 -->
                        <div class="card">
                            <div class="card-header">处理结果</div>
                            <div class="card-body">
                                <div id="flResultLoading" class="loading-shimmer-card" style="display: none;">
                                    <div class="loading-dots">
                                        <span></span><span></span><span></span>
                                    </div>
                                    <p>正在添加AI提亮...</p>
                                </div>
                                <div id="flResultContainer" style="display: none;">
                                    <img id="flResultImage" src="#" alt="Flash light image" class="img-fluid rounded border mb-2" style="background-color: rgba(0,0,0,0.2);">
                                    <a id="flResultLink" href="#" target="_blank" class="btn btn-outline-secondary btn-hover-glow btn-sm w-100 mt-2">
                                        <i class="bi bi-download me-1"></i> 在新标签页打开或下载
                                    </a>
                                </div>
                                <div id="flErrorAlert" class="alert alert-danger mt-3" role="alert" style="display: none;">
                                     <!-- Error message will be set by JS -->
                                 </div>
                                <p class="text-muted text-center" id="flResultsPlaceholder">在这里查看处理结果...</p>
                            </div>
                        </div>

                        <!-- 闪光灯历史记录卡片 -->
                        <div class="card mt-4">
                            <div class="card-header">
                                历史记录
                            </div>
                            <div class="card-body">
                                <div id="flHistoryList" class="history-list-flex">
                                    <!-- History items will be loaded here -->
                                    <p class="text-muted text-center w-100" id="flHistoryPlaceholder">加载历史记录中或暂无记录...</p>
                                </div>
                                <!-- 加载更多按钮容器 -->
                                <div id="flHistoryLoadMoreContainer" class="text-center mt-3" style="display: none;"> 
                                    <button class="btn btn-outline-secondary btn-sm" id="loadMoreFlHistoryBtn">加载更多</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> <!-- 结束：闪光灯 Tab Pane -->
            
            <!-- 在现有的标签页内容之后添加Flux图片处理标签页内容 -->
            <div class="tab-pane fade" id="flux-kontext-pane" role="tabpanel" aria-labelledby="flux-kontext-tab" tabindex="0">
                <div class="row">
                    <div class="col-md-5 mb-4"> <!-- 左侧上传与参数区域 -->
                        <div class="card">
                            <div class="card-header">
                                <span>上传图片并设置参数</span>
                            </div>
                            <div class="card-body">
                                <!-- 工作流选择 -->
                                <div class="mb-3">
                                    <label for="fluxWorkflowSelectBtn" class="form-label">工作流选择</label>
                                    <div class="dropdown">
                                        <button class="form-select text-start dropdown-toggle" type="button" id="fluxWorkflowSelectBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                            Flux 智能编辑 (多图)
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="fluxWorkflowSelectBtn" id="fluxWorkflowSelectMenu">
                                            <li><a class="dropdown-item active" href="#" data-value="flux_kontext_pro">Flux 智能编辑 (多图)</a></li>
                                            <li><a class="dropdown-item" href="#" data-value="comfy_flux_kontext">Comfy Flux 上下文(单图)</a></li>
                                        </ul>
                                        <input type="hidden" id="fluxWorkflowSelectValue" value="flux_kontext_pro">
                                    </div>
                                </div>
                                
                                <!-- 普通处理模式 -->
                                <div id="fluxNormalMode">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>1. 上传输入图片1</h6>
                                            <p class="text-muted small">选择 PNG, JPG, 或 WEBP 格式的图片。</p>
                                    <div class="image-drop-zone mb-3" id="fluxImageDropZone">
                                        <i class="bi bi-cloud-arrow-up"></i>
                                        <p>将输入图片1拖拽到此处，或点击选择文件</p>
                                        <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
                                    </div>
                                    <input class="visually-hidden" type="file" id="fluxImageInput" accept="image/png, image/jpeg, image/webp">
                                    <div class="preview-container mt-2" id="fluxPreviewContainer" style="display: none;">
                                        <img src="" class="preview-img" id="fluxPreviewImg">
                                        <div class="mt-2 text-center">
                                            <button type="button" class="btn btn-sm btn-outline-danger me-2" id="removeFluxImage">
                                                <i class="bi bi-trash"></i> 移除输入图片1
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-primary" id="openFluxExpandBtn">
                                                <i class="bi bi-arrows-fullscreen"></i> 扩图
                                            </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6" id="fluxImage2Column">
                                            <h6>2. 上传输入图片2</h6>
                                            <p class="text-muted small">选择 PNG, JPG, 或 WEBP 格式的图片。</p>
                                            <div class="image-drop-zone mb-3" id="fluxImage2DropZone">
                                                <i class="bi bi-cloud-arrow-up"></i>
                                                <p>将输入图片2拖拽到此处，或点击选择文件</p>
                                                <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
                                            </div>
                                            <input class="visually-hidden" type="file" id="fluxImage2Input" accept="image/png, image/jpeg, image/webp">
                                            <div class="preview-container mt-2" id="fluxPreview2Container" style="display: none;">
                                                <img src="" class="preview-img" id="fluxPreview2Img">
                                                <div class="mt-2 text-center">
                                                    <button type="button" class="btn btn-sm btn-outline-danger" id="removeFluxImage2">
                                                        <i class="bi bi-trash"></i> 移除输入图片2
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <hr class="my-4">

                                    <h6>3. 设置生成参数</h6>
                                    <div class="mb-3">
                                        <label for="fluxPromptInput" class="form-label">提示词 (Prompt)</label>
                                        <textarea class="form-control" id="fluxPromptInput" rows="3" placeholder="描述你想要生成的图片内容" required></textarea>
                                    </div>
                                    <div id="fluxProParams">
                                        <div class="row g-2">
                                            <div class="col-md-6 mb-3">
                                                <label for="fluxAspectRatioSelect" class="form-label">画面比例</label>
                                                <!-- <select class="form-select" id="fluxAspectRatioSelect">
                                                    <option value="1:1" selected>1:1 (正方形)</option>
                                                    <option value="16:9">16:9 (横向)</option>
                                                    <option value="9:16">9:16 (纵向)</option>
                                                    <option value="4:3">4:3 (横向)</option>
                                                    <option value="3:4">3:4 (纵向)</option>
                                                    <option value="21:9">21:9 (超宽)</option>
                                                    <option value="9:21">9:21 (超窄)</option>
                                                </select> -->
                                                <div class="dropdown">
                                                    <button class="form-select text-start dropdown-toggle" type="button" id="fluxAspectRatioBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                                        1:1 (正方形)
                                                    </button>
                                                    <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="fluxAspectRatioBtn" id="fluxAspectRatioMenu">
                                                        <li><a class="dropdown-item active" href="#" data-value="1:1">1:1 (正方形)</a></li>
                                                        <li><a class="dropdown-item" href="#" data-value="16:9">16:9 (横向)</a></li>
                                                        <li><a class="dropdown-item" href="#" data-value="9:16">9:16 (纵向)</a></li>
                                                        <li><a class="dropdown-item" href="#" data-value="4:3">4:3 (横向)</a></li>
                                                        <li><a class="dropdown-item" href="#" data-value="3:4">3:4 (纵向)</a></li>
                                                        <li><a class="dropdown-item" href="#" data-value="21:9">21:9 (超宽)</a></li>
                                                        <li><a class="dropdown-item" href="#" data-value="9:21">9:21 (超窄)</a></li>
                                                    </ul>
                                                    <input type="hidden" id="fluxAspectRatioValue" value="1:1">
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="fluxSeedInput" class="form-label">随机种子 (可选)</label>
                                                <input type="number" class="form-control" id="fluxSeedInput" placeholder="留空则随机">
                                            </div>
                                        </div>
                                        <div class="row g-2">
                                            <div class="col-md-6 mb-3">
                                                <label for="fluxOutputFormatSelect" class="form-label">输出格式</label>
                                                <!-- <select class="form-select" id="fluxOutputFormatSelect">
                                                    <option value="png" selected>PNG</option>
                                                    <option value="jpeg">JPEG</option>
                                                </select> -->
                                                <div class="dropdown">
                                                    <button class="form-select text-start dropdown-toggle" type="button" id="fluxOutputFormatBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                                        PNG
                                                    </button>
                                                    <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="fluxOutputFormatBtn" id="fluxOutputFormatMenu">
                                                        <li><a class="dropdown-item active" href="#" data-value="png">PNG</a></li>
                                                        <li><a class="dropdown-item" href="#" data-value="jpeg">JPEG</a></li>
                                                    </ul>
                                                    <input type="hidden" id="fluxOutputFormatValue" value="png">
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="fluxSafetyToleranceInput" class="form-label">安全级别 (0-6)</label>
                                                <input type="number" class="form-control" id="fluxSafetyToleranceInput" value="2" min="0" max="6">
                                                <div class="form-text">0最严格，6最宽松</div>
                                            </div>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="fluxPromptUpsamplingCheck">
                                            <label class="form-check-label" for="fluxPromptUpsamplingCheck">
                                                启用提示词优化 (自动优化提示词以获得更好的效果)
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 扩图功能模式 -->
                                <div id="fluxExpandMode" style="display: none;">
                                    <h6>1. 上传需要扩图的图片 (必选)</h6>
                                    <p class="text-muted small">选择 PNG, JPG, 或 WEBP 格式的图片进行扩展。</p>
                                    <div class="image-drop-zone mb-3" id="fluxExpandImageDropZone">
                                        <i class="bi bi-cloud-arrow-up"></i>
                                        <p>将图片拖拽到此处，或点击选择文件</p>
                                        <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
                                    </div>
                                    <input class="visually-hidden" type="file" id="fluxExpandImageInput" accept="image/png, image/jpeg, image/webp">
                                    <div class="preview-container mt-2" id="fluxExpandPreviewContainer" style="display: none;">
                                        <img src="" class="preview-img" id="fluxExpandPreviewImg">
                                        <div class="mt-2 text-center">
                                            <button type="button" class="btn btn-sm btn-outline-danger" id="removeFluxExpandImage">
                                                <i class="bi bi-trash"></i> 移除图片
                                            </button>
                                        </div>
                                    </div>

                                    <hr class="my-4">

                                    <h6>2. 设置扩图参数</h6>
                                    <div class="mb-3">
                                        <label for="fluxExpandPromptInput" class="form-label">提示词 (Prompt)</label>
                                        <textarea class="form-control" id="fluxExpandPromptInput" rows="3" placeholder="描述扩展区域的内容"></textarea>
                                    </div>
                                    
                                    <!-- 在扩图参数区域添加比例选择 -->
                                    <div class="col-md-3 p-4">
                                        <!-- 扩图参数设置 -->
                                        <h6>扩图区域</h6>
                                        
                                        <!-- 添加比例选择 -->
                                        <div class="mb-4">
                                            <label class="form-label">预设比例</label>
                                            <div class="ratio-selector d-flex flex-wrap gap-2">
                                                <button type="button" class="btn btn-sm ratio-btn" data-ratio="original">原始</button>
                                                <button type="button" class="btn btn-sm ratio-btn" data-ratio="1:1">1:1</button>
                                                <button type="button" class="btn btn-sm ratio-btn" data-ratio="16:9">16:9</button>
                                                <button type="button" class="btn btn-sm ratio-btn" data-ratio="9:16">9:16</button>
                                                <button type="button" class="btn btn-sm ratio-btn" data-ratio="4:3">4:3</button>
                                                <button type="button" class="btn btn-sm ratio-btn" data-ratio="3:4">3:4</button>
                                            </div>
                                            <!-- 添加目标尺寸显示 -->
                                            <div id="targetDimensions" class="mt-2 text-center small text-light"></div>
                                        </div>
                                        
                                        <!-- 显示当前扩展像素值，增加可编辑的输入框 -->
                                        <div class="row g-2 mb-3">
                                            <div class="col-6">
                                                <div class="border rounded p-2">
                                                    <div class="small text-muted text-center">顶部</div>
                                                    <div class="input-group input-group-sm">
                                                        <input type="number" class="form-control" id="expandTopInput" value="0" min="0" max="2048">
                                                        <span class="input-group-text">像素</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="border rounded p-2">
                                                    <div class="small text-muted text-center">底部</div>
                                                    <div class="input-group input-group-sm">
                                                        <input type="number" class="form-control" id="expandBottomInput" value="0" min="0" max="2048">
                                                        <span class="input-group-text">像素</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row g-2 mb-3">
                                            <div class="col-6">
                                                <div class="border rounded p-2">
                                                    <div class="small text-muted text-center">左侧</div>
                                                    <div class="input-group input-group-sm">
                                                        <input type="number" class="form-control" id="expandLeftInput" value="0" min="0" max="2048">
                                                        <span class="input-group-text">像素</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="border rounded p-2">
                                                    <div class="small text-muted text-center">右侧</div>
                                                    <div class="input-group input-group-sm">
                                                        <input type="number" class="form-control" id="expandRightInput" value="0" min="0" max="2048">
                                                        <span class="input-group-text">像素</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row g-2">
                                        <div class="col-md-6 mb-3">
                                            <label for="fluxExpandStepsInput" class="form-label">步数 (15-50)</label>
                                            <input type="number" class="form-control" id="fluxExpandStepsInput" value="50" min="15" max="50">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="fluxExpandSeedInput" class="form-label">随机种子 (可选)</label>
                                            <input type="number" class="form-control" id="fluxExpandSeedInput" placeholder="留空则随机">
                                        </div>
                                    </div>
                                    
                                    <div class="row g-2">
                                        <div class="col-md-6 mb-3">
                                            <label for="fluxExpandGuidanceInput" class="form-label">引导强度 (1.5-100)</label>
                                            <input type="number" class="form-control" id="fluxExpandGuidanceInput" value="60" min="1.5" max="100" step="0.1">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="fluxExpandOutputFormatSelect" class="form-label">输出格式</label>
                                            <select class="form-select" id="fluxExpandOutputFormatSelect">
                                                <option value="png" selected>PNG</option>
                                                <option value="jpeg">JPEG</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="row g-2">
                                        <div class="col-md-6 mb-3">
                                            <label for="fluxExpandSafetyToleranceInput" class="form-label">安全级别 (0-6)</label>
                                            <input type="number" class="form-control" id="fluxExpandSafetyToleranceInput" value="2" min="0" max="6">
                                            <div class="form-text">0最严格，6最宽松</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="form-check mt-4">
                                                <input class="form-check-input" type="checkbox" id="fluxExpandPromptUpsamplingCheck">
                                                <label class="form-check-label" for="fluxExpandPromptUpsamplingCheck">
                                                    启用提示词优化
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex mt-4 gap-2">
                                    <button class="btn btn-primary btn-lg flex-grow-1" id="startFluxProcessingBtn">
                                        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true" style="display: none;"></span>
                                        <i class="bi bi-palette me-1"></i>开始处理
                                    </button>
                                    <button class="btn btn-light btn-lg" id="showFluxExamplesBtn" data-bs-toggle="modal" data-bs-target="#fluxExamplesModal">
                                        <i class="bi bi-lightbulb me-1"></i>查看案例
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-7"> <!-- 右侧结果区域 -->
                        <div class="card">
                            <div class="card-header">处理结果</div>
                            <div class="card-body">
                                <div id="fluxResultLoading" class="loading-shimmer-card" style="display: none;">
                                    <div class="text-center d-flex flex-column justify-content-center align-items-center" style="height: 100%;">
                                        <div>
                                            <div class="loading-dots">
                                                <span></span><span></span><span></span>
                                            </div>
                                            <p>正在处理图片，请稍候...</p>
                                        </div>
                                    </div>
                                </div>
                                <div id="fluxResultContainer" style="display: none;">
                                    <div class="text-center">
                                        <img id="fluxResultImage" src="#" alt="Flux处理结果" class="img-fluid rounded border mb-2 cursor-pointer" style="background-color: rgba(0,0,0,0.2); max-height: 300px; object-fit: contain;">
                                    </div>
                                    <a id="fluxResultLink" href="#" target="_blank" class="btn btn-outline-secondary btn-hover-glow btn-sm w-100 mt-2">
                                        <i class="bi bi-download me-1"></i> 在新标签页打开或下载
                                    </a>
                                    <div id="fluxResultPromptContainer" class="mt-3" style="display: none;">
                                        <label class="form-label small text-muted mb-1">提示词:</label>
                                        <p id="fluxResultPrompt" class="alert alert-secondary p-2 small" style="white-space: pre-wrap; word-break: break-all; margin-bottom: 0;"></p>
                                    </div>
                                </div>
                                <div id="fluxErrorAlert" class="alert alert-danger mt-3" role="alert" style="display: none;">
                                    <!-- 错误消息将由JS设置 -->
                                </div>
                                <p class="text-muted text-center" id="fluxResultsPlaceholder">在这里查看Flux处理结果...</p>
                            </div>
                        </div>

                                <!-- Flux历史记录卡片 -->
                                <div class="card mt-4">
                                    <div class="card-header">
                                        历史记录
                                    </div>
                                    <div class="card-body">
                                        <div id="fluxHistoryList" class="history-list-flex">
                                            <!-- 历史记录将由JS动态填充 -->
                                            <p class="text-muted text-center w-100" id="fluxHistoryPlaceholder">加载历史记录中或暂无记录...</p>
                                        </div>
                                        <!-- 加载更多按钮容器 -->
                                        <div id="fluxHistoryLoadMoreContainer" class="text-center mt-3" style="display: none;"> 
                                            <button class="btn btn-outline-secondary btn-sm" id="loadMoreFluxHistoryBtn">加载更多</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Example Modal -->
    <div class="modal fade" id="addExampleModal" tabindex="-1" aria-labelledby="addExampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addExampleModalLabel">添加提示词案例</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="exampleForm">
                        <input type="hidden" id="exampleId">
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">标题</label>
                            <input type="text" class="form-control" id="title" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="category" class="form-label">分类</label>
                            <div class="dropdown">
                                <button class="form-select text-start dropdown-toggle" type="button" id="modalCategoryFilterBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                    选择分类
                                </button>
                                <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="modalCategoryFilterBtn" id="modalCategoryFilterMenu">
                                    <!-- 分类选项将由 JS 填充 -->
                                    <li><a class="dropdown-item active" href="#" data-value="">选择分类</a></li>
                                </ul>
                                <input type="hidden" id="modalCategoryFilterValue" value=""> <!-- 隐藏 input 存储值 -->
                            </div>
                            <!-- 结束：模态框内分类筛选 -->
                        </div>
                        
                        <div class="mb-3">
                            <label for="prompt" class="form-label">提示词</label>
                            <textarea class="form-control" id="prompt" rows="4" required></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="targetModelSelect" class="form-label">适用模型</label>
                            <div class="dropdown">
                                <button class="form-select text-start dropdown-toggle" type="button" id="modalModelFilterBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                    请选择或留空
                                </button>
                                <ul class="dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown" aria-labelledby="modalModelFilterBtn" id="modalModelFilterMenu">
                                     <li><a class="dropdown-item active" href="#" data-value="">请选择或留空</a></li>
                                     <li><a class="dropdown-item" href="#" data-value="MJ">MJ</a></li>
                                     <li><a class="dropdown-item" href="#" data-value="FLUX">FLUX</a></li>
                                     <li><a class="dropdown-item" href="#" data-value="FLUX-多图">FLUX-多图</a></li>
                                     <li><a class="dropdown-item" href="#" data-value="SDXL">SDXL</a></li>
                                     <li><a class="dropdown-item" href="#" data-value="超级生图">超级生图</a></li>
                                     <li><a class="dropdown-item" href="#" data-value="高级生图">高级生图</a></li>
                                     <li><a class="dropdown-item" href="#" data-value="GPT4O">GPT4O</a></li>
                                     <li><a class="dropdown-item" href="#" data-value="GPT4O-编辑">GPT4O-编辑</a></li> <!-- 新增 -->
                                     <li><a class="dropdown-item" href="#" data-value="其他">其他</a></li>
                                </ul>
                                <input type="hidden" id="modalModelFilterValue" value=""> <!-- 隐藏 input 存储值 -->
                            </div>
                            <!-- 结束：模态框内模型筛选 -->
                        </div>
                        
                        <div class="mb-3">
                            <label for="tags" class="form-label">标签</label>
                            <input type="text" class="form-control" id="tags">
                            <small class="text-muted">输入标签并按回车确认</small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">案例图片</label>
                            <div class="image-drop-zone" id="imageDropZone">
                                <i class="bi bi-cloud-arrow-up"></i>
                                <p>将图片拖拽到此处，或点击选择文件</p>
                                <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
                            </div>
                            <input class="visually-hidden" type="file" id="image" accept="image/*">
                            <div class="preview-container mt-2" id="imagePreview" style="display: none;">
                                <img src="" class="preview-img" id="previewImg">
                                <div class="mt-2 text-center">
                                    <button type="button" class="btn btn-sm btn-outline-danger" id="removeImage">
                                        <i class="bi bi-trash"></i> 移除图片
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 被编辑的图片上传 -->
                        <div class="mb-3 conditional-image-upload" id="sourceImageSection" style="display: none;">
                            <label class="form-label">被编辑的图片 (源图片)</label>
                            <div class="image-drop-zone" id="sourceImageDropZone">
                                <i class="bi bi-cloud-arrow-up"></i>
                                <p>将源图片拖拽到此处，或点击选择文件</p>
                            </div>
                            <input class="visually-hidden" type="file" id="source_image" accept="image/*">
                            <div class="preview-container mt-2" id="sourceImagePreview" style="display: none;">
                                <img src="" class="preview-img" id="previewSourceImg">
                                <div class="mt-2 text-center">
                                    <button type="button" class="btn btn-sm btn-outline-danger" id="removeSourceImage">
                                        <i class="bi bi-trash"></i> 移除源图片
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 参考图上传 -->
                        <div class="mb-3 conditional-image-upload" id="referenceImageSection" style="display: none;">
                            <label class="form-label">参考图 (风格/内容参考)</label>
                            <div class="image-drop-zone" id="referenceImageDropZone">
                                <i class="bi bi-cloud-arrow-up"></i>
                                <p>将参考图拖拽到此处，或点击选择文件</p>
                            </div>
                            <input class="visually-hidden" type="file" id="reference_image" accept="image/*">
                            <div class="preview-container mt-2" id="referenceImagePreview" style="display: none;">
                                <img src="" class="preview-img" id="previewReferenceImg">
                                <div class="mt-2 text-center">
                                    <button type="button" class="btn btn-sm btn-outline-danger" id="removeReferenceImage">
                                        <i class="bi bi-trash"></i> 移除参考图
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- FLUX第二张图片上传 -->
                        <div class="mb-3 conditional-image-upload" id="fluxSecondImageSection" style="display: none;">
                            <label class="form-label">图1 (第一张输入图片)</label>
                            <div class="image-drop-zone" id="fluxSecondImageDropZone" onclick="document.getElementById('flux_second_image').click();">
                                <i class="bi bi-cloud-arrow-up"></i>
                                <p>将图1拖拽到此处，或点击选择文件</p>
                            </div>
                            <input class="visually-hidden" type="file" id="flux_second_image" accept="image/*" onchange="if(this.files.length > 0) window.exampleFormHandler.handleFluxSecondImageSelect(this.files[0]);">
                            <div class="preview-container mt-2" id="fluxSecondImagePreview" style="display: none;">
                                <img src="" class="preview-img" id="previewFluxSecondImg">
                                <div class="mt-2 text-center">
                                    <button type="button" class="btn btn-sm btn-outline-danger" id="removeFluxSecondImage" onclick="document.getElementById('fluxSecondImagePreview').style.display='none';document.getElementById('fluxSecondImageDropZone').style.display='block';document.getElementById('previewFluxSecondImg').src='';window.selectedFluxSecondFile=null;">
                                        <i class="bi bi-trash"></i> 移除图1
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- FLUX结果图上传 -->
                        <div class="mb-3 conditional-image-upload" id="fluxResultImageSection" style="display: none;">
                            <label class="form-label">图2 (第二张输入图片)</label>
                            <div class="image-drop-zone" id="fluxResultImageDropZone" onclick="document.getElementById('flux_result_image').click();">
                                <i class="bi bi-cloud-arrow-up"></i>
                                <p>将图2拖拽到此处，或点击选择文件</p>
                            </div>
                            <input class="visually-hidden" type="file" id="flux_result_image" accept="image/*" onchange="if(this.files.length > 0) window.exampleFormHandler.handleFluxResultImageSelect(this.files[0]);">
                            <div class="preview-container mt-2" id="fluxResultImagePreview" style="display: none;">
                                <img src="" class="preview-img" id="previewFluxResultImg">
                                <div class="mt-2 text-center">
                                    <button type="button" class="btn btn-sm btn-outline-danger" id="removeFluxResultImage" onclick="document.getElementById('fluxResultImagePreview').style.display='none';document.getElementById('fluxResultImageDropZone').style.display='block';document.getElementById('previewFluxResultImg').src='';window.selectedFluxResultFile=null;">
                                        <i class="bi bi-trash"></i> 移除图2
                                    </button>
                                </div>
                            </div>
                        </div>
             
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveExample">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 个人资料设置模态框 (新增) -->
    <div class="modal fade" id="profileModal" tabindex="-1" aria-labelledby="profileModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="profileModalLabel">个人资料设置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="profileForm">
                        <div class="mb-3 text-center">
                            <label for="avatarInput" class="form-label">头像</label>
                            <div>
                                <div class="avatar-preview-container mb-2" onclick="document.getElementById('avatarInput').click();" style="cursor: pointer;" title="点击更换头像">
                                     <img src="images/default-avatar.png" alt="头像预览" class="rounded-circle" id="previewAvatarImg" width="100" height="100">
                                     <div class="avatar-edit-overlay">
                                         <i class="bi bi-pencil-fill"></i>
                                     </div>
                                </div>
                                <input class="visually-hidden" type="file" id="avatarInput" accept="image/png, image/jpeg, image/gif">
                                <!-- <button type="button" class="btn btn-sm btn-outline-secondary" onclick="document.getElementById('avatarInput').click();">选择图片</button> -->
                                <small class="d-block text-muted mt-1">建议使用方形图片，大小不超过 2MB</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="nicknameInput" class="form-label">昵称</label>
                            <input type="text" class="form-control" id="nicknameInput" placeholder="设置一个公开显示的昵称">
                            <small class="text-muted">如果不设置，将显示你的用户名</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">用户名 (账号)</label>
                            <input type="text" class="form-control" id="usernameDisplay" readonly disabled>
                        </div>

                        <hr class="my-4">

                        <!-- Email Section -->
                        <div class="mb-3">
                            <label class="form-label">注册邮箱</label>
                            <div class="input-group">
                                <input type="email" class="form-control" id="emailDisplay" readonly disabled>
                                <button type="button" id="changeEmailBtn" class="btn btn-outline-secondary" style="display: inline-block;">修改</button>
                            </div>
                        </div>

                        <!-- Change Email Form (hidden by default) -->
                        <div id="changeEmailSection" style="display: none; border: 1px solid rgba(255,255,255,0.1); border-radius: 8px; padding: 15px; background-color: rgba(255,255,255,0.03);">
                            <h6 class="mb-3">修改邮箱地址</h6>
                            <div id="emailChangeAlertPlaceholder"></div> <!-- Placeholder for alerts -->
                            <div class="mb-3">
                                <label for="newEmailInput" class="form-label">新邮箱地址</label>
                                <input type="email" class="form-control" id="newEmailInput" placeholder="请输入新的邮箱地址" required>
                            </div>
                            <div class="mb-3">
                                <label for="currentPasswordInput" class="form-label">当前密码</label>
                                <input type="password" class="form-control" id="currentPasswordInput" placeholder="请输入当前密码以验证身份" required>
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="button" class="btn btn-secondary me-2" id="cancelChangeEmailBtn">取消</button>
                                <button type="button" class="btn btn-primary" id="sendVerificationEmailBtn">
                                     <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true" style="display: none;"></span>
                                    发送验证邮件
                                </button>
                            </div>
                        </div>
                        <!-- End Change Email Form -->

                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveProfileBtn">保存昵称和头像</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 个人资料模态框结束 -->

    <!-- 确认删除模态框 -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    确定要删除这个提示词案例吗？此操作无法撤销。
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDelete">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类编辑模态框 -->
    <div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editCategoryModalLabel">编辑分类</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="categoryForm">
                        <input type="hidden" id="categoryId">
                        
                        <div class="mb-3">
                            <label for="categoryName" class="form-label">分类名称</label>
                            <input type="text" class="form-control" id="categoryName" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="categorySlug" class="form-label">分类标识符</label>
                            <input type="text" class="form-control" id="categorySlug" required>
                            <small class="text-muted">英文字母、数字和短横线，如"scene"、"art-style"</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="categoryDescription" class="form-label">分类描述</label>
                            <textarea class="form-control" id="categoryDescription" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveCategory">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认删除分类模态框 -->
    <div class="modal fade" id="deleteCategoryConfirmModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除分类</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    确定要删除这个分类吗？此操作无法撤销。
                    <p class="text-danger mt-2">注意：如果有案例使用了此分类，则无法删除。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteCategory">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 插件推广模态框 -->
    <div class="modal fade" id="pluginPromoModal" tabindex="-1" aria-labelledby="pluginPromoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="pluginPromoModalLabel">🚀 在 Figma 中释放提示词的力量！</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>刚刚复制的提示词很棒，对吧？</p>
                    <p>想要在 Figma 设计中直接使用它，或者需要更多灵感？</p>
                    <p>试试我们的 <strong>Figma 插件</strong>，它可以帮助你：</p>
                    <ul>
                        <li>直接在 Figma 画布中与 AI 交互</li>
                        <li>轻松管理和应用你的提示词</li>
                        <li>获取更多设计灵感和自动化工具</li>
                    </ul>
                    <p class="text-center mt-4">
                        <a href="https://www.figma.com/community/plugin/1437318459007099882" target="_blank" rel="noopener noreferrer" class="btn btn-primary btn-lg">
                            <i class="bi bi-gem"></i> 前往 Figma Community 安装
                        </a>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">下次再说</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动插件推广提示 -->
    <div id="floatingPluginPromo" class="floating-plugin-promo">
        <button type="button" class="btn-close btn-sm" id="closeFloatingPromoBtn" aria-label="关闭"></button>
        <div class="promo-icon me-2">💡</div>
        <div>
            在 Figma 中试试这个提示词？
            <a href="https://www.figma.com/community/plugin/1437318459007099882" target="_blank" rel="noopener noreferrer" class="promo-link">
                使用插件 <i class="bi bi-box-arrow-up-right ms-1"></i>
            </a>
        </div>
    </div>

    <!-- Local Bootstrap JS Bundle -->
    <script src="libs/js/bootstrap.bundle.min.js"></script>
    <!-- Local Tagify JS -->
    <script src="libs/js/tagify.min.js"></script>
    <!-- Local Crypto-JS -->
    <script src="libs/js/crypto-js.min.js"></script>
    <!-- Local Browser Image Compression -->
    <script src="libs/js/browser-image-compression.min.js"></script>
    <!-- Local HTML2Canvas -->
    <!-- <script src="libs/js/html2canvas.min.js"></script> --> <!-- Commented out, replacing with dom-to-image-more -->
    <!-- dom-to-image-more CDN (replace with local if preferred) -->
    <script src="libs/js/dom-to-image-more.min.js"></script>
    <!-- Local QRCode.js -->
    <script src="libs/js/qrcode.min.js"></script>
    <script src="js/main.js?=4.7" defer></script>
    <script src="js/ai-canvas-adapter.js?=1.3"></script>
    <script src="js/example-detail-modal.js?=1.37" defer></script> 
    <script src="js/share.js?=1.09" defer></script>
    <script src="js/reverse-prompt.js?=1.11" defer></script>
    <script src="js/api-key-manager.js?=1.16" defer></script> <!-- 新增 API Key 管理器 JS -->
    <script src="js/my-examples.js?=1.67" defer></script> <!-- 新增 我的案例 JS -->
    <script src="js/admin-management.js?=1.46" defer></script> <!-- 新增：管理中心 JS -->
    <script src="js/credits.js?=1.36"></script>
    <script src="js/user-credit-management.js?=2.02"></script> <!-- 重新引入用户积分管理 JS -->
    <script src="js/creative-upscale.js?=1.3"></script>
    <script src="js/remove-background.js?=1.3" defer></script> <!-- 新增：背景移除 JS -->
    <script src="js/vectorize-image.js?=1.34" defer></script> <!-- 新增：位图转 SVG JS -->
    <script src="js/image-to-3d.js?=1.48" defer></script> <!-- 新增：图片转 3D JS -->
    <script src="js/image-to-video.js?=1.36" defer></script> <!-- 新增：图生视频 JS -->
    <script src="js/image-to-video-history.js?=1.31" defer></script> <!-- 新增：图生视频历史记录 JS -->
    <script src="js/ai-chat.js?=1.12" defer></script> <!-- 新增：AI 聊天助手 JS -->
    <script src="js/example-detail-modal.js?=1.16" defer></script> <!-- 新增：案例详情模态框 JS -->
    <script src="js/gpt4o-image-edit.js?=1.36" defer></script> <!-- 新增：GPT-4o 图片编辑 JS -->
    <script src="js/image-utils.js?=1.08" defer></script>
    <script src="js/example-form-handler.js?=1.06" defer></script>
    <script src="js/remove-anything.js?=1.02" defer></script> <!-- 新增：移除万物 JS -->
<script src="js/flash-light.js?=1.05" defer></script> <!-- 新增：AI闪光灯 JS -->
    <!-- Three.js 库 (用于3D模型查看) -->
    <script src="libs/js/three.min.js"></script>
    <script src="libs/js/OrbitControls.js"></script>
    <script src="libs/js/GLTFLoader.js"></script>

    <!-- 新增：Figma 插件悬浮引流按钮 -->
    <a href="https://www.figma.com/community/plugin/1437318459007099882" 
       target="_blank" 
       rel="noopener noreferrer" 
       class="plugin-fab" 
       title="在 Figma 中使用提示词？试试我们的插件！">
        <span>AI 魔法 in Figma</span>
    </a>

    <!-- 新增：我的案例模态框 -->
    <div class="modal fade" id="myExamplesModal" tabindex="-1" aria-labelledby="myExamplesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-scrollable"> <!-- Use modal-xl for more space -->
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="myExamplesModalLabel">我发布的案例</h5>
                    <div class="ms-auto">
                        <select class="form-select form-select-sm" id="myExamplesSortFilter" aria-label="Sort examples">
                            <option value="date_desc" selected>最新发布</option>
                            <option value="likes_desc">最多点赞</option>
                        </select>
                    </div>
                    <button type="button" class="btn-close ms-2" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="my-examples-container"> <!-- Container for JS to populate -->
                       <div class="loading" style="display: none;">
                           <div class="spinner-border" role="status">
                               <span class="visually-hidden">加载中...</span>
                           </div>
                           <span>加载我的案例中...</span>
                       </div>
                       <!-- My Example cards will be dynamically added here -->
                       <div class="empty-state my-empty-state" style="display: none;">
                            <i class="bi bi-journal-x"></i>
                            <span>您还没有创建任何案例。</span>
                       </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-center"> <!-- Center footer content -->
                     <!-- Remove Pagination Nav -->
                     <!-- 
                     <nav aria-label="My Examples Pagination" id="myExamplesPaginationNav" style="display: none;" class="flex-grow-1">
                       <ul class="pagination justify-content-center mb-0" id="myExamplesPagination"></ul>
                     </nav>
                     -->
                    <button type="button" class="btn btn-secondary ms-auto" data-bs-dismiss="modal">关闭</button> <!-- Close button aligned right -->
                </div>
            </div>
        </div>
    </div>
    <!-- 我的案例模态框结束 -->

    <!-- 新增：我点赞的案例模态框 -->
    <div class="modal fade" id="likedExamplesModal" tabindex="-1" aria-labelledby="likedExamplesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-scrollable"> 
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="likedExamplesModalLabel">我点赞的案例</h5>
                    <div class="ms-auto">
                        <select class="form-select form-select-sm" id="likedExamplesSortFilter" aria-label="Sort liked examples">
                            <option value="date_desc" selected>最新发布</option>
                            <option value="likes_desc">最多点赞</option>
                            <!-- Add other options if needed, e.g., sort by like date if available -->
                        </select>
                    </div>
                    <button type="button" class="btn-close ms-2" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="liked-examples-container"> <!-- Container for JS to populate -->
                       <div class="loading" style="display: none;">
                           <div class="spinner-border" role="status">
                               <span class="visually-hidden">加载中...</span>
                           </div>
                           <span>加载点赞案例中...</span>
                       </div>
                       <!-- Liked Example cards will be dynamically added here -->
                       <div class="empty-state liked-empty-state" style="display: none;">
                            <i class="bi bi-heartbreak"></i> <!-- Different Icon -->
                            <span>您还没有点赞任何案例。</span>
                       </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-center">
                    <!-- Loading more indicator will be added here by JS if needed -->
                    <button type="button" class="btn btn-secondary ms-auto" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 我点赞的案例模态框结束 -->

    <!-- 新增：图片详情与上传模态框 -->
    <div class="modal fade" id="imageDetailModal" tabindex="-1" aria-labelledby="imageDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-centered"> <!-- 改为 modal-xl 提供更宽空间 -->
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageDetailModalLabel">图片详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-0"> <!-- 使用 Bootstrap Row, g-0 移除列间距 -->
                        <!-- 左侧图片列 -->
                        <div class="col-md-8 image-detail-left">
                            <img src="" id="modalImageView" class="img-fluid" alt="详细图片">
                        </div>
                        <!-- 右侧信息列 -->
                        <div class="col-md-4 image-detail-right">
                            <!-- 新增：信息卡片包裹层 -->
                            <div class="detail-info-card">
                                <!-- 提示词 -->
                                <div class="mb-3">
                                    <label class="image-detail-label">提示词</label>
                                    <pre id="modalPromptDisplay" class="image-detail-prompt"></pre>
                                </div>

                                <!-- 分割线 -->
                                <hr class="detail-separator">

                                <!-- 模型 -->
                                <div class="mb-3 parameter-item"> <!-- 恢复 mb-3, 添加 parameter-item 类 -->
                                    <label class="image-detail-label">模型</label>
                                    <span id="modalModelDisplay" class="image-detail-value">-</span>
                                </div>
                                <!-- 尺寸 -->
                                <div class="mb-3 parameter-item"> <!-- 恢复 mb-3, 添加 parameter-item 类 -->
                                    <label class="image-detail-label">尺寸</label>
                                    <span id="modalSizeDisplay" class="image-detail-value">-</span>
                                </div>
                                <!-- 生成时间 -->
                                <div class="mb-3 parameter-item"> <!-- 恢复 mb-3, 添加 parameter-item 类 -->
                                    <label class="image-detail-label">生成时间</label>
                                    <span id="modalDateDisplay" class="image-detail-value">-</span>
                                </div>
                            </div> <!-- 结束：信息卡片包裹层 -->

                            <!-- 存储数据的隐藏 input (保持不变) -->
                            <input type="hidden" id="modalDataPrompt">
                            <input type="hidden" id="modalDataModel">
                            <input type="hidden" id="modalDataWidth">
                            <input type="hidden" id="modalDataHeight">
                            <input type="hidden" id="modalDataImageUrl">
                            <input type="hidden" id="modalDataCreatedAt"> <!-- 新增：存储创建时间 -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="uploadToExamplesBtn">
                        <i class="bi bi-cloud-arrow-up-fill me-2"></i>上传到案例库
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- 图片详情模态框结束 -->

    <!-- Placeholder for Share Modal -->
    <div id="shareModalPlaceholder"></div>

    <!-- 新增：API Key 管理模态框 -->
    <div class="modal fade" id="apiKeyModal" tabindex="-1" aria-labelledby="apiKeyModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="apiKeyModalLabel">API Key 管理</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="apiKeyModalBody">
                    <!-- API Key 输入区域将由 JS 动态生成 -->
                    <p class="text-muted">加载中...</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <!-- 保存按钮可能需要在 JS 中动态添加或控制 -->
                </div>
            </div>
        </div>
    </div>
    <!-- API Key 管理模态框结束 --> 

    <!-- 新增：标签编辑模态框 (复用分类编辑模态框结构) -->
    <div class="modal fade" id="editTagModal" tabindex="-1" aria-labelledby="editTagModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editTagModalLabel">编辑标签</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="tagForm">
                        <input type="hidden" id="tagId">
                        <div class="mb-3">
                            <label for="tagName" class="form-label">标签名称</label>
                            <input type="text" class="form-control" id="tagName" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveTagBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增：用户编辑模态框 -->
    <div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editUserModalLabel">编辑用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="editUserId">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" id="editUsernameDisplay" readonly disabled>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">邮箱</label>
                            <input type="email" class="form-control" id="editEmailDisplay" readonly disabled>
                        </div>
                        <div class="mb-3">
                            <label for="editNickname" class="form-label">昵称</label>
                            <input type="text" class="form-control" id="editNickname">
                        </div>
                        <div class="mb-3">
                            <label for="editRole" class="form-label">角色</label>
                            <select class="form-select" id="editRole" required>
                                <option value="user">User</option>
                                <option value="admin">Admin</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveUserBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 复用删除确认模态框 (需要 JS 动态修改内容) -->
    <div class="modal fade" id="adminDeleteConfirmModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="adminDeleteConfirmModalLabel">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="adminDeleteConfirmModalBody">
                    <!-- JS Will fill this -->
                    确定要删除吗？此操作无法撤销。
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="adminConfirmDeleteBtn">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 在其他脚本之前，或者合适的位置添加 -->
    <script src="libs/js/marked.min.js"></script>

    <!-- 新增：案例详情模态框的 HTML 容器 -->
    <div id="exampleDetailModalContainer"></div>
    <!-- 新增：案例详情模态框的 JS -->
    
    <!-- Join Group Modal -->
    <div class="modal fade" id="joinGroupModal" tabindex="-1" aria-labelledby="joinGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content"> 
                <div class="modal-header">
                    <h5 class="modal-title" id="joinGroupModalLabel"><i class="bi bi-person-plus-fill me-2"></i>加入官方交流群</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <p>遇到问题？想与其他用户交流？<br>使用微信扫描下方二维码加入我们的官方社群！</p>
                    <img src="./images/qr-code.jpg" alt="微信群二维码" class="img-fluid rounded mb-3" style="max-width: 280px; border: 1px solid #444;">
                    <p class="small text-muted">获取最新功能资讯、使用帮助、参与活动并分享您的经验。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增：涂抹蒙版模态框 -->
    <div class="modal fade" id="maskDrawingModal" tabindex="-1" aria-labelledby="maskDrawingModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="maskDrawingModalLabel">涂抹蒙版</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div style="position: relative; width: fit-content; margin: auto;">
                                <canvas id="maskDrawingCanvas" class="img-fluid rounded border"></canvas>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">工具</label>
                                <div class="btn-group w-100" role="group" id="maskDrawingTools">
                                    <button type="button" class="btn btn-outline-primary active" data-tool="brush">
                                        <i class="bi bi-pencil"></i> 画笔
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" data-tool="eraser">
                                        <i class="bi bi-eraser"></i> 橡皮擦
                                    </button>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="maskDrawingBrushSize" class="form-label">画笔大小: <span id="maskDrawingBrushSizeValue">10</span></label>
                                <input type="range" class="form-range" id="maskDrawingBrushSize" min="1" max="50" value="10">
                            </div>
                             <div class="mb-3">
                                <button type="button" class="btn btn-outline-secondary w-100 mb-2" id="clearMaskDrawingBtn">
                                    <i class="bi bi-arrow-counterclockwise"></i> 清空重画
                                </button>
                            </div>
                            <div class="mb-3">
                                <button type="button" class="btn btn-primary w-100" id="saveDrawnMaskBtn">
                                    <i class="bi bi-save"></i> 保存蒙版
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 在body底部加AI助手弹窗结构 -->
    <div class="modal fade" id="aiPromptHelperModal" tabindex="-1" aria-labelledby="aiPromptHelperModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="aiPromptHelperModalLabel"><i class="bi bi-robot me-2"></i>AI提示词助手</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body" style="min-height:300px;max-height:400px;overflow-y:auto;">
            <div id="aiPromptHelperChatArea" class="mb-3" style="height:220px;overflow-y:auto;background:#181a1b;border-radius:8px;padding:10px;">
              <div class="text-muted text-center">和AI助手对话，让它帮你优化、扩写、翻译提示词...</div>
            </div>
            <div class="input-group">
              <input type="text" class="form-control" id="aiPromptHelperInput" placeholder="输入你的问题或让AI优化提示词...">
              <button class="btn btn-primary" id="aiPromptHelperSendBtn" type="button">
                <i class="bi bi-send"></i>
              </button>
            </div>
            <button class="btn btn-success w-100 mt-3" id="aiPromptHelperFillBtn" type="button" disabled>
              <i class="bi bi-arrow-down-circle"></i> 一键填入主提示词
            </button>
                </div>
            </div>
        </div>
    </div>
    <!-- 在body底部添加JS文件引用，放在其他JS文件引用后面，但在结束body标签前 -->
    <script src="js/flux-kontext.js?=1.30" defer></script> <!-- 新增：Flux图片处理 JS -->

    <!-- 添加扩图弹窗 -->
    <div class="modal fade" id="fluxExpandModal" tabindex="-1" aria-labelledby="fluxExpandModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="fluxExpandModalLabel">设置扩图区域</h5>
                    <div class="ms-auto me-3 d-flex align-items-center">
                        <span id="zoomPercentage" class="text-light me-2">100%</span>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-light" id="zoomOutBtn">
                                <i class="bi bi-zoom-out"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-light" id="zoomInBtn">
                                <i class="bi bi-zoom-in"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-light" id="zoomFitBtn">
                                <i class="bi bi-arrows-fullscreen"></i>
                            </button>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-0">
                        <div class="col-md-9">
                            <!-- 图片编辑区域容器 -->
                            <div class="image-editor-container">
                                <!-- 可视区域 -->
                                <div class="editor-viewport">
                                    <!-- 可缩放和拖动的画布容器 -->
                                    <div id="zoomableCanvas" class="zoomable-canvas">
                                        <!-- 图片和扩展区域包装器 -->
                                        <div id="fluxExpandEditorContainer" class="position-relative">
                                            <div class="position-relative" id="fluxExpandImageWrapper">
                                                <img id="fluxExpandEditorImg" src="" class="img-fluid">
                                                
                                                <!-- 扩展区域指示器 -->
                                                <div id="expandOverlay" class="expand-overlay">
                                                    <!-- 顶部扩展区域 -->
                                                    <div id="topExpandArea" class="expand-area top-expand-area"></div>
                                                    <!-- 底部扩展区域 -->
                                                    <div id="bottomExpandArea" class="expand-area bottom-expand-area"></div>
                                                    <!-- 左侧扩展区域 -->
                                                    <div id="leftExpandArea" class="expand-area left-expand-area"></div>
                                                    <!-- 右侧扩展区域 -->
                                                    <div id="rightExpandArea" class="expand-area right-expand-area"></div>
                                                    
                                                    <!-- 角落L形控制点 -->
                                                    <div id="topLeftHandle" class="corner-handle top-left-handle" data-position="top-left">
                                                        <div class="corner-handle-h"></div>
                                                        <div class="corner-handle-v"></div>
                                                    </div>
                                                    <div id="topRightHandle" class="corner-handle top-right-handle" data-position="top-right">
                                                        <div class="corner-handle-h"></div>
                                                        <div class="corner-handle-v"></div>
                                                    </div>
                                                    <div id="bottomLeftHandle" class="corner-handle bottom-left-handle" data-position="bottom-left">
                                                        <div class="corner-handle-h"></div>
                                                        <div class="corner-handle-v"></div>
                                                    </div>
                                                    <div id="bottomRightHandle" class="corner-handle bottom-right-handle" data-position="bottom-right">
                                                        <div class="corner-handle-h"></div>
                                                        <div class="corner-handle-v"></div>
                                                    </div>
                                                    
                                                    <!-- 边缘控制点 -->
                                                    <div id="topHandle" class="edge-handle top-handle" data-position="top">
                                                        <div class="edge-handle-bar"></div>
                                                    </div>
                                                    <div id="bottomHandle" class="edge-handle bottom-handle" data-position="bottom">
                                                        <div class="edge-handle-bar"></div>
                                                    </div>
                                                    <div id="leftHandle" class="edge-handle left-handle" data-position="left">
                                                        <div class="edge-handle-bar"></div>
                                                    </div>
                                                    <div id="rightHandle" class="edge-handle right-handle" data-position="right">
                                                        <div class="edge-handle-bar"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center mt-2 text-muted small">
                                    拖动蓝色控制点设置扩图区域，使用缩放按钮调整画布大小
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 p-4">
                            <!-- 扩图参数设置 -->
                            <h6>扩图区域</h6>
                            
                            <!-- 添加比例选择 -->
                            <div class="mb-4">
                                <label class="form-label">预设比例</label>
                                <div class="ratio-selector d-flex flex-wrap gap-2">
                                    <button type="button" class="btn btn-sm ratio-btn" data-ratio="original">原始</button>
                                    <button type="button" class="btn btn-sm ratio-btn" data-ratio="1:1">1:1</button>
                                    <button type="button" class="btn btn-sm ratio-btn" data-ratio="16:9">16:9</button>
                                    <button type="button" class="btn btn-sm ratio-btn" data-ratio="9:16">9:16</button>
                                    <button type="button" class="btn btn-sm ratio-btn" data-ratio="4:3">4:3</button>
                                    <button type="button" class="btn btn-sm ratio-btn" data-ratio="3:4">3:4</button>
                                </div>
                            </div>
                            
                            <!-- 显示当前扩展像素值，增加可编辑的输入框 -->
                            <div class="row g-2 mb-3">
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="small text-muted text-center">顶部</div>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" id="expandTopInput" value="0" min="0" max="2048">
                                            <span class="input-group-text">像素</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="small text-muted text-center">底部</div>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" id="expandBottomInput" value="0" min="0" max="2048">
                                            <span class="input-group-text">像素</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-2 mb-4">
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="small text-muted text-center">左侧</div>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" id="expandLeftInput" value="0" min="0" max="2048">
                                            <span class="input-group-text">像素</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="small text-muted text-center">右侧</div>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" id="expandRightInput" value="0" min="0" max="2048">
                                            <span class="input-group-text">像素</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 其他参数设置 -->
                            <h6>生成参数</h6>
                            <div class="mb-3">
                                <label for="fluxExpandPromptInput" class="form-label">提示词 (Prompt)</label>
                                <textarea class="form-control" id="fluxExpandPromptInput" rows="3" placeholder="描述扩展区域的内容"></textarea>
                            </div>
                            
                            <!-- 其他参数保持不变 -->
                            <!-- ... -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="startFluxExpandBtn">开始扩图</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 案例展示模态窗口容器 -->
    <div id="fluxExamplesModalContainer"></div>

    <!-- 自定义UI组件 JS -->
    <script src="js/custom-components.js?=1.0" defer></script> 
    <!-- AI 图片生成 JS -->
    <script src="js/ai-generate.js?=2.12" defer></script>
    <!-- Jaaz AI设计代理集成 JS -->
    <!-- <script src="js/jaaz-integration.js?=1.0" defer></script> -->
    <!-- 电商专栏 JS -->
    <script src="js/ecommerce-tools.js?=1.0" defer></script>

    <!-- 评论通知 Modal -->
    <div class="modal fade" id="commentsNotificationModal" tabindex="-1" aria-labelledby="commentsNotificationModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="commentsNotificationModalLabel"><i class="bi bi-chat-left-text-fill me-2"></i>收到的评论</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body" id="commentsNotificationModalBody">
            <!-- Notifications will be loaded here -->
            <p class="text-center">加载中...</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
          </div>
        </div>
      </div>
    </div>
</body>
</html>
