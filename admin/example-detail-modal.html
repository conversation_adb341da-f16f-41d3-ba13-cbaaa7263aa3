<!-- Case Detail Modal -->
<div class="modal fade" id="exampleDetailModal" tabindex="-1" aria-labelledby="exampleDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleDetailModalLabel">案例详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="container-fluid">
                    <div class="row">
                        <!-- Left Column: Image and Core Info -->
                        <div class="col-lg-7">
                            <!-- 多图展示区（主图区+缩略图） -->
                            <div id="detailImagesGallery" class="detail-images-gallery mb-3">
                                <div class="main-image-area text-center">
                                    <img src="" id="detailGalleryMainImage" class="img-fluid rounded" alt="案例图片" style="display: none;">
                                    <div class="example-detail-placeholder-main-image" id="detailGalleryMainImagePlaceholder" style="display: none;">
                                        <i class="bi bi-image-alt"></i>
                                        <p>暂无图片</p>
                                    </div>
                                </div>
                                <div class="thumbnails d-flex justify-content-center align-items-center mt-2" id="detailGalleryThumbnails" style="display: none;"></div>
                            </div>

                            <!-- Source and Reference Images (for GPT4O-Edit) -->
                            <div id="detailGpt4oEditImagesSection" class="row mt-3" style="display: none;">
                                <div class="col-md-6 mb-3 text-center">
                                    <h6>源图片</h6>
                                    <img src="" id="detailSourceImage" class="img-fluid rounded example-detail-secondary-image" alt="源图片">
                                     <div class="example-detail-placeholder-secondary-image source" style="display: none;">
                                        <i class="bi bi-image-alt"></i>
                                        <p>无源图</p>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3 text-center">
                                    <h6>参考图</h6>
                                    <img src="" id="detailReferenceImage" class="img-fluid rounded example-detail-secondary-image" alt="参考图">
                                    <div class="example-detail-placeholder-secondary-image reference" style="display: none;">
                                        <i class="bi bi-image-alt"></i>
                                        <p>无参考图</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column: Details, Actions, Prompt, Tags, Comments -->
                        <div class="col-lg-5">
                            <!-- User Info Block -->
                            <div class="d-flex align-items-center justify-content-between mb-3">
                                <div class="d-flex align-items-center">
                                    <img src="images/default-avatar.png" id="detailAuthorAvatar" alt="作者头像" class="rounded-circle me-2" style="width: 48px; height: 48px;">
                                    <div class="flex-grow-1">
                                        <h5 id="detailAuthorName" class="mb-0" style="font-size: 1rem;">加载中...</h5>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-outline-secondary" id="detailShareBtn" title="分享">
                                    <i class="bi bi-share-fill me-1"></i> 分享
                                </button>
                            </div>
                            
                            <hr class="my-2">

                            <!-- 作品信息 -->
                            <div class="mb-2">
                                <h4 id="detailTitle" class="mb-1" style="font-size: 1.25rem;">作品标题加载中...</h4>
                            </div>

                            <!-- Tabs -->
                            <div class="detail-tab-container">
                                <ul class="nav nav-tabs" id="detailTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="details-tab" data-bs-toggle="tab" data-bs-target="#details-tab-pane" type="button" role="tab">详情</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="comments-tab" data-bs-toggle="tab" data-bs-target="#comments-tab-pane" type="button" role="tab">评论 (<span id="detailCommentCount">0</span>)</button>
                                    </li>
                                </ul>
                                <div class="tab-content" id="detailTabsContent">
                                    <!-- 详情 Tab Pane -->
                                    <div class="tab-pane fade show active p-3 rounded-bottom" id="details-tab-pane">
                                        <h6><i class="bi bi-lightbulb me-1"></i>创意详情</h6>
                                        
                                        <h6 class="mt-3"><i class="bi bi-blockquote-left me-1"></i>创意描述 (Prompt)</h6>
                                        <div class="prompt-text-container mb-2">
                                            <pre id="detailPrompt" class="mb-0"></pre>
                                        </div>
                                        <button type="button" id="detailCopyPromptBtn" class="btn btn-sm btn-outline-secondary mb-3">
                                            <i class="bi bi-clipboard me-1"></i> 复制提示词
                                        </button>
                                        
                                        <div class="detail-info-grid">
                                            <div class="detail-info-item">
                                                <div class="detail-info-label">模型</div>
                                                <div class="detail-info-value" id="detailModel">N/A</div>
                                            </div>
                                            <div class="detail-info-item">
                                                <div class="detail-info-label">分类</div>
                                                <div class="detail-info-value" id="detailCategory">N/A</div>
                                            </div>
                                        </div>
                                        
                                        <h6 class="mt-3"><i class="bi bi-tags me-1"></i>标签</h6>
                                        <div id="detailTagsContainer" class="mb-3">
                                            <span class="text-muted">暂无标签</span>
                                        </div>
                                    </div>
                                    
                                    <!-- 评论 Tab Pane -->
                                    <div class="tab-pane fade p-3 rounded-bottom" id="comments-tab-pane">
                                        <div class="comments-section">
                                            <h6 class="comments-title"><i class="bi bi-chat-dots me-1"></i>评论区</h6>
                                            
                                            <div id="detailCommentsLoading" class="text-center py-4" style="display: none;">
                                                <div class="spinner-border spinner-border-sm text-secondary" role="status">
                                                    <span class="visually-hidden">加载中...</span>
                                                </div>
                                                <p class="mt-2 text-muted">加载评论中...</p>
                                            </div>
                                            
                                            <div id="detailCommentsError" class="text-center py-4" style="display: none;">
                                                <i class="bi bi-exclamation-circle text-danger fs-4"></i>
                                                <p class="mt-2 text-danger">加载评论失败，请稍后重试</p>
                                                <button id="retryCommentsBtn" class="btn btn-sm btn-outline-secondary mt-2">
                                                    <i class="bi bi-arrow-clockwise me-1"></i>重试
                                                </button>
                                            </div>
                                            
                                            <div id="detailCommentsList" class="comments-list mb-3">
                                                <p class="text-muted text-center py-4">暂无评论，快来抢沙发吧！</p>
                                            </div>
                                            
                                            <div class="comment-form-container">
                                                <form id="detailCommentForm">
                                                    <div class="mb-2 position-relative">
                                                        <textarea class="form-control" id="detailCommentText" rows="2" placeholder="输入你的评论..." required></textarea>
                                                        <div class="comment-text-count">
                                                            <small><span id="commentCharCount">0</span>/200</small>
                                                        </div>
                                                    </div>
                                                    <div class="mb-2 d-flex align-items-center">
                                                        <!-- 图片上传图标按钮 -->
                                                        <label for="detailCommentImage" class="btn btn-outline-secondary btn-sm mb-0 me-2" style="display:flex;align-items:center;justify-content:center;width:32px;height:32px;padding:0;">
                                                            <i class="bi bi-image" style="font-size:1.3rem;"></i>
                                                        </label>
                                                        <input type="file" accept="image/*" id="detailCommentImage" name="image" style="display:none;">
                                                        <div id="detailCommentImagePreview" class="ms-2 position-relative" style="display:none; width:48px; height:48px;">
                                                            <img src="" alt="评论图片预览" style="max-width:48px;max-height:48px;border-radius:6px;vertical-align:middle;">
                                                            <button type="button" class="btn btn-sm btn-danger p-0 position-absolute" id="removeCommentImageBtn" title="移除图片" style="top:-8px;right:-8px;width:20px;height:20px;display:flex;align-items:center;justify-content:center;border-radius:50%;font-size:0.9rem;z-index:2;"><i class="bi bi-x"></i></button>
                                                        </div>
                                                        <small class="text-muted ms-2">200kb内，仅1张</small>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <small class="text-muted">登录后才能发布评论</small>
                                                        <button type="submit" class="btn btn-primary btn-sm" id="commentSubmitBtn">
                                                            <i class="bi bi-send me-1"></i> 发表评论
                                                        </button>
                                                    </div>
                                                    <div id="commentSubmitError" class="alert alert-danger mt-2 py-2" style="display: none;"></div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer justify-content-end">
                <button class="btn btn-outline-danger me-2" id="detailLikeBtn" title="点赞">
                    <i class="bi bi-heart me-1"></i> 点赞 <span class="like-count ms-1" id="detailLikeCount">0</span>
                </button>
                <button type="button" id="detailGenerateSameBtn_footer" class="btn btn-success">
                    <i class="bi bi-stars me-1"></i> 一键同款
                </button>
            </div>
        </div>
    </div>
</div> 

<!-- 图片查看模态框 -->
<div class="modal fade" id="commentImageViewerModal" tabindex="-1" aria-labelledby="commentImageViewerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content bg-dark">
            <div class="modal-header border-0">
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-0">
                <img id="commentImageViewerImg" src="" alt="评论图片" class="img-fluid" style="max-height: 80vh;">
            </div>
        </div>
    </div>
</div> 