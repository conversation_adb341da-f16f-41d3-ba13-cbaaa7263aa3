<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 图片生成 - 像素星云</title>
    <!-- Local Bootstrap CSS -->
    <link href="libs/css/bootstrap.min.css" rel="stylesheet">
    <!-- Local Bootstrap Icons CSS -->
    <link href="libs/css/bootstrap-icons.min.css" rel="stylesheet">
    <!-- Local Tagify CSS -->
    <link href="libs/css/tagify.min.css" rel="stylesheet">
    <link href="css/style.css?=1.56" rel="stylesheet">
    <link href="css/ai-generate.css" rel="stylesheet"> <!-- 引入独立的AI生图样式 -->
    <!-- 添加图片压缩库 -->
    <script src="libs/js/browser-image-compression.min.js"></script>
</head>
<body>
    <div class="grid-mask"></div>
    <div class="container">
        <div class="page-header">
            <h1>AI 图片生成</h1>
            <div class="header-actions">
                <a href="index.html" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> 返回主页
                </a>
            </div>
        </div>

        <!-- AI 图片生成内容 -->
        <div id="ai-generate-tab-pane">
            <div class="row">
                <!-- 左侧：生成设置 -->
                <div class="col-lg-4">
                    <div class="card mb-4">
                        <div class="card-body">
                            <h5 class="card-title mb-3">生成设置</h5>

                            <!-- 提示词输入 -->
                            <div class="mb-3">
                                <label for="aiPrompt" class="form-label">提示词</label>
                                <textarea class="form-control" id="aiPrompt" rows="5" placeholder="输入您的创作想法..."></textarea>
                            </div>

                            <!-- 级联模型选择 -->
                            <div class="mb-3">
                                <label for="aiModelSelectBtn" class="form-label">选择模型</label>
                                <div class="dropdown">
                                    <button class="form-select text-start dropdown-toggle" type="button" id="aiModelSelectBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                        选择模型
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-dark w-100" id="aiModelSelectMenu">
                                        <!-- 模型将由 JS 动态填充 -->
                                    </ul>
                                    <input type="hidden" id="aiModelSelectValue">
                                </div>
                            </div>
                            
                            <!-- 图片比例选择 -->
                            <div class="mb-3">
                                <label class="form-label">图片比例</label>
                                <div id="aiRatioSelector" class="btn-group w-100" role="group" aria-label="Image Ratio">
                                    <!-- 按钮将由 JS 动态填充 -->
                                </div>
                            </div>

                            <!-- 图片数量 -->
                            <div class="mb-3">
                                <label for="aiImageCount" class="form-label">图片数量 (1-4)</label>
                                <input type="number" class="form-control" id="aiImageCount" value="4" min="1" max="4">
                            </div>

                            <!-- 生成按钮 -->
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary btn-lg" id="generateAiImageBtn">
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true" style="display: none;"></span>
                                    生成图片
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：生成结果和历史记录 -->
                <div class="col-lg-8">
                    <!-- 生成结果 -->
                    <div class="card mb-4">
                        <div class="card-header">生成结果</div>
                        <div class="card-body">
                            <div id="aiGenerationLoading" class="loading-shimmer-card">
                                <div class="loading-dots">
                                    <span></span><span></span><span></span>
                                </div>
                                <p class="loading-text-primary mt-3">正在努力生成中...</p>
                                <p class="loading-text-secondary">这可能需要几十秒，请稍候</p>
                            </div>
                            <div id="aiGenerationResult" class="ai-generation-result-container">
                                <!-- 结果将显示在这里 -->
                                <div class="empty-state">
                                    <i class="bi bi-image" style="font-size: 3rem;"></i>
                                    <p>生成的结果将会显示在这里</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 历史记录 -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <span>生成历史</span>
                            <button class="btn btn-sm btn-outline-secondary" id="refreshAiHistoryBtn" title="刷新历史">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="aiHistoryList">
                                <!-- 历史记录将显示在这里 -->
                            </div>
                            <nav id="aiHistoryPagination" aria-label="AI History Pagination">
                                <!-- 分页将显示在这里 -->
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End of AI 图片生成内容 -->

    </div> <!-- End of container -->

    <!-- Image Detail Modal -->
    <div class="modal fade" id="imageDetailModal" tabindex="-1" aria-labelledby="imageDetailModalLabel" aria-hidden="true">
        <!-- Modal content will be loaded here -->
    </div>


    <!-- Local Bootstrap JS -->
    <script src="libs/js/bootstrap.bundle.min.js"></script>
    <!-- Local Tagify JS -->
    <script src="libs/js/tagify.min.js"></script>
    <!-- Marked.js for Markdown -->
    <script src="libs/js/marked.min.js"></script>
    <!-- Crypto-JS for hashing -->
    <script src="libs/js/crypto-js.min.js"></script>
    <!-- Custom Scripts -->
    <script src="js/api-key-manager.js"></script> 
    <script src="js/main.js?=1.321"></script>
    <script src="js/ai-generate.js?=1.32"></script> <!-- 保留：AI 图片生成脚本 -->
    <script src="js/image-utils.js"></script> <!-- 保留：可能被依赖的图片工具脚本 -->

    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            // 这是独立页面，没有Tab切换，所以我们手动调用初始化函数

            // 1. 从服务器获取UI组件配置，这是最关键的前置步骤
            async function fetchAndInitializeAIComponents() {
                try {
                    const response = await fetch(`${window.API_URL}/ui-components/ai_generate`, {
                        headers: getAuthHeaders()
                    });
                    if (!response.ok) throw new Error('Failed to fetch UI components config');
                    const data = await response.json();
                    if (!data.success) throw new Error('Invalid UI components config response');
                    
                    // 将获取到的组件数据存到全局，以便其他函数使用
                    window.uiComponentsData = window.uiComponentsData || {};
                    window.uiComponentsData['ai_generate'] = data.components;
                    
                    console.log('UI Components for ai_generate loaded and stored.');
                    return true;
                } catch (error) {
                    console.error('获取AI生成组件配置失败:', error);
                    return false;
                }
            }

            const componentsLoaded = await fetchAndInitializeAIComponents();

            if (componentsLoaded) {
                // 2. 调用 ai-generate.js 中的函数来加载模型列表
                if (typeof populateModelSelectorFromAPI === 'function') {
                    populateModelSelectorFromAPI();
                } else {
                    console.error('关键函数 populateModelSelectorFromAPI 未找到。');
                }

                // 3. 调用 main.js 中的函数来初始化图片比例按钮
                if (typeof initializeRatioButtons === 'function') {
                    initializeRatioButtons('ai_generate');
                } else {
                    console.error('关键函数 initializeRatioButtons 未找到。');
                }

                // 4. 调用 ai-generate.js 中的函数来初始化其他UI和加载功能成本
                if (typeof handleTabShown === 'function') {
                    handleTabShown();
                } else {
                    console.error('关键函数 handleTabShown 未找到。');
                }
            } else {
                // 在页面上显示错误，提示用户刷新
                const container = document.getElementById('ai-generate-params-container');
                if(container) container.innerHTML = '<p class="text-danger">无法加载页面配置，请刷新重试。</p>';
            }
        });
    </script>
</body>
</html> 