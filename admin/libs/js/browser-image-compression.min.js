!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).imageCompression=t()}(this,function(){"use strict";function k(e,i){return new Promise(function(t,r){let n;return O(e).then(function(e){try{return n=e,t(new Blob([i.slice(0,2),n,i.slice(2)],{type:"image/jpeg"}))}catch(e){return r(e)}},r)})}const O=t=>new Promise((s,l)=>{const e=new FileReader;e.addEventListener("load",({target:{result:e}})=>{const r=new DataView(e);let n=0;if(65496!==r.getUint16(n))return l("not a valid JPEG");for(n+=2;;){var i=r.getUint16(n);if(65498===i)break;var o=r.getUint16(n+2);if(65505===i&&1165519206===r.getUint32(n+4)){i=n+10;let t;switch(r.getUint16(i)){case 18761:t=!0;break;case 19789:t=!1;break;default:return l("TIFF header contains invalid endian")}if(42!==r.getUint16(i+2,t))return l("TIFF header contains invalid version");var a=r.getUint32(i+4,t),f=i+a+2+12*r.getUint16(i+a,t);for(let e=i+a+2;e<f;e+=12)if(274==r.getUint16(e,t)){if(3!==r.getUint16(e+2,t))return l("Orientation data type is invalid");if(1!==r.getUint32(e+4,t))return l("Orientation data count is invalid");r.setUint16(e+8,1,t);break}return s(e.slice(n,n+2+o))}n+=2+o}return s(new Blob)}),e.readAsArrayBuffer(t)});var e,t,r={},S={};({get exports(){return r},set exports(e){r=e}}.exports=S).parse=function(e,t){for(var r=S.bin.readUshort,n=S.bin.readUint,i=0,o={},a=new Uint8Array(e),f=a.length-4;101010256!=n(a,f);)f--;var s=r(a,i=(i=f)+4+4),e=(r(a,i+=2),n(a,i+=2),n(a,i+=4));i+=4,i=e;for(var l=0;l<s;l++){n(a,i),n(a,i=(i=i+4+4)+4+4);var c=n(a,i+=4),u=n(a,i+=4),h=r(a,i+=4),d=r(a,i+2),g=r(a,i+4),A=n(a,i=i+6+8),i=i+4+(h+d+g);S._readLocal(a,A,o,c,u,t)}return o},S._readLocal=function(e,t,r,n,i,o){var a=S.bin.readUshort,f=S.bin.readUint,s=(f(e,t),a(e,t+=4),a(e,t+=2),a(e,t+=2)),f=(f(e,t+=2),f(e,t+=4),a(e,t=t+4+8)),a=a(e,t+=2),l=S.bin.readUTF8(e,t+=2,f);if(t=t+f+a,o)r[l]={size:i,csize:n};else{f=new Uint8Array(e.buffer,t);if(0==s)r[l]=new Uint8Array(f.buffer.slice(t,t+n));else{if(8!=s)throw"unknown compression method: "+s;a=new Uint8Array(i);S.inflateRaw(f,a),r[l]=a}}},S.inflateRaw=function(e,t){return S.F.inflate(e,t)},S.inflate=function(e,t){return e[0],e[1],S.inflateRaw(new Uint8Array(e.buffer,e.byteOffset+2,e.length-6),t)},S.deflate=function(e,t){null==t&&(t={level:6});var r=new Uint8Array(50+Math.floor(1.1*e.length)),e=(r[0]=120,r[1]=156,t=S.F.deflateRaw(e,r,2,t.level),S.adler(e,0,e.length));return r[t+0]=e>>>24&255,r[t+1]=e>>>16&255,r[t+2]=e>>>8&255,r[t+3]=e>>>0&255,new Uint8Array(r.buffer,0,t+4)},S.deflateRaw=function(e,t){null==t&&(t={level:6});var r=new Uint8Array(50+Math.floor(1.1*e.length)),e=S.F.deflateRaw(e,r,void 0,t.level);return new Uint8Array(r.buffer,0,e)},S.encode=function(e,t){null==t&&(t=!1);var r=0,n=S.bin.writeUint,i=S.bin.writeUshort,o={};for(d in e){var a=!S._noNeed(d)&&!t,f=e[d],s=S.crc.crc(f,0,f.length);o[d]={cpr:a,usize:f.length,crc:s,file:a?S.deflateRaw(f):f}}for(d in o)r+=o[d].file.length+30+46+2*S.bin.sizeUTF8(d);r+=22;var l=new Uint8Array(r),c=0,u=[];for(d in o){var h=o[d];u.push(c),c=S._writeHeader(l,c,d,h,0)}var d,g=0,A=c;for(d in o)h=o[d],u.push(c),c=S._writeHeader(l,c,d,h,1,u[g++]);var w=c-A;return n(l,c,101010256),i(l,c=c+4+4,g),i(l,c+=2,g),n(l,c+=2,w),n(l,c+=4,A),c=c+4+2,l.buffer},S._noNeed=function(e){e=e.split(".").pop().toLowerCase();return-1!="png,jpg,jpeg,zip".indexOf(e)},S._writeHeader=function(e,t,r,n,i,o){var a=S.bin.writeUint,f=S.bin.writeUshort,s=n.file;return a(e,t,0==i?67324752:33639248),t+=4,1==i&&(t+=2),f(e,t,20),f(e,t+=2,0),f(e,t+=2,n.cpr?8:0),a(e,t+=2,0),a(e,t+=4,n.crc),a(e,t+=4,s.length),a(e,t+=4,n.usize),f(e,t+=4,S.bin.sizeUTF8(r)),f(e,t+=2,0),t+=2,1==i&&(a(e,t=(t+=2)+2+6,o),t+=4),t+=S.bin.writeUTF8(e,t,r),0==i&&(e.set(s,t),t+=s.length),t},S.crc={table:function(){for(var e=new Uint32Array(256),t=0;t<256;t++){for(var r=t,n=0;n<8;n++)1&r?r=3988292384^r>>>1:r>>>=1;e[t]=r}return e}(),update:function(e,t,r,n){for(var i=0;i<n;i++)e=S.crc.table[255&(e^t[r+i])]^e>>>8;return e},crc:function(e,t,r){return 4294967295^S.crc.update(4294967295,e,t,r)}},S.adler=function(e,t,r){for(var n=1,i=0,o=t,a=t+r;o<a;){for(var f=Math.min(o+5552,a);o<f;)i+=n+=e[o++];n%=65521,i%=65521}return i<<16|n},S.bin={readUshort:function(e,t){return e[t]|e[t+1]<<8},writeUshort:function(e,t,r){e[t]=255&r,e[t+1]=r>>8&255},readUint:function(e,t){return 16777216*e[t+3]+(e[t+2]<<16|e[t+1]<<8|e[t])},writeUint:function(e,t,r){e[t]=255&r,e[t+1]=r>>8&255,e[t+2]=r>>16&255,e[t+3]=r>>24&255},readASCII:function(e,t,r){for(var n="",i=0;i<r;i++)n+=String.fromCharCode(e[t+i]);return n},writeASCII:function(e,t,r){for(var n=0;n<r.length;n++)e[t+n]=r.charCodeAt(n)},pad:function(e){return e.length<2?"0"+e:e},readUTF8:function(e,t,r){for(var n,i="",o=0;o<r;o++)i+="%"+S.bin.pad(e[t+o].toString(16));try{n=decodeURIComponent(i)}catch(n){return S.bin.readASCII(e,t,r)}return n},writeUTF8:function(e,t,r){for(var n=r.length,i=0,o=0;o<n;o++){var a=r.charCodeAt(o);if(0==(4294967168&a))e[t+i]=a,i++;else if(0==(4294965248&a))e[t+i]=192|a>>6,e[t+i+1]=128|a>>0&63,i+=2;else if(0==(4294901760&a))e[t+i]=224|a>>12,e[t+i+1]=128|a>>6&63,e[t+i+2]=128|a>>0&63,i+=3;else{if(0!=(4292870144&a))throw"e";e[t+i]=240|a>>18,e[t+i+1]=128|a>>12&63,e[t+i+2]=128|a>>6&63,e[t+i+3]=128|a>>0&63,i+=4}}return i},sizeUTF8:function(e){for(var t=e.length,r=0,n=0;n<t;n++){var i=e.charCodeAt(n);if(0==(4294967168&i))r++;else if(0==(4294965248&i))r+=2;else if(0==(4294901760&i))r+=3;else{if(0!=(4292870144&i))throw"e";r+=4}}return r}},S.F={},S.F.deflateRaw=function(e,t,r,n){var i=[[0,0,0,0,0],[4,4,8,4,0],[4,5,16,8,0],[4,6,16,16,0],[4,10,16,32,0],[8,16,32,32,0],[8,16,128,128,0],[8,32,128,256,0],[32,128,258,1024,1],[32,258,258,4096,1]][n],o=S.F.U,a=S.F._goodIndex,f=(S.F._hash,S.F._putsE),s=0,l=r<<3,c=0,u=e.length;if(0==n){for(;s<u;)f(t,l,s+(y=Math.min(65535,u-s))==u?1:0),l=S.F._copyExact(e,s,y,t,l+8),s+=y;return l>>>3}var h=o.lits,d=o.strt,g=o.prev,A=0,w=0,p=0,v=0,m=0;for(2<u&&(d[m=S.F._hash(e,0)]=0),s=0;s<u;s++){var b,y,U,F,E=m;s+1<u-2&&(m=S.F._hash(e,s+1),g[b=s+1&32767]=d[m],d[m]=b),c<=s&&((14e3<A||26697<w)&&100<u-s&&(c<s&&(h[A]=s-c,A+=2,c=s),l=S.F._writeBlock(s==u-1||c==u?1:0,h,A,v,e,p,s-p,t,l),A=w=v=0,p=s),b=0,y=(b=s<u-2?S.F._bestMatch(e,s,g,E,Math.min(i[2],u-s),i[3]):b)>>>16,E=65535&b,0!=b?(E=65535&b,U=a(y=b>>>16,o.of0),o.lhst[257+U]++,F=a(E,o.df0),o.dhst[F]++,v+=o.exb[U]+o.dxb[F],h[A]=y<<23|s-c,h[A+1]=E<<16|U<<8|F,A+=2,c=s+y):o.lhst[e[s]]++,w++)}for(p==s&&0!=e.length||(c<s&&(h[A]=s-c,A+=2,c=s),l=S.F._writeBlock(1,h,A,v,e,p,s-p,t,l),A=w=v=w=A=0,p=s);0!=(7&l);)l++;return l>>>3},S.F._bestMatch=function(e,t,r,n,i,o){var a=32767&t,f=r[a],s=a-f+32768&32767;if(f==a||n!=S.F._hash(e,t-s))return 0;for(var l=0,c=0,u=Math.min(32767,t);s<=u&&0!=--o&&f!=a;){if(0==l||e[t+l]==e[t+l-s]){var h=S.F._howLong(e,t,s);if(l<h){if(c=s,(l=h)>=i)break;s+2<h&&(h=s+2);for(var d=0,g=0;g<h-2;g++){var A=t-s+g+32768&32767,w=A-r[A]+32768&32767;d<w&&(d=w,f=A)}}}s+=(a=f)-(f=r[a])+32768&32767}return l<<16|c},S.F._howLong=function(e,t,r){if(e[t]!=e[t-r]||e[t+1]!=e[t+1-r]||e[t+2]!=e[t+2-r])return 0;var n=t,i=Math.min(e.length,t+258);for(t+=3;t<i&&e[t]==e[t-r];)t++;return t-n},S.F._hash=function(e,t){return(e[t]<<8|e[t+1])+(e[t+2]<<4)&65535},S.saved=0,S.F._writeBlock=function(e,t,r,n,i,o,a,f,s){var l,c,u,h,d,g,A,w,p=S.F.U,v=S.F._putsF,m=S.F._putsE,b=(p.lhst[256]++,l=(w=S.F.getTrees())[0],c=w[1],u=w[2],h=w[3],d=w[4],g=w[5],A=w[6],w=w[7],32+(0==(s+3&7)?0:8-(s+3&7))+(a<<3)),y=n+S.F.contSize(p.fltree,p.lhst)+S.F.contSize(p.fdtree,p.dhst),n=n+S.F.contSize(p.ltree,p.lhst)+S.F.contSize(p.dtree,p.dhst);n+=14+3*g+S.F.contSize(p.itree,p.ihst)+(2*p.ihst[16]+3*p.ihst[17]+7*p.ihst[18]);for(var U=0;U<286;U++)p.lhst[U]=0;for(U=0;U<30;U++)p.dhst[U]=0;for(U=0;U<19;U++)p.ihst[U]=0;var F,E,b=b<y&&b<n?0:y<n?1:2;if(v(f,s,e),v(f,s+1,b),s+=3,0==b){for(;0!=(7&s);)s++;s=S.F._copyExact(i,o,a,f,s)}else{if(1==b&&(F=p.fltree,E=p.fdtree),2==b){S.F.makeCodes(p.ltree,l),S.F.revCodes(p.ltree,l),S.F.makeCodes(p.dtree,c),S.F.revCodes(p.dtree,c),S.F.makeCodes(p.itree,u),S.F.revCodes(p.itree,u),F=p.ltree,E=p.dtree,m(f,s,h-257),m(f,s+=5,d-1),m(f,s+=5,g-4),s+=4;for(var _=0;_<g;_++)m(f,s+3*_,p.itree[1+(p.ordr[_]<<1)]);s=S.F._codeTiny(A,p.itree,f,s+=3*g),s=S.F._codeTiny(w,p.itree,f,s)}for(var C=o,I=0;I<r;I+=2){for(var B,M,R=t[I],x=R>>>23,T=C+(8388607&R);C<T;)s=S.F._writeLit(i[C++],F,f,s);0!=x&&(B=(R=t[I+1])>>16,M=255&R,m(f,s=S.F._writeLit(257+(R=R>>8&255),F,f,s),x-p.of0[R]),s+=p.exb[R],v(f,s=S.F._writeLit(M,E,f,s),B-p.df0[M]),s+=p.dxb[M],C+=x)}s=S.F._writeLit(256,F,f,s)}return s},S.F._copyExact=function(e,t,r,n,i){var o=i>>>3;return n[o]=r,n[1+o]=r>>>8,n[2+o]=255-n[o],n[3+o]=255-n[1+o],o+=4,n.set(new Uint8Array(e.buffer,t,r),o),i+(r+4<<3)},S.F.getTrees=function(){for(var e=S.F.U,t=S.F._hufTree(e.lhst,e.ltree,15),r=S.F._hufTree(e.dhst,e.dtree,15),n=[],i=S.F._lenCodes(e.ltree,n),o=[],a=S.F._lenCodes(e.dtree,o),f=0;f<n.length;f+=2)e.ihst[n[f]]++;for(f=0;f<o.length;f+=2)e.ihst[o[f]]++;for(var s=S.F._hufTree(e.ihst,e.itree,7),l=19;4<l&&0==e.itree[1+(e.ordr[l-1]<<1)];)l--;return[t,r,s,i,a,l,n,o]},S.F.getSecond=function(e){for(var t=[],r=0;r<e.length;r+=2)t.push(e[r+1]);return t},S.F.nonZero=function(e){for(var t="",r=0;r<e.length;r+=2)0!=e[r+1]&&(t+=(r>>1)+",");return t},S.F.contSize=function(e,t){for(var r=0,n=0;n<t.length;n++)r+=t[n]*e[1+(n<<1)];return r},S.F._codeTiny=function(e,t,r,n){for(var i=0;i<e.length;i+=2){var o=e[i],a=e[i+1],f=(n=S.F._writeLit(o,t,r,n),16==o?2:17==o?3:7);15<o&&(S.F._putsE(r,n,a,f),n+=f)}return n},S.F._lenCodes=function(e,t){for(var r=e.length;2!=r&&0==e[r-1];)r-=2;for(var n=0;n<r;n+=2){var i=e[n+1],o=n+3<r?e[n+3]:-1,a=n+5<r?e[n+5]:-1,f=0==n?-1:e[n-1];if(0==i&&o==i&&a==i){for(var s=n+5;s+2<r&&e[s+2]==i;)s+=2;(l=Math.min(s+1-n>>>1,138))<11?t.push(17,l-3):t.push(18,l-11),n+=2*l-2}else if(i==f&&o==i&&a==i){for(s=n+5;s+2<r&&e[s+2]==i;)s+=2;var l=Math.min(s+1-n>>>1,6);t.push(16,l-3),n+=2*l-2}else t.push(i,0)}return r>>>1},S.F._hufTree=function(e,t,r){for(var n=[],i=e.length,o=t.length,a=0,a=0;a<o;a+=2)t[a]=0,t[a+1]=0;for(a=0;a<i;a++)0!=e[a]&&n.push({lit:a,f:e[a]});var f=n.length,s=n.slice(0);if(0==f)return 0;if(1==f)return s=0==(g=n[0].lit)?1:0,t[1+(g<<1)]=1,t[1+(s<<1)]=1;n.sort(function(e,t){return e.f-t.f});var l=n[0],c=n[1],u=0,h=1,d=2;for(n[0]={lit:-1,f:l.f+c.f,l:l,r:c,d:0};h!=f-1;)l=u!=h&&(d==f||n[u].f<n[d].f)?n[u++]:n[d++],c=u!=h&&(d==f||n[u].f<n[d].f)?n[u++]:n[d++],n[h++]={lit:-1,f:l.f+c.f,l:l,r:c};var g=S.F.setDepth(n[h-1],0);for(r<g&&(S.F.restrictDepth(s,r,g),g=r),a=0;a<f;a++)t[1+(s[a].lit<<1)]=s[a].d;return g},S.F.setDepth=function(e,t){return-1!=e.lit?e.d=t:Math.max(S.F.setDepth(e.l,t+1),S.F.setDepth(e.r,t+1))},S.F.restrictDepth=function(e,t,r){var n=0,i=1<<r-t,o=0;for(e.sort(function(e,t){return t.d==e.d?e.f-t.f:t.d-e.d}),n=0;n<e.length&&e[n].d>t;n++){var a=e[n].d;e[n].d=t,o+=i-(1<<r-a)}for(o>>>=r-t;0<o;)(a=e[n].d)<t?(e[n].d++,o-=1<<t-a-1):n++;for(;0<=n;n--)e[n].d==t&&o<0&&(e[n].d--,o++);0!=o&&console.log("debt left")},S.F._goodIndex=function(e,t){var r=0;return t[16|r]<=e&&(r|=16),t[8|r]<=e&&(r|=8),t[4|r]<=e&&(r|=4),t[2|r]<=e&&(r|=2),t[1|r]<=e&&(r|=1),r},S.F._writeLit=function(e,t,r,n){return S.F._putsF(r,n,t[e<<1]),n+t[1+(e<<1)]},S.F.inflate=function(e,t){var r=Uint8Array;if(3==e[0]&&0==e[1])return t||new r(0);var n=S.F,i=n._bitsF,o=n._bitsE,a=n._decodeTiny,f=n.makeCodes,s=n.codes2map,l=n._get17,c=n.U,u=null==t;u&&(t=new r(e.length>>>2<<3));for(var h,d=0,g=0,A=0,w=0,p=0;0==d;)if(d=i(e,p,1),E=i(e,p+1,2),p+=3,0!=E){if(u&&(t=S.F._check(t,w+(1<<17))),1==E&&(y=c.flmap,U=c.fdmap,g=511,A=31),2==E){E=o(e,p,5)+257,B=o(e,p+5,5)+1,h=o(e,p+10,4)+4,p+=14;for(var v=0;v<38;v+=2)c.itree[v]=0,c.itree[v+1]=0;for(var m=1,v=0;v<h;v++){var b=o(e,p+3*v,3);m<(c.itree[1+(c.ordr[v]<<1)]=b)&&(m=b)}p+=3*h,f(c.itree,m),s(c.itree,m,c.imap);var y=c.lmap,U=c.dmap,p=a(c.imap,(1<<m)-1,E+B,e,p,c.ttree),F=n._copyOut(c.ttree,0,E,c.ltree),g=(1<<F)-1,E=n._copyOut(c.ttree,E,B,c.dtree),A=(1<<E)-1;f(c.ltree,F),s(c.ltree,F,y),f(c.dtree,E),s(c.dtree,E,U)}for(;;){var _=y[l(e,p)&g],_=(p+=15&_,_>>>4);if(_>>>8==0)t[w++]=_;else{if(256==_)break;var C=w+_-254,_=(264<_&&(C=w+((_=c.ldef[_-257])>>>3)+o(e,p,7&_),p+=7&_),U[l(e,p)&A]);p+=15&_;var _=c.ddef[_>>>4],I=(_>>>4)+i(e,p,15&_);for(p+=15&_,u&&(t=S.F._check(t,w+(1<<17)));w<C;)t[w]=t[w++-I],t[w]=t[w++-I],t[w]=t[w++-I],t[w]=t[w++-I];w=C}}}else{0!=(7&p)&&(p+=8-(7&p));var B=4+(p>>>3),F=e[B-4]|e[B-3]<<8;(t=u?S.F._check(t,w+F):t).set(new r(e.buffer,e.byteOffset+B,F),w),p=B+F<<3,w+=F}return t.length==w?t:t.slice(0,w)},S.F._check=function(e,t){var r=e.length;if(t<=r)return e;r=new Uint8Array(Math.max(r<<1,t));return r.set(e,0),r},S.F._decodeTiny=function(e,t,r,n,i,o){for(var a=S.F._bitsE,f=S.F._get17,s=0;s<r;){var l=e[f(n,i)&t],l=(i+=15&l,l>>>4);if(l<=15)o[s]=l,s++;else{var c=0,u=0;16==l?(u=3+a(n,i,2),i+=2,c=o[s-1]):17==l?(u=3+a(n,i,3),i+=3):18==l&&(u=11+a(n,i,7),i+=7);for(var h=s+u;s<h;)o[s]=c,s++}}return i},S.F._copyOut=function(e,t,r,n){for(var i=0,o=0,a=n.length>>>1;o<r;){var f=e[o+t];n[o<<1]=0,i<(n[1+(o<<1)]=f)&&(i=f),o++}for(;o<a;)n[o<<1]=0,n[1+(o<<1)]=0,o++;return i},S.F.makeCodes=function(e,t){for(var r,n,i,o=S.F.U,a=e.length,f=o.bl_count,s=0;s<=t;s++)f[s]=0;for(s=1;s<a;s+=2)f[e[s]]++;var l=o.next_code,c=0;for(f[0]=0,r=1;r<=t;r++)c=c+f[r-1]<<1,l[r]=c;for(n=0;n<a;n+=2)0!=(i=e[n+1])&&(e[n]=l[i],l[i]++)},S.F.codes2map=function(e,t,r){for(var n=e.length,i=S.F.U.rev15,o=0;o<n;o+=2)if(0!=e[o+1])for(var a=e[o+1],f=o>>1<<4|a,a=t-a,s=e[o]<<a,l=s+(1<<a);s!=l;)r[i[s]>>>15-t]=f,s++},S.F.revCodes=function(e,t){for(var r=S.F.U.rev15,n=15-t,i=0;i<e.length;i+=2){var o=e[i]<<t-e[i+1];e[i]=r[o]>>>n}},S.F._putsE=function(e,t,r){var n=t>>>3;e[n]|=r<<=7&t,e[1+n]|=r>>>8},S.F._putsF=function(e,t,r){var n=t>>>3;e[n]|=r<<=7&t,e[1+n]|=r>>>8,e[2+n]|=r>>>16},S.F._bitsE=function(e,t,r){return(e[t>>>3]|e[1+(t>>>3)]<<8)>>>(7&t)&(1<<r)-1},S.F._bitsF=function(e,t,r){return(e[t>>>3]|e[1+(t>>>3)]<<8|e[2+(t>>>3)]<<16)>>>(7&t)&(1<<r)-1},S.F._get17=function(e,t){return(e[t>>>3]|e[1+(t>>>3)]<<8|e[2+(t>>>3)]<<16)>>>(7&t)},S.F._get25=function(e,t){return(e[t>>>3]|e[1+(t>>>3)]<<8|e[2+(t>>>3)]<<16|e[3+(t>>>3)]<<24)>>>(7&t)},S.F.U=(e=Uint16Array,t=Uint32Array,{next_code:new e(16),bl_count:new e(16),ordr:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],of0:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],exb:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],ldef:new e(32),df0:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],dxb:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],ddef:new t(32),flmap:new e(512),fltree:[],fdmap:new e(32),fdtree:[],lmap:new e(32768),ltree:[],ttree:[],dmap:new e(32768),dtree:[],imap:new e(512),itree:[],rev15:new e(32768),lhst:new t(286),dhst:new t(30),ihst:new t(19),lits:new t(15e3),strt:new e(65536),prev:new e(32768)});for(var n=S.F.U,i=0;i<32768;i++){var o=(4278255360&(o=(4042322160&(o=(3435973836&(o=(2863311530&i)>>>1|(1431655765&i)<<1))>>>2|(858993459&o)<<2))>>>4|(252645135&o)<<4))>>>8|(16711935&o)<<8;n.rev15[i]=(o>>>16|o<<16)>>>17}function a(e,t,r){for(;0!=t--;)e.push(0,r)}for(i=0;i<32;i++)n.ldef[i]=n.of0[i]<<3|n.exb[i],n.ddef[i]=n.df0[i]<<4|n.dxb[i];a(n.fltree,144,8),a(n.fltree,112,9),a(n.fltree,24,7),a(n.fltree,8,8),S.F.makeCodes(n.fltree,9),S.F.codes2map(n.fltree,9,n.flmap),S.F.revCodes(n.fltree,9),a(n.fdtree,32,5),S.F.makeCodes(n.fdtree,5),S.F.codes2map(n.fdtree,5,n.fdmap),S.F.revCodes(n.fdtree,5),a(n.itree,19,0),a(n.ltree,286,0),a(n.dtree,30,0),a(n.ttree,320,0);f={__proto__:null,default:r},[r].forEach(function(r){r&&"string"!=typeof r&&!Array.isArray(r)&&Object.keys(r).forEach(function(e){var t;"default"===e||e in f||(t=Object.getOwnPropertyDescriptor(r,e),Object.defineProperty(f,e,t.get?t:{enumerable:!0,get:function(){return r[e]}}))})});var f,_,b,P=Object.freeze(f);_={nextZero(e,t){for(;0!=e[t];)t++;return t},readUshort:(e,t)=>e[t]<<8|e[t+1],writeUshort(e,t,r){e[t]=r>>8&255,e[t+1]=255&r},readUint:(e,t)=>16777216*e[t]+(e[t+1]<<16|e[t+2]<<8|e[t+3]),writeUint(e,t,r){e[t]=r>>24&255,e[t+1]=r>>16&255,e[t+2]=r>>8&255,e[t+3]=255&r},readASCII(t,r,n){let i="";for(let e=0;e<n;e++)i+=String.fromCharCode(t[r+e]);return i},writeASCII(t,r,n){for(let e=0;e<n.length;e++)t[r+e]=n.charCodeAt(e)},readBytes(t,r,n){const i=[];for(let e=0;e<n;e++)i.push(t[r+e]);return i},pad:e=>e.length<2?"0"+e:e,readUTF8(t,r,n){let e,i="";for(let e=0;e<n;e++)i+="%"+_.pad(t[r+e].toString(16));try{e=decodeURIComponent(i)}catch(e){return _.readASCII(t,r,n)}return e}},b=function(){const E={H:{}};E.H.N=function(t,r){const n=Uint8Array;let i,o,a=0,f=0,s=0,l,c,u=0,h=0,d=0,g=0;if(3==t[0]&&0==t[1])return r||new n(0);const A=E.H,w=A.b,p=A.e,v=A.R,m=A.n,b=A.A,y=A.Z,U=A.m,e=null==r;for(e&&(r=new n(t.length>>>2<<5));0==a;)if(a=w(t,g,1),f=w(t,g+1,2),g+=3,0!=f){if(e&&(r=E.H.W(r,d+(1<<17))),1==f&&(i=U.J,o=U.h,u=511,h=31),2==f){s=p(t,g,5)+257,l=p(t,g+5,5)+1,c=p(t,g+10,4)+4,g+=14;let e=1;for(var F=0;F<38;F+=2)U.Q[F]=0,U.Q[F+1]=0;for(F=0;F<c;F++){const r=p(t,g+3*F,3);(U.Q[1+(U.X[F]<<1)]=r)>e&&(e=r)}g+=3*c,m(U.Q,e),b(U.Q,e,U.u),i=U.w,o=U.d,g=v(U.u,(1<<e)-1,s+l,t,g,U.v);const r=A.V(U.v,0,s,U.C),n=(u=(1<<r)-1,A.V(U.v,s,l,U.D));h=(1<<n)-1,m(U.C,r),b(U.C,r,i),m(U.D,n),b(U.D,n,o)}for(;;){const E=i[y(t,g)&u],n=(g+=15&E,E>>>4);if(n>>>8==0)r[d++]=n;else{if(256==n)break;{let e=d+n-254;if(264<n){const r=U.q[n-257];e=d+(r>>>3)+p(t,g,7&r),g+=7&r}const i=o[y(t,g)&h],a=(g+=15&i,i>>>4),f=U.c[a],s=(f>>>4)+w(t,g,15&f);for(g+=15&f;d<e;)r[d]=r[d++-s],r[d]=r[d++-s],r[d]=r[d++-s],r[d]=r[d++-s];d=e}}}}else{0!=(7&g)&&(g+=8-(7&g));const i=4+(g>>>3),o=t[i-4]|t[i-3]<<8;(r=e?E.H.W(r,d+o):r).set(new n(t.buffer,t.byteOffset+i,o),d),g=i+o<<3,d+=o}return r.length==d?r:r.slice(0,d)},E.H.W=function(e,t){var r=e.length;if(t<=r)return e;const n=new Uint8Array(r<<1);return n.set(e,0),n},E.H.R=function(e,r,n,i,o,a){const f=E.H.e,t=E.H.Z;let s=0;for(;s<n;){const E=e[t(i,o)&r],n=(o+=15&E,E>>>4);if(n<=15)a[s]=n,s++;else{let e=0,t=0;16==n?(t=3+f(i,o,2),o+=2,e=a[s-1]):17==n?(t=3+f(i,o,3),o+=3):18==n&&(t=11+f(i,o,7),o+=7);const r=s+t;for(;s<r;)a[s]=e,s++}}return o},E.H.V=function(e,t,r,n){let i=0,o=0;for(var a=n.length>>>1;o<r;){const r=e[o+t];n[o<<1]=0,(n[1+(o<<1)]=r)>i&&(i=r),o++}for(;o<a;)n[o<<1]=0,n[1+(o<<1)]=0,o++;return i},E.H.n=function(e,t){var r,n=E.H.m,i=e.length;let o,a,f;const s=n.j;for(var l=0;l<=t;l++)s[l]=0;for(l=1;l<i;l+=2)s[e[l]]++;const c=n.K;for(o=0,s[0]=0,a=1;a<=t;a++)o=o+s[a-1]<<1,c[a]=o;for(f=0;f<i;f+=2)0!=(r=e[f+1])&&(e[f]=c[r],c[r]++)},E.H.A=function(r,n,i){const o=r.length,a=E.H.m.r;for(let t=0;t<o;t+=2)if(0!=r[t+1]){const o=t>>1,s=r[t+1],l=o<<4|s,c=n-s;let e=r[t]<<c;for(var f=e+(1<<c);e!=f;)i[a[e]>>>15-n]=l,e++}},E.H.l=function(t,r){var n=E.H.m.r,i=15-r;for(let e=0;e<t.length;e+=2){var o=t[e]<<r-t[e+1];t[e]=n[o]>>>i}},E.H.M=function(e,t,r){var n=t>>>3;e[n]|=r<<=7&t,e[1+n]|=r>>>8},E.H.I=function(e,t,r){var n=t>>>3;e[n]|=r<<=7&t,e[1+n]|=r>>>8,e[2+n]|=r>>>16},E.H.e=function(e,t,r){return(e[t>>>3]|e[1+(t>>>3)]<<8)>>>(7&t)&(1<<r)-1},E.H.b=function(e,t,r){return(e[t>>>3]|e[1+(t>>>3)]<<8|e[2+(t>>>3)]<<16)>>>(7&t)&(1<<r)-1},E.H.Z=function(e,t){return(e[t>>>3]|e[1+(t>>>3)]<<8|e[2+(t>>>3)]<<16)>>>(7&t)},E.H.i=function(e,t){return(e[t>>>3]|e[1+(t>>>3)]<<8|e[2+(t>>>3)]<<16|e[3+(t>>>3)]<<24)>>>(7&t)},E.H.m=function(){const e=Uint16Array,t=Uint32Array;return{K:new e(16),j:new e(16),X:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],T:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],q:new e(32),p:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],z:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],c:new t(32),J:new e(512),_:[],h:new e(32),$:[],w:new e(32768),C:[],v:[],d:new e(32768),D:[],u:new e(512),Q:[],r:new e(32768),s:new t(286),Y:new t(30),a:new t(19),t:new t(15e3),k:new e(65536),g:new e(32768)}}();{const n=E.H.m;for(var e=0,t;e<32768;e++)t=(4278255360&(t=(4042322160&(t=(3435973836&(t=(2863311530&e)>>>1|(1431655765&e)<<1))>>>2|(858993459&t)<<2))>>>4|(252645135&t)<<4))>>>8|(16711935&t)<<8,n.r[e]=(t>>>16|t<<16)>>>17;function r(e,t,r){for(;0!=t--;)e.push(0,r)}for(e=0;e<32;e++)n.q[e]=n.S[e]<<3|n.T[e],n.c[e]=n.p[e]<<4|n.z[e];r(n._,144,8),r(n._,112,9),r(n._,24,7),r(n._,8,8),E.H.n(n._,9),E.H.A(n._,9,n.J),E.H.l(n._,9),r(n.$,32,5),E.H.n(n.$,5),E.H.A(n.$,5,n.h),E.H.l(n.$,5),r(n.Q,19,0),r(n.C,286,0),r(n.D,30,0),r(n.v,320,0)}return E.H.N}();const u={decode:function(r){const n=new Uint8Array(r);let i=8;const e=_,o=e.readUshort,t=e.readUint,a={tabs:{},frames:[]},f=new Uint8Array(n.length);let s,l=0,c=0;for(var u,h=[137,80,78,71,13,10,26,10],d=0;d<8;d++)if(n[d]!=h[d])throw"The input is not a PNG file!";for(;i<n.length;){const _=e.readUint(n,i),r=(i+=4,e.readASCII(n,i,4));if(i+=4,"IHDR"==r)z(n,i,a);else if("iCCP"==r){for(var g=i;0!=n[g];)g++;e.readASCII(n,i,g-i),n[g+1];const o=n.slice(g+2,i+_);let t=null;try{t=U(o)}catch(e){t=b(o)}a.tabs[r]=t}else if("CgBI"==r)a.tabs[r]=n.slice(i,i+4);else if("IDAT"==r){for(d=0;d<_;d++)f[l+d]=n[i+d];l+=_}else if("acTL"==r)a.tabs[r]={num_frames:t(n,i),num_plays:t(n,i+4)},s=new Uint8Array(n.length);else if("fcTL"==r){0!=c&&((u=a.frames[a.frames.length-1]).data=y(a,s.slice(0,c),u.rect.width,u.rect.height),c=0);const _={x:t(n,i+12),y:t(n,i+16),width:t(n,i+4),height:t(n,i+8)};let e=o(n,i+22);e=o(n,i+20)/(0==e?100:e);const r={rect:_,delay:Math.round(1e3*e),dispose:n[i+24],blend:n[i+25]};a.frames.push(r)}else if("fdAT"==r){for(d=0;d<_-4;d++)s[c+d]=n[i+d+4];c+=_-4}else if("pHYs"==r)a.tabs[r]=[e.readUint(n,i),e.readUint(n,i+4),n[i+8]];else if("cHRM"==r)for(a.tabs[r]=[],d=0;d<8;d++)a.tabs[r].push(e.readUint(n,i+4*d));else if("tEXt"==r||"zTXt"==r){null==a.tabs[r]&&(a.tabs[r]={});var A,w,p=e.nextZero(n,i),v=e.readASCII(n,i,p-i),m=i+_-p-1;w="tEXt"==r?e.readASCII(n,p+1,m):(A=U(n.slice(p+2,p+2+m)),e.readUTF8(A,0,A.length)),a.tabs[r][v]=w}else if("iTXt"==r){null==a.tabs[r]&&(a.tabs[r]={}),p=0,g=i,p=e.nextZero(n,g),v=e.readASCII(n,g,p-g);const b=n[g=p+1];n[g+1],g+=2,p=e.nextZero(n,g),e.readASCII(n,g,p-g),g=p+1,p=e.nextZero(n,g),e.readUTF8(n,g,p-g),m=_-((g=p+1)-i),w=0==b?e.readUTF8(n,g,m):(A=U(n.slice(g,g+m)),e.readUTF8(A,0,A.length)),a.tabs[r][v]=w}else if("PLTE"==r)a.tabs[r]=e.readBytes(n,i,_);else if("hIST"==r){const _=a.tabs.PLTE.length/3;for(a.tabs[r]=[],d=0;d<_;d++)a.tabs[r].push(o(n,i+2*d))}else if("tRNS"==r)3==a.ctype?a.tabs[r]=e.readBytes(n,i,_):0==a.ctype?a.tabs[r]=o(n,i):2==a.ctype&&(a.tabs[r]=[o(n,i),o(n,i+2),o(n,i+4)]);else if("gAMA"==r)a.tabs[r]=e.readUint(n,i)/1e5;else if("sRGB"==r)a.tabs[r]=n[i];else if("bKGD"==r)0==a.ctype||4==a.ctype?a.tabs[r]=[o(n,i)]:2==a.ctype||6==a.ctype?a.tabs[r]=[o(n,i),o(n,i+2),o(n,i+4)]:3==a.ctype&&(a.tabs[r]=n[i]);else if("IEND"==r)break;i+=_,e.readUint(n,i),i+=4}return 0!=c&&((u=a.frames[a.frames.length-1]).data=y(a,s.slice(0,c),u.rect.width,u.rect.height)),a.data=y(a,f,a.width,a.height),delete a.compress,delete a.interlace,delete a.filter,a},toRGBA8:function(t){var r=t.width,n=t.height;if(null==t.tabs.acTL)return[L(t.data,r,n,t).buffer];const i=[],o=(null==t.frames[0].data&&(t.frames[0].data=t.data),r*n*4),a=new Uint8Array(o),f=new Uint8Array(o),s=new Uint8Array(o);for(let e=0;e<t.frames.length;e++){var l=t.frames[e],c=l.rect.x,u=l.rect.y,h=l.rect.width,d=l.rect.height,g=L(l.data,h,d,t);if(0!=e)for(var A=0;A<o;A++)s[A]=a[A];if(0==l.blend?w(g,h,d,a,r,n,c,u,0):1==l.blend&&w(g,h,d,a,r,n,c,u,1),i.push(a.buffer.slice(0)),0!=l.dispose)if(1==l.dispose)w(f,h,d,a,r,n,c,u,0);else if(2==l.dispose)for(A=0;A<o;A++)a[A]=s[A]}return i},_paeth:h,_copyTile:w,_bin:_};function L(e,t,r,n){const i=t*r,o=C(n),a=Math.ceil(t*o/8),f=new Uint8Array(4*i),s=new Uint32Array(f.buffer),l=n["ctype"],c=n["depth"],u=_.readUshort;if(6==l){const _=i<<2;if(8==c)for(var h=0;h<_;h+=4)f[h]=e[h],f[h+1]=e[h+1],f[h+2]=e[h+2],f[h+3]=e[h+3];if(16==c)for(h=0;h<_;h++)f[h]=e[h<<1]}else if(2==l){const _=n.tabs.tRNS;if(null==_){if(8==c)for(h=0;h<i;h++){var d=3*h;s[h]=255<<24|e[d+2]<<16|e[d+1]<<8|e[d]}if(16==c)for(h=0;h<i;h++)d=6*h,s[h]=255<<24|e[d+4]<<16|e[d+2]<<8|e[d]}else{var g=_[0];const t=_[1],r=_[2];if(8==c)for(h=0;h<i;h++){var A=h<<2,d=3*h;s[h]=255<<24|e[d+2]<<16|e[d+1]<<8|e[d],e[d]==g&&e[d+1]==t&&e[d+2]==r&&(f[A+3]=0)}if(16==c)for(h=0;h<i;h++)A=h<<2,d=6*h,s[h]=255<<24|e[d+4]<<16|e[d+2]<<8|e[d],u(e,d)==g&&u(e,d+2)==t&&u(e,d+4)==r&&(f[A+3]=0)}}else if(3==l){const _=n.tabs.PLTE,o=n.tabs.tRNS,s=o?o.length:0;if(1==c)for(var w=0;w<r;w++)for(var p=w*a,v=w*t,h=0;h<t;h++){var A=v+h<<2,m=3*(b=e[p+(h>>3)]>>7-((7&h)<<0)&1);f[A]=_[m],f[A+1]=_[m+1],f[A+2]=_[m+2],f[A+3]=b<s?o[b]:255}if(2==c)for(w=0;w<r;w++)for(p=w*a,v=w*t,h=0;h<t;h++)A=v+h<<2,m=3*(b=e[p+(h>>2)]>>6-((3&h)<<1)&3),f[A]=_[m],f[A+1]=_[m+1],f[A+2]=_[m+2],f[A+3]=b<s?o[b]:255;if(4==c)for(w=0;w<r;w++)for(p=w*a,v=w*t,h=0;h<t;h++)A=v+h<<2,m=3*(b=e[p+(h>>1)]>>4-((1&h)<<2)&15),f[A]=_[m],f[A+1]=_[m+1],f[A+2]=_[m+2],f[A+3]=b<s?o[b]:255;if(8==c)for(h=0;h<i;h++){var b;A=h<<2,m=3*(b=e[h]),f[A]=_[m],f[A+1]=_[m+1],f[A+2]=_[m+2],f[A+3]=b<s?o[b]:255}}else if(4==l){if(8==c)for(h=0;h<i;h++){A=h<<2;var y=e[U=h<<1];f[A]=y,f[A+1]=y,f[A+2]=y,f[A+3]=e[U+1]}if(16==c)for(h=0;h<i;h++){var U,y=e[U=A=h<<2];f[A]=y,f[A+1]=y,f[A+2]=y,f[A+3]=e[U+2]}}else if(0==l)for(g=n.tabs.tRNS||-1,w=0;w<r;w++){const _=w*a,r=w*t;if(1==c)for(var F=0;F<t;F++){var E=(y=255*(e[_+(F>>>3)]>>>7-(7&F)&1))==255*g?0:255;s[r+F]=E<<24|y<<16|y<<8|y}else if(2==c)for(F=0;F<t;F++)E=(y=85*(e[_+(F>>>2)]>>>6-((3&F)<<1)&3))==85*g?0:255,s[r+F]=E<<24|y<<16|y<<8|y;else if(4==c)for(F=0;F<t;F++)E=(y=17*(e[_+(F>>>1)]>>>4-((1&F)<<2)&15))==17*g?0:255,s[r+F]=E<<24|y<<16|y<<8|y;else if(8==c)for(F=0;F<t;F++)E=(y=e[_+F])==g?0:255,s[r+F]=E<<24|y<<16|y<<8|y;else if(16==c)for(F=0;F<t;F++)y=e[_+(F<<1)],E=u(e,_+(F<<1))==g?0:255,s[r+F]=E<<24|y<<16|y<<8|y}return f}function y(e,t,r,n){var i=C(e),i=Math.ceil(r*i/8),i=new Uint8Array((i+1+e.interlace)*n);return t=(e.tabs.CgBI?b:U)(t,i),0==e.interlace?t=D(t,e,0,r,n):1==e.interlace&&(t=function(a,f){const s=f.width,l=f.height,c=C(f),u=c>>3,h=Math.ceil(s*c/8),d=new Uint8Array(l*h);let g=0;var A=[0,0,4,0,2,0,1],w=[0,4,0,2,0,1,0],p=[8,8,8,4,4,2,2],v=[8,8,4,4,2,2,1];let m=0;for(;m<7;){var b=p[m],y=v[m];let e=0,t=0,r=A[m];for(;r<l;)r+=b,t++;let n=w[m];for(;n<s;)n+=y,e++;var U,F=Math.ceil(e*c/8);D(a,f,g,e,t);let i=0,o=A[m];for(;o<l;){let e=w[m],t=g+i*F<<3;for(;e<s;){if(1==c&&(U=(U=a[t>>3])>>7-(7&t)&1,d[o*h+(e>>3)]|=U<<7-((7&e)<<0)),2==c&&(U=(U=a[t>>3])>>6-(7&t)&3,d[o*h+(e>>2)]|=U<<6-((3&e)<<1)),4==c&&(U=(U=a[t>>3])>>4-(7&t)&15,d[o*h+(e>>1)]|=U<<4-((1&e)<<2)),8<=c){const s=o*h+e*u;for(let e=0;e<u;e++)d[s+e]=a[(t>>3)+e]}t+=c,e+=y}i++,o+=b}e*t!=0&&(g+=t*(1+F)),m+=1}return d}(t,e)),t}function U(e,t){return b(new Uint8Array(e.buffer,2,e.length-6),t)}function C(e){return[1,null,3,1,2,null,4][e.ctype]*e.depth}function D(t,e,r,n,i){var o,a,f=C(e),s=Math.ceil(n*f/8),f=Math.ceil(f/8);let l=t[r],c=0;if(1<l&&(t[r]=[0,0,1][l-2]),3==l)for(c=f;c<s;c++)t[c+1]=t[c+1]+(t[c+1-f]>>>1)&255;for(let e=0;e<i;e++)if(a=(o=r+e*s)+e+1,l=t[a-1],(c=0)==l)for(;c<s;c++)t[o+c]=t[a+c];else if(1==l){for(;c<f;c++)t[o+c]=t[a+c];for(;c<s;c++)t[o+c]=t[a+c]+t[o+c-f]}else if(2==l)for(;c<s;c++)t[o+c]=t[a+c]+t[o+c-s];else if(3==l){for(;c<f;c++)t[o+c]=t[a+c]+(t[o+c-s]>>>1);for(;c<s;c++)t[o+c]=t[a+c]+(t[o+c-s]+t[o+c-f]>>>1)}else{for(;c<f;c++)t[o+c]=t[a+c]+h(0,t[o+c-s],0);for(;c<s;c++)t[o+c]=t[a+c]+h(t[o+c-f],t[o+c-s],t[o+c-f-s])}return t}function h(e,t,r){var n=e+t-r,i=n-e,o=n-t,n=n-r;return i*i<=o*o&&i*i<=n*n?e:o*o<=n*n?t:r}function z(e,t,r){r.width=_.readUint(e,t),r.height=_.readUint(e,t+=4),r.depth=e[t+=4],r.ctype=e[++t],r.compress=e[++t],r.filter=e[++t],r.interlace=e[++t],t++}function w(r,n,i,o,a,e,f,s,l){var c=Math.min(n,a),u=Math.min(i,e);let h=0,d=0;for(let t=0;t<u;t++)for(let e=0;e<c;e++)if(d=0<=f&&0<=s?(h=t*n+e<<2,(s+t)*a+f+e<<2):(h=(-s+t)*n-f+e<<2,t*a+e<<2),0==l)o[d]=r[h],o[d+1]=r[h+1],o[d+2]=r[h+2],o[d+3]=r[h+3];else if(1==l){var g=r[h+3]*(1/255),A=r[h]*g,w=r[h+1]*g,p=r[h+2]*g,v=o[d+3]*(1/255),m=o[d]*v,b=o[d+1]*v,y=o[d+2]*v;const n=1-g,i=g+v*n,a=0==i?0:1/i;o[d+3]=255*i,o[d+0]=(A+m*n)*a,o[d+1]=(w+b*n)*a,o[d+2]=(p+y*n)*a}else if(2==l)g=r[h+3],A=r[h],w=r[h+1],p=r[h+2],v=o[d+3],m=o[d],b=o[d+1],y=o[d+2],g==v&&A==m&&w==b&&p==y?(o[d]=0,o[d+1]=0,o[d+2]=0,o[d+3]=0):(o[d]=A,o[d+1]=w,o[d+2]=p,o[d+3]=g);else if(3==l){if(g=r[h+3],A=r[h],w=r[h+1],p=r[h+2],v=o[d+3],m=o[d],b=o[d+1],y=o[d+2],g==v&&A==m&&w==b&&p==y)continue;if(g<220&&20<v)return!1}return!0}{const Q=u["_copyTile"],H=u["_bin"],m=u._paeth;var F={table:function(){const r=new Uint32Array(256);for(let e=0;e<256;e++){let t=e;for(let e=0;e<8;e++)1&t?t=3988292384^t>>>1:t>>>=1;r[e]=t}return r}(),update(t,r,n,i){for(let e=0;e<i;e++)t=F.table[255&(t^r[n+e])]^t>>>8;return t},crc:(e,t,r)=>4294967295^F.update(4294967295,e,t,r)};function E(e,t,r,n){t[r]+=e[0]*n>>4,t[r+1]+=e[1]*n>>4,t[r+2]+=e[2]*n>>4,t[r+3]+=e[3]*n>>4}function I(e){return Math.max(0,Math.min(255,e))}function N(e,t){var r=e[0]-t[0],n=e[1]-t[1],i=e[2]-t[2],e=e[3]-t[3];return r*r+n*n+i*i+e*e}function W(n,i,o,a,e,f,s){null==s&&(s=1);const l=a.length,c=[];for(var u=0;u<l;u++){const n=a[u];c.push([n>>>0&255,n>>>8&255,n>>>16&255,n>>>24&255])}for(u=0;u<l;u++){let e=4294967295;for(var h=0,d=0;d<l;d++){var g=N(c[u],c[d]);d!=u&&g<e&&(e=g,h=d)}}const A=new Uint32Array(e.buffer),w=new Int16Array(i*o*4),p=[0,8,2,10,12,4,14,6,3,11,1,9,15,7,13,5];for(u=0;u<p.length;u++)p[u]=255*((p[u]+.5)/16-.5);for(let r=0;r<o;r++)for(let t=0;t<i;t++){var u=4*(r*i+t),v=2!=s?[I(n[u]+w[u]),I(n[u+1]+w[u+1]),I(n[u+2]+w[u+2]),I(n[u+3]+w[u+3])]:(g=p[4*(3&r)+(3&t)],[I(n[u]+g),I(n[u+1]+g),I(n[u+2]+g),I(n[u+3]+g)]),h=0;let e=16777215;for(d=0;d<l;d++){const n=N(v,c[d]);n<e&&(e=n,h=d)}var m=c[h],m=[v[0]-m[0],v[1]-m[1],v[2]-m[2],v[3]-m[3]];1==s&&(t!=i-1&&E(m,w,u+4,7),r!=o-1&&(0!=t&&E(m,w,u+4*i-4,3),E(m,w,u+4*i,5),t!=i-1&&E(m,w,u+4*i+4,1))),f[u>>2]=h,A[u>>2]=a[h]}}function j(e,t,r,n,i){null==i&&(i={});const o=F["crc"],a=H.writeUint,f=H.writeUshort,s=H.writeASCII;let l=8;var c=1<e.frames.length;let u,h=!1,d=33+(c?20:0);if(null!=i.sRGB&&(d+=13),null!=i.pHYs&&(d+=21),null!=i.iCCP&&(u=pako.deflate(i.iCCP),d+=21+u.length+4),3==e.ctype){for(var g=e.plte.length,A=0;A<g;A++)e.plte[A]>>>24!=255&&(h=!0);d+=8+3*g+4+(h?+g+8+4:0)}for(var w=0;w<e.frames.length;w++)c&&(d+=38),d+=(b=e.frames[w]).cimg.length+12,0!=w&&(d+=4);d+=12;const p=new Uint8Array(d),v=[137,80,78,71,13,10,26,10];for(A=0;A<8;A++)p[A]=v[A];if(a(p,l,13),l+=4,s(p,l,"IHDR"),l+=4,a(p,l,t),l+=4,a(p,l,r),l+=4,p[l]=e.depth,l++,p[l]=e.ctype,l++,p[l]=0,l++,p[l]=0,l++,p[l]=0,l++,a(p,l,o(p,l-17,17)),l+=4,null!=i.sRGB&&(a(p,l,1),l+=4,s(p,l,"sRGB"),l+=4,p[l]=i.sRGB,l++,a(p,l,o(p,l-5,5)),l+=4),null!=i.iCCP){const e=13+u.length;a(p,l,e),l+=4,s(p,l,"iCCP"),l+=4,s(p,l,"ICC profile"),l=l+11+2,p.set(u,l),l+=u.length,a(p,l,o(p,l-(e+4),e+4)),l+=4}if(null!=i.pHYs&&(a(p,l,9),l+=4,s(p,l,"pHYs"),l+=4,a(p,l,i.pHYs[0]),l+=4,a(p,l,i.pHYs[1]),l+=4,p[l]=i.pHYs[2],l++,a(p,l,o(p,l-13,13)),l+=4),c&&(a(p,l,8),l+=4,s(p,l,"acTL"),l+=4,a(p,l,e.frames.length),l+=4,a(p,l,null!=i.loop?i.loop:0),l+=4,a(p,l,o(p,l-12,12)),l+=4),3==e.ctype){for(a(p,l,3*(g=e.plte.length)),l+=4,s(p,l,"PLTE"),l+=4,A=0;A<g;A++){const H=3*A,t=e.plte[A],F=255&t,r=t>>>8&255,n=t>>>16&255;p[l+H+0]=F,p[l+H+1]=r,p[l+H+2]=n}if(l+=3*g,a(p,l,o(p,l-3*g-4,3*g+4)),l+=4,h){for(a(p,l,g),l+=4,s(p,l,"tRNS"),l+=4,A=0;A<g;A++)p[l+A]=e.plte[A]>>>24&255;l+=g,a(p,l,o(p,l-g-4,g+4)),l+=4}}let m=0;for(w=0;w<e.frames.length;w++){var b=e.frames[w];c&&(a(p,l,26),l+=4,s(p,l,"fcTL"),l+=4,a(p,l,m++),l+=4,a(p,l,b.rect.width),l+=4,a(p,l,b.rect.height),l+=4,a(p,l,b.rect.x),l+=4,a(p,l,b.rect.y),l+=4,f(p,l,n[w]),l+=2,f(p,l,1e3),l+=2,p[l]=b.dispose,l++,p[l]=b.blend,l++,a(p,l,o(p,l-30,30)),l+=4);const H=b.cimg,t=(a(p,l,(g=H.length)+(0==w?0:4)),l+=4);s(p,l,0==w?"IDAT":"fdAT"),l+=4,0!=w&&(a(p,l,m++),l+=4),p.set(H,l),l+=g,a(p,l,o(p,t,l-t)),l+=4}return a(p,l,0),l+=4,s(p,l,"IEND"),l+=4,a(p,l,o(p,l-4,4)),l+=4,p.buffer}function q(t,r,n){for(let e=0;e<t.frames.length;e++){const a=t.frames[e];a.rect.width;var i=a.rect.height,o=new Uint8Array(i*a.bpl+i);a.cimg=function(t,r,n,i,o,e,a){const f=[];let s,l=[0,1,2,3,4];-1!=e?l=[e]:(5e5<r*i||1==n)&&(l=[0]),a&&(s={level:0});const c=P;for(var u=0;u<l.length;u++){for(let e=0;e<r;e++)!function(e,t,r,n,i,o){var a=r*n;var f=a+r;if(e[f]=o,f++,0==o)if(n<500)for(var s=0;s<n;s++)e[f+s]=t[a+s];else e.set(new Uint8Array(t.buffer,a,n),f);else if(1==o){for(s=0;s<i;s++)e[f+s]=t[a+s];for(s=i;s<n;s++)e[f+s]=t[a+s]-t[a+s-i]+256&255}else if(0==r){for(s=0;s<i;s++)e[f+s]=t[a+s];if(2==o)for(s=i;s<n;s++)e[f+s]=t[a+s];if(3==o)for(s=i;s<n;s++)e[f+s]=t[a+s]-(t[a+s-i]>>1)+256&255;if(4==o)for(s=i;s<n;s++)e[f+s]=t[a+s]-m(t[a+s-i],0,0)+256&255}else{if(2==o)for(s=0;s<n;s++)e[f+s]=t[a+s]+256-t[a+s-n]&255;if(3==o){for(s=0;s<i;s++)e[f+s]=t[a+s]+256-(t[a+s-n]>>1)&255;for(s=i;s<n;s++)e[f+s]=t[a+s]+256-(t[a+s-n]+t[a+s-i]>>1)&255}if(4==o){for(s=0;s<i;s++)e[f+s]=t[a+s]+256-m(0,t[a+s-n],0)&255;for(s=i;s<n;s++)e[f+s]=t[a+s]+256-m(t[a+s-i],t[a+s-n],t[a+s-i-n])&255}}}(o,t,e,i,n,l[u]);f.push(c.deflate(o,s))}let h,d=1e9;for(u=0;u<f.length;u++)f[u].length<d&&(h=u,d=f[u].length);return f[h]}(a.img,i,a.bpp,a.bpl,o,r,n)}}function G(t,n,e,i,r){var o=r[0],a=r[1],f=r[2],s=r[3],l=r[4],c=r[5];let u=6,h=8,d=255;for(var g=0;g<t.length;g++){const Q=new Uint8Array(t[g]);for(var A=Q.length,w=0;w<A;w+=4)d&=Q[w+3]}const p=255!=d,v=function(u,h,d,g,A,r){const n=[];for(var i,w,p=0;p<u.length;p++){const b=new Uint8Array(u[p]),w=new Uint32Array(b.buffer);let f=0,s=0,l=h,c=d,e=g?1:0;if(0!=p){var o=r||g||1==p||0!=n[p-2].dispose?1:2;let t=0,a=1e9;for(let e=0;e<o;e++){var v=new Uint8Array(u[p-1-e]);const g=new Uint32Array(u[p-1-e]);let r=h,n=d,i=-1,o=-1;for(let t=0;t<d;t++)for(let e=0;e<h;e++)w[y=t*h+e]!=g[y]&&(e<r&&(r=e),e>i&&(i=e),t<n&&(n=t),t>o&&(o=t));-1==i&&(r=n=i=o=0),A&&(1==(1&r)&&r--,1==(1&n)&&n--);var m=(i-r+1)*(o-n+1);m<a&&(a=m,t=e,f=r,s=n,l=i-r+1,c=o-n+1)}v=new Uint8Array(u[p-1-t]),1==t&&(n[p-1].dispose=2),i=new Uint8Array(l*c*4),Q(v,h,d,i,l,c,-f,-s,0),1==(e=Q(b,h,d,i,l,c,-f,-s,3)?1:0)?X(b,h,d,i,{x:f,y:s,width:l,height:c}):Q(b,h,d,i,l,c,-f,-s,0)}else i=b.slice(0);n.push({rect:{x:f,y:s,width:l,height:c},img:i,blend:e,dispose:0})}if(g)for(p=0;p<n.length;p++)if(1!=(w=n[p]).blend){const Q=w.rect,g=n[p-1].rect,r=Math.min(Q.x,g.x),i=Math.min(Q.y,g.y),v={x:r,y:i,width:Math.max(Q.x+Q.width,g.x+g.width)-r,height:Math.max(Q.y+Q.height,g.y+g.height)-i};p-(n[p-1].dispose=1)!=0&&K(u,h,d,n,p-1,v,A),K(u,h,d,n,p,v,A)}let b=0;if(1!=u.length)for(var y=0;y<n.length;y++)b+=(w=n[y]).rect.width*w.rect.height;return n}(t,n,e,o,a,f),m={},b=[],y=[];if(0!=i){const Q=[];for(w=0;w<v.length;w++)Q.push(v[w].img.buffer);const t=function(e){let i=0;for(var t=0;t<e.length;t++)i+=e[t].byteLength;const o=new Uint8Array(i);let a=0;for(t=0;t<e.length;t++){const i=new Uint8Array(e[t]),r=i.length;for(let n=0;n<r;n+=4){let e=i[n],t=i[n+1],r=i[n+2];var f=i[n+3];0==f&&(e=t=r=0),o[a+n]=e,o[a+n+1]=t,o[a+n+2]=r,o[a+n+3]=f}a+=r}return o.buffer}(Q),n=Y(t,i);for(w=0;w<n.plte.length;w++)b.push(n.plte[w].est.rgba);let e=0;for(w=0;w<v.length;w++){const Q=(F=v[w]).img.length;var U=new Uint8Array(n.inds.buffer,e>>2,Q>>2);y.push(U);const t=new Uint8Array(n.abuf,e,Q);c&&W(F.img,F.rect.width,F.rect.height,b,t,U),F.img.set(t),e+=Q}}else for(g=0;g<v.length;g++){var F=v[g];const Q=new Uint32Array(F.img.buffer);var E=F.rect.width,A=Q.length,U=new Uint8Array(A);for(y.push(U),w=0;w<A;w++){const t=Q[w];if(0!=w&&t==Q[w-1])U[w]=U[w-1];else if(E<w&&t==Q[w-E])U[w]=U[w-E];else{let e=m[t];if(null==e&&(m[t]=e=b.length,b.push(t),300<=b.length))break;U[w]=e}}}var _=b.length;for(_<=256&&0==l&&(h=_<=2?1:_<=4?2:_<=16?4:8,h=Math.max(h,s)),g=0;g<v.length;g++){(F=v[g]).rect.x,F.rect.y,E=F.rect.width;const Q=F.rect.height;let e=F.img,t=(new Uint32Array(e.buffer),4*E),r=4;if(_<=256&&0==l){t=Math.ceil(h*E/8);var C=new Uint8Array(t*Q);const i=y[g];for(let e=0;e<Q;e++){w=e*t;const Q=e*E;if(8==h)for(var I=0;I<E;I++)C[w+I]=i[Q+I];else if(4==h)for(I=0;I<E;I++)C[w+(I>>1)]|=i[Q+I]<<4-4*(1&I);else if(2==h)for(I=0;I<E;I++)C[w+(I>>2)]|=i[Q+I]<<6-2*(3&I);else if(1==h)for(I=0;I<E;I++)C[w+(I>>3)]|=i[Q+I]<<7-(7&I)}e=C,u=3,r=1}else if(0==p&&1==v.length){C=new Uint8Array(E*Q*3);const i=E*Q;for(w=0;w<i;w++){const Q=3*w,n=4*w;C[Q]=e[n],C[1+Q]=e[1+n],C[2+Q]=e[2+n]}e=C,u=2,r=3,t=3*E}F.img=e,F.bpl=t,F.bpp=r}return{ctype:u,depth:h,plte:b,frames:v}}function K(e,r,n,i,o,a,f){const s=Uint8Array,l=Uint32Array,t=new s(e[o-1]),c=new l(e[o-1]),u=o+1<e.length?new s(e[o+1]):null,h=new s(e[o]),d=new l(h.buffer);let g=r,A=n,w=-1,p=-1;for(let t=0;t<a.height;t++)for(let e=0;e<a.width;e++){const n=a.x+e,f=a.y+t,s=f*r+n,l=d[s];0!=l&&(0!=i[o-1].dispose||c[s]!=l||null!=u&&0==u[4*s+3])&&(n<g&&(g=n),n>w&&(w=n),f<A&&(A=f),f>p&&(p=f))}-1==w&&(g=A=w=p=0),f&&(1==(1&g)&&g--,1==(1&A)&&A--),a={x:g,y:A,width:w-g+1,height:p-A+1};const v=i[o];v.rect=a,v.blend=1,v.img=new Uint8Array(a.width*a.height*4),0==i[o-1].dispose?(Q(t,r,n,v.img,a.width,a.height,-a.x,-a.y,0),X(h,r,n,v.img,a)):Q(h,r,n,v.img,a.width,a.height,-a.x,-a.y,0)}function X(e,t,r,n,i){Q(e,t,r,n,i.width,i.height,-i.x,-i.y,2)}function Y(e,t){const r=new Uint8Array(e),n=r.slice(0),i=new Uint32Array(n.buffer),o=Z(n,t),a=o[0],f=o[1],s=r.length,l=new Uint8Array(s>>2);let c;if(r.length<2e7)for(var u=0;u<s;u+=4)c=p(a,h=r[u]*(1/255),d=r[u+1]*(1/255),g=r[u+2]*(1/255),A=r[u+3]*(1/255)),l[u>>2]=c.ind,i[u>>2]=c.est.rgba;else for(u=0;u<s;u+=4){var h=r[u]*(1/255),d=r[u+1]*(1/255),g=r[u+2]*(1/255),A=r[u+3]*(1/255);for(c=a;c.left;)c=V(c.est,h,d,g,A)<=0?c.left:c.right;l[u>>2]=c.ind,i[u>>2]=c.est.rgba}return{abuf:n.buffer,inds:l,plte:f}}function Z(r,e,n){null==n&&(n=1e-4);const i=new Uint32Array(r.buffer),t={i0:0,i1:r.length,bst:null,est:null,tdst:0,left:null,right:null},o=(t.bst=$(r,t.i0,t.i1),t.est=g(t.bst),[t]);for(;o.length<e;){let e=0,t=0;for(var a=0;a<o.length;a++)o[a].est.L>e&&(e=o[a].est.L,t=a);if(e<n)break;const f=o[t],s=function(e,t,r,n,i,o){for(n-=4;r<n;){for(;d(e,r,i)<=o;)r+=4;for(;d(e,n,i)>o;)n-=4;if(n<=r)break;var a=t[r>>2];t[r>>2]=t[n>>2],t[n>>2]=a,r+=4,n-=4}for(;d(e,r,i)>o;)r-=4;return r+4}(r,i,f.i0,f.i1,f.est.e,f.est.eMq255);if(f.i0>=s||f.i1<=s)f.est.L=0;else{const l={i0:f.i0,i1:s,bst:null,est:null,tdst:0,left:null,right:null},c=(l.bst=$(r,l.i0,l.i1),l.est=g(l.bst),{i0:s,i1:f.i1,bst:null,est:null,tdst:0,left:null,right:null});for(c.bst={R:[],m:[],N:f.bst.N-l.bst.N},a=0;a<16;a++)c.bst.R[a]=f.bst.R[a]-l.bst.R[a];for(a=0;a<4;a++)c.bst.m[a]=f.bst.m[a]-l.bst.m[a];c.est=g(c.bst),f.left=l,f.right=c,o[t]=l,o.push(c)}}for(o.sort((e,t)=>t.bst.N-e.bst.N),a=0;a<o.length;a++)o[a].ind=a;return[t,o]}function p(e,t,r,n,i){if(null==e.left)return e.tdst=function(e,t,r,n,i){t-=e[0],r-=e[1],n-=e[2],i-=e[3];return t*t+r*r+n*n+i*i}(e.est.q,t,r,n,i),e;var o=V(e.est,t,r,n,i);let a=e.left,f=e.right;0<o&&(a=e.right,f=e.left);e=p(a,t,r,n,i);if(e.tdst<=o*o)return e;o=p(f,t,r,n,i);return o.tdst<e.tdst?o:e}function V(e,t,r,n,i){var o=e["e"];return o[0]*t+o[1]*r+o[2]*n+o[3]*i-e.eMq}function d(e,t,r){return e[t]*r[0]+e[t+1]*r[1]+e[t+2]*r[2]+e[t+3]*r[3]}function $(t,r,n){const i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],o=[0,0,0,0],e=n-r>>2;for(let e=r;e<n;e+=4){const r=t[e]*(1/255),n=t[e+1]*(1/255),a=t[e+2]*(1/255),f=t[e+3]*(1/255);o[0]+=r,o[1]+=n,o[2]+=a,o[3]+=f,i[0]+=r*r,i[1]+=r*n,i[2]+=r*a,i[3]+=r*f,i[5]+=n*n,i[6]+=n*a,i[7]+=n*f,i[10]+=a*a,i[11]+=a*f,i[15]+=f*f}return i[4]=i[1],i[8]=i[2],i[9]=i[6],i[12]=i[3],i[13]=i[7],i[14]=i[11],{R:i,m:o,N:e}}function g(e){const t=e["R"],r=e["m"],n=e["N"],i=r[0],o=r[1],a=r[2],f=r[3],s=0==n?0:1/n,l=[t[0]-i*i*s,t[1]-i*o*s,t[2]-i*a*s,t[3]-i*f*s,t[4]-o*i*s,t[5]-o*o*s,t[6]-o*a*s,t[7]-o*f*s,t[8]-a*i*s,t[9]-a*o*s,t[10]-a*a*s,t[11]-a*f*s,t[12]-f*i*s,t[13]-f*o*s,t[14]-f*a*s,t[15]-f*f*s],c=l,u=J;let h=[Math.random(),Math.random(),Math.random(),Math.random()],d=0,g=0;if(0!=n)for(let e=0;e<16&&(h=u.multVec(c,h),g=Math.sqrt(u.dot(h,h)),h=u.sml(1/g,h),!(0!=e&&Math.abs(g-d)<1e-9));e++)d=g;e=[i*s,o*s,a*s,f*s];return{Cov:l,q:e,e:h,L:d,eMq255:u.dot(u.sml(255,e),h),eMq:u.dot(h,e),rgba:(Math.round(255*e[3])<<24|Math.round(255*e[2])<<16|Math.round(255*e[1])<<8|Math.round(255*e[0])<<0)>>>0}}var J={multVec:(e,t)=>[e[0]*t[0]+e[1]*t[1]+e[2]*t[2]+e[3]*t[3],e[4]*t[0]+e[5]*t[1]+e[6]*t[2]+e[7]*t[3],e[8]*t[0]+e[9]*t[1]+e[10]*t[2]+e[11]*t[3],e[12]*t[0]+e[13]*t[1]+e[14]*t[2]+e[15]*t[3]],dot:(e,t)=>e[0]*t[0]+e[1]*t[1]+e[2]*t[2]+e[3]*t[3],sml:(e,t)=>[e*t[0],e*t[1],e*t[2],e*t[3]]};u.encode=function(e,t,r,n,i,o,a){e=G(e,t,r,n=null==n?0:n,[!1,!1,!1,0,a=null==a?!1:a,!1]);return q(e,-1),j(e,t,r,i,o)},u.encodeLL=function(t,r,n,e,i,o,a,f){const s={ctype:0+(1==e?0:2)+(0==i?0:4),depth:o,frames:[]},l=(e+i)*o,c=l*r;for(let e=0;e<t.length;e++)s.frames.push({rect:{x:0,y:0,width:r,height:n},img:new Uint8Array(t[e]),blend:0,dispose:1,bpp:Math.ceil(l/8),bpl:Math.ceil(c/8)});return q(s,0,!0),j(s,r,n,a,f)},u.encode.compress=G,u.encode.dither=W,u.quantize=Y,u.quantize.getKDtree=Z,u.quantize.getNearest=p}const ee={toArrayBuffer(e,t){const r=e.width,n=e.height,i=r<<2,o=e.getContext("2d").getImageData(0,0,r,n),a=new Uint32Array(o.data.buffer),f=(32*r+31)/32<<2,s=f*n,l=122+s,c=new ArrayBuffer(l),u=new DataView(c);let h,d,g,A,w=1<<20,p=0,v=0,m=0;function b(e){u.setUint16(v,e,!0),v+=2}function y(e){u.setUint32(v,e,!0),v+=4}function U(e){v+=e}b(19778),y(l),U(4),y(122),y(108),y(r),y(-n>>>0),b(1),b(32),y(3),y(s),y(2835),y(2835),U(8),y(16711680),y(65280),y(255),y(4278190080),y(1466527264),function e(){for(;p<n&&0<w;){for(A=122+p*f,h=0;h<i;)w--,d=a[m++],g=d>>>24,u.setUint32(A+h,d<<8|g),h+=4;p++}m<a.length?(w=1<<20,setTimeout(e,ee._dly)):t(c)}()},toBlob(e,t){this.toArrayBuffer(e,e=>{t(new Blob([e],{type:"image/bmp"}))})},_dly:9};var s={CHROME:"CHROME",FIREFOX:"FIREFOX",DESKTOP_SAFARI:"DESKTOP_SAFARI",IE:"IE",IOS:"IOS",ETC:"ETC"},te={[s.CHROME]:16384,[s.FIREFOX]:11180,[s.DESKTOP_SAFARI]:16384,[s.IE]:8192,[s.IOS]:4096,[s.ETC]:8192};const re="undefined"!=typeof window,ne="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,l=re&&window.cordova&&window.cordova.require&&window.cordova.require("cordova/modulemapper"),ie=(re||ne)&&(l&&l.getOriginalSymbol(window,"File")||"undefined"!=typeof File&&File),oe=(re||ne)&&(l&&l.getOriginalSymbol(window,"FileReader")||"undefined"!=typeof FileReader&&FileReader);function ae(f,s,l=Date.now()){return new Promise(e=>{const t=f.split(","),r=t[0].match(/:(.*?);/)[1],n=globalThis.atob(t[1]);let i=n.length;const o=new Uint8Array(i);for(;i--;)o[i]=n.charCodeAt(i);const a=new Blob([o],{type:r});a.name=s,a.lastModified=l,e(a)})}function fe(n){return new Promise((e,t)=>{const r=new oe;r.onload=()=>e(r.result),r.onerror=e=>t(e),r.readAsDataURL(n)})}function se(n){return new Promise((e,t)=>{const r=new Image;r.onload=()=>e(r),r.onerror=e=>t(e),r.src=n})}function c(){if(void 0!==c.cachedResult)return c.cachedResult;let e=s.ETC;var t=navigator["userAgent"];return/Chrom(e|ium)/i.test(t)?e=s.CHROME:/iP(ad|od|hone)/i.test(t)&&/WebKit/i.test(t)?e=s.IOS:/Safari/i.test(t)?e=s.DESKTOP_SAFARI:/Firefox/i.test(t)?e=s.FIREFOX:!/MSIE/i.test(t)&&1!=!!document.documentMode||(e=s.IE),c.cachedResult=e,c.cachedResult}function le(e,t){var r=c(),n=te[r];let i=e,o=t,a=i*o;for(var f=i>o?o/i:i/o;a>n*n;){const e=(n+i)/2,t=(n+o)/2;i=e<t?(o=t)*f:(o=e*f,e),a=i*o}return{width:i,height:o}}function B(e,t){let r,n;try{if(r=new OffscreenCanvas(e,t),null===(n=r.getContext("2d")))throw new Error("getContext of OffscreenCanvas returns null")}catch(e){r=document.createElement("canvas"),n=r.getContext("2d")}return r.width=e,r.height=t,[r,n]}function ce(e,t){const{width:r,height:n}=le(e.width,e.height),[i,o]=B(r,n);return t&&/jpe?g/.test(t)&&(o.fillStyle="white",o.fillRect(0,0,i.width,i.height)),o.drawImage(e,0,0,i.width,i.height),i}function A(){return A.cachedResult=void 0===A.cachedResult?["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"undefined"!=typeof document&&"ontouchend"in document:A.cachedResult}function M(a,f={}){return new Promise(function(e,r){let n,t;function i(e){try{var t=function(e){try{throw e}catch(e){return r(e)}};try{return fe(a).then(function(e){try{return se(e).then(function(e){try{n=e;try{return o()}catch(e){return r(e)}return}catch(e){return t(e)}},t)}catch(e){return t(e)}},t)}catch(e){t(e)}}catch(e){return r(e)}}var o=function(){try{return t=ce(n,f.fileType||a.type),e([n,t])}catch(e){return r(e)}};try{if(A()||[s.DESKTOP_SAFARI,s.MOBILE_SAFARI].includes(c()))throw new Error("Skip createImageBitmap on IOS and Safari");return createImageBitmap(a).then(function(e){try{return n=e,o()}catch(e){return i()}},i)}catch(e){i()}})}function R(a,f,s,l,c=1){return new Promise(function(e,t){let n;if("image/png"!==f)return"image/bmp"===f?new Promise(e=>ee.toBlob(a,e)).then(function(e){try{return(n=e).name=s,n.lastModified=l,i.call(this)}catch(e){return t(e)}}.bind(this),t):"function"==typeof OffscreenCanvas&&a instanceof OffscreenCanvas?a.convertToBlob({type:f,quality:c}).then(function(e){try{return(n=e).name=s,n.lastModified=l,r.call(this)}catch(e){return t(e)}}.bind(this),t):ae(a.toDataURL(f,c),s,l).then(function(e){try{return n=e,r.call(this)}catch(e){return t(e)}}.bind(this),t);{let e,t,r;return t=(e=a.getContext("2d")).getImageData(0,0,a.width,a.height)["data"],r=u.encode([t.buffer],a.width,a.height,4096*c),(n=new Blob([r],{type:f})).name=s,n.lastModified=l,o.call(this)}function r(){return i.call(this)}function i(){return o.call(this)}function o(){return e(n)}})}function x(e){e.width=0,e.height=0}function T(){return new Promise(function(t,r){let n,i,o,a;return void 0!==T.cachedResult?t(T.cachedResult):ae("data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/xABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAAAAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==","test.jpg",Date.now()).then(function(e){try{return M(n=e).then(function(e){try{return R(i=e[1],n.type,n.name,n.lastModified).then(function(e){try{return o=e,x(i),M(o).then(function(e){try{return a=e[0],T.cachedResult=1===a.width&&2===a.height,t(T.cachedResult)}catch(e){return r(e)}},r)}catch(e){return r(e)}},r)}catch(e){return r(e)}},r)}catch(e){return r(e)}},r)})}function ue(r){return new Promise((o,t)=>{const e=new oe;e.onload=t=>{const r=new DataView(t.target.result);if(65496!=r.getUint16(0,!1))return o(-2);const n=r.byteLength;let i=2;for(;i<n;){if(r.getUint16(i+2,!1)<=8)return o(-1);const t=r.getUint16(i,!1);if(i+=2,65505==t){if(1165519206!=r.getUint32(i+=2,!1))return o(-1);const t=18761==r.getUint16(i+=6,!1),n=(i+=r.getUint32(i+4,t),r.getUint16(i,t));i+=2;for(let e=0;e<n;e++)if(274==r.getUint16(i+12*e,t))return o(r.getUint16(i+12*e+8,t))}else{if(65280!=(65280&t))break;i+=r.getUint16(i,!1)}}return o(-1)},e.onerror=e=>t(e),e.readAsArrayBuffer(r)})}function he(e,t){var r=e["width"],n=e["height"],t=t["maxWidthOrHeight"];let i,o=e;return isFinite(t)&&(t<r||t<n)&&([o,i]=B(r,n),n<r?(o.width=t,o.height=n/r*t):(o.width=r/n*t,o.height=t),i.drawImage(e,0,0,o.width,o.height),x(e)),o}function de(e,t){const r=e["width"],n=e["height"],[i,o]=B(r,n);switch(4<t&&t<9?(i.width=n,i.height=r):(i.width=r,i.height=n),t){case 2:o.transform(-1,0,0,1,r,0);break;case 3:o.transform(-1,0,0,-1,r,n);break;case 4:o.transform(1,0,0,-1,0,n);break;case 5:o.transform(0,1,1,0,0,0);break;case 6:o.transform(0,1,-1,0,n,0);break;case 7:o.transform(0,-1,-1,0,n,r);break;case 8:o.transform(0,-1,1,0,0,r)}return o.drawImage(e,0,0,r,n),x(e),i}function ge(C,I,e=0){return new Promise(function(i,o){let t,a,f,s,l,r,c,u,h,d,g,A,w,p,v,m,b,y,U,F;function E(e=5){if(I.signal&&I.signal.aborted)throw I.signal.reason;t+=e,I.onProgress(Math.min(t,100))}function _(e){if(I.signal&&I.signal.aborted)throw I.signal.reason;t=Math.min(Math.max(e,t),100),I.onProgress(t)}return t=e,a=I.maxIteration||10,f=1024*I.maxSizeMB*1024,E(),M(C,I).then(function(e){try{return[,s]=e,E(),l=he(s,I),E(),new Promise(function(e,t){var r;return(r=I.exifOrientation)?n.call(this):ue(C).then(function(e){try{return r=e,n.call(this)}catch(e){return t(e)}}.bind(this),t);function n(){return e(r)}}).then(function(e){try{return r=e,E(),T().then(function(e){try{return c=e?l:de(l,r),E(),u=I.initialQuality||1,h=I.fileType||C.type,R(c,h,C.name,C.lastModified,u).then(function(e){try{return(d=e,E(),g=d.size>f,A=d.size>C.size,g||A)?(w=C.size,p=d.size,v=p,U=c,F=!I.alwaysKeepResolution&&g,(t=function(e){for(;e;){if(e.then)return void e.then(t,o);try{if(e.pop){if(e.length)return e.pop()?n.call(this):e;e=r}else e=e.call(this)}catch(e){return o(e)}}}.bind(this))(r)):(_(100),i(d));var t;function r(){var e,t;return a--&&(v>f||v>w)?(e=F?.95*U.width:U.width,t=F?.95*U.height:U.height,[b,y]=B(e,t),y.drawImage(U,0,0,e,t),u*="image/png"===h?.85:.95,R(b,h,C.name,C.lastModified,u).then(function(e){try{return m=e,x(U),U=b,v=m.size,_(Math.min(99,Math.floor((p-v)/(p-f)*100))),r}catch(e){return o(e)}},o)):[1]}function n(){return x(U),x(b),x(l),x(c),x(s),_(100),i(m)}}catch(e){return o(e)}}.bind(this),o)}catch(e){return o(e)}}.bind(this),o)}catch(e){return o(e)}}.bind(this),o)}catch(e){return o(e)}}.bind(this),o)})}const Ae="\nlet scriptImported = false\nself.addEventListener('message', async (e) => {\n  const { file, id, imageCompressionLibUrl, options } = e.data\n  options.onProgress = (progress) => self.postMessage({ progress, id })\n  try {\n    if (!scriptImported) {\n      // console.log('[worker] importScripts', imageCompressionLibUrl)\n      self.importScripts(imageCompressionLibUrl)\n      scriptImported = true\n    }\n    // console.log('[worker] self', self)\n    const compressedFile = await imageCompression(file, options)\n    self.postMessage({ file: compressedFile, id })\n  } catch (e) {\n    // console.error('[worker] error', e)\n    self.postMessage({ error: e.message + '\\n' + e.stack, id })\n  }\n})\n";let we;function v(d,g){return new Promise(function(e,t){let r,n,i,o,a,f;if(r={...g},i=0,{onProgress:o}=r,r.maxSizeMB=r.maxSizeMB||Number.POSITIVE_INFINITY,a="boolean"!=typeof r.useWebWorker||r.useWebWorker,delete r.useWebWorker,r.onProgress=e=>{i=e,"function"==typeof o&&o(i)},!(d instanceof Blob||d instanceof ie))return t(new Error("The file given is not an instance of Blob or File"));if(!/^image/.test(d.type))return t(new Error("The file given is not an image"));if(f="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,!a||"function"!=typeof Worker||f)return ge(d,r).then(function(e){try{return n=e,h.call(this)}catch(e){return t(e)}}.bind(this),t);function s(e){try{return ge(d,r).then(function(e){try{return n=e,u()}catch(e){return t(e)}},t)}catch(e){return t(e)}}var l,c,u=function(){try{return h.call(this)}catch(e){return t(e)}}.bind(this);try{return r.libURL=r.libURL||"https://cdn.jsdelivr.net/npm/browser-image-compression@2.0.2/dist/browser-image-compression.js",l=d,c=r,new Promise((t,r)=>{we=we||function(e){const t=[];return"function"==typeof e?t.push(`(${e})()`):t.push(e),URL.createObjectURL(new Blob(t))}(Ae);const n=new Worker(we);n.addEventListener("message",function(e){if(c.signal&&c.signal.aborted)n.terminate();else if(void 0===e.data.progress){if(e.data.error)return r(new Error(e.data.error)),void n.terminate();t(e.data.file),n.terminate()}else c.onProgress(e.data.progress)}),n.addEventListener("error",r),c.signal&&c.signal.addEventListener("abort",()=>{r(c.signal.reason),n.terminate()}),n.postMessage({file:l,imageCompressionLibUrl:c.libURL,options:{...c,onProgress:void 0,signal:void 0}})}).then(function(e){try{return n=e,u()}catch(e){return s()}},s)}catch(e){s()}function h(){try{n.name=d.name,n.lastModified=d.lastModified}catch(e){}try{r.preserveExif&&"image/jpeg"===d.type&&(!r.fileType||r.fileType&&r.fileType===d.type)&&(n=k(d,n))}catch(e){}return e(n)}})}return v.getDataUrlFromFile=fe,v.getFilefromDataUrl=ae,v.loadImage=se,v.drawImageInCanvas=ce,v.drawFileInCanvas=M,v.canvasToFile=R,v.getExifOrientation=ue,v.handleMaxWidthOrHeight=he,v.followExifOrientation=de,v.cleanupCanvasMemory=x,v.isAutoOrientationInBrowser=T,v.approximateBelowMaximumCanvasSizeOfBrowser=le,v.copyExifWithoutOrientation=k,v.getBrowserName=c,v.version="2.0.2",v});