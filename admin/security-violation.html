<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全警告</title>
    <link href="libs/css/bootstrap.min.css" rel="stylesheet">
    <link href="libs/css/bootstrap-icons.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #121212;
            color: #fff;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
            text-align: center;
        }
        .security-icon {
            font-size: 80px;
            color: #dc3545;
            margin-bottom: 20px;
        }
        .warning-title {
            font-size: 32px;
            margin-bottom: 20px;
            color: #dc3545;
        }
        .warning-message {
            font-size: 18px;
            max-width: 600px;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .return-button {
            background-color: #0d6efd;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .return-button:hover {
            background-color: #0b5ed7;
        }
    </style>
</head>
<body>
    <i class="bi bi-shield-exclamation security-icon"></i>
    <h1 class="warning-title">安全违规警告</h1>
    <p class="warning-message">
        系统检测到异常操作行为。为保护您的账户和数据安全，系统已临时阻止访问。
        此页面已记录相关信息，频繁触发安全机制可能导致账户被限制。
    </p>
    <button onclick="goBack()" class="return-button">返回首页</button>

    <script>
        // 清除可能的敏感数据
        function clearData() {
            try {
                localStorage.removeItem('authToken');
                sessionStorage.clear();
                
                // 清除所有cookies
                document.cookie.split(";").forEach(function(c) {
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
                });
                
            } catch (e) {
                console.error('数据清理失败');
            }
        }
        
        // 执行清理
        clearData();
        
        // 返回首页
        function goBack() {
            window.location.href = 'index.html';
        }
        
        // 记录访问信息
        const image = new Image();
        image.src = `/api/security-log?location=${encodeURIComponent(document.referrer)}&time=${Date.now()}&type=violation`;
    </script>
</body>
</html> 