<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱验证 - 提示词案例管理系统</title>
    <link href="libs/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="libs/css/bootstrap-icons.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background-color: #212529; /* Dark background */
        }
        .verification-container {
            max-width: 500px;
            width: 100%;
            padding: 2rem;
            background-color: #343a40; /* Slightly lighter card background */
            border-radius: 0.5rem;
            text-align: center;
            color: #f8f9fa; /* Light text */
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
            margin-bottom: 1rem;
        }
        .icon-status {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        .icon-success { color: var(--bs-success); }
        .icon-danger { color: var(--bs-danger); }
    </style>
</head>
<body>
    <div class="verification-container">
        <div id="loadingState">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">正在验证您的邮箱地址...</p>
        </div>

        <div id="successState" style="display: none;">
            <i class="bi bi-check-circle-fill icon-status icon-success"></i>
            <h4>邮箱验证成功！</h4>
            <p>您的邮箱地址已成功更新。</p>
            <a href="login.html" class="btn btn-primary mt-3">返回登录</a>
        </div>

        <div id="errorState" style="display: none;">
            <i class="bi bi-x-octagon-fill icon-status icon-danger"></i>
            <h4>邮箱验证失败</h4>
            <p id="errorMessage">无法完成邮箱验证。令牌可能无效或已过期。</p>
            <a href="login.html" class="btn btn-secondary mt-3">返回登录</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const loadingState = document.getElementById('loadingState');
            const successState = document.getElementById('successState');
            const errorState = document.getElementById('errorState');
            const errorMessageElement = document.getElementById('errorMessage');

            // 1. 获取 URL 中的 token
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');

            if (!token) {
                loadingState.style.display = 'none';
                errorMessageElement.textContent = '无效的验证链接，缺少令牌。';
                errorState.style.display = 'block';
                return;
            }

            // 2. 向后端发送验证请求
            // 使用你配置的后端代理地址
            const API_BASE_URL = 'https://caca.yzycolour.top/api'; 
            // 或者从 .env 读取？但前端 JS 无法直接读取 .env，需要硬编码或通过其他方式注入
            // const API_BASE_URL = 'http://localhost:9527/api'; // 用于本地调试

            try {
                const response = await fetch(`${API_BASE_URL}/auth/verify-email-change?token=${token}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                loadingState.style.display = 'none';

                if (response.ok) {
                    // 验证成功
                    successState.style.display = 'block';
                } else {
                    // 验证失败
                    errorMessageElement.textContent = data.message || data.error || '验证失败，令牌可能无效或已过期。';
                    errorState.style.display = 'block';
                }

            } catch (error) {
                console.error('验证请求失败:', error);
                loadingState.style.display = 'none';
                errorMessageElement.textContent = '验证过程中发生网络错误，请稍后重试。';
                errorState.style.display = 'block';
            }
        });
    </script>
</body>
</html> 