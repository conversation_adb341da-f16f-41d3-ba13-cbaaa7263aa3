window.imageUtils = (function() {
    // 这些函数会依赖 main.js 中定义的全局变量 (如 window.ZHIPU_API_URL, window.selectedFile)
    // 和通过 document.getElementById 获取的 DOM 元素。
    // 这是一个过渡阶段，未来可以进一步优化依赖管理。

    /**
     * 将文件读取为Base64
     */
    function readFileAsBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const base64String = reader.result;
                resolve(base64String); // 直接返回 base64 字符串
            };
            reader.onerror = (error) => {
                reject(error);
            };
            reader.readAsDataURL(file);
        });
    }

    /**
     * 调用智普AI分析图片并生成标题和标签
     */
    async function analyzeImageWithAI(imageFile) {
        const titleInput = document.getElementById('title');
        const imagePreview = document.getElementById('imagePreview');
        //  确保 tagifyInstance 是全局可访问的，例如 window.tagifyInstance
        const tagify = window.tagifyInstance; 

        if (!window.ZHIPU_API_URL || !window.ZHIPU_API_KEY) {
            console.error('智普AI的URL或API Key未配置在window对象上。');
            alert('AI分析服务配置不完整，无法分析图片。');
            return { title: '', tags: [] };
        }
        if (typeof window.imageCompression !== 'function') {
            console.warn('imageCompression库未在window对象上找到，AI分析可能受影响（如果需要预处理）。');
        }


        try {
            const loadingMessage = document.createElement('div');
            loadingMessage.className = 'ai-loading position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-light bg-opacity-75';
            loadingMessage.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status"></div>
                    <p class="mt-2 mb-0">正在使用AI分析图片...</p>
                </div>
            `;
            
            if (imagePreview) {
                imagePreview.style.position = 'relative';
                imagePreview.appendChild(loadingMessage);
            } else {
                console.warn('imagePreview element not found for AI loading indicator.');
            }
            
            const base64Image = await readFileAsBase64(imageFile);
            
            const requestData = {
                model: "glm-4v-flash",
                messages: [
                    {
                        role: "user",
                        content: [
                            {
                                type: "text",
                                text: `这是一张图片。请仔细分析图片内容，然后：\n1.  **生成一个简洁但描述性的标题** (不超过 30 字)，要能体现图片的核心内容和风格。\n2.  **提供 3-5 个相关的标签**。这些标签应该涵盖：\n    *   **主要物体/主题** (例如：游戏手柄, 麦田, 人物)\n    *   **图片类型/媒介** (例如：照片, 3D渲染, 插画, 海报, 水彩画)\n    *   **艺术风格/效果** (例如：写实, 卡通, 抽象, 毛玻璃效果, 低多边形)\n    *   **相关概念/关键词** (例如：科技, 农业, 节日, 游戏)\n3.  **请特别注意区分** 这是真实照片还是艺术创作 (如 3D 渲染、插画、海报等)，并在标签或标题中体现出来。\n4.  **请以 JSON 格式回复**，包含两个字段：\`title\` (字符串) 和 \`tags\` (字符串数组)。\n\n例如，如果图片是蓝色毛玻璃手柄，你可能回复：\n{\n  \"title\": \"蓝色毛玻璃质感3D游戏手柄渲染图\",\n  \"tags\": [\"游戏手柄\", \"3D渲染\", \"毛玻璃效果\", \"科技\", \"蓝色\"]\n}\n如果图片是五一海报，你可能回复：\n{\n  \"title\": \"向劳动者致敬五一劳动节海报\",\n  \"tags\": [\"海报\", \"五一劳动节\", \"农业\", \"丰收\", \"插画\"]\n}`
                            },
                            {
                                type: "image_url",
                                image_url: {
                                    url: base64Image
                                }
                            }
                        ]
                    }
                ],
                temperature: 0.7,
                stream: false
            };
            
            const response = await fetch(window.ZHIPU_API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${window.ZHIPU_API_KEY}`
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`智普AI请求失败: ${response.status} - ${errorText}`);
            }
            
            const result = await response.json();
            const aiContent = result.choices[0].message.content;
            
            let aiData = {};
            try {
                const jsonMatch = aiContent.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    aiData = JSON.parse(jsonMatch[0]);
                } else {
                    const titleMatch = aiContent.match(/title[":]\s*["']?([^"'\n,]+)["']?/i);
                    const tagsMatch = aiContent.match(/tags[":]\s*\[([\s\S]*?)\]/i);
                    
                    if (titleMatch) aiData.title = titleMatch[1].trim();
                    if (tagsMatch) {
                        const tagsString = tagsMatch[1];
                        aiData.tags = tagsString.split(',')
                            .map(tag => tag.replace(/["']/g, '').trim())
                            .filter(tag => tag);
                    }
                }
            } catch (parseError) {
                console.error('解析AI响应失败:', parseError, '原始响应:', aiContent);
                 return { title: '', tags: [] };
            }
            
            if (imagePreview) {
                const loadingElement = imagePreview.querySelector('.ai-loading');
                if (loadingElement) imagePreview.removeChild(loadingElement);
            }
            
            if (titleInput && aiData.title) {
                titleInput.value = aiData.title;
            }
            if (tagify && aiData.tags && aiData.tags.length > 0) {
                tagify.removeAllTags();
                tagify.addTags(aiData.tags);
            } else if (!tagify) {
                console.warn('Tagify 实例尚未初始化，无法添加 AI 生成的标签。');
            }
            
            return { 
                title: aiData.title || '', 
                tags: aiData.tags || [] 
            };

        } catch (error) {
            console.error('AI分析图片错误:', error);
            if (imagePreview) {
                const loadingElement = imagePreview.querySelector('.ai-loading');
                if (loadingElement) {
                    try {
                        imagePreview.removeChild(loadingElement);
                    } catch(removeError) {
                        console.error('Error removing loading element in catch block:', removeError);
                    }
                }
            }
            alert(`AI分析图片失败: ${error.message}`);
            return { title: '', tags: [] }; 
        }
    }

    /**
     * 参数化后的显示图片预览函数
     */
    function displayImagePreviewFunc(selectedFileVar, targetImgElement, targetPreviewContainer, targetDropZoneElement) {
        if (!selectedFileVar || !targetImgElement || !targetPreviewContainer) {
            if (targetPreviewContainer) targetPreviewContainer.style.display = 'none';
            if (targetImgElement) targetImgElement.src = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            targetImgElement.src = e.target.result;
            targetPreviewContainer.style.display = 'block';
            if (targetDropZoneElement) targetDropZoneElement.style.display = 'none';
        };
        reader.onerror = function(e) {
            console.error("文件读取错误:", e);
            alert('读取图片预览失败');
            targetImgElement.src = '';
            targetPreviewContainer.style.display = 'none';
            if (targetDropZoneElement) targetDropZoneElement.style.display = 'block';
        };
        reader.readAsDataURL(selectedFileVar);
    }

    /**
     * 参数化后的移除图片函数
     * 注意：此函数直接修改了 main.js 中定义的全局变量 (如 window.selectedFile)
     */
    function removeImageFunc(
        fileVarName, // 'selectedFile', 'selectedSourceFile', 'selectedReferenceFile'
        targetImageInputElement,
        targetImgElement,
        targetPreviewContainer,
        targetDropZoneElement
    ) {
        if (fileVarName === 'selectedFile') window.selectedFile = null;
        else if (fileVarName === 'selectedSourceFile') window.selectedSourceFile = null;
        else if (fileVarName === 'selectedReferenceFile') window.selectedReferenceFile = null;

        if (targetImageInputElement) targetImageInputElement.value = '';
        if (targetImgElement) targetImgElement.src = '';
        if (targetPreviewContainer) targetPreviewContainer.style.display = 'none';
        if (targetDropZoneElement) targetDropZoneElement.style.display = 'block';
    }

    /**
     * 新的参数化文件选择处理函数
     * 注意：此函数直接修改了 main.js 中定义的全局变量 (如 window.selectedFile)
     * 并调用了本模块内的 analyzeImageWithAI
     */
    async function handleFileSelect(file, imageType) {
        // DOM元素获取 - 依赖全局ID
        const imageInput = document.getElementById('image');
        const sourceImageInput = document.getElementById('source_image');
        const referenceImageInput = document.getElementById('reference_image');
        const titleInput = document.getElementById('title');

        const previewImg = document.getElementById('previewImg');
        const imagePreview = document.getElementById('imagePreview');
        const imageDropZone = document.getElementById('imageDropZone');

        const previewSourceImg = document.getElementById('previewSourceImg');
        const sourceImagePreview = document.getElementById('sourceImagePreview');
        const sourceImageDropZone = document.getElementById('sourceImageDropZone');

        const previewReferenceImg = document.getElementById('previewReferenceImg');
        const referenceImagePreview = document.getElementById('referenceImagePreview');
        const referenceImageDropZone = document.getElementById('referenceImageDropZone');


        if (!file || !file.type.match('image.*')) {
            alert('请选择有效的图片文件 (例如 JPG, PNG, GIF)');
            if (imageType === 'main' && imageInput) imageInput.value = '';
            else if (imageType === 'source' && sourceImageInput) sourceImageInput.value = '';
            else if (imageType === 'reference' && referenceImageInput) referenceImageInput.value = '';
            return;
        }

        const maxSizeMB = (imageType === 'main') ? 5 : 2; 
        if (file.size > maxSizeMB * 1024 * 1024) {
            alert(`图片大小不能超过 ${maxSizeMB}MB`);
            if (imageType === 'main' && imageInput) imageInput.value = '';
            else if (imageType === 'source' && sourceImageInput) sourceImageInput.value = '';
            else if (imageType === 'reference' && referenceImageInput) referenceImageInput.value = '';
            return;
        }

        let currentPreviewImg, currentPreviewContainer, currentDropZone;

        if (imageType === 'main') {
            window.selectedFile = file; // 修改全局变量
            currentPreviewImg = previewImg;
            currentPreviewContainer = imagePreview;
            currentDropZone = imageDropZone;
        } else if (imageType === 'source') {
            window.selectedSourceFile = file; // 修改全局变量
            currentPreviewImg = previewSourceImg;
            currentPreviewContainer = sourceImagePreview;
            currentDropZone = sourceImageDropZone;
        } else if (imageType === 'reference') {
            window.selectedReferenceFile = file; // 修改全局变量
            currentPreviewImg = previewReferenceImg;
            currentPreviewContainer = referenceImagePreview;
            currentDropZone = referenceImageDropZone;
        } else {
            console.error('Unknown image type in handleFileSelect:', imageType);
            return;
        }
        
        displayImagePreviewFunc(
           (imageType === 'main') ? window.selectedFile : (imageType === 'source') ? window.selectedSourceFile : window.selectedReferenceFile, 
           currentPreviewImg, 
           currentPreviewContainer, 
           currentDropZone
        );

        if (imageType === 'main' && titleInput && !titleInput.value.trim()) {
            // 调用本模块的 analyzeImageWithAI
            if (typeof analyzeImageWithAI === 'function') {
                try {
                    await analyzeImageWithAI(file); 
                } catch (error) {
                    console.error(`Error during AI analysis for main image:`, error);
                }
            } else {
                console.warn('analyzeImageWithAI function is not available in imageUtils.');
            }
        }
    }

    /**
     * 设置拖拽区域事件
     */
    function setupDropZoneEvents(dropZoneElement, inputElement, imageType) {
        if (dropZoneElement && inputElement) {
            dropZoneElement.addEventListener('click', () => inputElement.click());
            dropZoneElement.addEventListener('dragover', (event) => {
                event.preventDefault();
                dropZoneElement.classList.add('drag-over');
            });
            dropZoneElement.addEventListener('dragleave', () => {
                dropZoneElement.classList.remove('drag-over');
            });
            dropZoneElement.addEventListener('drop', (event) => {
                event.preventDefault();
                dropZoneElement.classList.remove('drag-over');
                if (event.dataTransfer.files && event.dataTransfer.files[0]) {
                    // 调用本模块的 handleFileSelect
                    handleFileSelect(event.dataTransfer.files[0], imageType);
                }
            });
        }
    }

    /**
     * 设置图片粘贴监听器
     */
    function setupImagePasteListener(addExampleModalElement) {
        // DOM元素获取 - 依赖全局ID
        const modalModelFilterValue = document.getElementById('modalModelFilterValue');
        const sourceImageSection = document.getElementById('sourceImageSection');
        const referenceImageSection = document.getElementById('referenceImageSection');
        // 新增FLUX相关的DOM元素
        const fluxSecondImageSection = document.getElementById('fluxSecondImageSection');
        const fluxResultImageSection = document.getElementById('fluxResultImageSection');
        
        // 跟踪当前鼠标悬浮的元素
        let currentHoverElement = null;
        
        // 添加鼠标悬浮事件监听
        function setupHoverTracking(element, type) {
            if (!element) return;
            
            element.addEventListener('mouseenter', () => {
                currentHoverElement = { element, type };
            });
            
            element.addEventListener('mouseleave', () => {
                if (currentHoverElement && currentHoverElement.element === element) {
                    currentHoverElement = null;
                }
            });
        }
        
        // 为各个上传区域添加悬浮跟踪
        const imageDropZone = document.getElementById('imageDropZone');
        const sourceImageDropZone = document.getElementById('sourceImageDropZone');
        const referenceImageDropZone = document.getElementById('referenceImageDropZone');
        const fluxSecondImageDropZone = document.getElementById('fluxSecondImageDropZone');
        const fluxResultImageDropZone = document.getElementById('fluxResultImageDropZone');
        
        // 为上传区域和它们的预览区域添加鼠标跟踪
        setupHoverTracking(imageDropZone, 'main');
        setupHoverTracking(document.getElementById('imagePreview'), 'main');
        
        setupHoverTracking(sourceImageDropZone, 'source');
        setupHoverTracking(document.getElementById('sourceImagePreview'), 'source');
        
        setupHoverTracking(referenceImageDropZone, 'reference');
        setupHoverTracking(document.getElementById('referenceImagePreview'), 'reference');
        
        setupHoverTracking(fluxSecondImageDropZone, 'flux_second');
        setupHoverTracking(document.getElementById('fluxSecondImagePreview'), 'flux_second');
        
        setupHoverTracking(fluxResultImageDropZone, 'flux_result');
        setupHoverTracking(document.getElementById('fluxResultImagePreview'), 'flux_result');

        // 处理粘贴事件的函数
        function handlePaste(event) {
            // 如果添加示例模态框不可见，不处理粘贴
            if (!addExampleModalElement || addExampleModalElement.style.display === 'none' || 
                !addExampleModalElement.classList.contains('show')) {
                return;
            }
        
            const items = (event.clipboardData || event.originalEvent.clipboardData).items;
            for (let index in items) {
                const item = items[index];
                if (item.kind === 'file' && item.type.startsWith('image/')) {
                    const blob = item.getAsFile();
                    if (blob) {
                        const timestamp = Date.now();
                        const file = new File([blob], `pasted-image-${timestamp}.${blob.type.split('/')[1]}`, { type: blob.type });
                        
                        let pasteTargetType = 'main'; 
                        
                        // 首先检查是否有鼠标悬浮的上传区域
                        if (currentHoverElement) {
                            const hoverType = currentHoverElement.type;
                            const isValidTarget = (type) => {
                                // 检查对应的上传区域是否可见且该位置是否为空
                                switch(type) {
                                    case 'main':
                                        return !window.selectedFile;
                                    case 'source':
                                        return sourceImageSection && 
                                              sourceImageSection.style.display !== 'none' && 
                                              !window.selectedSourceFile;
                                    case 'reference':
                                        return referenceImageSection && 
                                              referenceImageSection.style.display !== 'none' && 
                                              !window.selectedReferenceFile;
                                    case 'flux_second':
                                        return fluxSecondImageSection && 
                                              fluxSecondImageSection.style.display !== 'none' && 
                                              !window.selectedFluxSecondFile;
                                    case 'flux_result':
                                        return fluxResultImageSection && 
                                              fluxResultImageSection.style.display !== 'none' && 
                                              !window.selectedFluxResultFile;
                                    default:
                                        return false;
                                }
                            };
                            
                            if (isValidTarget(hoverType)) {
                                pasteTargetType = hoverType;
                            } else {
                            }
                        }
                        
                        // 如果没有有效的鼠标悬浮目标，使用默认逻辑
                        if (pasteTargetType === 'main' && !currentHoverElement) {
                            // 检查当前模式
                            if (modalModelFilterValue && modalModelFilterValue.value === 'GPT4O-编辑') {
                                if (sourceImageSection && sourceImageSection.style.display !== 'none' && !window.selectedSourceFile) {
                                    pasteTargetType = 'source';
                                } else if (referenceImageSection && referenceImageSection.style.display !== 'none' && !window.selectedReferenceFile && window.selectedSourceFile) {
                                    pasteTargetType = 'reference';
                                } else if (!window.selectedFile) {
                                    pasteTargetType = 'main';
                                }
                            } 
                            // FLUX-多图模式的粘贴处理
                            else if (modalModelFilterValue && modalModelFilterValue.value === 'FLUX-多图') {
                                if (fluxSecondImageSection && fluxSecondImageSection.style.display !== 'none' && !window.selectedFluxSecondFile) {
                                    pasteTargetType = 'flux_second';
                                } else if (fluxResultImageSection && fluxResultImageSection.style.display !== 'none' && !window.selectedFluxResultFile && window.selectedFluxSecondFile) {
                                    pasteTargetType = 'flux_result';
                                } else if (!window.selectedFile) {
                                    pasteTargetType = 'main';
                                }
                            }
                            else if (window.selectedFile) {
                            }
                            
                        }
                        
                        // 处理FLUX相关粘贴上传
                        if (pasteTargetType === 'flux_second' && window.exampleFormHandler && typeof window.exampleFormHandler.handleFluxSecondImageSelect === 'function') {
                            window.exampleFormHandler.handleFluxSecondImageSelect(file);
                        } 
                        else if (pasteTargetType === 'flux_result' && window.exampleFormHandler && typeof window.exampleFormHandler.handleFluxResultImageSelect === 'function') {
                            window.exampleFormHandler.handleFluxResultImageSelect(file);
                        }
                        // 常规图片处理方式
                        else {
                            // 调用本模块的 handleFileSelect
                            handleFileSelect(file, pasteTargetType);
                        }
                        
                        // 添加视觉反馈，突出显示粘贴目标
                        const highlightPasteTarget = (type) => {
                            let targetElement;
                            switch(type) {
                                case 'main':
                                    targetElement = document.getElementById('imageDropZone') || document.getElementById('imagePreview');
                                    break;
                                case 'source':
                                    targetElement = document.getElementById('sourceImageDropZone') || document.getElementById('sourceImagePreview');
                                    break;
                                case 'reference':
                                    targetElement = document.getElementById('referenceImageDropZone') || document.getElementById('referenceImagePreview');
                                    break;
                                case 'flux_second':
                                    targetElement = document.getElementById('fluxSecondImageDropZone') || document.getElementById('fluxSecondImagePreview');
                                    break;
                                case 'flux_result':
                                    targetElement = document.getElementById('fluxResultImageDropZone') || document.getElementById('fluxResultImagePreview');
                                    break;
                            }
                            
                            if (targetElement) {
                                targetElement.classList.add('paste-highlight');
                                setTimeout(() => {
                                    targetElement.classList.remove('paste-highlight');
                                }, 500);
                            }
                        };
                        
                        highlightPasteTarget(pasteTargetType);
                        
                        event.preventDefault(); 
                        break; 
                    }
                }
            }
        }
        
        // 在整个文档上添加粘贴事件监听，这样用户不需要先点击上传框
        document.addEventListener('paste', handlePaste);
        
        // 如果模态框存在，也在模态框上添加粘贴监听（以确保兼容性）
        if (addExampleModalElement) {
            // 使用相同的处理函数，事件只会被处理一次，因为我们在检测到第一个图片时会调用 event.preventDefault()
        } else {
            console.warn('addExampleModalElement not found for paste listener setup.');
        }
    }

    /**
     * 新增：使用智谱AI在前端翻译文本 (中文到英文)
     */
    async function translateTextZhipuFrontend(textToTranslate) {
        if (!window.ZHIPU_API_URL || !window.ZHIPU_API_KEY) {
            console.error('智普AI的URL或API Key未配置在window对象上。');
            alert('AI翻译服务配置不完整，无法翻译提示词。');
            return textToTranslate; // 返回原始文本或抛出错误
        }

        // 优先使用 window.ZHIPU_TEXT_MODEL (如果配置了专用的文本模型)
        // 否则回退到 window.ZHIPU_API_MODEL (可能是多模态模型，但也应能处理文本)
        // 如果都没有，则使用一个默认的文本模型如 glm-3-turbo 或 glm-4 (需确保API Key支持)
        const modelToUse = window.ZHIPU_TEXT_MODEL || window.ZHIPU_API_MODEL || "glm-4-flash"; 
        // 注意: 如果API Key是针对特定模型的 (如glm-4v)，直接使用glm-4可能无效，需确认Key的适用范围。
        // 如果 ZHIPU_API_MODEL 是 glm-4v-flash，它也能处理文本，但可能不是最优选。


        try {
            const requestData = {
                model: modelToUse,
                messages: [
                    {
                        role: "user",
                        content: `Translate the following Chinese text to English. Return ONLY the translated English text, without any other explanations, preambles, or markdown formatting like \`\`\`json ... \`\`\` or \`\`\` ... \`\`\`.\n\nChinese text: "${textToTranslate}"`
                    }
                ],
                temperature: 0.1, // 对于翻译，通常使用较低的温度以获得更确定的输出
                stream: false
            };

            const response = await fetch(window.ZHIPU_API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${window.ZHIPU_API_KEY}`
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`智普AI翻译请求失败: ${response.status} - ${errorText}`);
            }

            const result = await response.json();
            if (result.choices && result.choices[0] && result.choices[0].message && result.choices[0].message.content) {
                const translatedText = result.choices[0].message.content.trim();
                return translatedText;
            } else {
                console.error('[Translate Frontend] 智普AI翻译响应格式不正确:', result);
                throw new Error('智普AI翻译响应格式不正确。');
            }

        } catch (error) {
            console.error('[Translate Frontend] AI翻译文本错误:', error);
            alert(`AI翻译提示词失败: ${error.message}`);
            return textToTranslate; // 发生错误时返回原始文本
        }
    }

    // 暴露公共方法
    return {
        readFileAsBase64,
        analyzeImageWithAI,
        translateTextZhipuFrontend,
        displayImagePreviewFunc,
        removeImageFunc,
        handleFileSelect,
        setupDropZoneEvents,
        setupImagePasteListener
    };
})();
