/**
 * debug-protection.js
 * 防止网站被调试、抓包或篡改的安全保护脚本
 * 检测调试器暂停并采取相应安全措施
 */

// 自执行函数以避免污染全局作用域
(function() {
  // 防止变量泄露到全局作用域
  const debugProtection = {
    // 配置参数
    config: {
      checkInterval: 500,          // 恢复到原来的检查间隔
      timeThreshold: 1000,         // 恢复到原来的阈值
      debuggerTraps: true,         // 是否使用debugger陷阱
      consoleClearing: true,       // 是否清理控制台
      redirectUrl: "security-violation.html", // 使用相对当前目录的路径
      message: "系统检测到异常操作，为保护您的账户安全，页面将重新加载。" // 警告信息
    },
    
    // 状态变量
    state: {
      lastCheckTime: Date.now(),
      isPaused: false,
      checkCount: 0,
      initialized: false,
      devToolsOpen: false
    },
    
    // 初始化检测系统
    init: function() {
      if (this.state.initialized) return;
      
      console.log("%c安全模块已加载", "color:green;font-weight:bold;");
      
      // 设置定期检查
      this.startChecking();
      
      // 添加控制台清理和debugger陷阱
      if (this.config.debuggerTraps) {
        this.setDebuggerTraps();
      }
      
      // 添加页面可见性变化监听
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
          this.state.lastCheckTime = Date.now();
        }
      });
      
      // 添加开发者工具状态检测
      this.detectDevTools();
      
      this.state.initialized = true;
    },
    
    // 开始定期检查
    startChecking: function() {
      setInterval(() => {
        this.checkTimeDifference();
      }, this.config.checkInterval);
    },
    
    // 检查执行时间差异
    checkTimeDifference: function() {
      const currentTime = Date.now();
      const elapsedTime = currentTime - this.state.lastCheckTime;
      
      // 更新最后检查时间
      this.state.lastCheckTime = currentTime;
      
      // 如果时间差异超过阈值，可能是在调试器中暂停了
      if (elapsedTime > this.config.timeThreshold) {
        this.handlePausedDetection(`暂停时间: ${elapsedTime}ms`);
      }
      
      // 定期执行调试器陷阱
      if (this.config.debuggerTraps && this.state.checkCount % 5 === 0) {
        this.executeDebuggerTrap();
      }
      
      // 定期清理控制台
      if (this.config.consoleClearing && this.state.checkCount % 10 === 0) {
        this.clearConsole();
      }
      
      this.state.checkCount++;
    },
    
    // 设置debugger陷阱
    setDebuggerTraps: function() {
      // 在函数中随机位置插入debugger陷阱
      this.wrapCriticalFunctions();
      
      // 在Event Loop中定期插入陷阱
      setTimeout(function insertDelayedTrap() {
        Function('debugger')();
        setTimeout(insertDelayedTrap, Math.random() * 5000 + 1000);
      }, 2000);
    },
    
    // 执行一个debugger陷阱并测量时间
    executeDebuggerTrap: function() {
      const start = performance.now();
      debugger;
      const end = performance.now();
      
      // 如果在debugger处停留时间过长
      if (end - start > 150) { // 提高阈值到150ms
        this.handlePausedDetection(`Debugger陷阱触发: ${end - start}ms`);
      }
    },
    
    // 包装关键函数以添加debugger检测
    wrapCriticalFunctions: function() {
      // 示例：包装XMLHttpRequest以防止API调用被检查
      const originalOpen = XMLHttpRequest.prototype.open;
      const self = this;
      
      XMLHttpRequest.prototype.open = function() {
        if (self.state.isPaused) {
          throw new Error("安全限制");
        }
        
        const start = performance.now();
        debugger;
        const end = performance.now();
        
        if (end - start > 150) { // 提高阈值到150ms
          self.handlePausedDetection("XHR调试触发");
          throw new Error("安全限制");
        }
        
        return originalOpen.apply(this, arguments);
      };
      
      // 包装fetch API
      const originalFetch = window.fetch;
      window.fetch = function() {
        if (self.state.isPaused) {
          return Promise.reject(new Error("安全限制"));
        }
        
        const start = performance.now();
        debugger;
        const end = performance.now();
        
        if (end - start > 150) { // 提高阈值到150ms
          self.handlePausedDetection("Fetch调试触发");
          return Promise.reject(new Error("安全限制"));
        }
        
        return originalFetch.apply(this, arguments);
      };
    },
    
    // 检测开发者工具状态
    detectDevTools: function() {
      // 方法1: 检查窗口尺寸差异
      window.addEventListener('resize', () => {
        const widthDiff = window.outerWidth - window.innerWidth > 200;
        const heightDiff = window.outerHeight - window.innerHeight > 200;
        
        if (widthDiff || heightDiff) {
          this.state.devToolsOpen = true;
          this.handlePausedDetection("DevTools窗口检测");
        }
      });
      
      // 方法2: 使用console.log的toString检测
      const originalLog = console.log;
      console.log = function() {
        const stack = new Error().stack || "";
        if (stack.indexOf("devtools") !== -1) {
          debugProtection.handlePausedDetection("Console DevTools检测");
        }
        return originalLog.apply(this, arguments);
      };
      
      // 方法3: 尝试检测断点
      setInterval(() => {
        const div = document.createElement('div');
        Object.defineProperty(div, 'id', {
          get: function() {
            const start = performance.now();
            debugger;
            const end = performance.now();
            if (end - start > 100) {
              debugProtection.handlePausedDetection("属性检查陷阱");
            }
            return '';
          }
        });
        console.log(div);
      }, 3000);
      
      // 方法4: 检测调试过程中的性能指标变化
      let lastStamp = Date.now();
      let minDiff = Number.MAX_SAFE_INTEGER;
      
      function checkPerformance() {
        const currentStamp = Date.now();
        const diff = currentStamp - lastStamp;
        
        // 更新最小执行时间差异，但设置一个最小值防止非常小的差异导致误判
        if (diff < minDiff && diff > 10) {
          minDiff = diff;
        }
        
        // 如果当前执行间隔远大于之前记录的最小执行间隔
        if (minDiff > 0 && diff > minDiff * 10 && diff > 1000) { // 提高倍数和阈值
          debugProtection.handlePausedDetection(`性能指标检测: ${diff}ms vs ${minDiff}ms`);
        }
        
        lastStamp = currentStamp;
        setTimeout(checkPerformance, 300);
      }
      
      checkPerformance();
      
      // 修改函数调试检测，减少误报
      function catchDebugger() {
        try {
          // 一个简单的调试检测函数
          function testForDebugger() {
            const startTime = performance.now();
            debugger;
            const endTime = performance.now();
            return endTime - startTime > 150; // 提高到150ms
          }
          
          // 定期执行检测，确保能捕获到突然启用的调试器
          if (testForDebugger()) {
            debugProtection.handlePausedDetection("快速调试检测");
          }
        } catch (e) {
          // 安静失败，不记录错误
        }
      }
      
      // 调整执行频率
      setTimeout(catchDebugger, 5000); // 延迟5秒后才开始检测
    },
    
    // 清理控制台
    clearConsole: function() {
      if (this.config.consoleClearing) {
        console.clear();
        console.log("%c安全脚本正在运行...", "color:green; font-size:10px;"); // 小字提示，降低干扰
      }
    },
    
    // 处理检测到调试暂停的情况
    handlePausedDetection: function(reason) {
      if (this.state.isPaused) return; // 避免重复处理
      
      this.state.isPaused = true;
      console.warn("[安全警告] 检测到调试操作:", reason);
      
      // 显示闪烁警告
      this.showFlashingWarning();
      
      // 执行安全措施
      this.executeSecurityMeasures();
    },
    
    // 显示闪烁警告
    showFlashingWarning: function() {
      try {
        // 创建一个全屏覆盖层
        const overlay = document.createElement('div');
        overlay.style.position = 'fixed';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.backgroundColor = 'rgba(220, 53, 69, 0.9)'; // 红色
        overlay.style.color = 'white';
        overlay.style.display = 'flex';
        overlay.style.flexDirection = 'column';
        overlay.style.alignItems = 'center';
        overlay.style.justifyContent = 'center';
        overlay.style.zIndex = '999999999';
        overlay.style.fontSize = '20px';
        overlay.style.textAlign = 'center';
        overlay.style.padding = '20px';
        
        overlay.innerHTML = `
          <div style="font-size: 40px; margin-bottom: 20px;">⚠️ 安全警告 ⚠️</div>
          <div style="font-size: 24px; margin-bottom: 20px;">检测到调试操作</div>
          <div>系统已记录此行为，页面将在3秒后重定向</div>
        `;
        
        document.body.appendChild(overlay);
        
        // 闪烁效果
        let visible = true;
        const flashInterval = setInterval(() => {
          if (visible) {
            overlay.style.opacity = '0.3';
          } else {
            overlay.style.opacity = '0.9';
          }
          visible = !visible;
        }, 200);
        
        // 3秒后清除闪烁
        setTimeout(() => {
          clearInterval(flashInterval);
          document.body.removeChild(overlay);
        }, 3000);
      } catch (e) {
        console.error("显示警告失败", e);
      }
    },
    
    // 执行安全措施
    executeSecurityMeasures: function() {
      // 1. 清除敏感数据
      this.clearSensitiveData();
      
      // 2. 禁用关键功能
      this.disableCriticalFunctions();
      
      // 3. 向后台报告异常
      this.reportSecurityIssue(location.href);
      
      // 4. 重定向或显示警告
      setTimeout(() => {
        alert(this.config.message);
        window.location.href = this.config.redirectUrl || window.location.href;
      }, 500);
    },
    
    // 清除敏感数据
    clearSensitiveData: function() {
      try {
        // 清除本地存储数据
        localStorage.removeItem("authToken");
        sessionStorage.removeItem("userData");
        
        // 清除Cookies (可选)
        const cookies = document.cookie.split(";");
        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i];
          const eqPos = cookie.indexOf("=");
          const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;";
        }
      } catch (e) {
        console.error("清除数据失败", e);
      }
    },
    
    // 禁用关键功能
    disableCriticalFunctions: function() {
      // 设置一个全局标志，其他代码可以检查这个标志
      window._securityLockdown = true;
      
      // 覆盖API函数，防止进一步操作
      const blockFunction = function() { 
        throw new Error("安全限制: 页面已被锁定"); 
      };
      
      try {
        // 禁用常见的API
        window.fetch = blockFunction;
        XMLHttpRequest.prototype.send = blockFunction;
        XMLHttpRequest.prototype.open = blockFunction;
        
        // 禁用事件监听
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        EventTarget.prototype.addEventListener = function() {
          if (window._securityLockdown) {
            return false;
          }
          return originalAddEventListener.apply(this, arguments);
        };
      } catch (e) {
        console.error("禁用功能失败", e);
      }
    },
    
    // 向后台报告安全问题（可选）
    reportSecurityIssue: function(location) {
      try {
        const image = new Image();
        image.src = `/api/security-log?location=${encodeURIComponent(location)}&time=${Date.now()}`;
      } catch (e) {
        // 静默失败
      }
    }
  };
  
  // 初始化防调试系统
  debugProtection.init();
  
  // 添加全局安全引用（使用难以猜测的名称）
  const securityKey = '_' + Math.random().toString(36).substring(2, 9);
  window[securityKey] = debugProtection;
  
  // 导出一个公共安全检查函数，供其他模块使用
  window.checkSecurity = function() {
    return !window._securityLockdown;
  };
})(); 