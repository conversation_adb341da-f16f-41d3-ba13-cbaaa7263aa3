// 配置
window.API_URL = 'https://caca.yzycolour.top/api';
window.imageBaseUrl = 'https://www.yzycolour.top/prompt-examples/server/uploads/'; // 确保 example-form-handler.js 可访问

// --- ZHIPU AI 相关配置 (确保 image-utils.js 可以访问) ---
window.ZHIPU_API_URL = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
window.ZHIPU_API_KEY = 'd94b3866a677983fd06d77db1db69ce0.jkPnbuvu1cStZK1l'; // 请替换为你的有效 API Key
window.ZHIPU_TEXT_MODEL = 'glm-4-flash'; // 添加文本模型配置
window.ZHIPU_API_MODEL = 'glm-4v-flash'; // 添加API模型配置

// 用户认证相关
let currentUser = null;

// --- 新增：分页和加载状态相关变量 ---
let currentPage = 1;
let isLoading = false;
let hasMore = true;
const examplesPerPage = 12; // 和后端默认值或期望值保持一致
// --- 分页变量结束 ---

// --- 新增：推广相关 ---
const PROMO_MODAL_SHOWN_KEY = 'pluginPromoModalShown';
let pluginPromoModalElement = null;
let floatingPluginPromoElement = null;
let closeFloatingPromoBtn = null;
// --- 推广相关结束 ---

// --- Category Color Generation --- START ---
const categoryColors = {}; // 存储分类 slug 到 HSL 颜色的映射

// 简单的字符串哈希函数 (用于更稳定的颜色分配)
function simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash |= 0; // Convert to 32bit integer
    }
    return Math.abs(hash);
}

// 获取分类颜色 (基于 HSL 模型)
function getCategoryColor(categorySlug) {
    // 未分类使用灰色
    const UNCLASSIFIED_COLOR = {
        backgroundColor: 'hsla(0, 0%, 35%, 0.8)',
        borderColor: 'hsl(0, 0%, 50%)'
    };
    if (!categorySlug) return UNCLASSIFIED_COLOR;

    if (!categoryColors[categorySlug]) {
        const baseHash = simpleHash(categorySlug);
        let hue;
        const probabilityCheck = baseHash % 10;

        if (probabilityCheck < 3) { // 30% 概率紫色系
            hue = (baseHash % 40) + 260;
        } else { // 70% 概率其他色系
            hue = baseHash % 260;
        }

        const saturation = 65;
        const baseLightness = 45;
        const backgroundAlpha = 0.8;
        const borderLightness = baseLightness + 15;

        const backgroundColor = `hsla(${hue}, ${saturation}%, ${baseLightness}%, ${backgroundAlpha})`;
        const borderColor = `hsl(${hue}, ${saturation}%, ${borderLightness}%)`;

        categoryColors[categorySlug] = { backgroundColor, borderColor };
    }
    return categoryColors[categorySlug];
}
// --- Category Color Generation --- END ---

// 检查登录状态
function checkAuth() {
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    
    if (!token || !userStr) {
        window.location.href = 'login.html';
        return;
    }

    try {
        currentUser = JSON.parse(userStr);
        
        // 检查是否有激活提示消息
        const activationMessage = localStorage.getItem('activation_message');
        if (activationMessage) {
            // 显示激活提醒 (如果页面上有处理激活消息的函数)
            if (typeof showActivationNotice === 'function') {
                showActivationNotice(activationMessage);
            }
            // 使用过一次后清除，避免重复显示
            localStorage.removeItem('activation_message');
        }
        
        updateUIByRole();
    } catch (error) {
        console.error('解析用户信息失败:', error);
        window.location.href = 'login.html';
    }
}

// --- 新增：显示/隐藏加载状态 ---
function showLoading(show) {
    if (show) {
        examplesContainer.innerHTML = `
            <div class="loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>
        `;
    }
}
// --- 结束：显示/隐藏加载状态 ---

// 根据用户角色更新界面
function updateUIByRole() {
    const isAdmin = currentUser.role === 'admin';
    
    // 更新用户信息显示
    if (userAvatar && userDisplayName) {
        
        // 设置显示名称：优先使用昵称，否则使用用户名
        userDisplayName.textContent = currentUser.nickname || currentUser.username;
        
        // 设置头像：如果用户有头像URL，则使用；否则使用默认头像
        const defaultAvatarUrl = 'https://www.yzycolour.top/prompt-examples/admin/images/default-avatar.png';
        
        if (currentUser.avatar_url) {
            userAvatar.src = currentUser.avatar_url;
        } else {
            userAvatar.src = defaultAvatarUrl;
        }
        
        // 添加错误处理，如果头像加载失败则显示默认头像
        userAvatar.onerror = () => {
            console.warn('用户头像加载失败:', currentUser.avatar_url);
            userAvatar.src = defaultAvatarUrl;
            userAvatar.onerror = null; // 防止无限循环
        };
    } else {
        console.warn('未找到用户头像或显示名称元素');
    }
    
    // 管理分类按钮 - 仅管理员可见
    const categoryManageBtn = document.querySelector('[data-bs-target="#categoriesModal"]');
    if (categoryManageBtn) {
        categoryManageBtn.style.display = isAdmin ? 'inline-block' : 'none';
    }
    
    // 添加案例按钮 - 所有登录用户可见
    const addExampleBtn = document.querySelector('[data-bs-target="#addExampleModal"]');
    if (addExampleBtn) {
        addExampleBtn.style.display = 'inline-block'; // 总是显示
    }

    // 隐藏非管理员的编辑和删除按钮 (案例卡片 和 分类管理列表)
    if (!isAdmin) {
       
        const confirmDelExampleBtn = document.getElementById('confirmDelete');
        // if(confirmDelExampleBtn) confirmDelExampleBtn.style.display = 'none'; // 注释掉这行
        
    } else {
       
        const confirmDelExampleBtn = document.getElementById('confirmDelete');
        if(confirmDelExampleBtn) confirmDelExampleBtn.style.display = 'inline-block';
    }
}

// 添加认证头到请求中
function getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

// 登出函数
function logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    window.location.href = 'login.html';
}
window.logoutMain = logout; // 暴露给 example-form-handler.js

let currentExamples = [];
let deleteExampleId = null;

// --- 修改：将这些变量挂载到 window，以便 image-utils.js 访问 ---
window.selectedFile = null; // 用于存储通过拖拽、粘贴或选择的主图片文件
window.selectedSourceFile = null; // 新增：用于存储选择的源图片文件
window.selectedReferenceFile = null; // 新增：用于存储选择的参考图片文件
// --- 修改结束 ---

// DOM元素
const userAvatar = document.getElementById('userAvatar'); // 新增：用户头像元素
const userDisplayName = document.getElementById('userDisplayName'); // 新增：用户显示名称元素
const examplesContainer = document.querySelector('.examples-container');
const categoryFilterBtn = document.getElementById('categoryFilterBtn'); // <-- Add new category button ref
const categoryFilterMenu = document.getElementById('categoryFilterMenu'); // <-- Add new category menu ref
const categoryFilterValue = document.getElementById('categoryFilterValue'); // <-- Add new category hidden input ref
// 新增：获取模型筛选元素
const modelFilterBtn = document.getElementById('modelFilterBtn'); // <-- Get the new button
const modelFilterMenu = document.getElementById('modelFilterMenu'); // <-- Get the new menu
const modelFilterValue = document.getElementById('modelFilterValue'); // <-- Get the hidden input
const searchInput = document.getElementById('searchInput');
// 新增：获取排序筛选元素
const sortFilterBtn = document.getElementById('sortFilterBtn'); // <-- Add new sort button ref
const sortFilterMenu = document.getElementById('sortFilterMenu'); // <-- Add new sort menu ref
const sortFilterValue = document.getElementById('sortFilterValue'); // <-- Add new sort hidden input ref
const exampleForm = document.getElementById('exampleForm');
const exampleIdInput = document.getElementById('exampleId');
const titleInput = document.getElementById('title');
const categorySelect = document.getElementById('category');
const promptTextarea = document.getElementById('prompt');
const tagsInput = document.getElementById('tags');
const imageInput = document.getElementById('image');
const imagePreview = document.getElementById('imagePreview');
// 新增：获取模型选择元素
const modelSelect = document.getElementById('targetModelSelect');
const previewImg = document.getElementById('previewImg');
const removeImageButton = document.getElementById('removeImage');
const saveButton = document.getElementById('saveExample');
const confirmDeleteButton = document.getElementById('confirmDelete');
const categoriesTableBody = document.getElementById('categoriesTableBody');
const addCategoryBtn = document.getElementById('addCategoryBtn');
const categoryForm = document.getElementById('categoryForm');
const categoryIdInput = document.getElementById('categoryId');
const categoryNameInput = document.getElementById('categoryName');
const categorySlugInput = document.getElementById('categorySlug');
const categoryDescriptionInput = document.getElementById('categoryDescription');
const saveCategoryButton = document.getElementById('saveCategory');
const confirmDeleteCategoryButton = document.getElementById('confirmDeleteCategory');
const imageDropZone = document.getElementById('imageDropZone'); // 新增：获取拖拽区域元素
const addExampleModalElement = document.getElementById('addExampleModal'); // 新增：获取模态框元素
const profileModalElement = document.getElementById('profileModal'); // 新增：个人资料模态框元素
const profileForm = document.getElementById('profileForm'); // 新增：个人资料表单
const avatarInput = document.getElementById('avatarInput'); // 新增：头像上传输入框
const previewAvatarImg = document.getElementById('previewAvatarImg'); // 新增：头像预览图片
// const removeAvatarBtn = document.getElementById('removeAvatarBtn'); // 不再需要
const nicknameInput = document.getElementById('nicknameInput'); // 新增：昵称输入框
const usernameDisplay = document.getElementById('usernameDisplay'); // 新增：用户名显示框 (只读)
const saveProfileBtn = document.getElementById('saveProfileBtn'); // 新增：保存个人资料按钮
const gridMask = document.querySelector('.grid-mask'); // 新增：获取鼠标光晕遮罩元素

// --- 新增：源图片和参考图上传控件的 DOM 元素 ---
const sourceImageSection = document.getElementById('sourceImageSection');
const sourceImageDropZone = document.getElementById('sourceImageDropZone');
const sourceImageInput = document.getElementById('source_image');
const sourceImagePreview = document.getElementById('sourceImagePreview');
const previewSourceImg = document.getElementById('previewSourceImg');
const removeSourceImageBtn = document.getElementById('removeSourceImage');

const referenceImageSection = document.getElementById('referenceImageSection');
const referenceImageDropZone = document.getElementById('referenceImageDropZone');
const referenceImageInput = document.getElementById('reference_image');
const referenceImagePreview = document.getElementById('referenceImagePreview');
const previewReferenceImg = document.getElementById('previewReferenceImg');
const removeReferenceImageBtn = document.getElementById('removeReferenceImage');
// --- 结束新增 ---

// --- 新增：获取模态框内筛选元素 --- START ---
const modalCategoryFilterBtn = document.getElementById('modalCategoryFilterBtn');
const modalCategoryFilterMenu = document.getElementById('modalCategoryFilterMenu');
const modalCategoryFilterValue = document.getElementById('modalCategoryFilterValue');
const modalModelFilterBtn = document.getElementById('modalModelFilterBtn');
const modalModelFilterMenu = document.getElementById('modalModelFilterMenu');
const modalModelFilterValue = document.getElementById('modalModelFilterValue');
// --- 新增：获取模态框内筛选元素 --- END ---

// --- 文件顶部或合适位置添加新状态变量 ---
let selectedAvatarFile = null; 
let tagify = null; 
window.tagifyInstance = tagify; // 暴露给 image-utils.js

// 新增：暴露一个函数来重置 selectedFile (由 ai-generate.js 调用)
window.resetSelectedFile = () => {
    window.selectedFile = null; // 修改为 window.selectedFile
};

// --- 新增：准备并显示添加案例模态框的函数 ---
// async function prepareAndShowAddExampleModal(data) { ... } // 旧的完整定义应该被移除

// --- DOMContentLoaded 内获取新元素 ---
document.addEventListener('DOMContentLoaded', async () => { 

    checkAuth(); 
    // --- 修改：加载并初始化案例详情模态框 --- START ---
    try {
        const response = await fetch('example-detail-modal.html');
        if (response.ok) {
            const modalHTML = await response.text();
            const container = document.getElementById('exampleDetailModalContainer');
            if (container) {
                container.innerHTML = modalHTML;
                // 确保 example-detail-modal.js 已经加载并且 initExampleDetailModal 函数已定义
                if (typeof window.initExampleDetailModal === 'function') {
                    window.initExampleDetailModal();
                } else {
                    // 如果 example-detail-modal.js 还没加载完，可以设置一个短延时重试，或者依赖 defer 的顺序
                    console.warn('initExampleDetailModal 函数暂未定义，可能脚本还未执行完毕。');
                    // 作为一种备选方案，如果脚本确实是defer加载，它理论上会在main.js的DOMContentLoaded之后执行
                    // 但为了更可靠，显式调用是好的。如果依然报错，可能需要调整脚本加载顺序或方式。
                }
            } else {
                console.error('#exampleDetailModalContainer 未找到。');
            }
        } else {
            console.error('加载 example-detail-modal.html 失败:', response.status);
        }
    } catch (error) {
        console.error('加载 example-detail-modal.html 出现错误:', error);
    }
    // --- 修改：加载并初始化案例详情模态框 --- END ---
    
    if (tagsInput) {
        tagify = new Tagify(tagsInput, {
            whitelist: [], // 初始为空，将由 loadTags 填充
            maxTags: 10,
            dropdown: {
                maxItems: 20,
                enabled: 0, // 字符数触发下拉
                closeOnSelect: false // 选择后不关闭
            }
        });
        window.tagifyInstance = tagify; // 确保 image-utils 可以访问
        loadTags();
    } else {
        console.error('Tagify input element #tags not found.');
    }
    
    loadCategories(); 
    window.loadCategories = loadCategories; // Expose for category-manager.js
    loadInitialExamples();

    searchInput.addEventListener('input', handleFilterChange);

    // 图片预览 input change (使用 imageUtils)
    imageInput.addEventListener('change', async (event) => {
        if (event.target.files && event.target.files[0]) {
            if (window.imageUtils && typeof window.imageUtils.handleFileSelect === 'function') {
                await window.imageUtils.handleFileSelect(event.target.files[0], 'main');
            } else {
                console.error('imageUtils.handleFileSelect is not available.');
            }
        }
    });
    removeImageButton.addEventListener('click', () => {
        if (window.imageUtils && typeof window.imageUtils.removeImageFunc === 'function') {
            window.imageUtils.removeImageFunc('selectedFile', imageInput, previewImg, imagePreview, imageDropZone);
        } else {
            console.error('imageUtils.removeImageFunc is not available.');
        }
    });

    window.addEventListener('scroll', handleScroll);

    // 添加AI画布按钮点击事件监听器
    const jumpToAiCanvasBtn = document.getElementById('jump-to-ai-canvas');
    if (jumpToAiCanvasBtn) {
        jumpToAiCanvasBtn.addEventListener('click', jumpToJaazCanvas);
    } else {
        console.warn('未找到AI画布按钮元素，无法添加点击事件监听器');
    }

    // --- 修改：saveButton 事件监听器调用新的处理函数 ---
    if (saveButton) { // saveButton 应该在此时已被获取
        saveButton.addEventListener('click', () => {
            if (window.exampleFormHandler && typeof window.exampleFormHandler.saveExample === 'function') {
                window.exampleFormHandler.saveExample();
            } else {
                console.error('exampleFormHandler.saveExample is not available.');
            }
        });
    }
    // --- 修改结束 ---

    if(confirmDeleteButton) confirmDeleteButton.addEventListener('click', deleteExample); // confirmDeleteButton 应该在此时已被获取

    const addExampleModal = document.getElementById('addExampleModal');
    if (addExampleModal) {
        // --- 修改：hidden.bs.modal 事件监听器调用新的处理函数 ---
        addExampleModal.addEventListener('hidden.bs.modal', () => {
            if (window.exampleFormHandler && typeof window.exampleFormHandler.resetForm === 'function') {
                window.exampleFormHandler.resetForm();
            } else {
                console.error('exampleFormHandler.resetForm is not available.');
                // Fallback: 简单的表单重置，如果handler不可用
                const exampleForm = document.getElementById('exampleForm');
                if(exampleForm) exampleForm.reset();
                document.getElementById('exampleId').value = '';
                if(window.tagifyInstance) window.tagifyInstance.removeAllTags();
                // 基础的图片清理
                if(window.imageUtils && typeof window.imageUtils.removeImageFunc === 'function'){
                    window.imageUtils.removeImageFunc('selectedFile', document.getElementById('image'), document.getElementById('previewImg'), document.getElementById('imagePreview'), document.getElementById('imageDropZone'));
                }

            }
        });
        // --- 修改结束 ---
    addExampleModal.addEventListener('show.bs.modal', () => {
        const title = addExampleModal.querySelector('.modal-title');
            const exampleIdInputLocal = document.getElementById('exampleId'); // 使用局部变量
        
            if (exampleIdInputLocal && exampleIdInputLocal.value) { 
            title.textContent = '编辑提示词案例';
                // 分类和模型按钮文本的更新由 populateExampleForm 处理
        } else {
                // exampleFormHandler.resetForm() 应该已经处理了表单的重置
            title.textContent = '添加提示词案例';
        }
    });
    }

    // --- 新增：获取并初始化推广相关元素 ---
    pluginPromoModalElement = document.getElementById('pluginPromoModal');
    floatingPluginPromoElement = document.getElementById('floatingPluginPromo');
    closeFloatingPromoBtn = document.getElementById('closeFloatingPromoBtn');

    // 为浮动提示的关闭按钮添加基础事件监听 (如果元素存在)
    if (closeFloatingPromoBtn && floatingPluginPromoElement) {
        closeFloatingPromoBtn.addEventListener('click', () => {
            floatingPluginPromoElement.style.opacity = '0';
            floatingPluginPromoElement.style.transform = 'translateX(-50%) translateY(20px)';
            setTimeout(() => { floatingPluginPromoElement.style.display = 'none'; }, 300);
        });
    }

    // --- 处理自定义模型筛选下拉菜单点击 ---
    if (modelFilterMenu) {
        modelFilterMenu.addEventListener('click', (event) => {
            if (event.target.classList.contains('dropdown-item')) {
                event.preventDefault();
                const selectedText = event.target.textContent;
                const selectedValue = event.target.dataset.value;

                if (modelFilterBtn) modelFilterBtn.textContent = selectedText;
                if (modelFilterValue) {
                    modelFilterValue.value = selectedValue;
                }

                modelFilterMenu.querySelectorAll('.dropdown-item').forEach(item => item.classList.remove('active'));
                event.target.classList.add('active');

                // loadExamples(1); 
                resetPaginationAndLoad(); // <--- 修改：调用 resetPaginationAndLoad
            }
        });
    }

    // --- 处理自定义分类筛选下拉菜单点击 ---
    if (categoryFilterMenu) {
        categoryFilterMenu.addEventListener('click', (event) => {
            if (event.target.classList.contains('dropdown-item')) {
                event.preventDefault();
                const selectedText = event.target.textContent;
                const selectedValue = event.target.dataset.value;

                if (categoryFilterBtn) categoryFilterBtn.textContent = selectedText;
                if (categoryFilterValue) {
                    categoryFilterValue.value = selectedValue;
                }

                categoryFilterMenu.querySelectorAll('.dropdown-item').forEach(item => item.classList.remove('active'));
                event.target.classList.add('active');

                resetPaginationAndLoad(); 
            }
        });
    }

    // --- 处理自定义排序筛选下拉菜单点击 ---
    if (sortFilterMenu) {
        sortFilterMenu.addEventListener('click', (event) => {
            if (event.target.classList.contains('dropdown-item')) {
                event.preventDefault();
                const selectedText = event.target.textContent;
                const selectedValue = event.target.dataset.value;

                if (sortFilterBtn) sortFilterBtn.textContent = selectedText;
                if (sortFilterValue) sortFilterValue.value = selectedValue;

                sortFilterMenu.querySelectorAll('.dropdown-item').forEach(item => item.classList.remove('active'));
                event.target.classList.add('active');

                // loadExamples(1);
                resetPaginationAndLoad(); // <--- 修改：调用 resetPaginationAndLoad
            }
        });
    }

    // --- 处理模态框内自定义分类筛选下拉菜单点击 ---
    if (modalCategoryFilterMenu) {
        modalCategoryFilterMenu.addEventListener('click', (event) => {
            if (event.target.classList.contains('dropdown-item')) {
                event.preventDefault();
                const selectedText = event.target.textContent;
                const selectedValue = event.target.dataset.value;

                if (modalCategoryFilterBtn) modalCategoryFilterBtn.textContent = selectedText;
                if (modalCategoryFilterValue) modalCategoryFilterValue.value = selectedValue;

                modalCategoryFilterMenu.querySelectorAll('.dropdown-item').forEach(item => item.classList.remove('active'));
                event.target.classList.add('active');
            }
        });
    }

    // --- 处理模态框内自定义模型筛选下拉菜单点击 ---
    if (modalModelFilterMenu) {
        modalModelFilterMenu.addEventListener('click', (event) => {
            if (event.target.classList.contains('dropdown-item')) {
                event.preventDefault();
                const selectedText = event.target.textContent;
                const selectedValue = event.target.dataset.value;

                if (modalModelFilterBtn) modalModelFilterBtn.textContent = selectedText;
                if (modalModelFilterValue) {
                    modalModelFilterValue.value = selectedValue;
                    // ... (console log for debugging) ...
                }

                modalModelFilterMenu.querySelectorAll('.dropdown-item').forEach(item => item.classList.remove('active'));
                event.target.classList.add('active');

                // --- 新增：根据选择的模型显示/隐藏额外的图片上传控件 ---
                if (selectedValue === 'GPT4O-编辑') {
                    if(sourceImageSection) sourceImageSection.style.display = 'block';
                    if(referenceImageSection) referenceImageSection.style.display = 'block';
                } else {
                    if(sourceImageSection) sourceImageSection.style.display = 'none';
                    if(referenceImageSection) referenceImageSection.style.display = 'none';
                    // 当切换到非"GPT4O-编辑"模型时，清空已选的源文件和参考文件及其预览
                    window.selectedSourceFile = null;
                    if(sourceImageInput) sourceImageInput.value = ''; 
                    if(previewSourceImg) previewSourceImg.src = '';
                    if(sourceImagePreview) sourceImagePreview.style.display = 'none';
                    if(sourceImageDropZone) sourceImageDropZone.style.display = 'block'; 

                    window.selectedReferenceFile = null;
                    if(referenceImageInput) referenceImageInput.value = ''; 
                    if(previewReferenceImg) previewReferenceImg.src = '';
                    if(referenceImagePreview) referenceImagePreview.style.display = 'none';
                    if(referenceImageDropZone) referenceImageDropZone.style.display = 'block'; 
                }
                // --- 结束新增 ---
            }
        });
    }

    // --- 动态设置 autocomplete 属性 ---
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.setAttribute('autocomplete', 'off');
        }
        // 你也可以为其他搜索框添加类似逻辑
        const adminUserSearchInput = document.getElementById('adminUserSearchInput');
        if (adminUserSearchInput) {
            adminUserSearchInput.setAttribute('autocomplete', 'off');
        }
        const userCreditSearchInput = document.getElementById('userCreditSearchInput');
        if (userCreditSearchInput) {
            userCreditSearchInput.setAttribute('autocomplete', 'off');
        }
    });

    // 为源图片移除按钮绑定事件
    if (removeSourceImageBtn) {
        removeSourceImageBtn.addEventListener('click', () => {
            if (window.imageUtils && typeof window.imageUtils.removeImageFunc === 'function') {
                window.imageUtils.removeImageFunc('selectedSourceFile', sourceImageInput, previewSourceImg, sourceImagePreview, sourceImageDropZone);
            }
        });
    }

    // 为参考图移除按钮绑定事件
    if (removeReferenceImageBtn) {
        removeReferenceImageBtn.addEventListener('click', () => {
            if (window.imageUtils && typeof window.imageUtils.removeImageFunc === 'function') {
                window.imageUtils.removeImageFunc('selectedReferenceFile', referenceImageInput, previewReferenceImg, referenceImagePreview, referenceImageDropZone);
            }
        });
    }

    if (sourceImageInput) {
        sourceImageInput.addEventListener('change', async (event) => {
            if (event.target.files && event.target.files[0]) {
                if (window.imageUtils && typeof window.imageUtils.handleFileSelect === 'function') {
                    await window.imageUtils.handleFileSelect(event.target.files[0], 'source');
                }
            }
        });
    }
    if (referenceImageInput) {
        referenceImageInput.addEventListener('change', async (event) => {
            if (event.target.files && event.target.files[0]) {
                if (window.imageUtils && typeof window.imageUtils.handleFileSelect === 'function') {
                    await window.imageUtils.handleFileSelect(event.target.files[0], 'reference');
                }
            }
        });
    }

    // --- 修改：使用 imageUtils 设置拖拽区事件 ---
    if (window.imageUtils && typeof window.imageUtils.setupDropZoneEvents === 'function') {
        window.imageUtils.setupDropZoneEvents(imageDropZone, imageInput, 'main');
        window.imageUtils.setupDropZoneEvents(sourceImageDropZone, sourceImageInput, 'source');
        window.imageUtils.setupDropZoneEvents(referenceImageDropZone, referenceImageInput, 'reference');
                            } else {
        console.error('imageUtils.setupDropZoneEvents is not available.');
    }
    
    // --- 修改：使用 imageUtils 设置粘贴事件 ---
    if (addExampleModalElement) {
        if (window.imageUtils && typeof window.imageUtils.setupImagePasteListener === 'function') {
            window.imageUtils.setupImagePasteListener(addExampleModalElement);
        } else {
            console.error('imageUtils.setupImagePasteListener is not available.');
        }
    }

    // --- 新增：为 "生成同款" 准备的全局函数 ---
    window.populatePromptForSameGeneration = function(prompt, model, category, sourceImageUrl, referenceImageUrl, imageDimensionsStr) {
        
        const aiPromptInput = document.getElementById('aiPromptInput');
        const aiModelSelect = document.getElementById('aiModelSelect');
        // 假设AI图片生成Tab的按钮ID是 'ai-generate-tab'
        const aiGenerateTabButton = document.getElementById('ai-generate-tab');

        // 处理GPT4O-编辑类型
        if (model === 'GPT4O-编辑') {
            // 获取GPT-4O图片编辑页签的按钮和输入框
            const gpt4oEditTabButton = document.getElementById('gpt4o-image-edit-tab');
            const editPromptInput = document.getElementById('gpt4oEditPromptInput');
            
            if (!gpt4oEditTabButton) {
                console.warn('GPT-4O图片编辑页签按钮未找到');
                alert('无法切换到GPT-4O图片编辑页签，请手动操作。');
                return;
            }
            
            // 切换到GPT-4O图片编辑页签
            const tab = new bootstrap.Tab(gpt4oEditTabButton);
            tab.show();
            
            // 填充提示词
            if (editPromptInput) {
                editPromptInput.value = prompt;
            } else {
                console.warn('GPT-4O图片编辑提示词输入框未找到');
            }
            
            // 处理源图和参考图
            if (sourceImageUrl || referenceImageUrl) {
                setTimeout(async () => {
                    try {
                        // 获取图片上传相关元素
                        const mainImageDropZone = document.getElementById('gpt4oMainImageDropZone');
                        const mainImagePreviewContainer = document.getElementById('gpt4oMainImagePreviewContainer');
                        const mainImagePreviewImg = document.getElementById('gpt4oMainImagePreviewImg');
                        
                        const ref1ImageDropZone = document.getElementById('gpt4oRef1ImageDropZone');
                        const ref1ImagePreviewContainer = document.getElementById('gpt4oRef1ImagePreviewContainer');
                        const ref1ImagePreviewImg = document.getElementById('gpt4oRef1ImagePreviewImg');
                        
                        // 定义fetchImageAsFile函数，用于下载图片并转换为File对象
                        async function fetchImageAsFile(url, filename) {
                            if (!url) return null;
                            try {
                                const response = await fetch(url);
                                if (!response.ok) {
                                    throw new Error(`下载图片失败: ${response.status} ${response.statusText}`);
                                }
                                const blob = await response.blob();
                                const mimeType = blob.type || 'image/png';
                                return new File([blob], filename || 'image.png', { type: mimeType });
                            } catch (error) {
                                console.error(`下载图片失败:`, error);
                                return null;
                            }
                        }
                        
                        // 下载并处理源图（作为主图）
                        if (sourceImageUrl) {
                            const mainFile = await fetchImageAsFile(sourceImageUrl, 'main_image.png');
                            if (mainFile) {
                                // 获取gpt4o-image-edit.js中的全局变量
                                if (window.gpt4oImageEdit && typeof window.gpt4oImageEdit.setMainImageFile === 'function') {
                                    window.gpt4oImageEdit.setMainImageFile(mainFile);
                                } else {
                                    // 直接操作DOM元素
                                    if (mainImagePreviewImg && mainImagePreviewContainer && mainImageDropZone) {
                                        const reader = new FileReader();
                                        reader.onload = (e) => {
                                            mainImagePreviewImg.src = e.target.result;
                                            mainImagePreviewContainer.style.display = 'block';
                                            mainImageDropZone.style.display = 'none';
                                        };
                                        reader.readAsDataURL(mainFile);
                                        
                                        // 尝试设置全局变量
                                        if (typeof mainImageFile !== 'undefined') {
                                            mainImageFile = mainFile;
                                        }
                                    }
                                }
                            }
                        }
                        
                        // 下载并处理参考图
                        if (referenceImageUrl) {
                            const refFile = await fetchImageAsFile(referenceImageUrl, 'reference_image.png');
                            if (refFile) {
                                // 获取gpt4o-image-edit.js中的全局变量
                                if (window.gpt4oImageEdit && typeof window.gpt4oImageEdit.setRef1ImageFile === 'function') {
                                    window.gpt4oImageEdit.setRef1ImageFile(refFile);
                                } else {
                                    // 直接操作DOM元素
                                    if (ref1ImagePreviewImg && ref1ImagePreviewContainer && ref1ImageDropZone) {
                                        const reader = new FileReader();
                                        reader.onload = (e) => {
                                            ref1ImagePreviewImg.src = e.target.result;
                                            ref1ImagePreviewContainer.style.display = 'block';
                                            ref1ImageDropZone.style.display = 'none';
                                        };
                                        reader.readAsDataURL(refFile);
                                        
                                        // 尝试设置全局变量
                                        if (typeof ref1ImageFile !== 'undefined') {
                                            ref1ImageFile = refFile;
                                        }
                                    }
                                }
                            }
                        }
                        
                        // 检查表单有效性，启用开始按钮
                        const startEditBtn = document.getElementById('startGpt4oImageEditBtn');
                        if (startEditBtn && editPromptInput && editPromptInput.value.trim()) {
                            startEditBtn.disabled = false;
                        }
                        
                        // 显示提示
                        if (typeof showToast === 'function') {
                            showToast('已填充提示词和图片到GPT-4O图片编辑区域', 'info');
                        }
                    } catch (error) {
                        console.error('[GPT4O-Edit] 处理图片时出错:', error);
                        alert('处理图片时出错，请手动上传图片。');
                    }
                }, 300); // 给页签切换一些时间
                
                return; // 已处理GPT4O-编辑类型，不继续执行后面的代码
            }
        }
        
        // 处理FLUX-多图类型
        if (model === 'FLUX-多图') {
            // 获取FLUX智能编辑图片页签的按钮
            const fluxEditTabButton = document.getElementById('flux-kontext-tab');
            const fluxPromptInput = document.getElementById('fluxPromptInput');
            
            if (!fluxEditTabButton) {
                console.warn('FLUX智能编辑图片页签按钮未找到');
                alert('无法切换到FLUX智能编辑图片页签，请手动操作。');
                return;
            }
            
            // 切换到FLUX智能编辑图片页签
            const tab = new bootstrap.Tab(fluxEditTabButton);
            tab.show();
            
            // 填充提示词
            if (fluxPromptInput) {
                fluxPromptInput.value = prompt;
            } else {
                console.warn('FLUX提示词输入框未找到');
            }
            
            // 处理图1和图2
            if (sourceImageUrl || referenceImageUrl) {
                setTimeout(async () => {
                    try {
                        // 获取图片上传相关元素
                        const fluxImageDropZone1 = document.getElementById('fluxSecondImageDropZone');
                        const fluxImagePreview1 = document.getElementById('fluxSecondImagePreview');
                        const fluxImagePreviewImg1 = document.getElementById('previewFluxSecondImg');
                        
                        const fluxImageDropZone2 = document.getElementById('fluxResultImageDropZone');
                        const fluxImagePreview2 = document.getElementById('fluxResultImagePreview');
                        const fluxImagePreviewImg2 = document.getElementById('previewFluxResultImg');
                        
                        // 定义fetchImageAsFile函数，用于下载图片并转换为File对象
                        async function fetchImageAsFile(url, filename) {
                            if (!url) return null;
                            try {
                                const response = await fetch(url);
                                if (!response.ok) {
                                    throw new Error(`下载图片失败: ${response.status} ${response.statusText}`);
                                }
                                const blob = await response.blob();
                                const mimeType = blob.type || 'image/png';
                                return new File([blob], filename || 'image.png', { type: mimeType });
                            } catch (error) {
                                console.error(`下载图片失败:`, error);
                                return null;
                            }
                        }
                        
                        // 下载并处理图1
                        if (sourceImageUrl && fluxImagePreviewImg1 && fluxImagePreview1 && fluxImageDropZone1) {
                            const file1 = await fetchImageAsFile(sourceImageUrl, 'flux_image1.png');
                            if (file1) {
                                // 优先使用window.fluxKontext的接口
                                if (window.fluxKontext && typeof window.fluxKontext.setImage1 === 'function') {
                                    window.fluxKontext.setImage1(file1);
                                    window.selectedFluxSecondFile = file1;  // 同时设置全局变量，供example-form-handler.js使用
                                }
                                // 如果fluxKontext接口不可用，则使用example-form-handler的接口
                                else if (window.exampleFormHandler && typeof window.exampleFormHandler.handleFluxSecondImageSelect === 'function') {
                                    window.exampleFormHandler.handleFluxSecondImageSelect(file1);
                                } 
                                // 最后才使用直接操作DOM的方式
                                else {
                                    // 直接操作DOM元素
                                    const reader = new FileReader();
                                    reader.onload = (e) => {
                                        fluxImagePreviewImg1.src = e.target.result;
                                        fluxImagePreview1.style.display = 'block';
                                        fluxImageDropZone1.style.display = 'none';
                                        
                                        // 设置全局变量
                                        window.selectedFluxSecondFile = file1;
                                        
                                        // 同时设置flux-kontext.js中需要的变量
                                        if (window.fluxImageBase64 !== undefined) {
                                            window.fluxImageBase64 = e.target.result;
                                        }
                                        if (window.fluxImageFile !== undefined) {
                                            window.fluxImageFile = file1;
                                        }
                                    };
                                    reader.readAsDataURL(file1);
                                }
                            }
                        }
                        
                        // 下载并处理图2
                        if (referenceImageUrl && fluxImagePreviewImg2 && fluxImagePreview2 && fluxImageDropZone2) {
                            const file2 = await fetchImageAsFile(referenceImageUrl, 'flux_image2.png');
                            if (file2) {
                                // 优先使用window.fluxKontext的接口
                                if (window.fluxKontext && typeof window.fluxKontext.setImage2 === 'function') {
                                    window.fluxKontext.setImage2(file2);
                                    window.selectedFluxResultFile = file2;  // 同时设置全局变量，供example-form-handler.js使用
                                }
                                // 如果fluxKontext接口不可用，则使用example-form-handler的接口
                                else if (window.exampleFormHandler && typeof window.exampleFormHandler.handleFluxResultImageSelect === 'function') {
                                    window.exampleFormHandler.handleFluxResultImageSelect(file2);
                                } 
                                // 最后才使用直接操作DOM的方式
                                else {
                                    // 直接操作DOM元素
                                    const reader = new FileReader();
                                    reader.onload = (e) => {
                                        fluxImagePreviewImg2.src = e.target.result;
                                        fluxImagePreview2.style.display = 'block';
                                        fluxImageDropZone2.style.display = 'none';
                                        
                                        // 设置全局变量
                                        window.selectedFluxResultFile = file2;
                                        
                                        // 同时设置flux-kontext.js中需要的变量
                                        if (window.fluxImage2Base64 !== undefined) {
                                            window.fluxImage2Base64 = e.target.result;
                                        }
                                        if (window.fluxImage2File !== undefined) {
                                            window.fluxImage2File = file2;
                                        }
                                    };
                                    reader.readAsDataURL(file2);
                                }
                            }
                        }
                        
                        // 显示提示
                        if (typeof showToast === 'function') {
                            showToast('已填充提示词和图片到FLUX智能编辑区域', 'info');
                        }
                    } catch (error) {
                        console.error('[FLUX-多图] 处理图片时出错:', error);
                        alert('处理图片时出错，请手动上传图片。');
                    }
                }, 300); // 给页签切换一些时间
                
                return; // 已处理FLUX-多图类型，不继续执行后面的代码
            }
        }
        
        if (aiPromptInput) {
            aiPromptInput.value = prompt;
        } else {
            console.warn('AI Prompt input (aiPromptInput) not found.');
        }

        // 设置模型 (使用全局的setModelSelection函数)
        if (window.setModelSelection) {
            // 将友好模型名称映射到系统内部使用的值
            let modelValueToSet;
            if (model === '超级生图') {
                modelValueToSet = 'super_image_3';
            } else if (model === '高级生图') {
                modelValueToSet = 'comfyui_imagefx_advanced';
            } else if (model === 'MJ' || model === 'Midjourney') {
                modelValueToSet = 'midjourney';
            } else if (model === 'GPT4O') {
                modelValueToSet = 'GPT4O'; // 假设这是正确的内部值
            } else {
                modelValueToSet = model; // 默认使用传入的值
            }
            window.setModelSelection(modelValueToSet);
        } else if (aiModelSelect) {
            // 使用旧的下拉框选择方式
            // 尝试匹配模型值。注意：这里的模型值可能需要与 aiModelSelect 中的 <option> value 完全一致
            // 例如，如果超级生图在select中的value是 "3.0"
            let modelValueToSelect = model; // 默认使用传入的 model
            if (model === '超级生图 V3.0' || model === '超级生图') modelValueToSelect = '3.0';
            else if (model === '超级生图 V2.1') modelValueToSelect = '2.1';
            else if (model === '高级生图') modelValueToSelect = 'comfyui_imagefx_advanced';
            else if (model === 'MJ' || model === 'Midjourney') modelValueToSelect = 'midjourney';
            // ...可以为其他模型添加映射...

            let foundOption = false;
            for (let i = 0; i < aiModelSelect.options.length; i++) {
                if (aiModelSelect.options[i].value === modelValueToSelect || aiModelSelect.options[i].text === model) {
                    aiModelSelect.value = aiModelSelect.options[i].value;
                    foundOption = true;
                    break;
                }
            }
            if (!foundOption) {
                 console.warn(`Model '${model}' (mapped to '${modelValueToSelect}') not found in aiModelSelect options.`);
            }
        } else {
            console.warn('AI Model select (aiModelSelect) not found and setModelSelection not available.');
        }

        // 切换到 AI 图片生成 Tab
        if (aiGenerateTabButton) {
            const tab = new bootstrap.Tab(aiGenerateTabButton);
            tab.show();
            // 滚动到页面顶部或Tab内容区，以便用户看到
            // window.scrollTo(0, 0); // 或者滚动到特定元素
            const tabPane = document.getElementById('ai-generate-tab-pane');
            if (tabPane) {
                // 微小延迟确保tab内容渲染完成，尤其是如果ai-generate.js有自己的初始化逻辑
                setTimeout(() => {
                tabPane.scrollIntoView({ behavior: 'smooth' });

                    // --- 将图片比例处理逻辑移到此处 ---
                    // Midjourney模型的特殊处理
                    if (model === 'MJ' || model === 'Midjourney') {
                        const mjModeSelect = document.getElementById('mjModeSelect');
                        if (mjModeSelect) {
                            // 默认设置为Fast模式，用户也可以根据需要切换
                            mjModeSelect.value = 'fast';
                            
                            // 触发模式切换事件，确保UI更新
                            const event = new Event('change');
                            mjModeSelect.dispatchEvent(event);
                            
                            // 提示用户可以在提示词中添加Midjourney参数
                            showToast && showToast('Midjourney一键同款已设置，您可以在提示词中添加--ar等参数来调整比例', 'info');
                        }
                    }
                    // 高级生图模型的特殊处理
                    else if (model === '高级生图') {
                        const comfyAspectRatioSelect = document.getElementById('comfyAspectRatioSelect');
                        if (comfyAspectRatioSelect && imageDimensionsStr) {
                            // 解析图片尺寸
                            const parts = imageDimensionsStr.split('x');
                            if (parts.length === 2) {
                                const width = parseInt(parts[0], 10);
                                const height = parseInt(parts[1], 10);

                                if (!isNaN(width) && !isNaN(height) && width > 0 && height > 0) {
                                    const actualRatio = width / height;

                                    // 高级生图的可用比例
                                    const comfyAspectRatios = [
                                        { value: "1:1 (Square)", ratio: 1.0 },
                                        { value: "16:9 (Landscape)", ratio: 16/9 },
                                        { value: "9:16 (Portrait)", ratio: 9/16 },
                                        { value: "3:2 (Landscape)", ratio: 3/2 },
                                        { value: "2:3 (Portrait)", ratio: 2/3 },
                                        { value: "4:3 (Landscape)", ratio: 4/3 },
                                        { value: "3:4 (Portrait)", ratio: 3/4 },
                                        { value: "21:9 (Landscape)", ratio: 21/9 },
                                        { value: "9:21 (Portrait)", ratio: 9/21 }
                                    ];

                                    // 查找最接近的比例
                                    let bestMatchValue = null;
                                    let minDiff = Infinity;

                                    for (const aspect of comfyAspectRatios) {
                                        const aspectParts = aspect.value.split(' ')[0].split(':');
                                        if (aspectParts.length !== 2) continue;
                                        
                                        const aspectWidth = parseFloat(aspectParts[0]);
                                        const aspectHeight = parseFloat(aspectParts[1]);
                                        const aspectRatio = aspectWidth / aspectHeight;
                                        
                                        const diff = Math.abs(actualRatio - aspectRatio);
                                        if (diff < minDiff) {
                                            minDiff = diff;
                                            bestMatchValue = aspect.value;
                                        }
                                    }

                                    if (bestMatchValue) {
                                        // 设置高级生图的比例下拉框
                                        comfyAspectRatioSelect.value = bestMatchValue;
                                        
                                        // 触发change事件，确保UI更新
                                        const event = new Event('change');
                                        comfyAspectRatioSelect.dispatchEvent(event);
                                    }
                                }
                            }
                        } else {
                            console.warn('[populatePrompt] 高级生图模式下未找到比例选择器或缺少图片尺寸信息');
                        }
                    } 
                    // 其他模型使用通用的比例按钮组
                    else {
                        const ratioSelectorContainer = document.getElementById('aiRatioSelector');
                        if (imageDimensionsStr && ratioSelectorContainer) {
                        const parts = imageDimensionsStr.split('x');
                        if (parts.length === 2) {
                            const width = parseInt(parts[0], 10);
                            const height = parseInt(parts[1], 10);

                            if (!isNaN(width) && !isNaN(height) && width > 0 && height > 0) {
                                const actualRatio = width / height;

                                const targetRatios = [
                                    { value: "1:1", ratio: 1.0 },
                                    { value: "16:9", ratio: 16/9 },
                                    { value: "9:16", ratio: 9/16 },
                                    { value: "4:3", ratio: 4/3 },
                                    { value: "3:4", ratio: 3/4 },
                                    { value: "3:2", ratio: 3/2 },
                                    { value: "2:3", ratio: 2/3 }
                                ];

                                let bestMatchValue = null;
                                let minDiff = Infinity;

                                for (const target of targetRatios) {
                                    const diff = Math.abs(actualRatio - target.ratio);
                                    if (diff < minDiff) {
                                        minDiff = diff;
                                        bestMatchValue = target.value;
                                    }
                                }
                                
                                if (bestMatchValue) {
                                    // 现在 ratioSelectorContainer 是按钮组的 div
                                    const targetButton = ratioSelectorContainer.querySelector(`button.ratio-button[data-ratio="${bestMatchValue}"]`);

                                    if (targetButton) {
                                        // 移除其他按钮的 active class
                                        const allRatioButtons = ratioSelectorContainer.querySelectorAll('button.ratio-button');
                                        allRatioButtons.forEach(btn => btn.classList.remove('active'));
                                        
                                        // 给目标按钮添加 active class
                                        targetButton.classList.add('active');

                                        // 调用 ai-generate.js 中的函数来更新输入框和锁定状态 (如果可用)
                                        // 这是一个理想情况，如果 ai-generate.js 将其相关函数暴露到全局
                                        if (typeof window.aiGenerateJsApi !== 'undefined' && typeof window.aiGenerateJsApi.selectRatio === 'function') {
                                            window.aiGenerateJsApi.selectRatio(bestMatchValue);
                                        } else {
                                            // 如果 ai-generate.js 的API不可用，我们至少更新了按钮状态。
                                            console.warn('[populatePrompt] aiGenerateJsApi.selectRatio not found. Manually activated button. Dimensions might not auto-update unless ai-generate.js handles button class changes or has its own click listener that re-reads active state.');
                                            }
                                    } else {
                                        console.warn(`[populatePrompt] Calculated best match aspect ratio button for "${bestMatchValue}" not found in #aiRatioSelector.`);
                                    }
                                } else {
                                    console.warn('[populatePrompt] Could not determine best match for aspect ratio.');
                                }
                            } else {
                                console.warn('[populatePrompt] Invalid image dimensions parsed from:', imageDimensionsStr);
                            }
                        } else {
                            console.warn('[populatePrompt] Could not parse image dimensions string:', imageDimensionsStr);
                        }
                    } else if (!imageDimensionsStr) {
                        } else if (!ratioSelectorContainer) {
                        console.warn('[populatePrompt] AI Image Aspect Ratio BUTTON CONTAINER (aiRatioSelector) STILL not found after tab show and delay.');
                        }
                    }
                    // --- 图片比例处理结束 ---
                }, 100); // 100ms延迟，给tab内容一点渲染时间
            }
        } else {
            console.warn('AI Generate Tab button (ai-generate-tab) not found.');
        }
        
        // 可以在此处添加一个 toast 提示，告知用户提示词已填充
        if (typeof showToast === 'function') {
            showToast('提示词已填充到AI图片生成区域。', 'info');
        }
    };
    // --- 结束："生成同款" 函数 ---

    window.toggleLikeDetailModal = async function(exampleId, buttonElement, countElement) {
        const isCurrentlyLiked = buttonElement.classList.contains('liked');
        const method = isCurrentlyLiked ? 'DELETE' : 'POST';
        const url = `${API_URL}/examples/${exampleId}/like`;

        buttonElement.disabled = true;
        const iconElement = buttonElement.querySelector('i');
        // buttonElement.classList.add('animating'); // 动画可以保留或移除

        try {
            const response = await fetch(url, {
                method: method,
                headers: getAuthHeaders() 
            });

            if (!response.ok) {
                // ... (错误处理，可以与原 toggleLike 类似) ...
                throw new Error(`操作失败 (${response.status})`);
            }
            const result = await response.json();

            countElement.textContent = result.likes_count;
            if (result.isLiked) {
                buttonElement.classList.add('liked');
                iconElement.className = 'bi bi-heart-fill';
                buttonElement.title = '取消点赞';
            } else {
                buttonElement.classList.remove('liked');
                iconElement.className = 'bi bi-heart';
                buttonElement.title = '点赞';
            }
        } catch (error) {
            console.error('[Main.js toggleLikeDetailModal] 点赞/取消点赞操作失败:', error);
            alert(`操作失败: ${error.message}`);
        } finally {
            buttonElement.disabled = false;
            // buttonElement.classList.remove('animating');
        }
    };
    // --- 结束调整/暴露 toggleLike ---

    // --- 暴露 main.js 的辅助函数给其他模块使用 ---
    window.getAuthHeaders = getAuthHeaders; // 暴露原始的 getAuthHeaders
    window.showToast = showToast;           // 暴露原始的 showToast
    window.getCategoryNameFromMain = getCategoryName; // 暴露 getCategoryName
    window.getCurrentUser = () => currentUser; // 新增：暴露获取当前用户的方法
    // populatePromptForSameGeneration 和 toggleLikeDetailModal 已经是全局的了

    // --- 新增：为个人资料模态框添加事件监听器以加载数据 ---
    const profileModal = document.getElementById('profileModal');
    if (profileModal) {
        profileModal.addEventListener('show.bs.modal', openProfileModal);
    }
    // --- 结束新增 ---

    // --- 新增：为头像上传和个人资料保存添加事件监听器 ---
    if (avatarInput) {
        avatarInput.addEventListener('change', handleAvatarSelect);
    } else {
        console.warn('未找到头像上传元素 (avatarInput)');
    }

    if (saveProfileBtn) {
        saveProfileBtn.addEventListener('click', saveProfile);
    } else {
        console.warn('未找到个人资料保存按钮 (saveProfileBtn)');
    }
    // --- 新增结束 ---

    const token = localStorage.getItem('token');
    if (token) {
        checkAuth();
        // Initial fetch for notifications for logged-in users
        fetchUnreadNotifications();
    } else {
        // ... existing else block ...
    }

    // ... existing event listeners ...

    // Event listener for notification dropdown
    const showCommentsBtn = document.getElementById('showCommentsNotifications');
    if (showCommentsBtn) {
        showCommentsBtn.addEventListener('click', (e) => {
            e.preventDefault();
            showCommentsNotifications();
        });
    }
    
    // ... more existing event listeners ...
}); // 结束 DOMContentLoaded

// --- 新增：为修改邮箱相关的按钮添加事件监听器 ---
document.addEventListener('DOMContentLoaded', () => {
    // 这些元素在文件末尾已经通过 getElementById 获取了，这里再次确保它们在监听器作用域内可用
    // 或者依赖于它们已经是全局变量（如此代码所示）

    if (changeEmailBtn && changeEmailSection) {
        changeEmailBtn.addEventListener('click', () => {
            changeEmailSection.style.display = 'block';
            changeEmailBtn.style.display = 'none';
            emailChangeAlertPlaceholder.innerHTML = ''; // 清空之前的提示
        });
    }

    if (cancelChangeEmailBtn && changeEmailSection && changeEmailBtn) {
        cancelChangeEmailBtn.addEventListener('click', () => {
            changeEmailSection.style.display = 'none';
            changeEmailBtn.style.display = 'inline-block';
            newEmailInput.value = ''; // 清空输入
            currentPasswordInput.value = ''; // 清空输入
            emailChangeAlertPlaceholder.innerHTML = ''; // 清空提示
        });
    }

    if (sendVerificationEmailBtn) {
        sendVerificationEmailBtn.addEventListener('click', requestEmailChange);
    }
});
// --- 结束：为修改邮箱相关的按钮添加事件监听器 ---


// --- 新增：处理点赞/取消点赞 --- (保留)
async function toggleLike(exampleId, buttonElement) {
    const isCurrentlyLiked = buttonElement.classList.contains('liked');
    const method = isCurrentlyLiked ? 'DELETE' : 'POST';
    const url = `${API_URL}/examples/${exampleId}/like`;

    // 增加视觉反馈：暂时禁用按钮并添加动画类
    buttonElement.disabled = true;
    const iconElement = buttonElement.querySelector('i');
    // const originalIconClass = iconElement.className; // 不再需要保存原始类，因为我们不替换它了
    // iconElement.className = 'spinner-border spinner-border-sm'; // 移除：不再显示加载图标
    buttonElement.classList.add('animating'); // 新增：添加动画触发类

    try {
        const response = await fetch(url, {
            method: method,
            headers: getAuthHeaders() // 使用现有函数获取认证头
        });

        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                alert('请先登录或检查您的权限');
                logout(); // 或者跳转到登录页
                return; // 停止执行
            }
            // 尝试解析错误信息
            let errorMsg = `操作失败 (${response.status})`;
            try {
                const errorData = await response.json();
                errorMsg = errorData.error || errorMsg;
            } catch (e) { /* 忽略解析错误 */ }
            throw new Error(errorMsg);
        }

        const result = await response.json();

        // 更新UI
        const likeCountElement = buttonElement.querySelector('.like-count');
        likeCountElement.textContent = result.likes_count;

        if (result.isLiked) {
            buttonElement.classList.add('liked');
            iconElement.className = 'bi bi-heart-fill'; // 更新为实心图标
            buttonElement.title = '取消点赞';
            } else {
            buttonElement.classList.remove('liked');
            iconElement.className = 'bi bi-heart'; // 更新为空心图标
            buttonElement.title = '点赞';
        }


        } catch (error) {
        console.error('点赞/取消点赞操作失败:', error);
        alert(`操作失败: ${error.message}`);
        // 出错时图标保持不变，或根据需要恢复 (但我们没改图标类，所以通常不用恢复)
        // iconElement.className = originalIconClass;
    } finally {
        // 无论成功或失败，最后都恢复按钮可用状态并移除动画类
        buttonElement.disabled = false;
        buttonElement.classList.remove('animating'); // 新增：移除动画触发类
        
        // 图标的最终状态 (bi-heart / bi-heart-fill) 会在 try 块的成功逻辑中设置
        // 这里不需要再处理图标类
    }
}

// 调试函数 (保留)
function logError(message, error) {
    console.error(`${message}:`, error);
    if (error.response) {
        console.error('响应数据:', error.response.data);
        console.error('响应状态:', error.response.status);
    } else if (error.request) {
        console.error('无响应，请求发送但没有收到响应');
    } else {
        console.error('错误详情:', error.message);
    }
}

// 新增：处理筛选变化的函数 (保留)
function handleFilterChange() {
    resetPaginationAndLoad();
}

// 新增：重置分页并加载第一页 (保留)
function resetPaginationAndLoad() {
    currentPage = 1;
    hasMore = true;
    if (examplesContainer) examplesContainer.innerHTML = ''; 
    loadExamples(currentPage); 
}
window.resetPaginationAndLoad = resetPaginationAndLoad; // 暴露给 example-form-handler.js

// 新增：加载初始案例数据 (保留)
function loadInitialExamples() {
    examplesContainer.innerHTML = `
        <div class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    `;
    loadExamples(1); // 加载第一页
}

// 修改：加载提示词案例函数，支持分页和追加
async function loadExamples(page = 1, append = false) {

    if (isLoading || (!append && !hasMore)) {
        return;
    }
    if (append && !hasMore) {
        return;
    }

    isLoading = true;
    if (!append) {
        showLoadingIndicator(true);
    }

    try {
        const category = categoryFilterValue.value;
        const model = modelFilterValue.value;
        const sort = sortFilterValue.value;
        const searchQuery = searchInput.value.trim();

        
        const params = new URLSearchParams({
            page: page.toString(),
            limit: examplesPerPage.toString(),
            sort: sort
        });

        if (model) {
            params.append('target_model', model === '_NULL_' ? '' : model);
        }

        // 将分类也作为查询参数添加
        if (category) {
            params.append('category_slug', category); // 确保后端使用 category_slug 这个参数名
        }

        let apiUrl = `${API_URL}/examples`; // 默认基础URL

        if (searchQuery) {
            apiUrl = `${API_URL}/examples/search`; // 如果有搜索词，则使用搜索接口
            params.append('query', searchQuery); 
            // 注意：分类 category_slug 已经通过上面的 if (category) 添加到 params 中了
        } 
        // 如果没有搜索词，apiUrl 保持为 /examples，所有筛选条件（分类、模型、排序）都通过 params 传递

        apiUrl = `${apiUrl}?${params.toString()}`;

        const response = await fetch(apiUrl, { 
            headers: getAuthHeaders()
        });

        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                window.location.href = 'login.html';
                return;
            }
            const errorText = await response.text();
            throw new Error(`HTTP error ${response.status}: ${errorText}`);
        }
        
        const responseData = await response.json();
        const examples = responseData.examples;
        
        if (!Array.isArray(examples)) {
            throw new Error('从服务器获取的案例数据格式不正确。');
        }
        
        currentPage = responseData.pagination.currentPage || page;
        hasMore = currentPage < (responseData.pagination.totalPages || 1);

        renderExamples(examples, append);
    } catch (error) {
        console.error('加载案例错误:', error);
        if (!append) {
            examplesContainer.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <h4>加载案例失败</h4>
                    <p>${error.message || '未知错误'}</p>
                    <button class="btn btn-outline-danger mt-3" onclick="loadExamples(1)">
                        <i class="bi bi-arrow-clockwise"></i> 重试
                    </button>
                </div>
            `;
        }
    } finally {
        isLoading = false;
        showLoadingIndicator(false);
    }
}

// 加载所有分类 (保留)
async function loadCategories() {
    try {
        const response = await fetch(`${API_URL}/categories`, {
            headers: getAuthHeaders()
        });
        
        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                window.location.href = 'login.html';
                return;
            }
            throw new Error(`HTTP error ${response.status}`);
        }
        
        const categories = await response.json();
        
        // --- 修改：填充新的自定义下拉菜单和旧的表单下拉菜单 --- START ---
        categoryFilterMenu.innerHTML = '<li><a class="dropdown-item active" href="#" data-value="">全部分类</a></li>'; // Reset and add default
        // categorySelect.innerHTML = '<option value="">选择分类</option>'; // Reset form select (保持对旧 select 的引用可能需要)
        modalCategoryFilterMenu.innerHTML = '<li><a class="dropdown-item active" href="#" data-value="">选择分类</a></li>'; // Reset modal dropdown

        categories.forEach(category => {
            const filterItemHTML = `<a class="dropdown-item" href="#" data-value="${category.slug}">${category.name}</a>`;

            // Add to filter dropdown
            const filterLi = document.createElement('li');
            filterLi.innerHTML = filterItemHTML;
            categoryFilterMenu.appendChild(filterLi);

            // Add to modal dropdown
            const modalLi = document.createElement('li'); // Create a separate li element
            modalLi.innerHTML = filterItemHTML;
            modalCategoryFilterMenu.appendChild(modalLi);

            // Add to form select (original one, if still used)
            // const formOption = document.createElement('option');
            // formOption.value = category.slug;
            // formOption.textContent = category.name;
            // categorySelect.appendChild(formOption); // Optional: keep updating the hidden select if needed elsewhere
        });
        // --- 修改：填充新的自定义下拉菜单和旧的表单下拉菜单 --- END ---

    } catch (error) {
        console.error('加载分类错误:', error);
        // categoriesTableBody.innerHTML = ` // THIS LINE SHOULD BE REMOVED AS categoriesTableBody is no longer in main.js
        //     <tr>
        //         <td colspan="4" class="text-center text-danger">
        //             加载失败，请刷新重试
        //         </td>
        //     </tr>
        // `;
    }
}

// 加载所有标签 (保留)
async function loadTags() {
    try {
        const response = await fetch(`${API_URL}/tags`, {
            headers: getAuthHeaders()
        });
        
        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                window.location.href = 'login.html';
                return;
            }
            throw new Error(`HTTP error ${response.status}`);
        }
        
        const tags = await response.json();
        tagify.settings.whitelist = tags.map(tag => tag.name);
    } catch (error) {
        console.error('加载标签错误:', error);
    }
}

// 修改：渲染提示词案例，支持追加 (保留)
function renderExamples(examplesToRender, append = false) {
    // const imageBaseUrl = 'https://www.yzycolour.top/prompt-examples/server/uploads/'; // 定义图片基础 URL - 已修正 -> 移至全局
    const defaultAvatar = 'images/default-avatar.png'; // 定义默认头像路径

    // 如果不是追加模式，清空容器
    if (!append) {
        examplesContainer.innerHTML = '';
    }

    // 移除现有的加载指示器（如果在追加模式下）
    const existingLoading = examplesContainer.querySelector('.loading-more');
    if (existingLoading) {
        existingLoading.remove();
    }

    if (examplesToRender.length === 0 && !append) {
        examplesContainer.innerHTML = `
            <div class="empty-state">
                <i class="bi bi-journal-x"></i>
                <span>没有找到符合条件的案例。</span>
            </div>
        `;
        return;
    }

    const fragment = document.createDocumentFragment();
    examplesToRender.forEach(example => {
        const card = document.createElement('div');
        card.classList.add('example-card'); 
        card.dataset.id = example.id;

        const categoryName = getCategoryName(example.category) || '未分类';
        const tagsHtml = Array.isArray(example.tags) 
            ? example.tags.map(tag => `<span class="tag">${tag}</span>`).join('')
            : '';
        
        // 定义图片 URL，如果不存在则使用占位符或留空 - 修改：使用 imageBaseUrl 和 image_file
        const imageUrl = example.image_file ? imageBaseUrl + example.image_file : '';
        
        // --- 修改：获取分类颜色对象 --- START --- (确保此函数调用在 getCategoryColor 定义之后)
        const categoryColor = getCategoryColor(example.category);
        // --- 修改：获取分类颜色对象 --- END ---
        
        // --- 新增：获取作者信息 --- START ---
        const author = example.author;
        let authorHtml = '';
        if (author) {
            const authorName = author.nickname || author.username || '匿名'; // 优先显示昵称
            const authorAvatarUrl = author.avatar_url ? imageBaseUrl + author.avatar_url : defaultAvatar;
            authorHtml = `
                <div class="author-info">
                    <img src="${authorAvatarUrl}" alt="${authorName}" class="author-avatar" loading="lazy">
                    <span>${authorName}</span>
                </div>
            `;
        }
        // --- 新增：获取作者信息 --- END ---

        // --- 新增：获取模型信息 --- START ---
        const modelName = example.target_model || null;
        let modelHtml = '';
        if (modelName) {
            modelHtml = `<span class="model-badge">${modelName}</span>`;
        }
        // --- 新增：获取模型信息 --- END ---

        card.innerHTML = `
            ${imageUrl ? `<img src="${imageUrl}" class="example-img" alt="${example.title || '案例图片'}" loading="lazy">` : '<div class="example-img-placeholder"></div>' /* 可选的占位符 */}
            <span class="category-badge" style="background-color: ${categoryColor.backgroundColor}; border-color: ${categoryColor.borderColor};">${categoryName}</span>
            ${modelHtml} <!-- 新增：插入模型标签 -->
            <div class="card-body">
                <h5>${example.title || '无标题'}</h5>
                <p>${example.prompt ? example.prompt.substring(0, 100) + (example.prompt.length > 100 ? '...' : '') : '无提示词'}</p>
                <div class="tags-container">
                    ${tagsHtml}
                </div>
                <div class="actions">
                    <!-- 修改：点赞按钮和计数 -->
                    <button class="btn btn-icon btn-outline-secondary like-btn ${example.is_liked_by_current_user ? 'liked' : ''}" 
                            onclick="toggleLike('${example.id}', this)" 
                            title="${example.is_liked_by_current_user ? '取消点赞' : '点赞'}">
                        <i class="bi ${example.is_liked_by_current_user ? 'bi-heart-fill' : 'bi-heart'}"></i>
                        <span class="like-count ms-1">${example.likes_count || 0}</span> <!-- 使用后端数据 -->
                    </button>
                    <button class="btn btn-icon btn-outline-secondary" onclick="copyPrompt('${example.id}')" title="复制提示词">
                        <i class="bi bi-clipboard"></i>
                    </button>
                    <button class="btn btn-icon btn-outline-secondary" onclick="showShareModal('${example.id}')" title="分享">
                        <i class="bi bi-share"></i>
                    </button>
                    ${currentUser && (example.author && example.author.id === currentUser.id || currentUser.role === 'admin') ? `
                    <div class="dropdown d-inline-block">
                      <button class="btn btn-icon btn-outline-secondary" type="button" id="dropdownMenuButton-${example.id}" data-bs-toggle="dropdown" aria-expanded="false" title="更多操作">
                        <i class="bi bi-three-dots-vertical"></i>
                      </button>
                      <ul class="dropdown-menu dropdown-menu-dark dropdown-menu-end" aria-labelledby="dropdownMenuButton-${example.id}">
                        <li><a class="dropdown-item" href="#" onclick="event.preventDefault(); editExample('${example.id}')"><i class="bi bi-pencil-square me-2"></i>编辑</a></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="event.preventDefault(); showDeleteConfirm('${example.id}')"><i class="bi bi-trash me-2"></i>删除</a></li>
                      </ul>
                    </div>
                    ` : ''
                    }
                </div>
            </div>
            <!-- 新增：在卡片底部添加作者信息 -->
            ${authorHtml}
            <!-- Optional: Add hover effect div if needed by JS later -->
            <!-- <div class="card-hover-effect"></div> -->
        `;

        // --- 新增：为整个卡片添加点击事件，用于打开详情 ---
        card.addEventListener('click', (event) => {
            // 检查点击的是否是卡片内的交互按钮
            let targetElement = event.target;
            let isInteractiveButton = false;
            while (targetElement && targetElement !== card) {
                if (targetElement.matches('.like-btn, .like-btn *, .btn-icon, .btn-icon *, .dropdown-toggle, .dropdown-toggle *, .dropdown-item, .dropdown-item *')) {
                    isInteractiveButton = true;
                    break;
                }
                targetElement = targetElement.parentElement;
            }

            if (!isInteractiveButton) {
                if (window.showExampleDetailModal) {
                    window.showExampleDetailModal(example.id);
                } else {
                    console.error('showExampleDetailModal function is not available on window.');
                    alert('无法打开案例详情。');
                }
            } else {
            }
        });
        // --- 结束新增卡片点击事件 ---

        fragment.appendChild(card);
    });

    examplesContainer.appendChild(fragment);
}

// 修改：显示/隐藏加载更多指示器 (保留)
function showLoadingIndicator(show) {
    let indicator = examplesContainer.querySelector('.loading-more');
    if (show) {
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'loading loading-more'; // 使用 loading 类以继承样式
            indicator.innerHTML = `
                <div class="spinner-border spinner-border-sm" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <span>加载更多...</span>
            `;
            examplesContainer.appendChild(indicator);
        }
    } else {
        if (indicator) {
            indicator.remove();
        }
    }
}

// 新增：处理滚动事件 (保留)
function handleScroll() {
    // 窗口滚动高度 + 窗口可视高度 >= 文档总高度 - 缓冲值
    if ((window.innerHeight + window.scrollY) >= document.documentElement.scrollHeight - 200) {
        if (!isLoading && hasMore) {
            loadExamples(currentPage + 1, true); // 加载下一页并追加
        }
    }
}

// --- 修改 `editExample` 函数 ---
async function editExample(id) {
    try {
        const response = await fetch(`${window.API_URL}/examples/${id}`, { 
            headers: getAuthHeaders()
        });
        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                alert('认证失败或已过期，请重新登录。');
                if(window.logoutMain) window.logoutMain();
            return;
        }
            const errorData = await response.json().catch(() => ({ error: `HTTP error ${response.status}` }));
            throw new Error(errorData.error || `获取案例详情失败: ${response.status}`);
        }
        const example = await response.json();
        
        if (!example) {
            alert('未找到该案例');
            return;
        }
        
        // 移除旧的表单填充逻辑：
        // exampleIdInput.value = example.id; // 由 populateExampleForm 处理
        // titleInput.value = example.title; // 由 populateExampleForm 处理
        // promptTextarea.value = example.prompt; // 由 populateExampleForm 处理
        // ... 其他字段和图片预览的直接操作 ... // 由 populateExampleForm 处理
        // if (example.imageUrl || example.image_url) { // 由 populateExampleForm 处理
        //     displayImagePreviewFunc(window.selectedFile, previewImg, imagePreview, imageDropZone); // displayImagePreviewFunc 已移走，且此处逻辑不对
        // } else {
        //      if(imageDropZone) imageDropZone.style.display = 'block'; // 由 populateExampleForm 处理
        // }
        
        // 调用 example-form-handler.js 中的 populateExampleForm
        if (window.exampleFormHandler && typeof window.exampleFormHandler.populateExampleForm === 'function') {
            await window.exampleFormHandler.populateExampleForm(example);
            // populateExampleForm 内部会负责显示模态框
                } else {
            console.error('exampleFormHandler.populateExampleForm is not available. Cannot populate or show modal.');
            alert('编辑功能组件加载失败，请刷新页面重试。');
        }

    } catch (error) {
        console.error('编辑案例错误:', error);
        alert('加载案例数据失败: ' + error.message);
        if (window.exampleFormHandler && typeof window.exampleFormHandler.resetForm === 'function') {
            const addExampleModalElement = document.getElementById('addExampleModal');
            const modalInstance = bootstrap.Modal.getInstance(addExampleModalElement);
            if (modalInstance && addExampleModalElement.classList.contains('show')) {
                 window.exampleFormHandler.resetForm();
            }
        }
    }
}
// --- `editExample` 修改结束 ---

// 显示删除确认框 (保留)
function showDeleteConfirm(id) {
    // *** 新增：检查是否是管理员 *** -> **移除此检查**
    /*
    if (!currentUser || currentUser.role !== 'admin') {
        console.warn('普通用户尝试删除案例，操作被阻止。');
        alert('您没有权限删除案例。');
        return; // 阻止非管理员执行删除操作
    }
    */
    deleteExampleId = id;
    const modalInstance = bootstrap.Modal.getOrCreateInstance(document.getElementById('deleteConfirmModal'));
    modalInstance.show();
}

// 删除提示词案例 (保留)
async function deleteExample() {
    // *** 移动权限检查到 showDeleteConfirm 中，这里假设 deleteExampleId 存在即表示权限已验证 ***
    if (!deleteExampleId) return;
    
    try {
        confirmDeleteButton.disabled = true;
        confirmDeleteButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 删除中...';
        
        const response = await fetch(`${API_URL}/examples/${deleteExampleId}`, {
            method: 'DELETE',
            headers: getAuthHeaders() // 确保 DELETE 请求也带认证头
        });
        
        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                alert('认证失败或权限不足，请重新登录。');
                logout();
                return;
            }
            let errorMsg = `HTTP error: ${response.status}`;
            try {
                const errorData = await response.json();
                errorMsg = errorData.message || errorData.error || errorMsg;
            } catch (e) { /* 忽略解析错误 */ }
            throw new Error(errorMsg);
        }
        
        const modalInstance = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modalInstance.hide();
        
        const cardToRemove = examplesContainer.querySelector(`.example-card[data-id="${deleteExampleId}"]`);
        if (cardToRemove) {
            cardToRemove.remove();
                    } else {
             resetPaginationAndLoad();
        }
        
        if (!examplesContainer.querySelector('.example-card')) {
             // renderExamples([], false); 
        }
        
    } catch (error) {
        console.error('删除案例错误:', error);
        alert('删除失败: ' + error.message);
    } finally {
        confirmDeleteButton.disabled = false;
        confirmDeleteButton.innerHTML = '删除';
        deleteExampleId = null;
    }
}

// 重置表单 (使用 imageUtils)
// function resetForm() { ... } // 删除此函数 (约 929行之后)

// 获取分类名称 (从 categorySelect 获取) (保留)
let categoryMap = {}; // 缓存分类 slug -> name
function getCategoryName(categorySlug) {
    if (Object.keys(categoryMap).length === 0 && categoryFilterMenu) { 
        const items = categoryFilterMenu.querySelectorAll('.dropdown-item');
        items.forEach(item => {
            const value = item.dataset.value;
            if (value) { 
                categoryMap[value] = item.textContent;
            }
        });
    }
    return categoryMap[categorySlug] || categorySlug;
}
window.getCategoryNameFromMainJS = getCategoryName; // 暴露给 example-form-handler.js

// --- 新增：复制提示词到剪贴板 --- (案例管理相关，保留)
async function copyPrompt(id) {
    try {
        const response = await fetch(`${API_URL}/examples/${id}`, {
             headers: getAuthHeaders()
        });
        if (!response.ok) {
             if (response.status === 401 || response.status === 403) {
                 alert('无法获取案例详情，请检查登录状态。');
                 return;
             }
            throw new Error(`获取案例详情失败: ${response.status}`);
        }
        const example = await response.json();

        if (!example || !example.prompt) {
            throw new Error('无法获取有效的提示词内容。');
        }

        const promptText = example.prompt;
        const promotionalText = "\n\n---\n✨ 发现更多 AI 绘画灵感，请访问 https://www.yzycolour.top/prompt-examples/admin ✨"; // 您的宣传文案
        const finalTextToCopy = promptText + promotionalText; // 拼接文本

        let copySuccess = false; // 标记是否复制成功

        // 使用 Clipboard API 复制
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(finalTextToCopy); // 使用拼接后的文本
            copySuccess = true;
                    } else {
            // 备用方法
            console.warn('Clipboard API 不可用或上下文不安全，尝试备用方法。');
            const textArea = document.createElement("textarea");
            textArea.value = finalTextToCopy; // 使用拼接后的文本
            // 避免在屏幕上闪烁
            textArea.style.position = "fixed";
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.width = "2em";
            textArea.style.height = "2em";
            textArea.style.padding = "0";
            textArea.style.border = "none";
            textArea.style.outline = "none";
            textArea.style.boxShadow = "none";
            textArea.style.background = "transparent";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    copySuccess = true;
                } else {
                    throw new Error('使用备用方法复制失败。');
                }
            } catch (err) {
                console.error('备用复制方法失败:', err);
                throw new Error('无法将提示词复制到剪贴板。');
            } finally {
                 if (document.body.contains(textArea)) {
                     document.body.removeChild(textArea);
                 }
            }
        }

        // --- 复制成功后的操作 ---
        if (copySuccess) {
            // 1. 显示简单的成功提示
            showToast("提示词已复制 (含来源信息)!"); // 修改提示信息
            localStorage.setItem('lastCopiedPromptForFigma', finalTextToCopy); // 存储包含宣传文案的完整内容

            // 2. 检查是否需要显示推广
            const promoShown = localStorage.getItem(PROMO_MODAL_SHOWN_KEY);

            if (promoShown !== 'true') {
                // 首次复制，显示模态框
                // 使用 setTimeout 稍微延迟，避免与 Toast 重叠太紧密
                setTimeout(showPluginPromoModal, 500); // 延迟 0.5 秒
            } else {
                // 后续复制，显示浮动提示
                 // 使用 setTimeout 稍微延迟
                setTimeout(showFloatingPluginPromo, 500); // 延迟 0.5 秒
            }
        } else {
             // 如果上面两种方法都失败了
             alert('复制失败，请重试或手动复制。');
        }

        } catch (error) {
        console.error('复制提示词错误:', error);
        alert('复制失败: ' + error.message);
    }
}

// showToast 函数保持不变，用于显示基础的 "提示词已复制!" 信息 (保留)
function showToast(message) {
    const toastId = 'copy-toast';
    let toastElement = document.getElementById(toastId);
    
    if (!toastElement) {
        toastElement = document.createElement('div');
        toastElement.id = toastId;
        toastElement.style.position = 'fixed';
        toastElement.style.bottom = '20px';
        toastElement.style.left = '50%';
        toastElement.style.transform = 'translateX(-50%)';
        toastElement.style.padding = '12px 25px'; // 稍微增加内边距以适应两行内容
        toastElement.style.background = 'rgba(40, 40, 40, 0.85)'; // 深一点的背景，增加对比度
        toastElement.style.color = 'white';
        toastElement.style.borderRadius = '8px'; // 圆角稍大
        toastElement.style.zIndex = '1050'; // Ensure it's above most elements
        toastElement.style.opacity = '0';
        toastElement.style.transition = 'opacity 0.5s ease, transform 0.3s ease'; // 添加 transform 过渡
        toastElement.style.textAlign = 'center'; // 文本居中
        toastElement.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)'; // 添加阴影
        document.body.appendChild(toastElement);
    }
    
    toastElement.innerHTML = message; // 使用 innerHTML 来渲染链接
    toastElement.style.opacity = '1';
    toastElement.style.transform = 'translateX(-50%) translateY(0)'; // 入场动画
    
    // Clear previous timeout if exists
    if (toastElement.timeoutId) {
        clearTimeout(toastElement.timeoutId);
    }
    
    // Fade out after a delay
    toastElement.timeoutId = setTimeout(() => {
        toastElement.style.opacity = '0';
        toastElement.style.transform = 'translateX(-50%) translateY(20px)'; // 离场动画
        // Optional: remove element after fade out
        // setTimeout(() => { if(toastElement) toastElement.remove(); }, 500);
    }, 3000); // 显示 3 秒
}
// --- 新增：复制提示词到剪贴板 --- END ---

// --- 新函数：显示插件推广模态框 --- (保留)
function showPluginPromoModal() {
    if (pluginPromoModalElement) {
        try {
            const modalInstance = bootstrap.Modal.getOrCreateInstance(pluginPromoModalElement);
            modalInstance.show();
            // 标记已显示
            localStorage.setItem(PROMO_MODAL_SHOWN_KEY, 'true');
        } catch (error) {
            console.error('显示推广模态框失败:', error);
        }
    } else {
        console.warn('推广模态框元素 (pluginPromoModal) 未找到，请确保 HTML 中存在该 ID。');
    }
}

// --- 新函数：显示浮动插件推广提示 --- (保留)
function showFloatingPluginPromo() {
    if (floatingPluginPromoElement) {
        // 确保元素可见并触发动画（如果 CSS 中定义了）
        floatingPluginPromoElement.style.display = 'flex'; // 或者 'block'
        // 强制重绘以确保动画触发
        floatingPluginPromoElement.offsetHeight; 
        floatingPluginPromoElement.style.opacity = '1';
        floatingPluginPromoElement.style.transform = 'translateX(-50%) translateY(0)'; // 设置最终居中位置

        // 可选：一段时间后自动隐藏 (如果需要的话)
        /*
        setTimeout(() => {
            if (floatingPluginPromoElement.style.display !== 'none') { // 检查是否已被手动关闭
                 floatingPluginPromoElement.style.opacity = '0';
                 floatingPluginPromoElement.style.transform = 'translateY(20px)'; // 假设 CSS 中有 transform 动画
                 setTimeout(() => { floatingPluginPromoElement.style.display = 'none'; }, 300); // 等待动画完成
            }
        }, 15000); // 15秒后自动隐藏
        */
    } else {
        console.warn('浮动推广提示元素 (floatingPluginPromo) 未找到，请确保 HTML 中存在该 ID。');
    }
}

// --- 新增：打开个人资料模态框 --- (保留)
async function openProfileModal() {
    // --- 新增：先获取最新用户信息 --- START ---
    try {
        const response = await fetch(`${API_URL}/auth/me`, {
            headers: getAuthHeaders()
        });
        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                alert('会话已过期，请重新登录。');
                logout();
                return; // 停止执行后续代码
            }
            throw new Error(`获取用户信息失败: ${response.status}`);
        }
        const data = await response.json();
        currentUser = data.user; // 更新内存中的 currentUser
        localStorage.setItem('user', JSON.stringify(currentUser)); // 更新 localStorage
        updateUIByRole(); // 可选：如果角色等信息可能变化，也更新顶部导航栏等
    } catch (error) {
        console.error('打开个人资料时获取最新用户信息失败:', error);
        alert('无法加载最新的个人资料，请稍后重试。');
        // 出错时，可以选择关闭模态框或使用旧数据（取决于产品需求）
        const modalInstance = bootstrap.Modal.getInstance(profileModalElement);
        if (modalInstance) modalInstance.hide(); // 出错则直接关闭模态框
        return; // 停止执行
    }
    // --- 获取最新用户信息 --- END ---

    // --- 使用更新后的 currentUser 填充数据 --- START ---
    if (!currentUser) return; // 再次检查以防万一

    // 填充当前数据
    nicknameInput.value = currentUser.nickname || '';
    usernameDisplay.value = currentUser.username; // 用户名不可修改
    emailDisplay.value = currentUser.email || '未设置'; // 使用更新后的邮箱

    // 重置头像选择状态
    selectedAvatarFile = null;
    avatarInput.value = ''; // 清空文件选择

    // --- 重置邮箱修改区域 ---
    changeEmailSection.style.display = 'none'; // 确保修改区域初始隐藏
    changeEmailBtn.style.display = 'inline-block'; // 确保"修改"按钮可见
    newEmailInput.value = '';
    currentPasswordInput.value = '';
    emailChangeAlertPlaceholder.innerHTML = ''; // 清空旧提示
    // --- 结束重置 ---

    // 设置头像预览
    const currentAvatarUrl = currentUser.avatar_url;
    if (currentAvatarUrl) {
        previewAvatarImg.src = currentAvatarUrl;
    } else {
        previewAvatarImg.src = 'https://www.yzycolour.top/prompt-examples/admin/images/default-avatar.png';
    }
    // 清除可能的错误状态
    previewAvatarImg.onerror = () => {
        console.warn('个人资料预览头像加载失败:', currentAvatarUrl);
        previewAvatarImg.src = 'https://www.yzycolour.top/prompt-examples/admin/images/default-avatar.png';
        previewAvatarImg.onerror = null; // 防止无限循环
    };
    // --- 使用更新后的 currentUser 填充数据 --- END ---
}

// --- 新增：处理头像选择 --- (保留)
function handleAvatarSelect(event) {
    const file = event.target.files ? event.target.files[0] : null;
    
    if (!file || !file.type.match('image.*')) {
        alert('请选择有效的图片文件 (JPG, PNG, GIF)');
        avatarInput.value = ''; // 清空选择
        return;
    }

    // 检查文件大小 (例如限制 2MB)
    const maxSizeMB = 2;
    if (file.size > maxSizeMB * 1024 * 1024) {
        alert(`图片大小不能超过 ${maxSizeMB}MB`);
        avatarInput.value = ''; // 清空选择
        return;
    }

    selectedAvatarFile = file; // 存储选中的文件
    // removeAvatarFlag = false; // 不再需要

    // 显示预览
    const reader = new FileReader();
    reader.onload = function(e) {
        previewAvatarImg.src = e.target.result;
        // removeAvatarBtn.style.display = 'inline-block'; // 不再需要
    };
    reader.onerror = function(e) {
        console.error("头像文件读取错误:", e);
        alert('读取图片预览失败');
        // 调用一个简化的重置预览函数，或者直接在这里重置
        avatarInput.value = ''; 
        previewAvatarImg.src = currentUser?.avatar_url || 'https://www.yzycolour.top/prompt-examples/admin/images/default-avatar.png'; // 恢复到当前用户头像或默认
        selectedAvatarFile = null;
        // removeAvatarPreview(); // 不再需要调用这个函数
    };
    reader.readAsDataURL(file);
}

// --- 新增：保存个人资料 --- (保留)
async function saveProfile() {
    if (!currentUser) return;

    // 禁用按钮并显示加载状态
    saveProfileBtn.disabled = true;
    saveProfileBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';

    try {
        const nickname = nicknameInput.value.trim(); // 获取并清理昵称

        const formData = new FormData();
        formData.append('nickname', nickname);

        // 检查头像操作
        if (selectedAvatarFile) {
            // 如果选择了新头像，则添加文件
            
            // --- 尝试压缩头像 --- 
            saveProfileBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 压缩头像中...';
            let compressedFile = null;
            
            try {
                // 检查压缩库是否可用
                if (typeof imageCompression === 'function') {
                    const options = {
                        maxSizeMB: 0.1,         // 目标最大体积 (100KB)
                        maxWidthOrHeight: 800,  // 限制最大宽度或高度
                        useWebWorker: true,
                        fileType: 'image/jpeg', // 强制输出为 JPG 格式
                        initialQuality: 0.8
                    };
                    
                    compressedFile = await imageCompression(selectedAvatarFile, options);
                } else {
                    console.warn('图片压缩库未加载，将使用原始图片');
                    compressedFile = selectedAvatarFile;
                }
            } catch (error) {
                console.error('头像压缩失败:', error);
                compressedFile = selectedAvatarFile; // 失败时回退到原图
            }
            
            // 恢复保存中状态
            saveProfileBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
            
            // 添加头像文件到表单数据
            formData.append('avatar', compressedFile || selectedAvatarFile, selectedAvatarFile.name);
        } else {
        }

        // 发送请求到服务器
        const response = await fetch(`${API_URL}/profile`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: formData
        });

        
        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                alert('认证失败或权限不足，请重新登录。');
                logout();
                return;
            }
            
            // 尝试解析错误信息
            let errorMsg = `HTTP error ${response.status}`;
            try {
                const errorData = await response.json();
                errorMsg = errorData.message || errorData.error || errorMsg;
                console.error('错误详情:', errorData);
            } catch(e) { 
                console.error('无法解析错误响应:', e);
            }
            throw new Error(errorMsg);
        }

        // 处理成功响应
        const updatedUser = await response.json();

        // 更新前端存储和界面
        if (updatedUser && updatedUser.user) {
            // 检查要更新的字段是否存在
            if (updatedUser.user.nickname !== undefined) {
                currentUser.nickname = updatedUser.user.nickname;
            }
            if (updatedUser.user.avatar_url !== undefined) {
                currentUser.avatar_url = updatedUser.user.avatar_url;
            }
            
            // 更新 localStorage
            localStorage.setItem('user', JSON.stringify(currentUser));
            
            // 更新顶部导航栏的用户信息显示
            updateUIByRole();
            
            // 显示成功提示
            let successMessage = '个人资料已更新！';
            if (selectedAvatarFile && updatedUser.user.avatar_url) {
                successMessage = '个人资料和头像已成功更新！';
            }
            showToast(successMessage);
            
            // 关闭模态框
            const modalInstance = bootstrap.Modal.getInstance(profileModalElement);
            if (modalInstance) {
                modalInstance.hide();
            } else {
                console.warn('无法获取模态框实例');
            }
        } else {
            console.error('服务器响应格式不正确:', updatedUser);
            throw new Error('服务器响应格式不正确');
        }

    } catch (error) {
        console.error('保存个人资料错误:', error);
        alert(`保存失败: ${error.message || '请检查网络连接或联系管理员'}`);
    } finally {
        // 恢复按钮状态
        saveProfileBtn.disabled = false;
        saveProfileBtn.innerHTML = '保存更改';
        selectedAvatarFile = null;
    }
}

// ... 其他 DOM 元素获取 ... (保留)
const emailDisplay = document.getElementById('emailDisplay');
const changeEmailBtn = document.getElementById('changeEmailBtn');
const changeEmailSection = document.getElementById('changeEmailSection');
const newEmailInput = document.getElementById('newEmailInput');
const currentPasswordInput = document.getElementById('currentPasswordInput');
const cancelChangeEmailBtn = document.getElementById('cancelChangeEmailBtn');
const sendVerificationEmailBtn = document.getElementById('sendVerificationEmailBtn');
const emailChangeAlertPlaceholder = document.getElementById('emailChangeAlertPlaceholder');

// --- 新增：处理邮件修改请求 --- (保留)
async function requestEmailChange() {
    const newEmail = newEmailInput.value.trim();
    const currentPassword = currentPasswordInput.value;

    // 前端验证
    if (!newEmail || !currentPassword) {
        displayEmailChangeAlert('请输入新邮箱地址和当前密码', 'danger');
        return;
    }
    if (!validateEmail(newEmail)) { // 复用 login.html 中的验证函数 (需要确保它在 main.js 中也可用)
        displayEmailChangeAlert('请输入有效的邮箱地址格式', 'danger');
        return;
    }
     if (newEmail === currentUser.email) {
         displayEmailChangeAlert('新邮箱地址不能与当前邮箱地址相同', 'warning');
         return;
     }


    // 显示加载状态
    const spinner = sendVerificationEmailBtn.querySelector('.spinner-border');
    const originalButtonText = '发送验证邮件'; // 保存原始文本
    sendVerificationEmailBtn.disabled = true;
    if(spinner) spinner.style.display = 'inline-block';
    sendVerificationEmailBtn.childNodes[sendVerificationEmailBtn.childNodes.length - 1].nodeValue = ' 发送中...'; // 修改按钮文本

    try {
        const response = await fetch(`${API_URL}/profile/request-email-change`, {
            method: 'POST',
            headers: getAuthHeaders(), // 使用现有的认证头函数
            body: JSON.stringify({
                newEmail: newEmail,
                currentPassword: currentPassword
            })
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || `请求失败 (${response.status})`);
        }

        // 请求成功
        displayEmailChangeAlert('验证邮件已发送至您的新邮箱，请在1小时内点击邮件中的链接完成验证。', 'success');
        currentPasswordInput.value = ''; // 清空密码输入框
        // 可选：稍后自动隐藏修改区域
        // setTimeout(() => {
        //     changeEmailSection.style.display = 'none';
        //     changeEmailBtn.style.display = 'inline-block';
        // }, 5000);

    } catch (error) {
        console.error('请求修改邮箱错误:', error);
        displayEmailChangeAlert(`错误: ${error.message}`, 'danger');
    } finally {
        // 恢复按钮状态
        sendVerificationEmailBtn.disabled = false;
         if(spinner) spinner.style.display = 'none';
        sendVerificationEmailBtn.childNodes[sendVerificationEmailBtn.childNodes.length - 1].nodeValue = ` ${originalButtonText}`; // 恢复原始文本 (注意空格)
    }
}

// --- 新增：显示邮箱修改提示信息 --- (保留)
function displayEmailChangeAlert(message, type = 'info') { // type 可以是 'success', 'danger', 'warning', 'info'
    if (!emailChangeAlertPlaceholder) return;
    emailChangeAlertPlaceholder.innerHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
}

// --- 确保 validateEmail 函数可用 --- (保留)
function validateEmail(email) {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}

// 新的参数化 handleFileSelect
async function handleFileSelect(file, imageType) {
    // 压缩文件
    const maxSize = 5; // MB
    const compressedFile = await compressImageIfNeeded(file, maxSize);
    
    // 预览 
    const reader = new FileReader();
    reader.onload = function(e) {
        const preview = document.getElementById(`${imageType}Preview`);
        preview.src = e.target.result;
        preview.style.display = 'block';
    };
    reader.readAsDataURL(compressedFile);
    
    return compressedFile;
}

// SSO 跳转到 JAAZ 画布系统
async function jumpToJaazCanvas(event) {
    const btn = event.currentTarget;
    const originalHtml = btn.innerHTML;

    try {
        btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 生成链接...';
        btn.disabled = true;

        
        // 1. 请求专用的 SSO Token
        const response = await fetch(`${window.API_URL}/sso/generate-token`, {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({}) // 根据后端定义，可以发送空对象
        });
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({})); // 尝试解析错误信息
            throw new Error(errorData.message || `生成跳转链接失败: ${response.status}`);
        }
        
        const data = await response.json();
        
        // 2. 检查响应并使用返回的 URL 跳转
        if (data.success && data.redirect_url) {
            showToast('正在打开AI画布...', 'info');
        // 在新标签页中打开 JAAZ 系统
            window.open(data.redirect_url, '_blank');
        } else {
            throw new Error(data.message || '从服务器返回的响应无效。');
        }
        
    } catch (error) {
        console.error('跳转到JAAZ失败:', error);
        showToast(`跳转失败: ${error.message}`, 'danger');
    } finally {
        // 3. 无论成功失败，都恢复按钮状态
        // 短暂延迟后恢复按钮，让用户能看到提示
        setTimeout(() => {
            btn.innerHTML = originalHtml;
        btn.disabled = false;
        }, 1500);
    }
}

/**
 * 获取未读通知并更新UI
 */
async function fetchUnreadNotifications() {
    try {
        const headers = getAuthHeaders();
        if (!headers) return;

        const response = await fetch('https://caca.yzycolour.top/api/notifications', { headers });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const notifications = await response.json();
        
        const unreadNotifications = notifications.filter(n => !n.is_read);
        const unreadCount = unreadNotifications.length;

        const notificationBadge = document.getElementById('notification-badge');
        if (unreadCount > 0) {
            notificationBadge.textContent = unreadCount > 99 ? '99+' : unreadCount;
            notificationBadge.style.display = 'block';
        } else {
            notificationBadge.style.display = 'none';
        }
        
        const commentNotifications = unreadNotifications.filter(n => n.type === 'new_comment');
        const commentNotificationCount = commentNotifications.length;
        const commentCountBadge = document.getElementById('comment-notification-count');
        if (commentNotificationCount > 0) {
            commentCountBadge.textContent = commentNotificationCount;
            commentCountBadge.style.display = 'inline-block';
        } else {
            commentCountBadge.style.display = 'none';
        }

    } catch (error) {
        console.error('获取未读通知失败:', error);
    }
}

/**
 * 显示评论通知弹窗
 */
async function showCommentsNotifications() {
    const modalElement = document.getElementById('commentsNotificationModal');
    const modalBody = document.getElementById('commentsNotificationModalBody');
    const commentsModal = new bootstrap.Modal(modalElement);

    modalBody.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">加载中...</span></div><p class="mt-2">正在加载评论...</p></div>';
    commentsModal.show();

    try {
        const headers = getAuthHeaders();
        const response = await fetch('https://caca.yzycolour.top/api/notifications', { headers });
        if (!response.ok) throw new Error('Failed to fetch notifications');
        const notifications = await response.json();

        const commentNotifications = notifications.filter(n => n.type === 'new_comment');

        if (commentNotifications.length === 0) {
            modalBody.innerHTML = '<p class="text-center text-muted">您暂时没有收到任何评论通知！</p>';
            return;
        }

        let notificationsHtml = '<div class="list-group">';
        const unreadIds = [];

        commentNotifications.forEach(n => {
            const isReadClass = n.is_read ? '' : 'list-group-item-secondary';
            if (!n.is_read) {
                unreadIds.push(n.id);
            }
            const actorName = n.actor ? n.actor.nickname || n.actor.username : '一位用户';
            const promptTitle = n.data?.prompt_title || '一个案例';
            const contentSummary = n.data?.content_summary || '发表了评论。';
            
            notificationsHtml += `
                <a href="#" onclick="showExampleDetailModal(${n.entity_id}); bootstrap.Modal.getInstance(document.getElementById('commentsNotificationModal')).hide(); return false;" class="list-group-item list-group-item-action ${isReadClass}" data-notification-id="${n.id}">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${actorName} 评论了您的案例 "${promptTitle}"</h6>
                        <small>${new Date(n.created_at).toLocaleString()}</small>
                    </div>
                    <p class="mb-1 text-muted">"${contentSummary}..."</p>
                </a>
            `;
        });
        notificationsHtml += '</div>';
        modalBody.innerHTML = notificationsHtml;
        
        modalElement.addEventListener('hidden.bs.modal', async () => {
            if (unreadIds.length > 0) {
                await markNotificationsAsRead(unreadIds);
            }
        }, { once: true });

    } catch (error) {
        console.error('加载评论通知失败:', error);
        modalBody.innerHTML = '<p class="text-center text-danger">加载失败，请稍后重试。</p>';
    }
}

/**
 * 标记通知为已读
 * @param {number[]} notificationIds
 */
async function markNotificationsAsRead(notificationIds) {
    if (!notificationIds || notificationIds.length === 0) return;

    try {
        const headers = getAuthHeaders();
        headers['Content-Type'] = 'application/json';
        const response = await fetch('https://caca.yzycolour.top/api/notifications/mark-read', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify({ notificationIds })
        });

        if (!response.ok) {
            throw new Error('标记已读失败');
        }
        
        await fetchUnreadNotifications();

    } catch (error) {
        console.error('标记通知为已读时出错:', error);
    }
}