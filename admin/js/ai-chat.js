document.addEventListener('DOMContentLoaded', () => {
    // Infini-AI 官方 API 端点
    const CHAT_API_ENDPOINT = 'https://cloud.infini-ai.com/maas/v1/chat/completions';
    const MODELS_API_ENDPOINT = 'https://cloud.infini-ai.com/maas/v1/models';
    const AI_CHAT_API_BASE_URL = 'https://caca.yzycolour.top/api/ai-chat';

    // DOM Elements
    const chatTabBtn = document.getElementById('ai-chat-assistant-tab');
    const messagesContainer = document.getElementById('aiChatMessagesContainer');
    const messageInput = document.getElementById('aiChatInput');
    const sendBtn = document.getElementById('aiChatSendBtn');
    const newConversationBtn = document.getElementById('aiChatNewConversationBtn');
    const startPlaceholder = document.getElementById('aiChatStartPlaceholder');
    const tokenInfoDisplay = document.getElementById('aiChatTokenInfo');
    const currentTokensDisplay = document.getElementById('aiChatCurrentTokens');

    // --- MODIFIED: Platform Select Elements ---
    const apiPlatformSelectBtn = document.getElementById('aiChatApiPlatformSelectBtn');
    const apiPlatformSelectMenu = document.getElementById('aiChatApiPlatformSelectMenu');
    const apiPlatformSelectValue = document.getElementById('aiChatApiPlatformSelectValue');
    // --- END MODIFIED ---
    const dynamicApiKeyInputArea = document.getElementById('dynamicApiKeyInputArea');
    // We will create the actual API key input and its toggle button dynamically.

    // --- MODIFIED: Model Select Elements ---
    const modelSelectBtn = document.getElementById('aiChatModelSelectBtn');
    const modelSelectMenu = document.getElementById('aiChatModelSelectMenu');
    const modelSelectValue = document.getElementById('aiChatModelSelectValue');
    // --- END NEW ---

    const temperatureRange = document.getElementById('aiChatTemperature');
    const temperatureValueDisplay = document.getElementById('aiChatTemperatureValueDisplay');
    const topPRange = document.getElementById('aiChatTopP');
    const topPValueDisplay = document.getElementById('aiChatTopPValueDisplay');
    const maxTokensInput = document.getElementById('aiChatMaxTokens');
    const streamResponseCheckbox = document.getElementById('aiChatStreamResponse');
    const saveSettingsBtn = document.getElementById('aiChatSaveSettingsBtn');

    // History Elements
    const historyListContainer = document.getElementById('aiChatHistoryList');
    const clearHistoryBtn = document.getElementById('aiChatClearHistoryBtn');
    const historyEmptyPlaceholder = document.getElementById('aiChatHistoryEmptyPlaceholder');

    // Image Upload Elements
    const uploadImageBtn = document.getElementById('aiChatUploadImageBtn');
    const fileInput = document.getElementById('aiChatFileInput');
    const imagePreviewArea = document.getElementById('aiChatImagePreview');
    const previewImage = document.getElementById('aiChatPreviewImage');
    const removeImageBtn = document.getElementById('aiChatRemoveImageBtn');

    // --- MODIFIED: Persona Elements ---
    const personaSelectBtn = document.getElementById('aiChatPersonaSelectBtn');
    const personaSelectMenu = document.getElementById('aiChatPersonaSelectMenu');
    const personaSelectValue = document.getElementById('aiChatPersonaSelectValue');
    // --- END MODIFIED ---
    const customPersonaContainer = document.getElementById('aiChatCustomPersonaContainer');
    const customPersonaTextarea = document.getElementById('aiChatCustomPersonaTextarea');

    // Panel Collapse Elements
    const leftPanel = document.getElementById('aiChatLeftPanel');
    const rightPanelCollapse = document.getElementById('aiChatRightPanelCollapse');
    const toggleRightPanelBtn = document.getElementById('toggleAiChatRightPanelBtn');

    // Chat State
    let currentConversation = [];
    let currentConversationId = null;
    let conversationsHistory = [];
    let isLoading = false;
    let currentImageBase64 = null;

    const MAX_CONVERSATION_HISTORY_FOR_API = 20; // Added for Infini-AI history limit

    // --- NEW: API Platform Definitions ---
    const API_PLATFORMS = {
        'infini': {
            id: 'infini',
            displayName: 'Infini-AI',
            apiKeyLocalStorageKey: 'aiChatApiKey_infini',
            apiKeyPlaceholder: 'sk-...',
            helpText: 'API Key 将安全地存储在您的浏览器本地。',
            helpLinkText: '点击这里',
            helpLinkUrl: 'https://cloud.infini-ai.com/iam/secret/key',
            fetchModelsFunction: fetchAvailableModels // Specific function to fetch Infini-AI models
        },
        'gemini': {
            id: 'gemini',
            displayName: 'Google Gemini',
            apiKeyLocalStorageKey: 'aiChatApiKey_gemini',
            apiKeyPlaceholder: '输入您的 Gemini API Key...',
            helpText: 'API Key 将安全地存储在您的浏览器本地。',
            helpLinkText: '点击这里',
            helpLinkUrl: 'https://aistudio.google.com/app/apikey'
            // Gemini models are currently static in updateModelDropdown
        },
        'openai': {
            id: 'openai',
            displayName: 'OpenAI GPT',
            apiKeyLocalStorageKey: 'aiChatApiKey_openai',
            apiKeyPlaceholder: 'sk-...',
            helpText: 'API Key 将安全地存储在您的浏览器本地。',
            helpLinkText: 'OpenAI Platform',
            helpLinkUrl: 'https://platform.openai.com/api-keys'
            // OpenAI models will also be static for now or fetched if API exists
        }
    };
    const DEFAULT_PLATFORM_ID = 'infini';
    const DYNAMIC_API_KEY_INPUT_ID = 'currentPlatformApiKeyInput';
    const DYNAMIC_API_KEY_TOGGLE_BTN_ID = 'currentPlatformApiKeyToggleBtn';
    // --- END NEW ---


    const predefinedPersonas = {
        "none": "无 (不设置特定人设)",
        "helpful_assistant": "乐于助人的助手",
        "code_expert": "编程专家",
        "creative_writer": "创意写手",
        "english_teacher": "英语口语老师",
        "custom": "自定义..."
    };

    const personaPrompts = {
        "helpful_assistant": "你是一个乐于助人、耐心且专业的AI助手。",
        "code_expert": "你是一位世界顶级的软件工程师和编程专家，精通所有主流编程语言、框架和算法。请提供清晰、准确、可直接运行的代码示例，并解释复杂概念。",
        "creative_writer": "你是一位才华横溢的创意作家，擅长撰写引人入胜的故事、诗歌、剧本和营销文案。你的文字富有想象力且充满感染力。",
        "english_teacher": "You are a patient and experienced English speaking teacher. Your goal is to help me practice and improve my spoken English. Please correct my grammar and pronunciation, suggest better vocabulary, and engage in natural conversation. Start by greeting me and asking how I am doing or what I'd like to talk about."
        // "custom" will take text from customPersonaTextarea
    };

    if (typeof marked !== 'undefined') {
        marked.setOptions({ gfm: true, breaks: true, langPrefix: 'language-' });
    } else {
        console.error("Marked.js library is not loaded!");
    }

    function escapeHtml(unsafe) {
        if (typeof unsafe !== 'string') return '';
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    function extractJsonObjectsForGeminiStream(strBuffer) {
        const jsonObjects = [];
        let searchIndex = 0;
        while (searchIndex < strBuffer.length) {
            const firstBrace = strBuffer.indexOf('{', searchIndex);
            if (firstBrace === -1) break; 

            let openBraces = 0;
            let currentObjectEnd = -1;

            for (let i = firstBrace; i < strBuffer.length; i++) {
                if (strBuffer[i] === '{') {
                    openBraces++;
                } else if (strBuffer[i] === '}') {
                    openBraces--;
                }
                if (openBraces === 0) {
                    currentObjectEnd = i + 1;
                    break;
                }
            }

            if (currentObjectEnd !== -1) {
                try {
                    const jsonStr = strBuffer.substring(firstBrace, currentObjectEnd);
                    jsonObjects.push(JSON.parse(jsonStr));
                    searchIndex = currentObjectEnd;
                } catch (e) {
                    // Invalid JSON, break or try to find next valid start
                    // For simplicity, we'll assume valid JSON objects are sequential
                    // and an error means the buffer might be incomplete for this object.
                    // console.warn("Could not parse JSON object from stream buffer fragment:", jsonStr, e);
                    searchIndex = firstBrace + 1; // Move past the problematic brace to avoid infinite loop on malformed JSON
                    break; // Assume the rest of the buffer is not yet a complete object
                }
            } else {
                // No closing brace found, buffer might be incomplete
                break;
            }
        }
        // Return the parsed objects and the remaining part of the buffer
        const remainingBuffer = searchIndex < strBuffer.length ? strBuffer.substring(searchIndex) : "";
        return { objects: jsonObjects, remainingBuffer: remainingBuffer };
    }


    // --- Helper: Fetch Available Infini-AI Models ---
    async function fetchAvailableModels(apiKey) { // Renamed for clarity, specific to Infini-AI
        if (!apiKey) {
            console.warn("Infini-AI API Key is missing, cannot fetch models.");
            return null;
        }
        try {
            const response = await fetch(MODELS_API_ENDPOINT, { // MODELS_API_ENDPOINT is Infini-AI's
                method: 'GET',
                headers: { 'Accept': 'application/json', 'Authorization': `Bearer ${apiKey}` }
            });
            if (!response.ok) {
                if (response.status === 401) console.error('Failed to fetch Infini-AI models: Invalid API Key (401).');
                else console.error(`Failed to fetch Infini-AI models: ${response.status} ${response.statusText}`);
                return null;
            }
            const data = await response.json();
            return (data && data.object === 'list' && Array.isArray(data.data)) ? data.data : null;
        } catch (error) {
            console.error('Error fetching Infini-AI models:', error);
            return null;
        }
    }

    // --- Helper: Populate Model Select Dropdown ---
    function updateModelDropdown(infiniModelsData = null, currentPlatformId = DEFAULT_PLATFORM_ID) {
        const previouslySelectedModel = localStorage.getItem(`aiChatModel_${currentPlatformId}`) || localStorage.getItem('aiChatModel_infini'); // Fallback for old key
        const defaultModelId = currentPlatformId === 'infini' ? 'deepseek-r1' : (currentPlatformId === 'gemini' ? 'gemini-1.5-flash-latest' : ''); // Example defaults

        modelSelectMenu.innerHTML = ''; // Clear previous items

        let availableModels = [];

        if (currentPlatformId === 'infini' && infiniModelsData && infiniModelsData.length > 0) {
            availableModels = availableModels.concat(infiniModelsData.map(m => ({ id: m.id, name: m.id, group: 'Infini-AI' })));
        } else if (currentPlatformId === 'gemini') {
            // GEMINI 1.5 Series
            availableModels.push({ id: 'gemini-1.5-flash-latest', name: 'Gemini 1.5 Flash (Google)', group: 'GEMINI 1.5' });
            availableModels.push({ id: 'gemini-1.5-pro-latest', name: 'Gemini 1.5 Pro (Google)', group: 'GEMINI 1.5' });

            // GEMINI 2.0 / 2.5 Series (Adjusted based on error feedback and script.js conventions)
            availableModels.push({ id: 'gemini-2.0-flash', name: 'Gemini 2.0 Flash', group: 'GEMINI 2.0' }); // Text model
            availableModels.push({ id: 'gemini-2.0-flash-preview-image-generation', name: 'Gemini 2.0 Flash Image Generation', group: 'GEMINI 2.0 (Image)' }); // Image model, needs response_modalities

            // Corrected Gemini 2.5 Pro from Preview to Experimental (Free Tier)
            availableModels.push({ id: 'gemini-2.5-pro-exp-03-25', name: 'Gemini 2.5 Pro Exp (03-25) [Free Tier]', group: 'GEMINI 2.5' });
            
            // Keeping this if it's a distinct text model and works. If it's also an image model, it would need response_modalities.
            // For now, assuming gemini-2.5-flash-preview-04-17 is a text model or has its own specific handling if image-capable.
            // From user screenshot this existed.
            availableModels.push({ id: 'gemini-2.5-flash-preview-04-17', name: 'Gemini 2.5 Flash Preview 04-17', group: 'GEMINI 2.5' });


            // Example of how you might add other Gemini models if they become available/relevant:
            // availableModels.push({ id: 'gemini-X.X-some-other-model', name: 'Gemini X.X Some Other (Google)', group: 'GEMINI X.X' });

        } else if (currentPlatformId === 'openai' && openaiModelsData && openaiModelsData.length > 0) { // Assuming openaiModelsData exists
            availableModels.push({ id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo (OpenAI)', group: 'OpenAI GPT' });
            availableModels.push({ id: 'gpt-4', name: 'GPT-4 (OpenAI)', group: 'OpenAI GPT' });
            availableModels.push({ id: 'gpt-4-turbo', name: 'GPT-4 Turbo (OpenAI)', group: 'OpenAI GPT' });
            availableModels.push({ id: 'gpt-4o', name: 'GPT-4 Omni (OpenAI)', group: 'OpenAI GPT' });
            // Add other static OpenAI models
        }

        if (availableModels.length > 0) {
            const groupedModels = availableModels.reduce((acc, model) => {
                const groupName = model.group || 'Other Models';
                if (!acc[groupName]) acc[groupName] = [];
                acc[groupName].push(model);
                return acc;
            }, {});

            Object.keys(groupedModels).sort().forEach(groupName => {
                const optgroupHeader = document.createElement('li');
                optgroupHeader.innerHTML = `<h6 class="dropdown-header">${escapeHtml(groupName)}</h6>`;
                modelSelectMenu.appendChild(optgroupHeader);

                groupedModels[groupName].forEach(model => {
                    const listItem = document.createElement('li');
                    const link = document.createElement('a');
                    link.classList.add('dropdown-item');
                    link.href = '#';
                    link.dataset.value = model.id;
                    link.textContent = model.name;
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        modelSelectBtn.textContent = model.name;
                        modelSelectValue.value = model.id;
                        // Optionally, trigger a custom event or call a function if needed on selection change
                    });
                    listItem.appendChild(link);
                    modelSelectMenu.appendChild(listItem);
                });
            });

            let modelToSelectId = defaultModelId;
            let modelToSelectName = '';
            const allModelIds = availableModels.map(m => m.id);

            if (previouslySelectedModel && allModelIds.includes(previouslySelectedModel)) {
                modelToSelectId = previouslySelectedModel;
            } else if (!allModelIds.includes(defaultModelId) && availableModels.length > 0) {
                modelToSelectId = availableModels[0].id;
            }

            const selectedModelData = availableModels.find(m => m.id === modelToSelectId);
            if (selectedModelData) {
                modelToSelectName = selectedModelData.name;
            } else if (availableModels.length > 0) { // Fallback if specific model not found but list not empty
                modelToSelectId = availableModels[0].id;
                modelToSelectName = availableModels[0].name;
            }

            modelSelectBtn.textContent = modelToSelectName || '选择模型';
            modelSelectValue.value = modelToSelectId;
            modelSelectBtn.disabled = false;

        } else {
            const apiKeyInput = document.getElementById(DYNAMIC_API_KEY_INPUT_ID);
            const placeholderText = (apiKeyInput && apiKeyInput.value.trim()) ? "加载模型失败或无可用模型" : "选择平台并输入API Key以加载模型";
            modelSelectBtn.textContent = placeholderText;
            modelSelectValue.value = "";
            const listItem = document.createElement('li');
            const link = document.createElement('a');
            link.classList.add('dropdown-item', 'disabled');
            link.href = '#';
            link.textContent = placeholderText;
            listItem.appendChild(link);
            modelSelectMenu.appendChild(listItem);
            modelSelectBtn.disabled = true;
        }
    }


    function enhanceCodeBlocks(containerElement) {
        const codeBlocks = containerElement.querySelectorAll('pre'); // Find all <pre> elements

        codeBlocks.forEach(preElement => {
            // Check if a button hasn't already been added
            if (preElement.parentNode.classList.contains('code-block-wrapper')) {
                 return; // Already enhanced
            }
            
            const codeElement = preElement.querySelector('code');
            // Marked.js might sometimes create <pre> without <code> if language is unknown? Be safe.
            const codeTextToCopy = codeElement ? codeElement.textContent : preElement.textContent;
            if (!codeTextToCopy) return; // Skip if no text content found


            // 1. Create a wrapper div
            const wrapper = document.createElement('div');
            wrapper.className = 'code-block-wrapper';

            // 2. Create the copy button
            const copyBtn = document.createElement('button');
            copyBtn.className = 'btn btn-sm btn-outline-secondary copy-code-btn';
            copyBtn.innerHTML = '<i class="bi bi-clipboard"></i>';
            copyBtn.title = '复制代码';

            // 3. Insert wrapper before pre, move pre inside wrapper, add button
            // Check if preElement has a parentNode before inserting (safety check)
            if (preElement.parentNode) {
                preElement.parentNode.insertBefore(wrapper, preElement);
                wrapper.appendChild(preElement);
                wrapper.appendChild(copyBtn);
            } else {
                // If preElement somehow lost its parent (shouldn't happen here), log error
                console.error("preElement lost its parent before enhancement:", preElement);
                return; // Skip enhancement for this block
            }


            // 4. Add click listener to the button
            copyBtn.addEventListener('click', () => {
                navigator.clipboard.writeText(codeTextToCopy).then(() => {
                    // Success feedback
                    copyBtn.innerHTML = '<i class="bi bi-check-lg"></i>';
                    copyBtn.title = '已复制!';
                    copyBtn.classList.remove('btn-danger'); // Ensure error style is removed
                    copyBtn.classList.add('btn-success'); 

                    setTimeout(() => {
                        copyBtn.innerHTML = '<i class="bi bi-clipboard"></i>';
                        copyBtn.title = '复制代码';
                        copyBtn.classList.remove('btn-success');
                    }, 2000); // Reset after 2 seconds
                }).catch(err => {
                    console.error('无法复制代码到剪贴板:', err);
                    copyBtn.innerHTML = '<i class="bi bi-x-lg"></i>'; // Error icon
                    copyBtn.title = '复制失败';
                    copyBtn.classList.remove('btn-success');
                    copyBtn.classList.add('btn-danger');
                     setTimeout(() => {
                        copyBtn.innerHTML = '<i class="bi bi-clipboard"></i>';
                        copyBtn.title = '复制代码';
                        copyBtn.classList.remove('btn-danger');
                    }, 3000);
                });
            });
        });
    }

    // --- NEW: Render API Key Input for Selected Platform ---
    function renderApiKeyInputForPlatform(platformId) {
        if (!dynamicApiKeyInputArea || !API_PLATFORMS[platformId]) return;

        const platform = API_PLATFORMS[platformId];
        dynamicApiKeyInputArea.innerHTML = ''; // Clear previous content

        const label = document.createElement('label');
        label.setAttribute('for', DYNAMIC_API_KEY_INPUT_ID);
        label.classList.add('form-label');
        label.textContent = `${platform.displayName} API Key`;

        const inputGroup = document.createElement('div');
        inputGroup.classList.add('input-group');

        const apiKeyInput = document.createElement('input');
        apiKeyInput.type = 'password';
        apiKeyInput.classList.add('form-control', 'form-control-sm');
        apiKeyInput.id = DYNAMIC_API_KEY_INPUT_ID;
        apiKeyInput.placeholder = platform.apiKeyPlaceholder;
        apiKeyInput.value = localStorage.getItem(platform.apiKeyLocalStorageKey) || '';

        const toggleBtn = document.createElement('button');
        toggleBtn.classList.add('btn', 'btn-sm', 'btn-outline-secondary');
        toggleBtn.type = 'button';
        toggleBtn.id = DYNAMIC_API_KEY_TOGGLE_BTN_ID;
        toggleBtn.title = '显示/隐藏 API Key';
        toggleBtn.innerHTML = '<i class="bi bi-eye-fill"></i>';
        toggleBtn.addEventListener('click', () => toggleApiKeyVisibility(apiKeyInput, toggleBtn));

        inputGroup.appendChild(apiKeyInput);
        inputGroup.appendChild(toggleBtn);

        const helpTextSmall = document.createElement('small');
        helpTextSmall.classList.add('form-text', 'text-muted');
        helpTextSmall.textContent = platform.helpText;

        const helpLinkSmall = document.createElement('small');
        helpLinkSmall.classList.add('form-text', 'text-muted', 'mt-1');
        if (platform.helpLinkUrl && platform.helpLinkText) {
            const link = document.createElement('a');
            link.href = platform.helpLinkUrl;
            link.target = '_blank';
            link.rel = 'noopener noreferrer';
            link.classList.add('text-decoration-underline');
            link.textContent = platform.helpLinkText;
            
            let prefixText = '';
            if (platform.id === 'infini')  prefixText = '不知道 API Key 在哪里？';
            else if (platform.id === 'gemini') prefixText = '从 ';
            else if (platform.id === 'openai') prefixText = '从 ';

            if (prefixText) helpLinkSmall.appendChild(document.createTextNode(prefixText));
            helpLinkSmall.appendChild(link);
            if (platform.id === 'infini') helpLinkSmall.appendChild(document.createTextNode(` 获取 ${platform.displayName} API Key。`));
            else if (platform.id === 'gemini') helpLinkSmall.appendChild(document.createTextNode(` 获取 ${platform.displayName} API Key。`));
            else if (platform.id === 'openai') helpLinkSmall.appendChild(document.createTextNode(` 获取您的 API Key。`));

        }

        dynamicApiKeyInputArea.appendChild(label);
        dynamicApiKeyInputArea.appendChild(inputGroup);
        dynamicApiKeyInputArea.appendChild(helpTextSmall);
        if (platform.helpLinkUrl) {
            dynamicApiKeyInputArea.appendChild(helpLinkSmall);
        }
    }
    // --- END NEW ---

    async function initializeChat() {
        // Populate platform select dropdown
        apiPlatformSelectMenu.innerHTML = ''; // Clear placeholder or existing items
        for (const platformId in API_PLATFORMS) {
            const listItem = document.createElement('li');
            const link = document.createElement('a');
            link.classList.add('dropdown-item');
            link.href = '#';
            link.dataset.value = API_PLATFORMS[platformId].id;
            link.textContent = API_PLATFORMS[platformId].displayName;
            link.addEventListener('click', async (e) => {
                e.preventDefault();
                apiPlatformSelectBtn.textContent = API_PLATFORMS[platformId].displayName;
                apiPlatformSelectValue.value = API_PLATFORMS[platformId].id;
                await handlePlatformChange(API_PLATFORMS[platformId].id); // Call handler after selection
            });
            listItem.appendChild(link);
            apiPlatformSelectMenu.appendChild(listItem);
        }

        loadSettings(); // Loads API keys, selected platform, persona, etc.
        initializePersonaUI();

        const currentPlatformId = apiPlatformSelectValue.value; // Value is now set by loadSettings or default
        renderApiKeyInputForPlatform(currentPlatformId); // Render input for the loaded/default platform
        
        // Attempt to load models based on the current platform and its key
        const currentApiKeyInput = document.getElementById(DYNAMIC_API_KEY_INPUT_ID);
        const currentApiKey = currentApiKeyInput ? currentApiKeyInput.value.trim() : '';

        if (API_PLATFORMS[currentPlatformId] && API_PLATFORMS[currentPlatformId].fetchModelsFunction && currentApiKey) {
            const models = await API_PLATFORMS[currentPlatformId].fetchModelsFunction(currentApiKey);
            updateModelDropdown(models, currentPlatformId);
        } else {
            // For platforms without a fetchModelsFunction (like Gemini/OpenAI static lists) or no API key
            updateModelDropdown(null, currentPlatformId);
        }

        await loadHistory();
        renderHistory();

        if (!currentConversationId) { 
            messagesContainer.innerHTML = '';
            startPlaceholder.style.display = 'block';
            tokenInfoDisplay.style.display = 'none';
            currentTokensDisplay.textContent = '0';
            messageInput.value = '';
            adjustTextareaHeight(messageInput);
            removeImage();
            currentConversation = [];
            // currentConversationId remains null, indicating a new, unsaved conversation
        }

        if (chatTabBtn) {
            chatTabBtn.addEventListener('shown.bs.tab', () => {
                messageInput.focus();
                adjustTextareaHeight(messageInput);
            });
        }

        uploadImageBtn.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', handleImageSelect);
        removeImageBtn.addEventListener('click', removeImage);

        if (rightPanelCollapse && leftPanel && toggleRightPanelBtn) {
            rightPanelCollapse.addEventListener('show.bs.collapse', () => {
                leftPanel.classList.remove('col-md-12');
                leftPanel.classList.add('col-md-8');
                toggleRightPanelBtn.title = '收起设置与历史面板';
                toggleRightPanelBtn.innerHTML = '<i class="bi bi-layout-sidebar-inset-reverse"></i>';
            });

            rightPanelCollapse.addEventListener('hide.bs.collapse', () => {
                leftPanel.classList.remove('col-md-8');
                leftPanel.classList.add('col-md-12');
                toggleRightPanelBtn.title = '展开设置与历史面板';
                toggleRightPanelBtn.innerHTML = '<i class="bi bi-layout-sidebar-inset"></i>';
            });
        }
    }


    function initializePersonaUI() {
        if (!personaSelectBtn || !personaSelectMenu || !personaSelectValue) return; // Guard if elements don't exist

        personaSelectMenu.innerHTML = ''; // Clear existing options
        for (const key in predefinedPersonas) {
            const listItem = document.createElement('li');
            const link = document.createElement('a');
            link.classList.add('dropdown-item');
            link.href = '#';
            link.dataset.value = key;
            link.textContent = predefinedPersonas[key];
            link.addEventListener('click', (e) => {
                e.preventDefault();
                personaSelectBtn.textContent = predefinedPersonas[key];
                personaSelectValue.value = key;
                customPersonaContainer.style.display = key === 'custom' ? 'block' : 'none';
                if (key === 'custom') {
                    customPersonaTextarea.focus();
                }
            });
            listItem.appendChild(link);
            personaSelectMenu.appendChild(listItem);
        }

        // Load saved persona or default
        const savedPersonaKey = localStorage.getItem('aiChatSelectedPersona_infini') || 'helpful_assistant';
        const savedCustomPersonaText = localStorage.getItem('aiChatCustomPersonaText_infini') || '';

        let selectedPersonaText = predefinedPersonas[savedPersonaKey] || predefinedPersonas['helpful_assistant'];
        let selectedPersonaKeyValue = savedPersonaKey;

        if (!predefinedPersonas[savedPersonaKey]) {
            selectedPersonaKeyValue = 'helpful_assistant';
        }
        
        personaSelectBtn.textContent = selectedPersonaText;
        personaSelectValue.value = selectedPersonaKeyValue;
        customPersonaTextarea.value = savedCustomPersonaText;

        // Show/hide custom textarea based on initial selection
        customPersonaContainer.style.display = personaSelectValue.value === 'custom' ? 'block' : 'none';

        // Event listener for persona change (now handled by individual link clicks)
    }

    // --- Settings Management ---
    function loadSettings() {
        const savedPlatformId = localStorage.getItem('aiChatSelectedPlatform') || DEFAULT_PLATFORM_ID;
        const platformData = API_PLATFORMS[savedPlatformId];
        if (platformData) {
            apiPlatformSelectBtn.textContent = platformData.displayName;
            apiPlatformSelectValue.value = savedPlatformId;
        } else { // Fallback to default if saved is invalid
            apiPlatformSelectBtn.textContent = API_PLATFORMS[DEFAULT_PLATFORM_ID].displayName;
            apiPlatformSelectValue.value = DEFAULT_PLATFORM_ID;
        }

        // API keys themselves are loaded by renderApiKeyInputForPlatform via its call to localStorage

        // Load Model for current platform
        const currentPlatformForLoad = apiPlatformSelectValue.value; // Use hidden input
        const savedModel = localStorage.getItem(`aiChatModel_${currentPlatformForLoad}`);
        if (savedModel) {
            // Attempt to set it. updateModelDropdown will later try to find the name.
            // If the model list isn't populated yet, this value will be used by updateModelDropdown.
            modelSelectValue.value = savedModel;
            // We will rely on updateModelDropdown to set the button text after models are fetched.
        } else {
            modelSelectValue.value = ''; // Ensure it's cleared if nothing saved for this platform
        }

        // Load Persona
        const savedPersona = localStorage.getItem('aiChatSelectedPersona_infini') || 'helpful_assistant';
        const personaText = predefinedPersonas[savedPersona];
        if (personaText) {
            personaSelectBtn.textContent = personaText;
            personaSelectValue.value = savedPersona;
        } else {
            personaSelectBtn.textContent = predefinedPersonas['helpful_assistant'];
            personaSelectValue.value = 'helpful_assistant';
        }
        customPersonaContainer.style.display = personaSelectValue.value === 'custom' ? 'block' : 'none';
        const savedCustomPersona = localStorage.getItem('aiChatCustomPersonaText_infini');
        if (savedCustomPersona) customPersonaTextarea.value = savedCustomPersona;


        const savedTemperature = localStorage.getItem('aiChatTemperature_infini'); // Keep generic for now
        const savedTopP = localStorage.getItem('aiChatTopP_infini');
        const savedMaxTokens = localStorage.getItem('aiChatMaxTokens_infini');
        const savedStreamResponse = localStorage.getItem('aiChatStreamResponse_infini');

        if (savedTemperature) { temperatureRange.value = savedTemperature; temperatureValueDisplay.textContent = savedTemperature; }
        if (savedTopP) { topPRange.value = savedTopP; topPValueDisplay.textContent = savedTopP; }
        if (savedMaxTokens) maxTokensInput.value = savedMaxTokens;
        if (savedStreamResponse !== null) streamResponseCheckbox.checked = savedStreamResponse === 'true';
    }

    async function saveSettings() {
        const currentPlatformId = apiPlatformSelectValue.value; // Use hidden input
        const platformApiKeyInput = document.getElementById(DYNAMIC_API_KEY_INPUT_ID);

        if (platformApiKeyInput && API_PLATFORMS[currentPlatformId]) {
            localStorage.setItem(API_PLATFORMS[currentPlatformId].apiKeyLocalStorageKey, platformApiKeyInput.value.trim());
        }
        localStorage.setItem('aiChatSelectedPlatform', currentPlatformId);
        localStorage.setItem(`aiChatModel_${currentPlatformId}`, modelSelectValue.value); // Use hidden input value
        localStorage.setItem('aiChatTemperature_infini', temperatureRange.value); // Keep generic for now
        localStorage.setItem('aiChatTopP_infini', topPRange.value);
        localStorage.setItem('aiChatMaxTokens_infini', maxTokensInput.value.trim());
        localStorage.setItem('aiChatStreamResponse_infini', streamResponseCheckbox.checked);

        if (personaSelectValue) localStorage.setItem('aiChatSelectedPersona_infini', personaSelectValue.value); // Use hidden input value
        if (customPersonaTextarea) localStorage.setItem('aiChatCustomPersonaText_infini', customPersonaTextarea.value.trim());

        // Re-fetch/update models for the current platform if its API key might have changed
        const currentApiKey = platformApiKeyInput ? platformApiKeyInput.value.trim() : '';
        if (API_PLATFORMS[currentPlatformId] && API_PLATFORMS[currentPlatformId].fetchModelsFunction && currentApiKey) {
            const models = await API_PLATFORMS[currentPlatformId].fetchModelsFunction(currentApiKey);
            updateModelDropdown(models, currentPlatformId);
        } else {
            updateModelDropdown(null, currentPlatformId); // Update with static models or "enter key" message
        }

        const originalBtnText = saveSettingsBtn.innerHTML;
        saveSettingsBtn.innerHTML = '<i class="bi bi-check-lg"></i> 已保存';
        saveSettingsBtn.classList.add('btn-success');
        saveSettingsBtn.classList.remove('btn-primary');
        setTimeout(() => {
            saveSettingsBtn.innerHTML = originalBtnText;
            saveSettingsBtn.classList.remove('btn-success');
            saveSettingsBtn.classList.add('btn-primary');
        }, 1500);
    }

    // --- NEW: Platform Select Change Handler (Function to be called on click) ---
    async function handlePlatformChange(newPlatformId) {
        // Note: No need to save the *old* platform's key here explicitly on change,
        // because `saveSettings` will handle saving the *currently displayed* key
        // when the "Save Settings" button is clicked. If we save on every dropdown change,
        // it might be unexpected if the user is just browsing platforms without intending to save yet.
        // However, if immediate saving on platform switch is desired, that logic would go here.
        localStorage.setItem('aiChatSelectedPlatform', newPlatformId); // Save selected platform immediately on change from dropdown click
        renderApiKeyInputForPlatform(newPlatformId);

        // Fetch/update models for the newly selected platform
        const newApiKeyInput = document.getElementById(DYNAMIC_API_KEY_INPUT_ID);
        const newApiKey = newApiKeyInput ? newApiKeyInput.value.trim() : '';

        if (API_PLATFORMS[newPlatformId] && API_PLATFORMS[newPlatformId].fetchModelsFunction && newApiKey) {
            const models = await API_PLATFORMS[newPlatformId].fetchModelsFunction(newApiKey);
            updateModelDropdown(models, newPlatformId);
        } else {
            updateModelDropdown(null, newPlatformId); // Handles static lists or no key
        }
    }
    // Remove old event listener: apiPlatformSelect.addEventListener('change', async () => { ... });
    // The logic is now in handlePlatformChange and called from the link click event listeners.
    // --- END NEW ---


    function getActivePersonaPrompt() {
        if (!personaSelectValue) return null; // Use the hidden input's value

        const selected = personaSelectValue.value;
        if (selected === 'custom') {
            return customPersonaTextarea.value.trim() || null; // Return null if custom is empty
        }
        if (selected === 'none') {
            return null;
        }
        return personaPrompts[selected] || null;
    }

    async function loadHistory() {
        conversationsHistory = [];
        const token = getAuthToken();
        if (!token) {
            console.warn("用户未登录，无法加载聊天历史。");
            updateModelDropdown(null);
            renderHistory();
            return;
        }

        try {
            const data = await fetchWithAuth(`${AI_CHAT_API_BASE_URL}/conversations`);
            if (data && Array.isArray(data)) {
                conversationsHistory = data.map(convo => ({
                    ...convo,
                    timestamp: new Date(convo.last_active_at).getTime()
                }));
            } else {
                conversationsHistory = [];
            }
        } catch (error) {
            console.error('加载聊天历史失败:', error);
            conversationsHistory = [];
        }
    }

    function renderHistory() {
        historyListContainer.innerHTML = '';
        if (conversationsHistory.length === 0) {
            const token = getAuthToken();
            historyEmptyPlaceholder.textContent = token ? '暂无历史会话' : '请先登录以查看或同步历史会话';
            historyEmptyPlaceholder.style.display = 'block';
            clearHistoryBtn.style.display = 'none';
            return;
        }
        historyEmptyPlaceholder.style.display = 'none';
        clearHistoryBtn.style.display = 'inline-block';

        const sortedHistory = [...conversationsHistory].sort((a, b) => b.timestamp - a.timestamp);

        sortedHistory.forEach(convo => {
            const historyItem = document.createElement('a');
            historyItem.href = '#';
            historyItem.dataset.conversationId = convo.id; // Add data-conversation-id
            historyItem.classList.add('list-group-item', 'list-group-item-action', 'd-flex', 'justify-content-between', 'align-items-center');
            if (convo.id === currentConversationId) {
                historyItem.classList.add('active');
            }

            const titleDiv = document.createElement('div');
            const titleSpan = document.createElement('span');
            titleSpan.classList.add('history-item-title');
            titleSpan.textContent = convo.title || `对话 ${new Date(convo.timestamp).toLocaleString()}`;
            const dateSpan = document.createElement('small');
            dateSpan.classList.add('history-item-date', 'text-muted');
            dateSpan.textContent = new Date(convo.timestamp).toLocaleDateString();
            titleDiv.appendChild(titleSpan);
            titleDiv.appendChild(dateSpan);

            const actionsDiv = document.createElement('div');
            actionsDiv.classList.add('history-item-actions');
            const deleteBtn = document.createElement('button');
            deleteBtn.classList.add('btn', 'btn-sm', 'btn-icon', 'btn-delete-history-item');
            deleteBtn.innerHTML = '<i class="bi bi-trash3"></i>';
            deleteBtn.title = '删除此对话';
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                deleteConversation(convo.id);
            });
            actionsDiv.appendChild(deleteBtn);

            historyItem.appendChild(titleDiv);
            historyItem.appendChild(actionsDiv);

            historyItem.addEventListener('click', (e) => {
                e.preventDefault();
                loadConversation(convo.id);
            });
            historyListContainer.appendChild(historyItem);
        });
    }

    async function startNewConversation() {
        const token = getAuthToken();
        if (!token) {
            addMessageToUI('system', '错误：用户未登录，无法开始新对话。请先登录。');
            return;
        }

        messageInput.value = '';
        adjustTextareaHeight(messageInput);
        removeImage();
        currentConversation = [];
        currentConversationId = null; // Reset, do not create on backend yet
        
        messagesContainer.innerHTML = '';
        startPlaceholder.style.display = 'block';
        tokenInfoDisplay.style.display = 'none';
        currentTokensDisplay.textContent = '0';
        messageInput.focus();
        
        // Deselect any active item in the history list
        const historyItems = historyListContainer.querySelectorAll('.list-group-item.active');
        historyItems.forEach(item => item.classList.remove('active'));
        // No need to call loadHistory or renderHistory as nothing is saved to backend yet.
    }

    async function loadConversation(id) {
        if (!id) {
            console.warn("尝试加载无效的对话ID");
            // Instead of starting a new one, just clear the view or show a message
            messagesContainer.innerHTML = '';
            startPlaceholder.style.display = 'block';
            tokenInfoDisplay.style.display = 'none';
            currentTokensDisplay.textContent = '0';
            currentConversation = [];
            currentConversationId = null;
            const activeHistoryItem = historyListContainer.querySelector('.list-group-item.active');
            if (activeHistoryItem) activeHistoryItem.classList.remove('active');
            return;
        }
        const token = getAuthToken();
        if (!token) {
            addMessageToUI('system', '错误：用户未登录，无法加载对话。请先登录。');
            return;
        }

        try {
            const messages = await fetchWithAuth(`${AI_CHAT_API_BASE_URL}/conversations/${id}/messages`);
            const conversationDetails = conversationsHistory.find(c => c.id === id);

            currentConversationId = id;
            currentConversation = [];
            messagesContainer.innerHTML = '';
            startPlaceholder.style.display = 'none';

            if (messages && Array.isArray(messages)) {
                messages.forEach(msg => {
                    let textContent = '';
                    let imageBase64 = null;
                    let reasoning = msg.reasoning;

                    if (msg.role === 'user') {
                        if (Array.isArray(msg.content)) {
                            const textPart = msg.content.find(part => part.type === 'text');
                            const imagePart = msg.content.find(part => part.type === 'image_url');
                            textContent = textPart ? textPart.text : '';
                            if (imagePart && imagePart.image_url && imagePart.image_url.url) {
                                imageBase64 = imagePart.image_url.url;
                                if (!textContent) textContent = '(图片)';
                            }
                        } else if (typeof msg.content === 'object' && msg.content.type === 'text') {
                            textContent = msg.content.text;
                        } else {
                            textContent = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
                        }
                    } else {
                        if (typeof msg.content === 'object' && msg.content.type === 'text') {
                            textContent = msg.content.text;
                        } else {
                            textContent = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
                        }
                    }
                    addMessageToUI(msg.role, textContent, reasoning, imageBase64);
                    currentConversation.push({
                        role: msg.role,
                        content: msg.content,
                        reasoning: msg.reasoning
                    });
                });
            }
            updateTokenCount();
            messageInput.focus();
            
            const historyItems = historyListContainer.querySelectorAll('.list-group-item');
            historyItems.forEach(item => item.classList.remove('active'));
            
            const newlyActiveItem = historyListContainer.querySelector(`a.list-group-item[data-conversation-id='${id}']`);
            if (newlyActiveItem) {
                newlyActiveItem.classList.add('active');
            } else {
                 console.warn(`Loaded conversation ${id} but could not find it in the history list to mark active.`);
            }

        } catch (error) {
            console.error(`加载对话 ${id} 失败:`, error);
            addMessageToUI('system', `错误：无法加载对话内容。${error.message}`);
            // Reset to a clean state rather than forcing a new conversation creation
            messagesContainer.innerHTML = '';
            startPlaceholder.style.display = 'block';
            tokenInfoDisplay.style.display = 'none';
            currentTokensDisplay.textContent = '0';
            currentConversation = [];
            currentConversationId = null;
            const activeHistoryItem = historyListContainer.querySelector('.list-group-item.active');
            if (activeHistoryItem) activeHistoryItem.classList.remove('active');
        }
    }

    async function deleteConversation(id) {
        if (!id) return;
        const token = getAuthToken();
        if (!token) {
            addMessageToUI('system', '错误：用户未登录，无法删除对话。');
            return;
        }

        if (confirm('确定要删除此对话吗？此操作无法撤销。')) {
            try {
                await fetchWithAuth(`${AI_CHAT_API_BASE_URL}/conversations/${id}`, { method: 'DELETE' });
                conversationsHistory = conversationsHistory.filter(convo => convo.id !== id);
                renderHistory();
                if (currentConversationId === id) {
                    await startNewConversation();
                }
            } catch (error) {
                console.error(`删除对话 ${id} 失败:`, error);
                addMessageToUI('system', `错误：删除对话失败。${error.message}`);
            }
        }
    }

    async function clearAllHistory() {
        const token = getAuthToken();
        if (!token) {
            addMessageToUI('system', '错误：用户未登录，无法清空历史。');
            return;
        }
        if (confirm('确定要删除所有历史会话吗？此操作无法撤销。')) {
            try {
                await fetchWithAuth(`${AI_CHAT_API_BASE_URL}/conversations`, { method: 'DELETE' });
                conversationsHistory = [];
                await startNewConversation();
                renderHistory();
            } catch (error) {
                console.error('清空所有历史失败:', error);
                addMessageToUI('system', `错误：清空历史失败。${error.message}`);
            }
        }
    }

    async function updateConversationTitle(conversationId, newTitle) {
        if (!conversationId || !newTitle || typeof newTitle !== 'string' || newTitle.trim() === '') return;
        const token = getAuthToken();
        if (!token) return;

        try {
            await fetchWithAuth(`${AI_CHAT_API_BASE_URL}/conversations/${conversationId}`, {
                method: 'PUT',
                body: JSON.stringify({ title: newTitle.trim() })
            });
            const convoIndex = conversationsHistory.findIndex(c => c.id === conversationId);
            if (convoIndex > -1) {
                conversationsHistory[convoIndex].title = newTitle.trim();
                renderHistory();
            }
        } catch (error) {
            console.error(`更新对话 ${conversationId} 标题失败:`, error);
        }
    }

    // --- UI Management ---
    function addMessageToUI(role, content, reasoning = null, imageBase64 = null) {
        console.log(`[addMessageToUI] Called with role: ${role}, content type: ${typeof content}, content: ${content?.substring ? content.substring(0,30) : JSON.stringify(content).substring(0,30)}, reasoning provided: ${!!reasoning}, image provided: ${!!imageBase64}`);
        startPlaceholder.style.display = 'none';
        const messageBubble = document.createElement('div');
        messageBubble.classList.add('chat-message-bubble', role === 'user' ? 'user-message' : 'assistant-message');

        const avatar = document.createElement('div');
        avatar.classList.add('avatar');
        avatar.innerHTML = role === 'user' ? '<i class="bi bi-person-fill"></i>' : '<i class="bi bi-robot"></i>';

        const messageContentDiv = document.createElement('div');
        messageContentDiv.classList.add('message-content');

        let plainTextContent = '';

        if (role === 'assistant' && content === 'loading') {
            const typingIndicator = document.createElement('div');
            typingIndicator.classList.add('typing-indicator');
            typingIndicator.innerHTML = '<span></span><span></span><span></span>';
            messageContentDiv.appendChild(typingIndicator);
            messageBubble.dataset.streamingId = currentConversationId;
        } else {
            let reasoningContainer = null;
            if (role === 'assistant' && reasoning) {
                reasoningContainer = document.createElement('div');
                reasoningContainer.className = 'reasoning-container';
                const thinkingLabel = document.createElement('div');
                thinkingLabel.className = 'thinking-label';
                thinkingLabel.innerHTML = '<span><i class="bi bi-lightbulb-fill me-1"></i>推理过程</span><i class="bi bi-chevron-down toggle-arrow ms-auto"></i>';
                const reasoningTextDiv = document.createElement('div');
                reasoningTextDiv.className = 'reasoning-text collapsed';
                reasoningTextDiv.style.whiteSpace = 'pre-wrap';
                reasoningTextDiv.textContent = reasoning;
                thinkingLabel.addEventListener('click', () => {
                    const isCollapsed = reasoningTextDiv.classList.toggle('collapsed');
                    const arrow = thinkingLabel.querySelector('.toggle-arrow');
                    arrow.classList.toggle('bi-chevron-down', isCollapsed);
                    arrow.classList.toggle('bi-chevron-up', !isCollapsed);
                });
                reasoningContainer.appendChild(thinkingLabel);
                reasoningContainer.appendChild(reasoningTextDiv);
            }

            let assistantImageRendered = false;
            if (role === 'assistant' && imageBase64) {
                const img = document.createElement('img');
                img.src = imageBase64;
                img.alt = "AI generated image";
                img.classList.add('generated-ai-image'); 
                img.style.maxWidth = '100%';
                img.style.maxHeight = '512px'; 
                img.style.objectFit = 'contain';
                img.style.borderRadius = '8px';
                img.style.marginBottom = '10px'; 
                img.style.display = 'block';
                img.style.marginLeft = 'auto';
                img.style.marginRight = 'auto';
                messageContentDiv.appendChild(img);
                assistantImageRendered = true;
            }
            
            // Now append reasoning container if it exists (it should be above text but below image)
            if (reasoningContainer) {
                messageContentDiv.appendChild(reasoningContainer);
            }

            const finalAnswerContainer = document.createElement('div');
            finalAnswerContainer.className = 'final-answer-content';

            let htmlOutput = '';
            // Ensure plainTextContent is derived correctly before this block
            // plainTextContent should hold the original string content passed to the function

            if (content && (typeof content !== 'string' || (content.trim() !== '' && content.trim() !== '(图片)') || !assistantImageRendered)) {
                if (typeof content === 'string' && typeof marked !== 'undefined') {
                    try {
                        htmlOutput = marked.parse(String(content).trimStart());
                    } catch (e) {
                       console.error("Error parsing markdown with Marked.js:", e);
                       htmlOutput = escapeHtml(String(content).trimStart()).replace(/\n/g, '<br>');
                    }
                } else if (typeof content === 'string') {
                     htmlOutput = escapeHtml(String(content).trimStart()).replace(/\n/g, '<br>');
                }
                 finalAnswerContainer.innerHTML = htmlOutput;
            }
            
            if (finalAnswerContainer.innerHTML.trim() !== '') {
                 messageContentDiv.appendChild(finalAnswerContainer);
            }

            // If an image was rendered and the original text content was just a placeholder like "(图片)",
            // ensure plainTextContent (used for copy button) is empty or more appropriate.
            if (assistantImageRendered && plainTextContent === '(图片)') {
                plainTextContent = ''; 
            } else if (typeof content === 'string') { // Recapture plainTextContent if it was a string
                plainTextContent = content;
            }

            if (plainTextContent && plainTextContent.trim() !== '') {
                const copyBubbleBtn = document.createElement('button');
                copyBubbleBtn.className = 'btn btn-sm copy-code-btn copy-bubble-btn';
                copyBubbleBtn.innerHTML = '<i class="bi bi-clipboard"></i>';
                copyBubbleBtn.title = '复制文本';
                copyBubbleBtn.dataset.text = plainTextContent;

                copyBubbleBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const button = e.currentTarget;
                    const text = button.dataset.text;
                    navigator.clipboard.writeText(text).then(() => {
                        button.innerHTML = '<i class="bi bi-check-lg"></i>';
                        button.title = '已复制!';
                        button.classList.remove('btn-danger');
                        button.classList.add('btn-success');
                        setTimeout(() => {
                            button.innerHTML = '<i class="bi bi-clipboard"></i>';
                            button.title = '复制文本';
                            button.classList.remove('btn-success');
                        }, 2000);
                    }).catch(err => {
                        console.error('无法复制气泡文本:', err);
                        button.innerHTML = '<i class="bi bi-x-lg"></i>';
                        button.title = '复制失败';
                        button.classList.remove('btn-success');
                        button.classList.add('btn-danger');
                        setTimeout(() => {
                            button.innerHTML = '<i class="bi bi-clipboard"></i>';
                            button.title = '复制文本';
                            button.classList.remove('btn-danger');
                        }, 3000);
                    });
                });
                messageBubble.appendChild(copyBubbleBtn);
            }

            enhanceCodeBlocks(messageContentDiv);

        } 

        messageBubble.appendChild(avatar);
        messageBubble.appendChild(messageContentDiv);
        messagesContainer.appendChild(messageBubble);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        return messageBubble;
    }

    function updateStreamingMessage(streamingId, newReasoningChunk, newContentChunk, isDeepSeekR1) {
        console.log('[updateStreamingMessage] Called. newReasoningChunk:', newReasoningChunk, 'newContentChunk:', newContentChunk, 'isDeepSeekR1:', isDeepSeekR1); // 新增
        const streamingBubble = messagesContainer.querySelector(`.chat-message-bubble[data-streaming-id="${streamingId}"]`);
        if (streamingBubble) {
            const messageContentDiv = streamingBubble.querySelector('.message-content');
            if (messageContentDiv) {
                let reasoningDisplay = messageContentDiv.querySelector('.streaming-reasoning-text');
                let finalAnswerDisplay = messageContentDiv.querySelector('.streaming-final-answer');

                const typingIndicator = messageContentDiv.querySelector('.typing-indicator');
                let isFirstMeaningfulChunk = false;
                if (typingIndicator) {
                    typingIndicator.remove();
                    isFirstMeaningfulChunk = true;
                }

                if (isDeepSeekR1 && !reasoningDisplay) {
                    reasoningDisplay = document.createElement('div');
                    reasoningDisplay.className = 'streaming-reasoning-text';
                    console.log('[updateStreamingMessage] Created reasoningDisplay for DeepSeek.'); // 新增
                    reasoningDisplay.style.whiteSpace = 'pre-wrap'; 
                    reasoningDisplay.style.paddingBottom = '10px';
                    reasoningDisplay.style.marginBottom = '10px';
                    reasoningDisplay.style.borderBottom = '1px dashed rgba(255,255,255,0.2)';
                    if (finalAnswerDisplay) {
                        messageContentDiv.insertBefore(reasoningDisplay, finalAnswerDisplay);
                    } else {
                        messageContentDiv.appendChild(reasoningDisplay);
                    }
                }

                if (!finalAnswerDisplay) {
                    finalAnswerDisplay = document.createElement('div');
                    finalAnswerDisplay.className = 'streaming-final-answer';
                    messageContentDiv.appendChild(finalAnswerDisplay);
                }

                if (isDeepSeekR1 && reasoningDisplay && newReasoningChunk) {
                    console.log('[updateStreamingMessage] Appending to reasoningDisplay:', newReasoningChunk); // 新增
                    reasoningDisplay.textContent += newReasoningChunk;
                }

                if (newContentChunk) {
                    let chunkToAppend = newContentChunk;
                    if (isFirstMeaningfulChunk && finalAnswerDisplay.innerHTML === '') {
                        if(newContentChunk) chunkToAppend = newContentChunk.trimStart();
                    }
                    
                    if(chunkToAppend){
                        finalAnswerDisplay.innerHTML += escapeHtml(chunkToAppend).replace(/\n/g, '<br>'); 
                    }
                }
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        }
    }


    function finalizeStreamingMessage(streamingId, finalContent, reasoningContent, isDeepSeekR1) {
        console.log('[finalizeStreamingMessage] Called. finalContent length:', finalContent?.length, 'reasoningContent:', reasoningContent, 'isDeepSeekR1:', isDeepSeekR1); // 新增
         const streamingBubble = messagesContainer.querySelector(`.chat-message-bubble[data-streaming-id="${streamingId}"]`);
        if (streamingBubble) {
            const messageContentDiv = streamingBubble.querySelector('.message-content');
            if (messageContentDiv) {
                messageContentDiv.innerHTML = ''; 

                if (isDeepSeekR1 && reasoningContent) {
                    console.log('[finalizeStreamingMessage] Condition met for showing DeepSeek reasoning. Content:', reasoningContent); // 新增
                    const reasoningContainer = document.createElement('div');
                    reasoningContainer.className = 'reasoning-container'; 

                    const thinkingLabel = document.createElement('div');
                    thinkingLabel.className = 'thinking-label'; 
                    thinkingLabel.innerHTML = '<span><i class="bi bi-lightbulb-fill me-1"></i>推理过程</span><i class="bi bi-chevron-down toggle-arrow ms-auto"></i>'; 
                    
                    const reasoningTextDiv = document.createElement('div');
                    reasoningTextDiv.className = 'reasoning-text collapsed'; 
                    reasoningTextDiv.style.whiteSpace = 'pre-wrap'; 
                    reasoningTextDiv.textContent = reasoningContent; 

                    thinkingLabel.addEventListener('click', () => {
                        const isCollapsed = reasoningTextDiv.classList.toggle('collapsed');
                        const arrow = thinkingLabel.querySelector('.toggle-arrow');
                        arrow.classList.toggle('bi-chevron-down', isCollapsed); 
                        arrow.classList.toggle('bi-chevron-up', !isCollapsed);  
                    });

                    reasoningContainer.appendChild(thinkingLabel);
                    reasoningContainer.appendChild(reasoningTextDiv);
                    messageContentDiv.appendChild(reasoningContainer);
                }

                const finalAnswerContainer = document.createElement('div');
                finalAnswerContainer.className = 'final-answer-content';

                let htmlOutput = '';
                if (typeof finalContent === 'string' && finalContent && typeof marked !== 'undefined') {
                    try {
                        htmlOutput = marked.parse(String(finalContent).trimStart());
                    } catch (e) {
                       console.error("Error parsing markdown with Marked.js:", e);
                       htmlOutput = escapeHtml(String(finalContent).trimStart()).replace(/\n/g, '<br>');
                    }
                } else if (typeof finalContent === 'string' && finalContent) {
                     htmlOutput = escapeHtml(String(finalContent).trimStart()).replace(/\n/g, '<br>');
                }
                finalAnswerContainer.innerHTML = htmlOutput;
                messageContentDiv.appendChild(finalAnswerContainer);

                enhanceCodeBlocks(messageContentDiv);

                delete streamingBubble.dataset.streamingId; 
            }
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }

    function adjustTextareaHeight(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 150) + 'px';
    }

    function updateTokenCount() {
        const textContent = currentConversation.map(msg => msg.content).join(' ');
        const estimatedTokens = Math.ceil(textContent.length / 2.5);
        currentTokensDisplay.textContent = estimatedTokens;
        tokenInfoDisplay.style.display = currentConversation.length > 0 ? 'block' : 'none';
    }

    async function handleImageSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.type.startsWith('image/')) {
            alert('请选择图片文件。');
            fileInput.value = '';
            return;
        }

        const maxSize = 5 * 1024 * 1024;
        if (file.size > maxSize) {
            alert('图片文件过大，请选择小于 5MB 的图片。');
            fileInput.value = '';
            return;
        }

        try {
            currentImageBase64 = await convertToBase64(file);
            previewImage.src = currentImageBase64;
            imagePreviewArea.style.display = 'inline-block';
        } catch (error) {
            console.error("Error converting image to Base64:", error);
            alert('图片处理失败，请重试。');
            removeImage();
        }
    }

    function removeImage() {
        currentImageBase64 = null;
        previewImage.src = '';
        imagePreviewArea.style.display = 'none';
        fileInput.value = '';
    }

    function convertToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
            reader.readAsDataURL(file);
        });
    }

    async function ensureConversationBackendExists() {
        if (currentConversationId) {
            return true; // Conversation already exists
        }

        const token = getAuthToken();
        if (!token) {
            // This message might be redundant if sendMessage also checks, but good for direct calls.
            addMessageToUI('system', '错误：用户未登录，无法创建对话。请先登录。');
            return false;
        }
        
        const selectedModel = modelSelectValue.value; // Use hidden input value
        // Backend will handle title update based on first message if "新对话" is sent.
        const initialTitle = '新对话'; 

        isLoading = true; // Indicate loading state for conversation creation
        sendBtn.disabled = true;
        // Consider a different loading text for creating conversation vs sending message
        sendBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 创建中...';


        try {
            const newConversation = await fetchWithAuth(`${AI_CHAT_API_BASE_URL}/conversations`, {
                method: 'POST',
                body: JSON.stringify({ title: initialTitle, model_used: selectedModel })
            });

            if (newConversation && newConversation.id) {
                currentConversationId = newConversation.id;
                
                const newConvoForHistory = {
                    id: newConversation.id,
                    title: newConversation.title,
                    messages: [], // Messages will be populated as they are sent/received
                    timestamp: new Date(newConversation.last_active_at).getTime(),
                    model_used: newConversation.model_used,
                    // Ensure all fields expected by renderHistory are present
                    user_id: newConversation.user_id, // If needed by any client logic
                    created_at: newConversation.created_at,
                    updated_at: newConversation.updated_at,
                    last_active_at: newConversation.last_active_at
                };
                // Add to local history cache and re-render
                conversationsHistory.unshift(newConvoForHistory); 
                renderHistory(); // This will also mark the new conversation as active due to currentConversationId being set

                return true;
            } else {
                throw new Error('创建新会话失败，未返回有效的会话信息。');
            }
        } catch (error) {
            console.error('创建新对话后端记录失败:', error);
            addMessageToUI('system', `错误：无法在服务器上创建新对话。${error.message || '请稍后再试。'}`);
            currentConversationId = null; // Ensure it's reset if creation failed
            return false;
        } finally {
             isLoading = false;
             sendBtn.disabled = false;
             sendBtn.innerHTML = '<i class="bi bi-send-fill"></i><span class="ms-1">发送</span>';
        }
    }

    async function sendMessage() {
        const userMessageContent = messageInput.value.trim();
        if ((!userMessageContent && !currentImageBase64) || isLoading) return;

        const currentPlatformId = apiPlatformSelectValue.value; // Use hidden input
        const selectedModel = modelSelectValue.value; // Use hidden input value
        
        const platformApiKeyInput = document.getElementById(DYNAMIC_API_KEY_INPUT_ID);
        const apiKey = platformApiKeyInput ? platformApiKeyInput.value.trim() : '';

        if (!apiKey) {
            addMessageToUI('system', `错误：请为当前选择的平台 (${API_PLATFORMS[currentPlatformId].displayName}) 输入 API Key。`);
            if (platformApiKeyInput) platformApiKeyInput.focus();
            return;
        }

        const token = getAuthToken();
        if (!token) {
            addMessageToUI('system', '错误：用户未登录，无法发送消息。请先登录。');
            return;
        }
        if (!currentConversationId) {
            const conversationCreated = await ensureConversationBackendExists();
            if (!conversationCreated) {
                // ensureConversationBackendExists handles its own button state reset on failure and returns
                return;
            }
        }

        isLoading = true;
        sendBtn.disabled = true;
        sendBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 发送中...';

        let userMessageForUI = userMessageContent;
        let userMessageContentForBackend;

        if (currentImageBase64 && userMessageContent) {
            userMessageContentForBackend = [
                { type: "image_url", image_url: { url: currentImageBase64 } },
                { type: "text", text: userMessageContent }
            ];
        } else if (currentImageBase64) {
            userMessageContentForBackend = [{ type: "image_url", image_url: { url: currentImageBase64 } }];
            userMessageForUI = "(图片)";
        } else {
            userMessageContentForBackend = { type: "text", text: userMessageContent };
        }
        
        // Add user message to UI and save it to backend
        // Determine if image should be displayed in user bubble based on platform (example, might need refinement)
        const displayUserImage = currentImageBase64 && (currentPlatformId === 'gemini' || currentPlatformId === 'openai' || selectedModel.includes('-vl') || selectedModel.includes('vision'));
        addMessageToUI('user', userMessageForUI, null, displayUserImage ? currentImageBase64 : null);
        
        const userMessageForState = {
            role: 'user',
            content: userMessageContentForBackend 
        };
        currentConversation.push(userMessageForState);

        try {
            await fetchWithAuth(`${AI_CHAT_API_BASE_URL}/conversations/${currentConversationId}/messages`, {
                method: 'POST',
                body: JSON.stringify(userMessageForState)
            });
        } catch (error) {
            console.error('保存用户消息到后端失败:', error);
            addMessageToUI('system', `警告：您的消息 "${userMessageForUI.substring(0,20)}..." 可能未成功保存到历史记录。`);
        }
        
        messageInput.value = '';
        adjustTextareaHeight(messageInput);
        updateTokenCount();

        let assistantMessageBubble; // For streaming UI updates
        let assistantReasoningContent = null;
        let assistantResponseContent = ''; 
        let assistantResponseContentForBackend = null; // Initialize to null

        try { // Main try for AI processing and saving AI response
        if (currentPlatformId === 'gemini') {
            const isImageGenModel = selectedModel === 'gemini-2.0-flash-preview-image-generation';
            const apiVersion = "v1beta";
                const useStream = streamResponseCheckbox.checked && !isImageGenModel;
            const geminiApiUrl = `https://generativelanguage.googleapis.com/${apiVersion}/models/${selectedModel}:${useStream ? 'streamGenerateContent' : 'generateContent'}?key=${apiKey}`;
            let apiParts = [];
                if (currentImageBase64 && (isImageGenModel || selectedModel.includes('vision') || selectedModel.includes('pro') || selectedModel.includes('flash'))) { // Broader check for Gemini models that might accept images
                const match = currentImageBase64.match(/^data:(image\/.+);base64,(.+)$/);
                if (match) {
                    apiParts.push({ inlineData: { mimeType: match[1], data: match[2] } });
                }
            }
            if (userMessageContent) {
                apiParts.push({ text: userMessageContent });
            }
                if (apiParts.length === 0 && !isImageGenModel) { // Image gen might take empty prompt if image is there
                 addMessageToUI('system', '错误：Gemini请求内容为空。');
                     assistantResponseContentForBackend = null; // Ensure no save attempt
                } else {
                    let contentsForApi = [{ role: "user", parts: apiParts }]; 
                    const activePersonaPrompt = getActivePersonaPrompt();
                    
                    // System instruction handling
                    if(activePersonaPrompt && currentPlatformId !== 'gemini'){
                        // Only add system instruction if persona is active AND it's NOT a Gemini platform call
                        // Based on API errors, Gemini models (both stream/non-stream tested) don't support role:"system" in contents this way.
                        const systemInstructionPart = { role: "system", parts: [{text: activePersonaPrompt}] };
                        contentsForApi = [systemInstructionPart, ...contentsForApi]; 
                    }

            let generationConfig = {
            temperature: parseFloat(temperatureRange.value),
                        topP: parseFloat(topPRange.value),
                maxOutputTokens: parseInt(maxTokensInput.value.trim()) || undefined,
            };
            if (isImageGenModel) {
                        generationConfig.response_modalities = ["IMAGE", "TEXT"]; // Request both
                        // topP and topK might be good for image gen, but ensure they are valid for the model
                        // generationConfig.topK = 40; 
                    }

            let requestBody = {
                        contents: contentsForApi, // `contents` now includes system instruction if present
                generationConfig: generationConfig
            };
                    // Removed the more complex system_instruction logic here as it's now part of contentsForApi
                    // if(systemInstruction && selectedModel.startsWith('gemini-1.5')) { ...
                    // } else if (systemInstruction) { ... 

                    if (useStream) {
                        if (!assistantMessageBubble) { // Ensure bubble is created for streaming
                            assistantMessageBubble = addMessageToUI('assistant', 'loading');
                        }
                try {
                    const response = await fetch(geminiApiUrl, {
                method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify(requestBody)
            });
            if (!response.ok) {
                        const errorData = await response.json().catch(() => ({ error: { message: response.statusText } }));
                        throw new Error(`Gemini Stream API请求失败: ${response.status} ${errorData.error?.message || '未知错误'}`);
            }
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                    let streamBuffer = '';
                    let accumulatedStreamedText = '';
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                        streamBuffer += decoder.decode(value, { stream: true });
                        const { objects, remainingBuffer } = extractJsonObjectsForGeminiStream(streamBuffer);
                        streamBuffer = remainingBuffer;
                        for (const jsonObj of objects) {
                                    if (jsonObj.candidates && jsonObj.candidates.length > 0 && jsonObj.candidates[0].content && jsonObj.candidates[0].content.parts) {
                                        for (const part of jsonObj.candidates[0].content.parts) {
                                        if (part.text) {
                                            accumulatedStreamedText += part.text;
                                    }
                                }
                            } else if (jsonObj.promptFeedback && jsonObj.promptFeedback.blockReason) {
                                accumulatedStreamedText += `\n[请求被阻止: ${jsonObj.promptFeedback.blockReason}]`;
                                    }
                                }
                                // Update UI progressively
                                const tempBubble = messagesContainer.querySelector(`.chat-message-bubble[data-streaming-id="${currentConversationId}"] .message-content`);
                                if (tempBubble) {
                                    const finalAnswerDiv = tempBubble.querySelector('.streaming-final-answer') || tempBubble.querySelector('.final-answer-content') || tempBubble;
                                    if (finalAnswerDiv.innerHTML.includes('typing-indicator')) finalAnswerDiv.innerHTML = '';
                                    finalAnswerDiv.innerHTML = marked.parse(accumulatedStreamedText.trimStart());
                                    enhanceCodeBlocks(finalAnswerDiv);
                                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                                }
                            }
                    finalizeStreamingMessage(currentConversationId, accumulatedStreamedText, null, false);
                    assistantResponseContent = accumulatedStreamedText;
                    assistantResponseContentForBackend = { type: "text", text: assistantResponseContent };
                        } catch (streamApiError) {
                            console.error('Gemini Stream API Error:', streamApiError);
                            if(assistantMessageBubble && assistantMessageBubble.dataset.streamingId) {
                         const messageContentDiv = assistantMessageBubble.querySelector('.message-content');
                                 if(messageContentDiv) messageContentDiv.innerHTML = `<p class="text-danger">Gemini 流式API错误: ${streamApiError.message}</p>`;
                         delete assistantMessageBubble.dataset.streamingId;
                    } else {
                                addMessageToUI('assistant', `Gemini 流式API错误: ${streamApiError.message}`);
                            }
                            assistantResponseContentForBackend = null; // Do not save error as response
                        }
                    } else { // Non-Streaming Path
                         if (assistantMessageBubble) { // Remove loading bubble if it was created due to checkbox but then switched path
                            const mc = assistantMessageBubble.querySelector('.message-content');
                            if(mc) mc.innerHTML=''; // Clear it before potential error or proper message
                         }
                try {
                    const response = await fetch(geminiApiUrl, {
                            method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(requestBody)
                    });
                    if (!response.ok) {
                        const errorData = await response.json().catch(() => ({ error: { message: response.statusText } }));
                        throw new Error(`Gemini API请求失败: ${response.status} ${errorData.error?.message || '未知错误'}`);
                    }
                const data = await response.json();
                    let textResponsePart = '';
                            let imageResponseBase64 = null;
                    if (data.candidates && data.candidates.length > 0 && data.candidates[0].content && data.candidates[0].content.parts) {
                        for (const part of data.candidates[0].content.parts) {
                            if (part.text) {
                                textResponsePart += part.text;
                            } else if (part.inlineData && part.inlineData.mimeType && part.inlineData.data) {
                                        imageResponseBase64 = `data:${part.inlineData.mimeType};base64,${part.inlineData.data}`;
                                    }
                                }
                                assistantResponseContent = textResponsePart || (imageResponseBase64 ? '(图片)' : '');
                                if (imageResponseBase64 && !textResponsePart.trim()) assistantResponseContent = "(图片)";
                                else if (imageResponseBase64 && textResponsePart.trim()) assistantResponseContent = textResponsePart; // Text takes precedence for display if both

                                addMessageToUI('assistant', assistantResponseContent, null, imageResponseBase64);
                                
                                let contentForBackendArray = [];
                                if (textResponsePart.trim()) contentForBackendArray.push({type: "text", text: textResponsePart});
                                if (imageResponseBase64) contentForBackendArray.push({type: "image_url", image_url: { url: imageResponseBase64 } });
                                
                                if(contentForBackendArray.length === 1 && contentForBackendArray[0].type === "text"){
                                    assistantResponseContentForBackend = contentForBackendArray[0];
                                } else if (contentForBackendArray.length > 0){
                                    assistantResponseContentForBackend = contentForBackendArray;
                                } else {
                                     assistantResponseContentForBackend = {type: "text", text: ""}; 
                                }
                    } else if (data.promptFeedback && data.promptFeedback.blockReason) {
                        assistantResponseContent = `请求被阻止: ${data.promptFeedback.blockReason}.`;
                                addMessageToUI('assistant', assistantResponseContent);
                                assistantResponseContentForBackend = { type: "text", text: assistantResponseContent };
                    } else {
                        assistantResponseContent = '抱歉，未能从Gemini获取有效回复。';
                                addMessageToUI('assistant', assistantResponseContent);
                                assistantResponseContentForBackend = { type: "text", text: assistantResponseContent };
                            }
                        } catch (nonStreamApiError) {
                            console.error('Gemini API Error (Non-Streaming):', nonStreamApiError);
                            addMessageToUI('assistant', `Gemini API 错误: ${nonStreamApiError.message}`);
                            assistantResponseContentForBackend = null; // Do not save error as response
                        }
                    }
                }
            } else if (currentPlatformId === 'openai') {
                addMessageToUI('assistant', `OpenAI (${selectedModel}) 功能暂未完全实现。`);
                assistantResponseContentForBackend = { type: "text", text: `OpenAI (${selectedModel}) 功能暂未完全实现。` };
            } else if (currentPlatformId === 'infini') {
                // --- Restored Infini-AI Logic ---
                const isVLModel = selectedModel.includes('-vl') || selectedModel.includes('vision');
                if (currentImageBase64 && !isVLModel) {
                    addMessageToUI('assistant', '错误：当前选择的 Infini-AI 模型不支持图片输入。请选择一个视觉（VL）模型或移除图片后再发送。');
                    isLoading = false; // Reset loading state
                    sendBtn.disabled = false;
                    sendBtn.innerHTML = '<i class="bi bi-send-fill"></i><span class="ms-1">发送</span>';
                    assistantResponseContentForBackend = null; // Ensure no save attempt for this error
                    // The main finally block will reset the button, so we just need to ensure isLoading is false and return if needed.
                    // However, since assistantResponseContentForBackend is null, the save logic will skip.
                    // Let's ensure the flow continues to finally by not returning prematurely here,
                    // but the button state should be handled if this was a hard stop.
                    // For now, the message is added, and the flow will go to finally.
                } else {
                    const activePersonaPrompt = getActivePersonaPrompt();
                    let messagesForApi = currentConversation.slice(-MAX_CONVERSATION_HISTORY_FOR_API).map(msg => {
                        let formattedContent;
                        if (msg.content && typeof msg.content === 'object' && msg.content.type === 'text') {
                            formattedContent = msg.content.text;
                        } else if (Array.isArray(msg.content)) {
                            formattedContent = msg.content; // Already in array format for multi-modal
                        } else if (typeof msg.content === 'string') { // Should ideally not happen with new structure but handle just in case
                            formattedContent = msg.content;
                        } else {
                            formattedContent = ''; // Fallback for unknown structure
                        }
                        return { role: msg.role, content: formattedContent };
                    });

                    if (activePersonaPrompt) {
                        messagesForApi.unshift({ role: "system", content: activePersonaPrompt });
                    }
                    
                    // Format current user message for Infini-AI API
                    let currentUserApiContent;
                    if (userMessageContentForBackend && typeof userMessageContentForBackend === 'object' && userMessageContentForBackend.type === 'text') {
                        currentUserApiContent = userMessageContentForBackend.text;
                    } else if (Array.isArray(userMessageContentForBackend)) {
                        currentUserApiContent = userMessageContentForBackend;
                    } else {
                        currentUserApiContent = userMessageContent; // Fallback, should be based on userMessageContentForBackend
                    }
                    messagesForApi.push({ role: "user", content: currentUserApiContent });

                    const requestBody = {
                        model: selectedModel,
                        messages: messagesForApi,
                        temperature: parseFloat(temperatureRange.value),
                        top_p: parseFloat(topPRange.value),
                        max_tokens: parseInt(maxTokensInput.value.trim()) || null, // API expects null for default
                        stream: streamResponseCheckbox.checked,
                        stream_options: { include_usage: true } // Infini-AI specific
                    };

                    if (streamResponseCheckbox.checked && selectedModel.startsWith('deepseek-r1')) {
                        if (!assistantMessageBubble) {
                           assistantMessageBubble = addMessageToUI('assistant', 'loading');
                        }
                        try {
                            const response = await fetch(CHAT_API_ENDPOINT, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${apiKey}` },
                                body: JSON.stringify(requestBody)
                            });

                            if (!response.ok) {
                                const errorText = await response.text();
                                throw new Error(`Infini-AI Stream API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
                            }

                            const reader = response.body.getReader();
                            const decoder = new TextDecoder();
                            let sseBuffer = '';
                            let accumulatedFinalAnswer = "";
                            let accumulatedReasoning = "";
                            const isDeepSeekR1 = selectedModel === 'deepseek-r1';
                            let isDone = false; // Flag to break outer loop

                            function readLine(buffer) {
                                const newlineIndex = buffer.indexOf('\n');
                                if (newlineIndex === -1) {
                                    return null; // No complete line yet
                                }
                                const line = buffer.substring(0, newlineIndex);
                                sseBuffer = buffer.substring(newlineIndex + 1);
                                return line;
                            }

                            while (!isDone) { // Modified loop condition
                                const { done, value } = await reader.read();
                                if (done) {
                                    isDone = true; // Mark done if reader is done
                                    break;
                                }
                                sseBuffer += decoder.decode(value, { stream: true });
                                
                                let line;
                                while ((line = readLine(sseBuffer)) !== null) {
                                    if (line.startsWith("data: ")) {
                                        const jsonData = line.substring(6).trim(); // Added trim()
                                        if (jsonData === "[DONE]") {
                                            console.log('[Infini-AI Stream] Received [DONE] signal.');
                                            isDone = true; 
                                            break; 
                                        }
                                        try {
                                            const chunk = JSON.parse(jsonData);
                                            // >>> EXISTING LOG TO BE REMOVED or COMMENTED OUT FOR CLEANUP LATER <<<
                                            // if (isDeepSeekR1) {
                                            //     console.log('[Infini-AI DeepSeekR1 Stream Chunk RAW]:', JSON.stringify(chunk, null, 2));
                                            // }
                                            // >>> END LOG <<<

                                            let contentChunk = "";
                                            let reasoningChunk = null;

                                            if (chunk.choices && chunk.choices.length > 0) {
                                                const delta = chunk.choices[0].delta;
                                                if (delta) {
                                                    if (delta.content) {
                                                        contentChunk = delta.content;
                                                        accumulatedFinalAnswer += contentChunk;
                                                    }
                                                    // CORRECTED FIELD NAME HERE
                                                    if (isDeepSeekR1 && delta.reasoning_content) { 
                                                        reasoningChunk = delta.reasoning_content;
                                                        accumulatedReasoning += reasoningChunk;
                                                    }
                                                }
                                            }
                                            // console.log('[Infini-AI Stream] Calling updateStreamingMessage. ReasoningChunk:', reasoningChunk, 'ContentChunk:', contentChunk, 'isDeepSeekR1:', isDeepSeekR1);
                                            updateStreamingMessage(currentConversationId, reasoningChunk, contentChunk, isDeepSeekR1);
                                        } catch (e) {
                                            console.warn('Error parsing Infini-AI stream chunk:', jsonData, e);
                                        }
                                    }
                                }
                                if (isDone) break; // Break outer loop if [DONE] was processed
                            }
                            // console.log('[Infini-AI Stream] Calling finalizeStreamingMessage. AccumulatedFinalAnswer:', accumulatedFinalAnswer, 'AccumulatedReasoning:', accumulatedReasoning, 'isDeepSeekR1:', isDeepSeekR1);
                            finalizeStreamingMessage(currentConversationId, accumulatedFinalAnswer, accumulatedReasoning, isDeepSeekR1);
                            assistantResponseContent = accumulatedFinalAnswer;
                            assistantReasoningContent = accumulatedReasoning || null;
                            assistantResponseContentForBackend = { type: "text", text: assistantResponseContent };

                        } catch (streamApiError) {
                            console.error('Infini-AI Stream API Error:', streamApiError);
                            if(assistantMessageBubble && assistantMessageBubble.dataset.streamingId) {
                                 const messageContentDiv = assistantMessageBubble.querySelector('.message-content');
                                 if(messageContentDiv) messageContentDiv.innerHTML = `<p class="text-danger">Infini-AI 流式API错误: ${streamApiError.message}</p>`;
                                 delete assistantMessageBubble.dataset.streamingId;
                    } else {
                                addMessageToUI('assistant', `Infini-AI 流式API错误: ${streamApiError.message}`);
                            }
                            assistantResponseContentForBackend = null;
                        }
                    } else { // Non-Streaming for Infini-AI
                        if (assistantMessageBubble) { 
                            const mc = assistantMessageBubble.querySelector('.message-content');
                            if(mc) mc.innerHTML=''; 
                         }
                        try {
                            const response = await fetch(CHAT_API_ENDPOINT, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${apiKey}` },
                                body: JSON.stringify({...requestBody, stream: false }) // Ensure stream is false
                            });

                            if (!response.ok) {
                                const errorData = await response.json().catch(() => ({ message: response.statusText }));
                                throw new Error(`Infini-AI API请求失败: ${response.status} ${errorData.message || '未知错误'}`);
                            }
                            const data = await response.json();
                            if (data.choices && data.choices.length > 0 && data.choices[0].message) {
                                assistantResponseContent = data.choices[0].message.content || "";
                                assistantReasoningContent = data.choices[0].message.reasoning || null;
                                // For Infini-AI, images are not typically returned in chat completions this way.
                                // If they were, imageBase64 would need to be extracted and passed to addMessageToUI.
                                addMessageToUI('assistant', assistantResponseContent, assistantReasoningContent);
                                assistantResponseContentForBackend = { type: "text", text: assistantResponseContent };
                            } else {
                                assistantResponseContent = '抱歉，未能从Infini-AI获取有效回复。';
                                addMessageToUI('assistant', assistantResponseContent);
                                assistantResponseContentForBackend = { type: "text", text: assistantResponseContent };
                            }
                        } catch (nonStreamApiError) {
                            console.error('Infini-AI API Error (Non-Streaming):', nonStreamApiError);
                            addMessageToUI('assistant', `Infini-AI API 错误: ${nonStreamApiError.message}`);
                            assistantResponseContentForBackend = null;
                        }
                    }
                }
                // --- End Restored Infini-AI Logic ---
            } else {
                addMessageToUI('system', `错误：未知的 AI 平台 "${currentPlatformId}"。`);
                assistantResponseContentForBackend = null; // No response to save
            }

            // Save assistant message to backend if available and conversation exists
            if (assistantResponseContentForBackend && currentConversationId) {
            currentConversation.push({
                    role: 'assistant',
                    content: assistantResponseContentForBackend,
                    reasoning: assistantReasoningContent // Ensure assistantReasoningContent is defined or null
            });
                updateTokenCount(); // Update token count based on new AI message
                try {
                    await fetchWithAuth(`${AI_CHAT_API_BASE_URL}/conversations/${currentConversationId}/messages`, {
                        method: 'POST',
                        body: JSON.stringify({ 
                            role: 'assistant', 
                            content: assistantResponseContentForBackend, 
                            reasoning: assistantReasoningContent 
                        })
                    });
                } catch (error) {
                    console.error(`保存 ${currentPlatformId || 'assistant'} 消息到后端失败:`, error);
                    addMessageToUI('system', `警告：AI助手的回复可能未成功保存到历史记录。`);
                }
            }
            
            // Update conversation title if it's the first proper exchange in a new conversation
            if (currentConversationId) {
                const convo = conversationsHistory.find(c => c.id === currentConversationId);
                if (convo && convo.title === '新对话') {
                    const firstUserMessage = currentConversation.find(m => m.role === 'user');
                    if (firstUserMessage) {
                        let titleCandidate = '对话';
                        const userContent = firstUserMessage.content;
                        if (Array.isArray(userContent)) {
                            const textPart = userContent.find(p => p.type === 'text');
                            if (textPart && textPart.text.trim()) titleCandidate = textPart.text.substring(0, 30);
                            else if (userContent.find(p => p.type === 'image_url')) titleCandidate = '图片对话';
                        } else if (typeof userContent === 'object' && userContent.type === 'text' && userContent.text.trim()) {
                            titleCandidate = userContent.text.substring(0, 30);
                        } else if (typeof userContent === 'string' && userContent.trim()){
                            titleCandidate = userContent.substring(0,30);
                        }
                        if (titleCandidate.trim() && titleCandidate !== '新对话') {
                            await updateConversationTitle(currentConversationId, titleCandidate);
                        }
                    }
                }
            }

        } catch (generalError) {
            console.error('Unhandled error in sendMessage processing AI response:', generalError);
            addMessageToUI('system', `处理AI响应时发生意外错误: ${generalError.message}`);
        } finally {
            isLoading = false;
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="bi bi-send-fill"></i><span class="ms-1">发送</span>';
            removeImage(); // Clear the image preview and currentImageBase64 for the next message input
            messageInput.focus(); // Return focus to the input field
        }
    }

    sendBtn.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    messageInput.addEventListener('input', () => adjustTextareaHeight(messageInput));

    newConversationBtn.addEventListener('click', startNewConversation);

    saveSettingsBtn.addEventListener('click', saveSettings);
    temperatureRange.addEventListener('input', () => temperatureValueDisplay.textContent = temperatureRange.value);
    topPRange.addEventListener('input', () => topPValueDisplay.textContent = topPRange.value);
    
    // Generic API Key toggle function
    function toggleApiKeyVisibility(inputElement, buttonElement) {
        if (!inputElement || !buttonElement) return;
        const icon = buttonElement.querySelector('i');
        if (!icon) return;
        if (inputElement.type === 'password') {
            inputElement.type = 'text';
            icon.classList.remove('bi-eye-fill');
            icon.classList.add('bi-eye-slash-fill');
        } else {
            inputElement.type = 'password';
            icon.classList.remove('bi-eye-slash-fill');
            icon.classList.add('bi-eye-fill');
        }
    }

    clearHistoryBtn.addEventListener('click', clearAllHistory);
    initializeChat();
    function getAuthToken() {
        return localStorage.getItem('token');
    }
    async function fetchWithAuth(url, options = {}) {
        const token = getAuthToken();
        const headers = {
            'Content-Type': 'application/json',
            ...options.headers,
        };
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }

        const response = await fetch(url, {
            ...options,
            headers,
        });

        if (!response.ok) {
            let errorData;
            try {
                errorData = await response.json();
            } catch (e) {
                errorData = { message: response.statusText };
            }
            const error = new Error(`API请求失败: ${response.status} ${errorData.message || errorData.error?.message || '未知错误'}`);
            error.status = response.status;
            error.data = errorData;
            throw error;
        }
        if (response.status === 204) {
            return null;
        }
        return response.json();
    }
}); 