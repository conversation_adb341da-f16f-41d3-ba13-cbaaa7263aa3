document.addEventListener('DOMContentLoaded', () => {
    const imageInput = document.getElementById('upscaleImageInput');
    const imageDropZone = document.getElementById('upscaleImageDropZone');
    const imagePreview = document.getElementById('upscalePreviewImg');
    const previewContainer = document.getElementById('upscalePreviewContainer');
    const removeImageBtn = document.getElementById('removeUpscaleImage');
    const startButton = document.getElementById('startUpscaleBtn');
    const resultContainer = document.getElementById('upscaleResultContainer');
    const resultImage = document.getElementById('upscaleResultImage');
    let resultLink = document.getElementById('upscaleResultLink');
    const resultLoading = document.getElementById('upscaleResultLoading');
    const errorAlert = document.getElementById('upscaleErrorAlert');
    const startButtonSpinner = startButton.querySelector('.spinner-border');
    const startButtonIcon = startButton.querySelector('i');
    // --- New elements for history ---
    const historyListContainer = document.getElementById('upscaleHistoryList');
    const historyPaginationContainer = document.getElementById('upscaleHistoryPagination');
    const historyPlaceholder = document.getElementById('upscaleHistoryPlaceholder');
    const resultsPlaceholder = document.getElementById('upscaleResultsPlaceholder'); // Get results placeholder
    let loadMoreHistoryBtn = null; // Initialize button variable

    let selectedFile = null;
    let featureCost = null; // 添加功能成本变量
    const BACKEND_UPSCALE_URL = 'https://caca.yzycolour.top/api/creative-upscale'; // Your backend proxy URL
    // const BACKEND_HISTORY_URL = '/api/upscale-history'; // History endpoint - REMOVED, use API_URL from main.js
    let currentHistoryPage = 1;
    let totalHistoryPages = 1; // Store total pages

    // --- 新增：可复用的图片下载函数 --- START ---
    async function initiateImageDownload(imageUrl, clickedElement) {
        const originalLinkText = clickedElement.textContent; 
        const originalTitle = clickedElement.title;
        let isResultLink = (clickedElement.id === 'upscaleResultLink'); // 检查是否是主下载链接
        
        if (isResultLink) {
            clickedElement.textContent = '下载中...';
        }
        // 对于所有链接，都更新title并尝试禁用
        clickedElement.title = '下载中...'; 
        if (clickedElement.disabled !== undefined) {
            clickedElement.disabled = true;
        }

        try {
            const response = await fetch(imageUrl);
            if (!response.ok) {
                throw new Error(`下载图片失败: ${response.status} ${response.statusText}`);
            }
            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob);

            const tempLink = document.createElement('a');
            tempLink.href = blobUrl;
            
            let filename = 'upscaled_image.png'; // 默认文件名
            try {
                const urlObj = new URL(imageUrl);
                const nameFromParams = urlObj.searchParams.get("filename");
                if (nameFromParams) {
                    filename = nameFromParams;
                }
            } catch (e) {
                console.warn('无法从URL解析文件名，使用默认文件名。');
            }
            tempLink.download = filename;
            
            document.body.appendChild(tempLink);
            tempLink.click();
            document.body.removeChild(tempLink);

            URL.revokeObjectURL(blobUrl);
            
        } catch (error) {
            console.error('下载图片时出错:', error);
            showError(`下载失败: ${error.message}`); 
        } finally {
            // Restore link text/state
            if (isResultLink) {
                clickedElement.textContent = originalLinkText || '在新标签页打开或下载'; // 确保主链接有合适的恢复文本
            }
            // 对于所有链接，恢复title和启用状态
            clickedElement.title = originalTitle;
            if (clickedElement.disabled !== undefined) {
                clickedElement.disabled = false;
            }
        }
    }
    // --- 新增：可复用的图片下载函数 --- END ---

    // --- 新增：获取功能成本的函数 --- START ---
    async function fetchFeatureCost() {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('未找到认证令牌，无法获取功能成本');
                return;
            }
            
            const response = await fetch('https://caca.yzycolour.top/api/features/cost?key=creative_upscale', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (!response.ok) {
                console.error('获取功能成本失败:', response.status);
                return;
            }
            
            const data = await response.json();
            if (data.success && data.cost !== undefined) {
                featureCost = data.cost;
                
                // 更新按钮文本
                updateButtonWithCost();
            }
        } catch (error) {
            console.error('获取功能成本出错:', error);
        }
    }
    
    function updateButtonWithCost() {
        if (startButton && featureCost !== null) {
            // 查找按钮中的文本节点
            const textNode = Array.from(startButton.childNodes)
                .find(node => node.nodeType === Node.TEXT_NODE && node.textContent.trim().length > 0);
            
            // 添加或更新积分信息
            if (textNode) {
                const originalText = textNode.textContent.trim();
                textNode.textContent = ` ${originalText} (${featureCost}积分)`;
            } else {
                // 如果没有找到文本节点，则添加一个包含积分信息的 span
                const costBadge = document.createElement('span');
                costBadge.className = 'ms-1 badge bg-secondary';
                costBadge.textContent = `${featureCost}积分`;
                startButton.appendChild(costBadge);
            }
        }
    }
    // --- 新增：获取功能成本的函数 --- END ---

    // --- File Handling Function ---
    function handleFileSelect(file) {
        if (!file) return;

        // Reset state
        selectedFile = null;
        startButton.disabled = true;
        previewContainer.style.display = 'none';
        resultContainer.style.display = 'none';
        errorAlert.style.display = 'none';
        imageInput.value = ''; // Clear input value in case of errors or removal

        // Simple validation
        const allowedTypes = ['image/png', 'image/jpeg', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            showError('请选择 PNG, JPG, 或 WEBP 格式的图片。');
            return;
        }
        if (file.size > 5 * 1024 * 1024) { // 5MB limit
            showError('图片大小不能超过 5MB。');
            return;
        }

        selectedFile = file;

        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            imagePreview.src = e.target.result;
            previewContainer.style.display = 'block';
            imageDropZone.style.display = 'none'; // Hide drop zone when preview is shown
            startButton.disabled = false;
        }
        reader.readAsDataURL(file);
        // Hide placeholder when selecting file
        resultsPlaceholder.style.display = 'none';
    }

    // --- History Loading and Rendering Functions --- START ---
    async function loadUpscaleHistory(page = 1, append = false) { // Add append flag
        
        if (!append) {
            historyListContainer.innerHTML = ''; // Clear only if not appending
            historyPlaceholder.textContent = '加载历史记录中...';
            historyPlaceholder.style.display = 'block';
        }
        
        // Hide/disable load more button during load
        if(loadMoreHistoryBtn) {
            loadMoreHistoryBtn.disabled = true;
            loadMoreHistoryBtn.textContent = '加载中...';
        }
        
        // Remove pagination container (we handle it with load more button now)
        historyPaginationContainer.innerHTML = '';
        historyPaginationContainer.style.display = 'none';

        const token = localStorage.getItem('token');
        if (!token) {
            historyPlaceholder.textContent = '请先登录以查看历史记录。';
            console.warn('Token not found for loading history');
            return;
        }

        try {
            // Use the API_URL defined in main.js, use limit=8
            const historyUrl = `${API_URL}/upscale-history?page=${page}&limit=8`; 
            const response = await fetch(historyUrl, { 
                headers: {
                    'Authorization': `Bearer ${token}`,
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data && data.history && data.history.length > 0) {
                historyPlaceholder.style.display = 'none';
                renderHistoryList(data.history, append); // Pass append flag
                
                // Update total pages
                totalHistoryPages = data.pagination.totalPages;
                currentHistoryPage = data.pagination.currentPage; // Update current page

                // Update load more button state
                updateLoadMoreButtonState();

            } else if (!append) {
                historyPlaceholder.textContent = '暂无放大历史记录。';
                historyPlaceholder.style.display = 'block';
                if (loadMoreHistoryBtn) loadMoreHistoryBtn.style.display = 'none'; // Hide button if no initial results
            } else {
                // No more items to append
                if (loadMoreHistoryBtn) loadMoreHistoryBtn.style.display = 'none'; 
            }

        } catch (error) {
            console.error('加载放大历史记录失败:', error);
            if (!append) {
                historyPlaceholder.textContent = '加载历史记录失败，请稍后重试。';
                historyPlaceholder.style.display = 'block';
            }
            // Restore button text on error
            updateLoadMoreButtonState(); 
        }
    }

    // Modify renderHistoryList to just append items
    // Add CSS for horizontal layout
    function renderHistoryList(historyItems) {
        // Apply flexbox styles to the container if not already done
        if (historyListContainer.style.display !== 'flex') {
             historyListContainer.classList.remove('list-group', 'list-group-flush');
             historyListContainer.style.display = 'flex';
             historyListContainer.style.flexWrap = 'wrap';
             historyListContainer.style.gap = '1rem';
        }

        historyItems.forEach(item => {
            const div = document.createElement('div');
            div.className = 'upscale-history-item text-center'; 
            div.style.flex = '0 0 auto';
            div.style.maxWidth = '144px';

            const date = new Date(item.created_at);
            const formattedDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;

            const dateEl = document.createElement('small');
            dateEl.className = 'd-block text-muted mb-1';
            dateEl.textContent = formattedDate;
            div.appendChild(dateEl);

            const linkEl = document.createElement('a');
            linkEl.href = item.upscaled_image_url; // Keep href for context, right-click open in new tab, etc.
            linkEl.title = '点击下载图片'; // Updated title
            // linkEl.target = '_blank'; // REMOVED to prevent new tab navigation on left click

            const imgEl = document.createElement('img');
            imgEl.src = item.upscaled_image_url;
            imgEl.alt = 'Upscaled image';
            imgEl.className = 'img-fluid rounded border';
            imgEl.style.objectFit = 'cover';
            imgEl.style.aspectRatio = '1 / 1';

            linkEl.appendChild(imgEl);
            div.appendChild(linkEl);
            historyListContainer.appendChild(div);

            // Add click listener for download
            linkEl.addEventListener('click', (event) => {
                event.preventDefault();
                // Pass the link element itself for UI feedback if needed by initiateImageDownload
                initiateImageDownload(item.upscaled_image_url, linkEl); 
            });
        });
    }

    // Function to create and manage the load more button
    function setupLoadMoreButton() {
        if (!loadMoreHistoryBtn) {
            loadMoreHistoryBtn = document.createElement('button');
            loadMoreHistoryBtn.id = 'loadMoreUpscaleHistoryBtn';
            loadMoreHistoryBtn.className = 'btn btn-outline-secondary btn-sm mt-3';
            loadMoreHistoryBtn.textContent = '加载更多';
            loadMoreHistoryBtn.style.display = 'none'; // Initially hidden
            
            // Insert after the list container
            historyListContainer.parentNode.insertBefore(loadMoreHistoryBtn, historyListContainer.nextSibling);

            loadMoreHistoryBtn.addEventListener('click', () => {
                if (currentHistoryPage < totalHistoryPages) {
                    loadUpscaleHistory(currentHistoryPage + 1, true); // Load next page and append
                }
            });
        }
    }

    // Function to update button state based on pagination
    function updateLoadMoreButtonState() {
        if (!loadMoreHistoryBtn) setupLoadMoreButton(); // Ensure button exists
        
        if (currentHistoryPage < totalHistoryPages) {
            loadMoreHistoryBtn.disabled = false;
            loadMoreHistoryBtn.textContent = '加载更多';
            loadMoreHistoryBtn.style.display = 'block';
        } else {
            loadMoreHistoryBtn.disabled = true;
            loadMoreHistoryBtn.textContent = '没有更多了';
            // Optionally hide if preferred: loadMoreHistoryBtn.style.display = 'none';
            loadMoreHistoryBtn.style.display = 'block'; // Keep it visible but disabled
        }
    }
    // --- History Loading and Rendering Functions --- END ---

    // --- Event Listeners ---

    // Listener for hidden file input change
    imageInput.addEventListener('change', (event) => {
        handleFileSelect(event.target.files[0]);
    });

    // Listener for clicking the drop zone
    imageDropZone.addEventListener('click', () => {
        imageInput.click();
    });

    // Drag and Drop Listeners
    imageDropZone.addEventListener('dragover', (event) => {
        event.preventDefault();
        imageDropZone.classList.add('drag-over');
    });

    imageDropZone.addEventListener('dragleave', () => {
        imageDropZone.classList.remove('drag-over');
    });

    imageDropZone.addEventListener('drop', (event) => {
        event.preventDefault();
        imageDropZone.classList.remove('drag-over');
        if (event.dataTransfer.files && event.dataTransfer.files[0]) {
            handleFileSelect(event.dataTransfer.files[0]);
        }
    });

     // Paste Listener (added for consistency)
    document.addEventListener('paste', (event) => {
        // Check if the paste event is happening within or targeted at the upscale tab content
        const upscalePane = document.getElementById('creative-upscale-pane');
        // Basic check: is the upscale tab active?
        // A more robust check might involve checking event.target or document.activeElement
        if (upscalePane.classList.contains('active') || upscalePane.contains(document.activeElement)) {
            const items = event.clipboardData?.items;
            if (!items) return;
            for (let i = 0; i < items.length; i++) {
                if (items[i].type.indexOf('image') !== -1) {
                    const blob = items[i].getAsFile();
                    if (blob) {
                        handleFileSelect(blob);
                        event.preventDefault(); // Prevent default paste behavior only if an image is handled
                        break; // Handle only the first image found
                    }
                }
            }
        }
    });

    // Listener for remove image button
    removeImageBtn.addEventListener('click', () => {
        selectedFile = null;
        imageInput.value = ''; // Clear the file input
        imagePreview.src = '#';
        previewContainer.style.display = 'none';
        imageDropZone.style.display = 'block'; // Show drop zone again
        startButton.disabled = true;
        resultContainer.style.display = 'none';
        errorAlert.style.display = 'none';
    });


    // Listener for start button click (Modify to reload history on success)
    startButton.addEventListener('click', async () => {
        if (!selectedFile) {
            showError('请先选择一张图片。');
            return;
        }

        setLoadingState(true);
        errorAlert.style.display = 'none';
        resultContainer.style.display = 'block'; // <-- Make container visible
        resultImage.style.display = 'none'; // <-- Hide previous result image
        resultLink.style.display = 'none'; // <-- Hide previous result link
        resultLoading.style.display = 'flex'; // <-- Show the loading card (use flex based on CSS)
        resultsPlaceholder.style.display = 'none'; // Hide placeholder on click

        const formData = new FormData();
        formData.append('file', selectedFile);

        try {
            // --- Add Authorization Header --- START ---
            const token = localStorage.getItem('token');
            const headers = {};
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            } else {
                // Handle case where token is missing - maybe show error or redirect?
                console.warn('JWT Token not found in localStorage.');
                showError('用户未登录或登录已过期，请重新登录。');
                setLoadingState(false);
                return; // Stop execution if no token
            }
            // --- Add Authorization Header --- END ---

            const response = await fetch(BACKEND_UPSCALE_URL, {
                method: 'POST',
                headers: headers, // Pass the headers object
                body: formData,
                // Content-Type is set automatically by browser for FormData
            });

            if (!response.ok) {
                let errorData;
                try {
                    errorData = await response.json();
                } catch (e) {
                    throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`);
                }
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result && result.image && result.image.url) {
                const upscaledImageUrl = result.image.url;
                showResult(upscaledImageUrl);

                // --- Refresh credits display --- START ---
                if (typeof window.fetchCredits === 'function') {
                    window.fetchCredits();
                } else {
                    console.warn('[Creative Upscale] window.fetchCredits function not found.');
                }
                // --- Refresh credits display --- END ---

                // --- Reload history after successful upscale (Backend saves automatically) ---
                loadUpscaleHistory(1); // Reload history to show the new item saved by the backend
                // --- Save to history --- END ---
            } else {
                throw new Error('从后端收到的响应格式不正确。');
            }

        } catch (error) {
            console.error('Upscale error:', error);
            showError(`放大失败：${error.message}`);
        } finally {
            setLoadingState(false);
            resultLoading.style.display = 'none';
        }
    });

    // --- Helper Functions ---

    function setLoadingState(isLoading) {
        if (isLoading) {
            startButton.disabled = true;
            startButtonIcon.style.display = 'none';
            // Find the text node to update, avoiding replacing the icon/spinner
            const textNode = Array.from(startButton.childNodes).find(node => node.nodeType === Node.TEXT_NODE && node.textContent.trim().length > 0);
            if(textNode) textNode.textContent = ' 正在处理...';
        } else {
            startButton.disabled = (selectedFile === null); // Re-enable only if a file is selected
            startButtonIcon.style.display = 'inline-block';
             const textNode = Array.from(startButton.childNodes).find(node => node.nodeType === Node.TEXT_NODE && node.textContent.trim().length > 0);
            if(textNode) textNode.textContent = '开始清晰放大';
        }
    }

    function showResult(imageUrl) {
        resultImage.src = imageUrl;
        // resultLink.href = imageUrl; // 旧的直接设置 href 的方式

        // --- 修改：通过 fetch 和 Blob 实现强制下载 --- START ---
        // 移除可能存在的旧事件监听器，以防重复绑定
        const newResultLink = resultLink.cloneNode(true);
        // It's important that resultLink's parentNode is correct and exists.
        if (resultLink.parentNode) {
            resultLink.parentNode.replaceChild(newResultLink, resultLink);
        }
        resultLink = newResultLink; // 更新对 resultLink 的引用
        // Ensure the text content is set if the cloneNode(true) doesn't carry it over or it was dynamic
        // This might need adjustment based on how resultLink gets its initial text.
        // For example, if it's static in HTML:
        // resultLink.textContent = "在新标签页打开或下载"; // Or whatever it should be.

        resultLink.addEventListener('click', async (event) => {
            event.preventDefault(); // 阻止 <a> 标签的默认导航行为
            initiateImageDownload(imageUrl, resultLink); // Call the reusable download function
        });
        // --- 修改：通过 fetch 和 Blob 实现强制下载 --- END ---

        resultContainer.style.display = 'block'; // Ensure container is visible
        resultLoading.style.display = 'none'; // Hide loading card
        resultImage.style.display = 'block'; // Show result image
        resultLink.style.display = 'block'; // Show result link
        errorAlert.style.display = 'none';
        resultsPlaceholder.style.display = 'none'; // Hide placeholder when showing result
    }

    function showError(message) {
        errorAlert.textContent = message;
        errorAlert.style.display = 'block';
        resultContainer.style.display = 'none';
        resultLoading.style.display = 'none'; // Ensure loading card is hidden on error
        resultsPlaceholder.style.display = 'block'; // Show placeholder on error
    }

    // --- Initial Load ---
    setupLoadMoreButton(); // Create the button structure on initial load
    loadUpscaleHistory(1); // Load first page of history when the page loads
    resultsPlaceholder.style.display = 'block'; // Show placeholder initially

    // --- Add Listener for Tab Activation --- START ---
    const upscaleTab = document.getElementById('creative-upscale-tab');
    if (upscaleTab) {
        upscaleTab.addEventListener('shown.bs.tab', handleTabShown);
    }
    // --- Add Listener for Tab Activation --- END ---

    // --- Add Function to Load Image from URL --- START ---
    async function loadImageFromUrl(imageUrl) {
        // Show some loading indicator if needed, or rely on processFile's preview update
        try {
            // Fetch the image data
            const response = await fetch(imageUrl); // Consider CORS implications
            if (!response.ok) {
                throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
            }
            const blob = await response.blob();

            // Extract filename or create a default one
            let filename = 'image_from_history.png'; // Default filename
            try {
                const urlParts = new URL(imageUrl).pathname.split('/');
                filename = urlParts[urlParts.length - 1] || filename;
                // Basic sanitation for filename if needed
            } catch (e) { console.warn('Could not parse filename from URL'); }
            
            // Create a File object
            const imageFile = new File([blob], filename, { type: blob.type });

            // Process the file as if it was selected by the user
            handleFileSelect(imageFile); // Reuse existing file handling logic

        } catch (error) {
            console.error('[Creative Upscale] Error loading image from URL:', error);
            showError(`加载图片失败: ${error.message}`);
        }
    }
    // --- Add Function to Load Image from URL --- END ---

    // --- Add Tab Shown Handler --- START ---
    function handleTabShown() {
        if (window.pendingImageData && window.pendingImageData.targetPaneId === 'creative-upscale-pane') {
            const imageUrl = window.pendingImageData.imageUrl;
            // Clear the pending data immediately
            delete window.pendingImageData;
            // Load the image
            loadImageFromUrl(imageUrl);
        }
        
        // 获取功能成本（如果尚未获取）
        if (featureCost === null) {
            fetchFeatureCost();
        }
    }
    // --- Add Tab Shown Handler --- END ---

    // --- 初始加载 ---
    // 页面加载时获取功能成本
    fetchFeatureCost();
}); 