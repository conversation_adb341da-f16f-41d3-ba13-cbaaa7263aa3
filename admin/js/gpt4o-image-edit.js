document.addEventListener('DOMContentLoaded', () => {

    // 创建全局对象，暴露变量和函数给其他模块使用
    window.gpt4oImageEdit = {
        setMainImageFile: null,
        setRef1ImageFile: null,
        setRef2ImageFile: null
    };

    // --- 新增：可复用的图片下载函数 --- START ---
    async function initiateImageDownload(imageUrl, clickedElement) {
        const originalLinkText = clickedElement.textContent; 
        const originalTitle = clickedElement.title;
        // 在 gpt4o-image-edit.js 中，我们不区分主下载链接，统一处理
        // let isResultLink = (clickedElement.id === 'upscaleResultLink'); 
        
        // 更新 title 和禁用状态
        clickedElement.title = '下载中...'; 
        if (clickedElement.disabled !== undefined) {
            clickedElement.disabled = true;
        }
        if (clickedElement.classList) {
            clickedElement.classList.add('disabled-link'); // 可选：添加一个视觉禁用的类
        }

        try {
            const response = await fetch(imageUrl);
            if (!response.ok) {
                throw new Error(`下载图片失败: ${response.status} ${response.statusText}`);
            }
            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob);

            const tempLink = document.createElement('a');
            tempLink.href = blobUrl;
            
            let filename = 'edited_image.png'; // 默认文件名
            try {
                // 尝试从 URL 获取文件名，如果它是通过 presigned URL 等方式给出的
                const urlObj = new URL(imageUrl); 
                const pathnameParts = urlObj.pathname.split('/');
                const potentialFilename = pathnameParts[pathnameParts.length - 1];
                if (potentialFilename && potentialFilename.includes('.')) { // 简单检查是否像文件名
                    filename = potentialFilename;
                } else {
                    // 如果URL不包含明确文件名，尝试从 searchParams 获取（如果有）
                    const nameFromParams = urlObj.searchParams.get("filename");
                    if (nameFromParams) {
                        filename = nameFromParams;
                    }
                }
            } catch (e) {
                console.warn('无法从URL解析文件名，使用默认文件名。', e);
            }
            tempLink.download = filename;
            
            document.body.appendChild(tempLink);
            tempLink.click();
            document.body.removeChild(tempLink);

            URL.revokeObjectURL(blobUrl);
            
        } catch (error) {
            console.error('下载图片时出错:', error);
            // 这里可以考虑调用一个通用的错误提示函数，如果 gpt4o-image-edit.js 中有的话
            // 例如：showGpt4oError(`下载失败: ${error.message}`); 
            if(errorAlert) { // 复用已有的 errorAlert 元素
                errorAlert.textContent = `下载失败: ${error.message}`;
                errorAlert.style.display = 'block';
            }
        } finally {
            // 恢复链接状态
            clickedElement.title = originalTitle;
            if (clickedElement.disabled !== undefined) {
                clickedElement.disabled = false;
            }
            if (clickedElement.classList) {
                clickedElement.classList.remove('disabled-link');
            }
            // 注意：不恢复 textContent，因为历史记录项的链接通常只有图标或图片，没有文本
        }
    }
    // --- 新增：可复用的图片下载函数 --- END ---

    const mainImageDropZone = document.getElementById('gpt4oMainImageDropZone');
    const mainImageInput = document.getElementById('gpt4oMainImageInput');
    const mainImagePreviewContainer = document.getElementById('gpt4oMainImagePreviewContainer');
    const mainImagePreviewImg = document.getElementById('gpt4oMainImagePreviewImg');
    const removeMainImageBtn = document.getElementById('removeGpt4oMainImageBtn');

    const ref1ImageDropZone = document.getElementById('gpt4oRef1ImageDropZone');
    const ref1ImageInput = document.getElementById('gpt4oRef1ImageInput');
    const ref1ImagePreviewContainer = document.getElementById('gpt4oRef1ImagePreviewContainer');
    const ref1ImagePreviewImg = document.getElementById('gpt4oRef1ImagePreviewImg');
    const removeRef1ImageBtn = document.getElementById('removeGpt4oRef1ImageBtn');

    const ref2ImageDropZone = document.getElementById('gpt4oRef2ImageDropZone');
    const ref2ImageInput = document.getElementById('gpt4oRef2ImageInput');
    const ref2ImagePreviewContainer = document.getElementById('gpt4oRef2ImagePreviewContainer');
    const ref2ImagePreviewImg = document.getElementById('gpt4oRef2ImagePreviewImg');
    const removeRef2ImageBtn = document.getElementById('removeGpt4oRef2ImageBtn');

    const editPromptInput = document.getElementById('gpt4oEditPromptInput');
    const startEditBtn = document.getElementById('startGpt4oImageEditBtn');
    const loadingIndicator = document.getElementById('gpt4oEditLoadingIndicator');
    const errorAlert = document.getElementById('gpt4oEditErrorAlert');
    const resultContainer = document.getElementById('gpt4oEditResultContainer');
    const resultImage = document.getElementById('gpt4oEditResultImage');
    let resultDownloadLink = document.getElementById('gpt4oEditResultDownloadLink');
    const resultsPlaceholder = document.getElementById('gpt4oEditResultsPlaceholder');
    // 新增：获取质量和尺寸的选择框
    const qualitySelect = document.getElementById('gpt4oEditQualitySelect');
    const sizeSelect = document.getElementById('gpt4oEditSizeSelect');
    // 新增：获取数量输入框
    const nSelect = document.getElementById('gpt4oEditNSelect'); 
    // History elements (get them later if needed)

    let mainImageFile = null;
    let mainImageDataUrl = null;
    let ref1ImageFile = null;
    let ref1ImageDataUrl = null;
    let ref2ImageFile = null;
    let ref2ImageDataUrl = null;

    const historyListContainer = document.getElementById('gpt4oEditHistoryList');
    const historyPaginationContainer = document.getElementById('gpt4oEditHistoryPagination'); // 主要用于放置加载更多按钮
    const historyPlaceholder = document.getElementById('gpt4oEditHistoryPlaceholder');

    // 调整中等屏幕及以上历史记录列数，使其更宽
    if (historyListContainer && historyListContainer.classList.contains('row-cols-md-4')) {
        historyListContainer.classList.remove('row-cols-md-4');
        historyListContainer.classList.add('row-cols-md-3'); // 改为3列，使每项更宽
    }

    let loadMoreHistoryBtn = null;
    let currentHistoryPage = 1;
    let totalHistoryPages = 1;
    const HISTORY_API_URL = `${API_URL}/gpt4o-edit/history`; // API_URL 来自 main.js

    // --- Reusable Image Handling Function ---
    function setupImageUpload(dropZone, input, previewContainer, previewImg, removeBtn, fileVariableSetter, dataUrlSetter) {
        
        function handleFile(file) {
            if (file && file.type.startsWith('image/')) {
                fileVariableSetter(file);
                const reader = new FileReader();
                reader.onload = (e) => {
                    previewImg.src = e.target.result;
                    dataUrlSetter(e.target.result); // Store the data URL
                    previewContainer.style.display = 'block';
                    dropZone.style.display = 'none'; // Hide drop zone
                    checkFormValidity(); // Check if the main form is ready
                };
                reader.readAsDataURL(file);
            } else {
                // Handle invalid file type maybe?
                console.warn('Invalid file type selected.');
                fileVariableSetter(null);
                dataUrlSetter(null);
            }
        }

        // Click to upload
        dropZone.addEventListener('click', () => input.click());
        input.addEventListener('change', (e) => {
            if (e.target.files && e.target.files[0]) {
                handleFile(e.target.files[0]);
            }
            // Reset input value so the same file can be selected again after removal
            input.value = null; 
        });

        // Drag and Drop
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragging');
        });
        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragging');
        });
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragging');
            if (e.dataTransfer.files && e.dataTransfer.files[0]) {
                handleFile(e.dataTransfer.files[0]);
            }
        });

        // Paste from Clipboard
        dropZone.addEventListener('paste', (e) => {
             e.preventDefault();
             const items = (e.clipboardData || window.clipboardData).items;
             for (let i = 0; i < items.length; i++) {
                 if (items[i].type.indexOf('image') !== -1) {
                     const blob = items[i].getAsFile();
                     // Ensure it's treated as a file with a name
                     const file = new File([blob], `pasted_image_${Date.now()}.${blob.type.split('/')[1] || 'png'}`, { type: blob.type });
                     handleFile(file);
                     break; // Handle only the first image found
                 }
             }
         });

        // Remove button
        removeBtn.addEventListener('click', () => {
            fileVariableSetter(null);
            dataUrlSetter(null);
            previewContainer.style.display = 'none';
            previewImg.src = ''; // Clear preview source
            dropZone.style.display = 'flex'; // Show drop zone again
            input.value = null; // Clear the file input
            checkFormValidity();
        });
    }

    // --- Setup for each image area ---
    setupImageUpload(mainImageDropZone, mainImageInput, mainImagePreviewContainer, mainImagePreviewImg, removeMainImageBtn, 
                     (file) => mainImageFile = file, (url) => mainImageDataUrl = url);
    setupImageUpload(ref1ImageDropZone, ref1ImageInput, ref1ImagePreviewContainer, ref1ImagePreviewImg, removeRef1ImageBtn, 
                     (file) => ref1ImageFile = file, (url) => ref1ImageDataUrl = url);
    setupImageUpload(ref2ImageDropZone, ref2ImageInput, ref2ImagePreviewContainer, ref2ImagePreviewImg, removeRef2ImageBtn, 
                     (file) => ref2ImageFile = file, (url) => ref2ImageDataUrl = url);

    // 实现并分配全局函数，用于设置图片文件
    window.gpt4oImageEdit.setMainImageFile = function(file) {
        if (!file) return false;
        mainImageFile = file;
        const reader = new FileReader();
        reader.onload = (e) => {
            mainImageDataUrl = e.target.result;
            mainImagePreviewImg.src = mainImageDataUrl;
            mainImagePreviewContainer.style.display = 'block';
            mainImageDropZone.style.display = 'none';
            checkFormValidity();
        };
        reader.readAsDataURL(file);
        return true;
    };

    window.gpt4oImageEdit.setRef1ImageFile = function(file) {
        if (!file) return false;
        ref1ImageFile = file;
        const reader = new FileReader();
        reader.onload = (e) => {
            ref1ImageDataUrl = e.target.result;
            ref1ImagePreviewImg.src = ref1ImageDataUrl;
            ref1ImagePreviewContainer.style.display = 'block';
            ref1ImageDropZone.style.display = 'none';
        };
        reader.readAsDataURL(file);
        return true;
    };

    window.gpt4oImageEdit.setRef2ImageFile = function(file) {
        if (!file) return false;
        ref2ImageFile = file;
        const reader = new FileReader();
        reader.onload = (e) => {
            ref2ImageDataUrl = e.target.result;
            ref2ImagePreviewImg.src = ref2ImageDataUrl;
            ref2ImagePreviewContainer.style.display = 'block';
            ref2ImageDropZone.style.display = 'none';
        };
        reader.readAsDataURL(file);
        return true;
    };

    // --- Check if main image and prompt are ready ---
    function checkFormValidity() {
        const promptText = editPromptInput.value.trim();
        if (mainImageFile && promptText) {
            startEditBtn.disabled = false;
        } else {
            startEditBtn.disabled = true;
        }
    }

    // --- Event listener for prompt input ---
    editPromptInput.addEventListener('input', checkFormValidity);

    // --- Add message and cost to the button --- START ---
    if (startEditBtn) {
        // Cost span (placeholder, will be dynamic later)
        const costSpan = document.createElement('span');
        costSpan.id = 'gpt4oEditCostSpan'; 
        costSpan.className = 'ms-2'; // Removed text-muted to inherit button's default text color
        costSpan.style.fontSize = '0.9em';
        costSpan.style.fontWeight = 'bold';
        costSpan.textContent = '(消耗: X积分)'; // Placeholder X

        // The "(赠送积分无法使用该功能！)" span
        const giftCreditMessageSpan = document.createElement('span');
        giftCreditMessageSpan.className = 'ms-2 text-info';
        giftCreditMessageSpan.style.fontSize = '0.8em';
        giftCreditMessageSpan.textContent = '(赠送积分无法使用该功能！)';

        // Append new spans. Assumes "✨ 开始编辑" is already in the button's initial HTML/text content.
        startEditBtn.appendChild(costSpan);
        startEditBtn.appendChild(giftCreditMessageSpan);
    }
    // --- Add message and cost to the button --- END ---

    // --- Function to fetch feature cost --- START ---
    async function fetchGpt4oEditCost() {
        const featureKey = 'gpt4o_image_edit';
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('[GPT-4o Edit Cost] Token not found, cannot fetch cost.');
                return null;
            }
            // 确保 API_URL 来自 main.js 或已在此文件定义
            const fullApiUrl = `${API_URL}/features/cost?key=${featureKey}`; 
            const response = await fetch(fullApiUrl, {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            if (!response.ok) {
                console.error(`[GPT-4o Edit Cost] Failed to fetch cost for ${featureKey}:`, response.status);
                return null;
            }
            const data = await response.json();
            if (data.success && data.cost !== undefined) {
                return data.cost;
            }
            console.warn(`[GPT-4o Edit Cost] Cost data not found or invalid for ${featureKey}:`, data);
            return null;
        } catch (error) {
            console.error(`[GPT-4o Edit Cost] Error fetching cost for ${featureKey}:`, error);
            return null;
        }
    }
    // --- Function to fetch feature cost --- END ---

    // --- Function to update button with cost --- START ---
    async function updateGpt4oEditButtonCost() {
        const cost = await fetchGpt4oEditCost();
        const costSpan = document.getElementById('gpt4oEditCostSpan'); // Get the span by its ID

        if (costSpan) {
            if (cost !== null) {
                costSpan.textContent = `(消耗: ${cost}积分)`;
            } else {
                costSpan.textContent = '(成本未知)'; // Fallback if cost cannot be fetched
            }
        }
    }
    // --- Function to update button with cost --- END ---

    // --- Start Button Click Handler (Placeholder for API call) ---
    startEditBtn.addEventListener('click', async () => {
        if (!mainImageFile || !editPromptInput.value.trim()) {
            console.error("Main image and prompt are required.");
            if (errorAlert) { // Assuming errorAlert is the element to show errors
                errorAlert.textContent = "主图片和编辑指令不能为空。";
                errorAlert.style.display = 'block';
            }
            return;
        }

        // --- Prepare for API Call ---
        if (loadingIndicator) loadingIndicator.style.display = 'block';
        if (errorAlert) errorAlert.style.display = 'none';
        if (resultContainer) resultContainer.style.display = 'none';
        if (resultsPlaceholder) resultsPlaceholder.style.display = 'block';
        startEditBtn.disabled = true;

        const formData = new FormData();

        const promptValue = editPromptInput.value.trim();
        if (!promptValue) {
            if (typeof showError === "function") showError("编辑指令不能为空。");
            else if (errorAlert) { errorAlert.textContent = "编辑指令不能为空。"; errorAlert.style.display = 'block'; }
            if (loadingIndicator) loadingIndicator.style.display = 'none';
            startEditBtn.disabled = false;
            return;
        }
        formData.append('prompt', promptValue);

        // 主图片，字段名为 image
        if (mainImageFile) {
            formData.append('image', mainImageFile); // 使用 'image' 作为键名
        } else {
            // 这部分理论上前端已校验，但以防万一
            if (typeof showError === "function") showError("主图片必须上传。");
            else if (errorAlert) { errorAlert.textContent = "主图片必须上传。"; errorAlert.style.display = 'block'; }
            if (loadingIndicator) loadingIndicator.style.display = 'none';
            startEditBtn.disabled = false;
            return;
        }

        // 参考图1，也使用 'image' 作为键名
        if (ref1ImageFile) {
            formData.append('image', ref1ImageFile); // 再次使用 'image' 作为键名
        }
        // 新增：参考图2，也使用 'image' 作为键名
        if (ref2ImageFile) {
            formData.append('image', ref2ImageFile);
        }

        formData.append('model', 'gpt-image-1');
        formData.append('response_format', 'b64_json');

        const qualityValue = qualitySelect ? qualitySelect.value : 'auto';
        const sizeValue = sizeSelect ? sizeSelect.value : 'auto';
        const nCountValue = nSelect ? nSelect.value : '1'; // 读取 'n' 的值，默认为 '1'

        if (qualityValue && qualityValue !== 'auto') {
            formData.append('quality', qualityValue);
        }
        if (sizeValue && sizeValue !== 'auto') {
            formData.append('size', sizeValue);
        }
        // 添加 'n' 到 formData
        if (nCountValue) { // 确保 nCountValue 存在
            formData.append('n', nCountValue);
        }

        try {
            const requestUrl = `${API_URL}/gpt4o-edit/edit-image`; // 确保 API_URL 正确设置

            const response = await fetch(requestUrl, {
                method: 'POST',
                body: formData,
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });

            if (!response.ok) {
                let displayErrorMessage = `图片编辑请求失败 (状态码: ${response.status})。`;
                try {
                    const errorData = await response.json();
                    if (errorData && errorData.error) displayErrorMessage = errorData.error;
                } catch (parseError) { console.warn('GPT-4o Edit: Failed to parse error JSON from non-ok fetch:', parseError); }
                
                if (typeof showError === "function") showError(displayErrorMessage);
                else if (errorAlert) { errorAlert.textContent = displayErrorMessage; errorAlert.style.display = 'block';}
                
                if (loadingIndicator) loadingIndicator.style.display = 'none';
                startEditBtn.disabled = false;
                return;
            }

            const data = await response.json();

            if (data.success && data.result && data.result.imageUrl) {
                if (resultImage) resultImage.src = data.result.imageUrl;
                if (resultDownloadLink) {
                    resultDownloadLink.href = '#'; 
                    const newDownloadLink = resultDownloadLink.cloneNode(true);
                    if (resultDownloadLink.parentNode) {
                        resultDownloadLink.parentNode.replaceChild(newDownloadLink, resultDownloadLink);
                    }
                    resultDownloadLink = newDownloadLink; 
                    
                    let filename = 'edited_image.png';
                    try {
                        const urlObj = new URL(data.result.imageUrl);
                        const nameFromParams = urlObj.searchParams.get("filename");
                        if (nameFromParams) filename = nameFromParams;
                    } catch(e) { /* ignore */}

                    resultDownloadLink.addEventListener('click', function(e) {
                        e.preventDefault();
                        initiateImageDownload(data.result.imageUrl, this, filename); 
                    });
                    resultDownloadLink.style.display = 'inline-block';
                }
                if (resultContainer) resultContainer.style.display = 'block';
                if (resultsPlaceholder) resultsPlaceholder.style.display = 'none';
                if (typeof loadGpt4oEditHistory === 'function') loadGpt4oEditHistory(1, false); 
            } else {
                throw new Error(data.error || data.message || '图片编辑失败，未返回有效结果。');
            }

        } catch (error) {
            console.error('Error during GPT-4o image edit:', error);
            let displayErrorMessage = `图片编辑请求发生意外错误: ${error.message || '未知错误'}`;
            if (typeof showError === "function") showError(displayErrorMessage);
            else if (errorAlert) { errorAlert.textContent = displayErrorMessage; errorAlert.style.display = 'block';}
        } finally {
            if (loadingIndicator) loadingIndicator.style.display = 'none';
            startEditBtn.disabled = false; 
        }
    });

    // --- Initial check ---
    checkFormValidity(); 
    updateGpt4oEditButtonCost(); // <<< --- Call this on DOMContentLoaded to load initial cost

    async function loadGpt4oEditHistory(page = 1, append = false) {
        if (!append) {
            if(historyListContainer) {
                historyListContainer.innerHTML = ''; 
            } else {
                console.error('[loadGpt4oEditHistory] historyListContainer is null before clearing!');
            }
            if(historyPlaceholder) {
                historyPlaceholder.textContent = '加载历史记录中...';
                historyPlaceholder.style.display = 'block';
            } else {
                console.error('[loadGpt4oEditHistory] historyPlaceholder is null before setting loading text!');
            }
        }

        if (loadMoreHistoryBtn) {
            loadMoreHistoryBtn.disabled = true;
            loadMoreHistoryBtn.textContent = '加载中...';
        }

        const token = localStorage.getItem('token');
        if (!token) {
            if(historyPlaceholder) historyPlaceholder.textContent = '请先登录以查看历史记录。';
            console.warn('Token not found for loading GPT-4o edit history');
            return;
        }

        try {
            const historyUrl = `${HISTORY_API_URL}?page=${page}&limit=8`; // 每页8条
            const response = await fetch(historyUrl, {
                headers: { 'Authorization': `Bearer ${token}` }
            });

            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();


            if (data && data.success && data.data && data.data.length > 0) {
                if(historyPlaceholder) {
                    historyPlaceholder.style.display = 'none';
                } else {
                    console.warn('[loadGpt4oEditHistory] historyPlaceholder is null or undefined before hiding.');
                }
                renderGpt4oEditHistoryList(data.data, append);
                totalHistoryPages = data.pagination.totalPages;
                currentHistoryPage = data.pagination.currentPage;
                updateLoadMoreGpt4oEditHistoryButtonState();
            } else if (!append && data.success) { // 成功但无数据
                if(historyPlaceholder) {
                    historyPlaceholder.textContent = '暂无编辑历史记录。';
                    historyPlaceholder.style.display = 'block'; // 确保在无数据时占位符可见
                } else {
                    console.warn('[loadGpt4oEditHistory] historyPlaceholder is null or undefined when history is empty.');
                }
            } else if (!data.success) {
                console.error('[loadGpt4oEditHistory] API call was not successful:', data.error);
                if(historyPlaceholder) {
                    historyPlaceholder.textContent = data.error || '加载历史失败';
                    historyPlaceholder.style.display = 'block'; // 确保错误时占位符可见
                } else {
                     console.warn('[loadGpt4oEditHistory] historyPlaceholder is null or undefined on API error.');
                }
            }

        } catch (error) {
            console.error('加载GPT4o编辑历史失败:', error);
            if (!append && historyPlaceholder) {
                historyPlaceholder.textContent = '加载历史记录失败，请稍后重试。';
            }
        } finally {
            updateLoadMoreGpt4oEditHistoryButtonState(); // 确保按钮状态更新
        }
    }

    function renderGpt4oEditHistoryList(historyItems, append) {
        if (!historyListContainer) {
            console.error('[renderGpt4oEditHistoryList] historyListContainer is null or undefined. Cannot render history.');
            if (historyPlaceholder) {
                historyPlaceholder.textContent = '无法渲染历史记录 (列表容器丢失)。';
                historyPlaceholder.style.display = 'block';
            }
            return;
        }
        if (!append) {
            historyListContainer.innerHTML = '';
        } else {
            if (historyPlaceholder && historyPlaceholder.style.display !== 'none') {
                historyPlaceholder.style.display = 'none';
            }
        }

        historyItems.forEach(item => {
            const colDiv = document.createElement('div');
            colDiv.className = 'col';

            const cardDiv = document.createElement('div');
            cardDiv.className = 'card h-100 shadow-sm upscale-history-item';

            // 结果图片
            if (item.result_image_url && item.status === 'completed') {
                const imgLink = document.createElement('a');
                imgLink.href = item.result_image_url;
                imgLink.title = '点击下载编辑结果';
                imgLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    initiateImageDownload(item.result_image_url, imgLink);
                });

                const img = document.createElement('img');
                img.src = item.result_image_url;
                img.className = 'card-img-top';
                img.style.aspectRatio = '1 / 1';
                img.style.objectFit = 'cover';
                img.alt = `编辑结果: ${item.prompt ? item.prompt.substring(0,30) : '无提示'}`;
                imgLink.appendChild(img);
                cardDiv.appendChild(imgLink);
            } else if (item.status === 'failed') {
                const failedPlaceholder = document.createElement('div');
                failedPlaceholder.className = 'card-img-top d-flex align-items-center justify-content-center bg-light text-danger';
                failedPlaceholder.style.aspectRatio = '1 / 1';
                failedPlaceholder.innerHTML = '<i class="bi bi-x-octagon-fill fs-1"></i>';
                cardDiv.appendChild(failedPlaceholder);
            }

            const cardBody = document.createElement('div');
            cardBody.className = 'card-body small p-2';

            // Prompt
            if (item.prompt) {
                const promptEl = document.createElement('p');
                promptEl.className = 'card-text mb-1 text-truncate';
                promptEl.textContent = `提示: ${item.prompt}`;
                promptEl.title = item.prompt;
                cardBody.appendChild(promptEl);
            }
            
            // 新增：复用按钮
            const reuseBtn = document.createElement('button');
            reuseBtn.className = 'btn btn-outline-primary btn-sm mb-2';
            reuseBtn.textContent = '复用参数';
            reuseBtn.title = '将此历史的参数和图片自动填入编辑区';
            reuseBtn.addEventListener('click', async (e) => {
                e.preventDefault();
                reuseBtn.disabled = true;
                reuseBtn.textContent = '填充中...';
                await reuseHistoryItem(item);
                reuseBtn.disabled = false;
                reuseBtn.textContent = '复用参数';
            });
            cardBody.appendChild(reuseBtn);

            // 模型和时间
            const metaText = document.createElement('p');
            metaText.className = 'card-text text-muted';
            metaText.style.whiteSpace = 'nowrap';
            const date = new Date(item.created_at);
            metaText.textContent = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
            cardBody.appendChild(metaText);
            
            if (item.status === 'failed' && item.error_message) {
                const errorEl = document.createElement('p');
                errorEl.className = 'card-text text-danger small text-truncate';
                errorEl.textContent = `错误: ${item.error_message}`;
                errorEl.title = item.error_message;
                cardBody.appendChild(errorEl);
            }

            cardDiv.appendChild(cardBody);
            colDiv.appendChild(cardDiv);
            historyListContainer.appendChild(colDiv);
        });
    }

    function setupLoadMoreGpt4oEditHistoryButton() {
        if (!historyPaginationContainer) return;
        if (!loadMoreHistoryBtn) {
            loadMoreHistoryBtn = document.createElement('button');
            loadMoreHistoryBtn.id = 'loadMoreGpt4oEditHistoryBtn';
            loadMoreHistoryBtn.className = 'btn btn-outline-secondary btn-sm mt-3';
            loadMoreHistoryBtn.textContent = '加载更多';
            loadMoreHistoryBtn.style.display = 'none';
            historyPaginationContainer.appendChild(loadMoreHistoryBtn);
            loadMoreHistoryBtn.addEventListener('click', () => {
                if (currentHistoryPage < totalHistoryPages) {
                    loadGpt4oEditHistory(currentHistoryPage + 1, true);
                }
            });
        }
    }

    function updateLoadMoreGpt4oEditHistoryButtonState() {
        if (!loadMoreHistoryBtn) setupLoadMoreGpt4oEditHistoryButton();
        if (!loadMoreHistoryBtn) return; // Still not there, bail

        const hasItems = historyListContainer && historyListContainer.children.length > 0;

        if (currentHistoryPage < totalHistoryPages) {
            loadMoreHistoryBtn.disabled = false;
            loadMoreHistoryBtn.textContent = '加载更多';
            loadMoreHistoryBtn.style.display = 'block';
        } else {
            loadMoreHistoryBtn.disabled = true;
            loadMoreHistoryBtn.textContent = hasItems ? '没有更多了' : '暂无记录可加载'; // 更准确的文本
            if (!hasItems && currentHistoryPage === 1) {
                 loadMoreHistoryBtn.style.display = 'none';
                 // 如果没有项目且是第一页，再次确保占位符是正确的状态
                 if (historyPlaceholder) {
                    historyPlaceholder.textContent = '暂无编辑历史记录。';
                    historyPlaceholder.style.display = 'block';
                 }
            } else {
                 loadMoreHistoryBtn.style.display = 'block';
            }
        }
    }

    // --- 初始加载和事件绑定 ---
    if (document.getElementById('gpt4o-image-edit-pane')) { //只在该 pane 存在时执行
        setupLoadMoreGpt4oEditHistoryButton();
        loadGpt4oEditHistory(1);

        // 当编辑成功时，刷新历史记录
        // 你需要在你的 handleImageEditSuccess (或类似函数) 的末尾调用: loadGpt4oEditHistory(1);
        // 例如，在你的 `fetch` 调用并成功处理结果后:
        // .then(result => { /* ... 处理结果 ... */ loadGpt4oEditHistory(1); })
    }

    // 确保在你的图片编辑成功的回调中调用 loadGpt4oEditHistory(1);
    // 例如，在你的 startButton 的 click 事件处理函数，当 fetch 成功并显示结果后：
    // ... (原有的 startButton 点击事件处理逻辑) ...
    // try {
    //    ... (API 调用) ...
    //    if (resultData.success && resultData.result) {
    //        ...
    //        loadUserCredits(); // 更新积分
    //        loadGpt4oEditHistory(1); // <<< --- 在这里或类似位置刷新历史记录
    //    }
    // } catch (error) { ... }

    async function fetchImageAsFile(url, filename) {
        const response = await fetch(url);
        const blob = await response.blob();
        // 尝试获取 mimeType
        const mimeType = blob.type || 'image/png';
        return new File([blob], filename, { type: mimeType });
    }

    // 复用历史记录
    async function reuseHistoryItem(historyItem) {
        // 1. 填充 prompt
        editPromptInput.value = historyItem.prompt || '';
        // 2. 填充图片
        for (const img of historyItem.input_images) {
            if (!img.comfy_url) continue;
            try {
                const file = await fetchImageAsFile(img.comfy_url, img.original_filename || 'input.png');
                if (img.type === 'main') {
                    mainImageFile = file;
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        mainImagePreviewImg.src = e.target.result;
                        mainImagePreviewContainer.style.display = 'block';
                        mainImageDropZone.style.display = 'none';
                    };
                    reader.readAsDataURL(file);
                } else if (img.type === 'ref1') {
                    ref1ImageFile = file;
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        ref1ImagePreviewImg.src = e.target.result;
                        ref1ImagePreviewContainer.style.display = 'block';
                        ref1ImageDropZone.style.display = 'none';
                    };
                    reader.readAsDataURL(file);
                } else if (img.type === 'ref2') {
                    ref2ImageFile = file;
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        ref2ImagePreviewImg.src = e.target.result;
                        ref2ImagePreviewContainer.style.display = 'block';
                        ref2ImageDropZone.style.display = 'none';
                    };
                    reader.readAsDataURL(file);
                }
            } catch (e) {
                if (errorAlert) {
                    errorAlert.textContent = `加载历史图片失败: ${e.message}`;
                    errorAlert.style.display = 'block';
                }
            }
        }
        checkFormValidity();
    }

}); 