document.addEventListener('DOMContentLoaded', () => {

    // --- Element References ---
    const rbTab = document.getElementById('remove-background-tab');
    const rbPane = document.getElementById('remove-background-pane');
    const dropZone = document.getElementById('rbImageDropZone');
    const fileInput = document.getElementById('rbImageInput');
    const previewContainer = document.getElementById('rbPreviewContainer');
    const previewImg = document.getElementById('rbPreviewImg');
    const removeImageBtn = document.getElementById('removeRbImage');
    const startBtn = document.getElementById('startRbBtn');
    const resultContainer = document.getElementById('rbResultContainer');
    const resultLoading = document.getElementById('rbResultLoading');
    const resultImage = document.getElementById('rbResultImage');
    let resultLink = document.getElementById('rbResultLink');
    const errorAlert = document.getElementById('rbErrorAlert');
    const resultsPlaceholder = document.getElementById('rbResultsPlaceholder');
    const historyListContainer = document.getElementById('rbHistoryList');
    const historyPlaceholder = document.getElementById('rbHistoryPlaceholder');
    const historyLoadMoreContainer = document.getElementById('rbHistoryLoadMoreContainer');
    const loadMoreHistoryBtn = document.getElementById('loadMoreRbHistoryBtn');

    // 新增：获取对新添加的下拉框元素的引用
    const engineSelect = document.getElementById('rbEngineSelect');
    const comfyUIRemModeSection = document.getElementById('comfyuiRemModeSection');
    const comfyUIRemModeSelect = document.getElementById('comfyuiRemModeSelect');

    // 新的自定义下拉框元素引用
    const rbEngineSelectBtn = document.getElementById('rbEngineSelectBtn');
    const rbEngineSelectMenu = document.getElementById('rbEngineSelectMenu');
    const rbEngineSelectValue = document.getElementById('rbEngineSelectValue');

    const comfyuiRemModeSelectBtn = document.getElementById('comfyuiRemModeSelectBtn');
    const comfyuiRemModeSelectMenu = document.getElementById('comfyuiRemModeSelectMenu');
    const comfyuiRemModeSelectValue = document.getElementById('comfyuiRemModeSelectValue');

    let currentFile = null;
    let featureCost = null;
    let currentRbHistoryPage = 1;
    let totalRbHistoryPages = 1;
    let isLoadingRbHistory = false;

    // +++ 新增函数：用于通过 Token 获取并显示图片 +++
    async function displayImageWithToken(imageUrl, imageElement) {
        const token = localStorage.getItem('token');
        const headers = {};
        // 只有目标是我们的API代理时才添加 token
        const isTargetApiDomain = imageUrl.includes('caca.yzycolour.top/api/');

        if (token && isTargetApiDomain) {
            headers['Authorization'] = `Bearer ${token}`;
        } else {
            console.warn(`[displayImageWithToken] Authorization header SKIPPED for ${imageUrl}. Target API: ${isTargetApiDomain}, Token: ${!!token}`);
        }

        try {
            const response = await fetch(imageUrl, { headers });
            if (!response.ok) {
                // 尝试解析错误信息
                let errorBody = '服务器返回错误';
                try {
                    const errJson = await response.json();
                    errorBody = errJson.message || JSON.stringify(errJson);
                } catch(e) {
                    // 如果响应不是JSON，或者没有message，使用状态文本
                    errorBody = response.statusText;
                }
                throw new Error(`获取图片失败: ${response.status} ${errorBody}`);
            }
            const blob = await response.blob();
            
            // 释放旧的 blob URL (如果存在且是 blob URL)
            if (imageElement.src && imageElement.src.startsWith('blob:')) {
                URL.revokeObjectURL(imageElement.src);
            }
            
            const blobUrl = URL.createObjectURL(blob);
            imageElement.src = blobUrl;
            
            // 注意: blobUrl 应该在图片元素不再需要时被 revokeObjectURL
            // 例如，当用户上传新图片或清除结果时。
            // 对于主结果图片 resultImage，可以在 resetResultState 中处理
            // 对于历史图片，如果它们动态加载和移除，则需要更复杂的管理

        } catch (error) {
            console.error(`[displayImageWithToken] Error loading image ${imageUrl}:`, error);
            showError(`无法加载图片预览: ${error.message}`); // 显示错误给用户
            // 可以设置一个损坏的图片占位符
            imageElement.src = '#'; // 或者一个指向本地损坏图片图标的路径
        }
    }
    // --- END 新增函数 ---

    // --- 可复用的图片下载函数 (copied from creative-upscale.js) --- START ---
    async function initiateImageDownload(imageUrl, clickedElement) {
        const originalLinkText = clickedElement.textContent;
        const originalTitle = clickedElement.title;
        let isMainResultLink = (clickedElement.id === 'rbResultLink');

        if (isMainResultLink) {
            clickedElement.textContent = '下载中...';
        }
        clickedElement.title = '下载中...';
        if (clickedElement.disabled !== undefined) {
            clickedElement.disabled = true;
        }

        try {
            const token = localStorage.getItem('token');
            const headers = {};
            // const isSameOrigin = imageUrl.startsWith(window.location.origin);
            // 更改条件：只要 token 存在并且是发往你的 API 域名，就添加 token
            const isTargetApiDomain = imageUrl.includes('caca.yzycolour.top/api/');

            if (token && isTargetApiDomain) { 
                headers['Authorization'] = `Bearer ${token}`;
            } else {
                console.warn(`[RB initiateImageDownload] Authorization header SKIPPED. Target API: ${isTargetApiDomain}, Token: ${!!token}`);
            }

            const response = await fetch(imageUrl, { headers }); 
            if (!response.ok) {
                throw new Error(`下载图片失败: ${response.status} ${response.statusText}`);
            }
            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob);
            const tempLink = document.createElement('a');
            tempLink.href = blobUrl;
            let filename = 'removed_background_image.png';
            try {
                const urlObj = new URL(imageUrl);
                const nameFromParams = urlObj.searchParams.get("filename");
                if (nameFromParams) {
                    filename = nameFromParams;
                }
            } catch (e) {
                console.warn('无法从URL解析文件名，使用默认文件名。');
            }
            tempLink.download = filename;
            document.body.appendChild(tempLink);
            tempLink.click();
            document.body.removeChild(tempLink);
            URL.revokeObjectURL(blobUrl);
        } catch (error) {
            console.error('下载图片时出错:', error);
            showError(`下载失败: ${error.message}`);
        } finally {
            if (isMainResultLink) {
                clickedElement.textContent = originalLinkText || '下载结果'; 
            }
            clickedElement.title = originalTitle;
            if (clickedElement.disabled !== undefined) {
                clickedElement.disabled = false;
            }
        }
    }
    // --- 可复用的图片下载函数 --- END ---

    // --- Event Listeners ---
    dropZone.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', handleFileSelect);
    dropZone.addEventListener('dragover', handleDragOver);
    dropZone.addEventListener('dragleave', handleDragLeave);
    dropZone.addEventListener('drop', handleDrop);
    rbPane.addEventListener('paste', handlePaste);
    removeImageBtn.addEventListener('click', resetInput);
    startBtn.addEventListener('click', handleStartProcessing);

    // 新增：为引擎选择下拉框添加事件监听
    if (engineSelect) {
        engineSelect.addEventListener('change', handleEngineChange);
    }

    if (rbTab) {
        rbTab.addEventListener('shown.bs.tab', handleTabShown);
    }
    if (loadMoreHistoryBtn) {
        loadMoreHistoryBtn.addEventListener('click', () => {
            if (!isLoadingRbHistory && currentRbHistoryPage < totalRbHistoryPages) {
                loadRbHistory(currentRbHistoryPage + 1, true);
            }
        });
    }

    // +++ 新增：通用自定义下拉框处理函数 +++
    function setupCustomDropdown(buttonElement, menuElement, valueInputElement, callbackOnChange) {
        menuElement.addEventListener('click', (event) => {
            if (event.target.classList.contains('dropdown-item')) {
                event.preventDefault();
                const selectedValue = event.target.dataset.value;
                const selectedText = event.target.textContent;

                valueInputElement.value = selectedValue;
                buttonElement.textContent = selectedText;

                // 更新 active状态
                menuElement.querySelectorAll('.dropdown-item').forEach(item => {
                    item.classList.remove('active');
                });
                event.target.classList.add('active');

                if (callbackOnChange) {
                    callbackOnChange(selectedValue);
                }
            }
        });
    }

    // 初始化自定义下拉框
    setupCustomDropdown(rbEngineSelectBtn, rbEngineSelectMenu, rbEngineSelectValue, handleEngineChange);
    setupCustomDropdown(comfyuiRemModeSelectBtn, comfyuiRemModeSelectMenu, comfyuiRemModeSelectValue, null); // ComfyUI模式选择变化时不需要立即回调特定函数

    // +++ 设置 ComfyUI 为默认引擎 +++
    function setDefaultEngineToComfyUI() {
        const comfyUIOption = rbEngineSelectMenu.querySelector('.dropdown-item[data-value="comfyui"]');
        if (rbEngineSelectValue && rbEngineSelectBtn && comfyUIOption) {
            rbEngineSelectValue.value = 'comfyui';
            rbEngineSelectBtn.textContent = comfyUIOption.textContent;

            // 更新 active 状态
            rbEngineSelectMenu.querySelectorAll('.dropdown-item').forEach(item => {
                item.classList.remove('active');
            });
            comfyUIOption.classList.add('active');

            // 触发引擎变更处理，以显示/隐藏 ComfyUI 特定选项并更新成本
            handleEngineChange(); 
        } else {
            console.warn('Could not set ComfyUI as default engine - elements not found.');
        }
    }
    setDefaultEngineToComfyUI();
    // --- 结束：新增 --- 

    // 新增：处理引擎选择变化的函数
    function handleEngineChange() {
        if (rbEngineSelectValue.value === 'comfyui') {
            comfyUIRemModeSection.style.display = 'block';
            updateButtonWithCost('comfy_remove_background'); // 更新为 ComfyUI 的成本
        } else {
            comfyUIRemModeSection.style.display = 'none';
            updateButtonWithCost('remove_background'); // 更新为默认 API 的成本
        }
    }

    async function fetchFeatureCost(featureKey = 'remove_background') { // 修改：接收 featureKey
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('未找到认证令牌，无法获取功能成本');
                return null; // 修改：返回 null
            }
            // 修改：使用 featureKey 构建 API URL
            const response = await fetch(`https://caca.yzycolour.top/api/features/cost?key=${featureKey}`, {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            if (!response.ok) {
                console.error(`获取功能 ${featureKey} 成本失败:`, response.status);
                return null; // 修改：返回 null
            }
            const data = await response.json();
            if (data.success && data.cost !== undefined) {
                return data.cost; // 修改：返回成本值
            }
            return null; // 修改：返回 null
        } catch (error) {
            console.error(`获取功能 ${featureKey} 成本出错:`, error);
            return null; // 修改：返回 null
        }
    }
    
    // 修改：updateButtonWithCost 现在也需要异步，因为它依赖 fetchFeatureCost
    async function updateButtonWithCost(featureKey = 'remove_background') {
        const cost = await fetchFeatureCost(featureKey);
        if (startBtn && cost !== null) {
            // 移除旧的成本显示 (如果存在)
            const existingBadge = startBtn.querySelector('.cost-badge');
            if (existingBadge) {
                existingBadge.remove();
            }
            
            // 查找主文本节点
            let textNode = Array.from(startBtn.childNodes)
                .find(node => node.nodeType === Node.TEXT_NODE && node.textContent.trim().startsWith('开始移除背景'));

            if (!textNode) { // 如果找不到，可能按钮文本被spinner替换了，尝试找到包含特定图标的按钮的文本
                 const iconNode = startBtn.querySelector('i.bi-eraser');
                 if (iconNode && iconNode.nextSibling && iconNode.nextSibling.nodeType === Node.TEXT_NODE) {
                    textNode = iconNode.nextSibling;
                 }
            }
            
            // 如果还是找不到，就直接在按钮末尾添加
            if (!textNode && startBtn.firstChild && startBtn.firstChild.nodeType === Node.TEXT_NODE) {
                 textNode = startBtn.firstChild; //  fallback
            }


            const baseButtonText = '开始移除背景';
            const newButtonText = `${baseButtonText} (${cost}积分)`;

            if (textNode) {
                 // 清理可能存在的旧积分文本
                textNode.textContent = textNode.textContent.replace(/\s*\([^)]*积分\)/, '').trim();
                if (textNode.textContent.trim() === '正在处理...') { // 如果正在处理，则只更新徽章
                     // 徽章更新在下面处理
                } else {
                    textNode.textContent = ` ${baseButtonText}`; // 设置基础文本
                }
            } else { // 如果没有文本节点（例如，只有图标），则在图标后添加文本节点
                const newTextNode = document.createTextNode(` ${baseButtonText}`);
                const icon = startBtn.querySelector('i');
                if (icon && icon.nextSibling) {
                    startBtn.insertBefore(newTextNode, icon.nextSibling);
                } else if (icon) {
                    startBtn.appendChild(newTextNode);
                } else {
                    startBtn.prepend(newTextNode); // 如果连图标都没有，就放最前面
                }
                textNode = newTextNode;
            }
            
            // 创建并添加新的成本徽章
            const costBadge = document.createElement('span');
            costBadge.className = 'ms-1 badge bg-secondary cost-badge'; // 添加 cost-badge 类用于识别
            costBadge.textContent = `${cost}积分`;
            
            // 将徽章插入到文本节点之后，或者按钮的末尾
            if (textNode && textNode.nextSibling) {
                startBtn.insertBefore(costBadge, textNode.nextSibling);
            } else if (textNode) {
                startBtn.appendChild(costBadge); // 如果文本节点是最后一个子节点
            } else {
                // 如果没有文本节点（可能只有图标），尝试添加到图标后，或按钮末尾
                const icon = startBtn.querySelector('i');
                if (icon && icon.nextSibling) {
                    startBtn.insertBefore(costBadge, icon.nextSibling);
                } else if (icon) {
                    startBtn.appendChild(costBadge);
                } else {
                    startBtn.appendChild(costBadge); // 最后尝试直接添加到按钮
                }
            }
        }
    }

    function handleFileSelect(event) {
        const files = event.target.files;
        if (files && files.length > 0) {
            processFile(files[0]);
        }
        fileInput.value = null;
    }

    function handleDragOver(event) {
        event.preventDefault();
        dropZone.classList.add('drag-over');
    }

    function handleDragLeave(event) {
        event.preventDefault();
        dropZone.classList.remove('drag-over');
    }

    function handleDrop(event) {
        event.preventDefault();
        dropZone.classList.remove('drag-over');
        const files = event.dataTransfer.files;
        if (files && files.length > 0) {
            processFile(files[0]);
        }
    }

    function handlePaste(event) {
        const items = (event.clipboardData || window.clipboardData).items;
        for (let item of items) {
            if (item.kind === 'file' && item.type.startsWith('image/')) {
                const file = item.getAsFile();
                processFile(file);
                event.preventDefault();
                break;
            }
        }
    }

    function processFile(file) {
        if (!file.type.startsWith('image/')) {
            showError('请选择图片文件。');
            return;
        }
        if (file.size > 5 * 1024 * 1024) {
            showError('图片文件大小不能超过 5MB。');
            return;
        }
        currentFile = file;
        const reader = new FileReader();
        reader.onload = (e) => {
            previewImg.src = e.target.result;
            previewContainer.style.display = 'block';
            dropZone.style.display = 'none';
            startBtn.disabled = false;
            resetResultState();
        };
        reader.readAsDataURL(currentFile);
    }

    function resetInput() {
        currentFile = null;
        fileInput.value = null;
        previewImg.src = '';
        previewContainer.style.display = 'none';
        dropZone.style.display = 'flex';
        startBtn.disabled = true;
        resetResultState();
    }

    function resetResultState() {
        resultContainer.style.display = 'none';
        resultLoading.style.display = 'none';
        errorAlert.style.display = 'none';
        resultsPlaceholder.style.display = 'block';
        
        // 释放旧的 blob URL (如果 resultImage.src 是 blob URL)
        if (resultImage.src && resultImage.src.startsWith('blob:')) {
            URL.revokeObjectURL(resultImage.src);
        }
        resultImage.src = '#'; // 重置为无效/占位符

        const newResultLink = resultLink.cloneNode(true);
        if (resultLink.parentNode) {
             resultLink.parentNode.replaceChild(newResultLink, resultLink);
        }
        resultLink = newResultLink;
    }

    function showError(message) {
        errorAlert.textContent = message;
        errorAlert.style.display = 'block';
        resultContainer.style.display = 'none';
        resultLoading.style.display = 'none';
        resultsPlaceholder.style.display = 'block'; // 出错时重新显示占位符
    }

    function setProcessingState(isProcessing) {
        if (isProcessing) {
            startBtn.disabled = true;
             const icon = startBtn.querySelector('i');
             if (icon) icon.style.display = 'none';
             const textNode = Array.from(startBtn.childNodes).find(node => node.nodeType === Node.TEXT_NODE && node.textContent.trim().length > 0);
            if (textNode) textNode.textContent = ' 正在处理...';
            
            resultLoading.style.display = 'flex'; // 显示加载动画
            resultContainer.style.display = 'none'; // 隐藏结果容器
            errorAlert.style.display = 'none';    // 隐藏错误提示
            resultsPlaceholder.style.display = 'none'; // 隐藏占位符
        } else {
            startBtn.disabled = !currentFile;
             const icon = startBtn.querySelector('i');
             if (icon) icon.style.display = 'inline-block';
             const textNode = Array.from(startBtn.childNodes).find(node => node.nodeType === Node.TEXT_NODE && node.textContent.trim().length > 0);
            if (textNode) textNode.textContent = '开始移除背景';
            resultLoading.style.display = 'none'; // 处理结束（无论成功失败），隐藏加载动画
        }
    }

    async function handleStartProcessing() {
        if (!currentFile) {
            showError('请先选择一个图片文件。');
            return;
        }

        setProcessingState(true); // 设置为处理中状态 (会显示加载动画，隐藏其他)

        const formData = new FormData();
        formData.append('file', currentFile); // 后端 multer 使用 'file'

        const selectedEngine = rbEngineSelectValue.value;
        let endpoint = '';
        let isComfyRequest = false;

        if (selectedEngine === 'comfyui') {
            const remMode = comfyuiRemModeSelectValue.value;
            formData.append('rem_mode', remMode);
            endpoint = 'https://caca.yzycolour.top/api/images/comfy-remove-background';
            isComfyRequest = true;
        } else {
            endpoint = 'https://caca.yzycolour.top/api/images/remove-background'; // 默认 API 接口
        }

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                showError('用户未登录，请先登录。');
                setProcessingState(false);
                return;
            }

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ message: `请求失败，状态码: ${response.status}` }));
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                let imageUrlToDisplay; // 这个变量主要用于下载链接
                if (isComfyRequest) {
                    // ComfyUI 代理接口返回的是代理后的 URL
                    imageUrlToDisplay = data.proxiedImageUrl; 
                    if (!imageUrlToDisplay) {
                        throw new Error('未能获取处理后的图片URL (ComfyUI)。');
                    }
                    // 使用新函数显示图片
                    await displayImageWithToken(imageUrlToDisplay, resultImage);
                } else {
                    // 旧 API 返回的是直接的图片 URL (可能是 CDN)
                    imageUrlToDisplay = data.imageUrl;
                    if (!imageUrlToDisplay) {
                        throw new Error('未能获取处理后的图片URL (API)。');
                    }
                    if (imageUrlToDisplay.includes('caca.yzycolour.top/api/')) { 
                         await displayImageWithToken(imageUrlToDisplay, resultImage);
                    } else {
                         resultImage.src = imageUrlToDisplay;
                    }
                }

                resultContainer.style.display = 'block'; // 成功时显示结果容器
                // resultsPlaceholder.style.display = 'none'; // 已在 setProcessingState(true) 中隐藏
                // resultLoading.style.display = 'none'; // 将由 finally 中的 setProcessingState(false) 隐藏
                
                // 更新下载链接
                resultLink.onclick = (e) => {
                    e.preventDefault();
                    initiateImageDownload(imageUrlToDisplay, resultLink); // imageUrlToDisplay 应该是原始的、可下载的URL
                };
                resultLink.href = '#'; // 避免默认跳转

                // 更新积分显示
                if (typeof updateUserCredits === 'function' && data.newCredits !== undefined) {
                    updateUserCredits(data.newCredits, data.newDailyFreeUsed);
                }
                loadRbHistory(1, false); // 刷新历史记录
            } else {
                showError(data.message || '处理图片失败，请稍后再试。');
            }

        } catch (error) {
            console.error('移除背景过程中发生错误:', error);
            showError(`处理失败: ${error.message}`); // showError 会隐藏加载，显示错误和占位符
        } finally {
            // 在隐藏加载卡片前，如果卡片当前是显示的，则播放一次性扫描动画
            if (rbResultLoading.style.display === 'flex') {
                rbResultLoading.classList.add('play-scan-once');
                rbResultLoading.addEventListener('animationend', () => {
                    rbResultLoading.classList.remove('play-scan-once');
                }, { once: true });
            }
            setProcessingState(false); // 设置为非处理中状态 (会隐藏加载动画，重置按钮)
            handleEngineChange(); // 调用它来确保按钮成本根据当前引擎更新
        }
    }

    async function loadRbHistory(page = 1, append = false) {
        if (isLoadingRbHistory) return;
        isLoadingRbHistory = true;

        if (!append) {
            historyListContainer.innerHTML = '';
            historyPlaceholder.textContent = '加载历史记录中...';
            historyPlaceholder.style.display = 'block';
        } else {
            if(loadMoreHistoryBtn) {
            loadMoreHistoryBtn.disabled = true;
            loadMoreHistoryBtn.textContent = '加载中...';
            }
        }
        if(historyLoadMoreContainer) historyLoadMoreContainer.style.display = 'none';

        const token = localStorage.getItem('token');
        if (!token) {
            historyPlaceholder.textContent = '请先登录以查看历史记录。';
            isLoadingRbHistory = false;
            return;
        }

        try {
            const apiBaseUrl = window.API_URL || 'https://caca.yzycolour.top';
            const historyUrl = `${apiBaseUrl}/remove-background/history?page=${page}&limit=8`;
            const response = await fetch(historyUrl, {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();

            if (data && data.history) {
                historyPlaceholder.style.display = 'none';
                renderRbHistory(data.history);
                currentRbHistoryPage = data.pagination.currentPage;
                totalRbHistoryPages = data.pagination.totalPages;
                updateLoadMoreRbButtonState();
            } else {
                if (!append) {
                    historyPlaceholder.textContent = '暂无背景移除历史记录。';
                    historyPlaceholder.style.display = 'block';
                }
                updateLoadMoreRbButtonState();
            }
        } catch (error) {
            console.error('[Remove Background History] 加载历史记录失败:', error);
            if (!append) {
                historyPlaceholder.textContent = '加载历史记录失败，请稍后重试。';
                historyPlaceholder.style.display = 'block';
            } else {
                if(window.showToast) window.showToast('加载更多历史记录失败', 'error');
            }
            updateLoadMoreRbButtonState();
        } finally {
            isLoadingRbHistory = false;
            if (append && loadMoreHistoryBtn) {
                loadMoreHistoryBtn.disabled = false;
                loadMoreHistoryBtn.textContent = '加载更多';
            }
        }
    }

    function renderRbHistory(historyItems) {
        if (!historyListContainer.classList.contains('history-list-flex-applied')) {
             historyListContainer.style.display = 'flex';
             historyListContainer.style.flexWrap = 'wrap';
             historyListContainer.style.gap = '1rem';
            historyListContainer.classList.add('history-list-flex-applied');
        }

        if (historyItems.length === 0 && historyListContainer.children.length === 0) {
             historyPlaceholder.textContent = '暂无背景移除历史记录。';
             historyPlaceholder.style.display = 'block';
             return;
        }
        historyPlaceholder.style.display = 'none';

        historyItems.forEach(item => {
            const historyCard = document.createElement('div');
            historyCard.className = 'upscale-history-item text-center'; 
            historyCard.style.flex = '0 0 auto';
            historyCard.style.maxWidth = '144px'; 

            const timeSmall = document.createElement('small');
            timeSmall.className = 'd-block text-muted mb-1';
            timeSmall.textContent = formatFullDateTime(item.created_at);
            historyCard.appendChild(timeSmall);

            const imgLink = document.createElement('a');
            imgLink.href = item.result_image_url;
            imgLink.title = `点击下载图片 (原始文件名: ${item.original_image_filename || 'N/A'})`;

            const img = document.createElement('img');
            img.alt = 'Removed background image history';
            img.loading = 'lazy'; 
            img.className = 'img-fluid rounded border'; 
            img.style.objectFit = 'cover';
            img.style.aspectRatio = '1 / 1';
            
            // 修改：如果历史图片URL是代理URL，则使用 displayImageWithToken
            if (item.result_image_url && item.result_image_url.includes('caca.yzycolour.top/api/')) {
                // 设置一个占位符或者初始不设置src，然后异步加载
                // img.src = 'placeholder.gif'; // 你可以有一个小的加载中gif
                displayImageWithToken(item.result_image_url, img);
            } else if (item.result_image_url) {
                img.src = item.result_image_url; 
            } else {
                img.src = '#'; // 无效URL或占位符
            }

            imgLink.appendChild(img);

            imgLink.addEventListener('click', (event) => {
                event.preventDefault();
                initiateImageDownload(item.result_image_url, imgLink);
            });

            historyCard.appendChild(imgLink);
            historyListContainer.appendChild(historyCard);
        });
    }

    function updateLoadMoreRbButtonState() {
        if (!loadMoreHistoryBtn || !historyLoadMoreContainer) return;
        if (currentRbHistoryPage < totalRbHistoryPages) {
            historyLoadMoreContainer.style.display = 'block';
            loadMoreHistoryBtn.disabled = false;
            loadMoreHistoryBtn.textContent = '加载更多';
        } else {
            historyLoadMoreContainer.style.display = 'none';
        }
    }

    function formatFullDateTime(dateString) {
        if (!dateString) return '未知时间';
        try {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric', month: '2-digit', day: '2-digit', 
                hour: '2-digit', minute: '2-digit', hour12: false
            });
        } catch (e) { return '日期无效'; }
    }

    async function loadImageFromUrl(imageUrl) {
        try {
            // displayImageWithToken 内部会处理 token 和 blob
            // processFile 需要一个 File 对象
            // 所以，这里我们需要先 fetch blob，然后创建 File 对象，再传给 processFile

            const token = localStorage.getItem('token');
            const headers = {};
            const isTargetApiDomain = imageUrl.includes('caca.yzycolour.top/api/');
            
            if (token && isTargetApiDomain) { 
                headers['Authorization'] = `Bearer ${token}`;
            } else {
                console.warn(`[RB loadImageFromUrl] Authorization header SKIPPED. Target API: ${isTargetApiDomain}, Token: ${!!token}`);
            }

            const response = await fetch(imageUrl, { headers }); 
            if (!response.ok) throw new Error(`Failed to fetch image: ${response.status}`);
            const blob = await response.blob();
            
            let filename = 'image_from_history.png';
            try { 
                // 尝试从 Content-Disposition 获取文件名 (如果后端设置了)
                const disposition = response.headers.get('Content-Disposition');
                if (disposition) {
                    const filenameRegex = /filename[^;=\\n]*=((['"]).*?\\2|[^;\\n]*)/;
                    const matches = filenameRegex.exec(disposition);
                    if (matches != null && matches[1]) {
                        filename = matches[1].replace(/['"]/g, '');
                    } else { // 尝试从URL路径获取
                        filename = new URL(imageUrl).pathname.split('/').pop() || filename;
                    }
                } else { // 尝试从URL路径获取
                     filename = new URL(imageUrl).pathname.split('/').pop() || filename;
                }
            } catch (e) {
                 console.warn('[RB loadImageFromUrl] Could not determine filename, using default.');
            }
            
            const imageFile = new File([blob], filename, { type: blob.type });
            processFile(imageFile); // processFile 会设置左侧预览图
        } catch (error) {
            console.error('[Remove Background] Error loading image from URL:', error);
            showError(`加载图片失败: ${error.message}`);
        }
    }

    function handleTabShown() {
        // 确保打开 tab 时总是尝试加载第一页历史记录 (如果当前没有正在加载的任务)
        if (!isLoadingRbHistory) { 
            loadRbHistory(1, false);
        } else {
        }
        handleEngineChange(); // 确保打开 tab 时根据当前选择更新成本和UI
    }

    // 在 DOMContentLoaded 的最后，也主动调用一次 handleTabShown，前提是该 tab 当前是激活的
    // 这确保了页面首次加载时，如果背景移除 tab 默认激活，历史也会被加载
    if (rbTab && rbPane && rbTab.classList.contains('active') && rbPane.classList.contains('active')) {
        // setTimeout(handleTabShown, 100); // 加一个小的延迟确保其他初始化完成
         handleTabShown(); // 直接调用，如果内部有isLoadingRbHistory保护，应该没问题
    }

    fetchFeatureCost();
}); 