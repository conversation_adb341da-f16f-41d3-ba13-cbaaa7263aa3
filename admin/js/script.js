// 全局变量用于存储功能成本
let featureCosts = {};

// 获取功能成本信息
async function fetchFeatureCosts() {
    try {
        const response = await fetch('https://caca.yzycolour.top/api/credits/feature-costs');
        if (!response.ok) {
            throw new Error(`HTTP error: ${response.status}`);
        }
        const data = await response.json();
        if (data.success && data.costs) {
            featureCosts = data.costs;
            updateButtonsWithCosts();
        }
    } catch (error) {
        console.error('获取功能积分成本失败:', error);
    }
}

// 更新按钮显示积分成本
function updateButtonsWithCosts() {
    // 定义功能按钮映射关系
    const buttonMappings = {
        'startAIGenerationBtn': 'ai_generate',
        'startUpscaleBtn': 'creative_upscale',
        'startRemoveBgBtn': 'remove_background',
        'startVectorizeBtn': 'vectorize_image',
        'startImage3dBtn': 'image_to_3d',
        // 添加更多按钮ID和对应的feature_key
    };

    // 遍历按钮映射关系
    Object.entries(buttonMappings).forEach(([btnId, featureKey]) => {
        const button = document.getElementById(btnId);
        if (button && featureCosts[featureKey]) {
            const cost = featureCosts[featureKey].cost;
            
            // 检查按钮是否包含<i>元素
            const iconElement = button.querySelector('i');
            let buttonText = button.innerHTML;
            
            if (iconElement) {
                // 如果有图标，需要保留图标部分
                const iconHtml = iconElement.outerHTML;
                buttonText = buttonText.replace(iconHtml, '').trim();
                
                // 添加积分信息到按钮文本中
                button.innerHTML = `${iconHtml} ${buttonText} <span class="badge bg-light text-dark ms-1">${cost}积分</span>`;
            } else {
                // 如果没有图标，直接添加积分信息
                button.innerHTML = `${buttonText} <span class="badge bg-light text-dark ms-1">${cost}积分</span>`;
            }
        }
    });
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin script loaded');
    
    // 获取功能积分成本
    fetchFeatureCosts();
    
    // Tab激活时触发事件
    const tabEls = document.querySelectorAll('button[data-bs-toggle="tab"]');
    tabEls.forEach(tabEl => {
        tabEl.addEventListener('shown.bs.tab', event => {
            const targetTabId = event.target.getAttribute('data-bs-target');
            console.log(`Tab ${targetTabId} is shown`);
        });
    });
    
    // 加载积分信息
    if (typeof fetchCredits === 'function') {
        fetchCredits();
    }
}); 