document.addEventListener('DOMContentLoaded', () => {

    // --- 元素引用 ---
    const flTab = document.getElementById('flash-light-tab');
    const flPane = document.getElementById('flash-light-pane');
    const dropZone = document.getElementById('flImageDropZone');
    const fileInput = document.getElementById('flImageInput');
    const previewContainer = document.getElementById('flPreviewContainer');
    const previewImg = document.getElementById('flPreviewImg');
    const removeImageBtn = document.getElementById('removeFlImage');
    const startBtn = document.getElementById('startFlBtn');
    const resultContainer = document.getElementById('flResultContainer');
    const resultLoading = document.getElementById('flResultLoading');
    const resultImage = document.getElementById('flResultImage');
    let resultLink = document.getElementById('flResultLink');
    const errorAlert = document.getElementById('flErrorAlert');
    const resultsPlaceholder = document.getElementById('flResultsPlaceholder');
    const historyListContainer = document.getElementById('flHistoryList');
    const historyPlaceholder = document.getElementById('flHistoryPlaceholder');
    const historyLoadMoreContainer = document.getElementById('flHistoryLoadMoreContainer');
    const loadMoreHistoryBtn = document.getElementById('loadMoreFlHistoryBtn');
    
    // 新增美颜相关元素
    const functionTypeSelectBtn = document.getElementById('flFunctionTypeSelectBtn');
    const functionTypeSelectMenu = document.getElementById('flFunctionTypeSelectMenu');
    const functionTypeSelectValue = document.getElementById('flFunctionTypeSelectValue');
    const beautyParamsContainer = document.getElementById('beautyParamsContainer');
    const beautyStrengthRange = document.getElementById('beautyStrengthRange');
    const beautyStrengthValue = document.getElementById('beautyStrengthValue');
    const enhancedBeautyCheck = document.getElementById('enhancedBeautyCheck');
    const slimFaceCheck = document.getElementById('slimFaceCheck');
    const flProcessDescription = document.getElementById('flProcessDescription');
    
    // 工作流名称常量
    const WORKFLOW_FLASH_LIGHT = '闪光灯';  // ID: 7
    const WORKFLOW_BEAUTY = '美颜';       // ID: 8

    let currentFile = null;
    let featureCost = null;
    let currentFlHistoryPage = 1;
    let totalFlHistoryPages = 1;
    let isLoadingFlHistory = false;
    // 当前选择的功能类型
    let currentFunctionType = 'flash_light';

    // +++ 通过 Token 获取并显示图片函数 +++
    async function displayImageWithToken(imageUrl, imageElement) {
        const token = localStorage.getItem('token');
        const headers = {};
        // 只有目标是我们的API代理时才添加 token
        const isTargetApiDomain = imageUrl.includes('caca.yzycolour.top/api/');

        if (token && isTargetApiDomain) {
            headers['Authorization'] = `Bearer ${token}`;
        } else {
            console.warn(`[displayImageWithToken] Authorization header SKIPPED for ${imageUrl}. Target API: ${isTargetApiDomain}, Token: ${!!token}`);
        }

        try {
            const response = await fetch(imageUrl, { headers });
            if (!response.ok) {
                // 尝试解析错误信息
                let errorBody = '服务器返回错误';
                try {
                    const errJson = await response.json();
                    errorBody = errJson.message || JSON.stringify(errJson);
                } catch(e) {
                    // 如果响应不是JSON，或者没有message，使用状态文本
                    errorBody = response.statusText;
                }
                throw new Error(`获取图片失败: ${response.status} ${errorBody}`);
            }
            const blob = await response.blob();
            
            // 释放旧的 blob URL (如果存在且是 blob URL)
            if (imageElement.src && imageElement.src.startsWith('blob:')) {
                URL.revokeObjectURL(imageElement.src);
            }
            
            const blobUrl = URL.createObjectURL(blob);
            imageElement.src = blobUrl;
        } catch (error) {
            console.error(`[displayImageWithToken] Error loading image ${imageUrl}:`, error);
            showError(`无法加载图片预览: ${error.message}`); // 显示错误给用户
            // 可以设置一个损坏的图片占位符
            imageElement.src = '#'; // 或者一个指向本地损坏图片图标的路径
        }
    }
    
    // +++ 获取ComfyUI队列信息 +++
    async function getComfyUIQueueStatus() {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('[Flash Light] 未找到认证令牌，无法获取队列信息');
                return null;
            }
            
            const response = await fetch('https://caca.yzycolour.top/api/flux/comfy-queue-status', {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            
            if (!response.ok) {
                console.error('[Flash Light] 获取队列信息失败:', response.status);
                return null;
            }
            
            const data = await response.json();
            
            // 修正：同时处理两种可能的数据结构
            const queueInfo = {
                exec_info: {
                    queue_remaining: 0
                }
            };
            
            // 直接结构中包含queue_remaining的情况（旧结构）
            if (data && data.exec_info && data.exec_info.queue_remaining !== undefined) {
                queueInfo.exec_info.queue_remaining = data.exec_info.queue_remaining;
            } 
            // 新结构，包含在queue.remaining中
            else if (data && data.queue && data.queue.remaining !== undefined) {
                queueInfo.exec_info.queue_remaining = data.queue.remaining;
            }
            
            return queueInfo;
        } catch (error) {
            console.error('[Flash Light] 获取队列信息错误:', error);
            return null;
        }
    }
    
    // +++ 更新队列信息显示 +++
    function updateQueueMessage(position) {
        if (!resultLoading) return;
        
        if (position && resultLoading.style.display === 'block') {
            if (position > 1) {
                resultLoading.innerHTML = `
                    <div class="loading-dots"><span></span><span></span><span></span></div>
                    <p class="text-center">服务器繁忙，您前面还有 ${position - 1} 个任务。</p>
                    <p class="text-center">您的任务排在第 ${position} 位，请稍候...</p>`;
            } else {
                resultLoading.innerHTML = `
                    <div class="loading-dots"><span></span><span></span><span></span></div>
                    <p class="text-center">您的任务已提交，即将开始处理...</p>`;
            }
        }
    }

    // --- 图片下载函数 ---
    async function initiateImageDownload(imageUrl, clickedElement) {
        const originalLinkText = clickedElement.textContent;
        const originalTitle = clickedElement.title;
        let isMainResultLink = (clickedElement.id === 'flResultLink');

        if (isMainResultLink) {
            clickedElement.textContent = '下载中...';
        }
        clickedElement.title = '下载中...';
        if (clickedElement.disabled !== undefined) {
            clickedElement.disabled = true;
        }

        try {
            const token = localStorage.getItem('token');
            const headers = {};
            // 更改条件：只要 token 存在并且是发往你的 API 域名，就添加 token
            const isTargetApiDomain = imageUrl.includes('caca.yzycolour.top/api/');

            if (token && isTargetApiDomain) { 
                headers['Authorization'] = `Bearer ${token}`;
            } else {
                console.warn(`[FL initiateImageDownload] Authorization header SKIPPED. Target API: ${isTargetApiDomain}, Token: ${!!token}`);
            }

            const response = await fetch(imageUrl, { headers }); 
            if (!response.ok) {
                throw new Error(`下载图片失败: ${response.status} ${response.statusText}`);
            }
            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob);
            const tempLink = document.createElement('a');
            tempLink.href = blobUrl;
            let filename = 'flash_light_image.png';
            try {
                const urlObj = new URL(imageUrl);
                const nameFromParams = urlObj.searchParams.get("filename");
                if (nameFromParams) {
                    filename = nameFromParams;
                }
            } catch (e) {
                console.warn('无法从URL解析文件名，使用默认文件名。');
            }
            tempLink.download = filename;
            document.body.appendChild(tempLink);
            tempLink.click();
            document.body.removeChild(tempLink);
            URL.revokeObjectURL(blobUrl);
        } catch (error) {
            console.error('下载图片时出错:', error);
            showError(`下载失败: ${error.message}`);
        } finally {
            if (isMainResultLink) {
                clickedElement.textContent = originalLinkText || '下载结果'; 
            }
            clickedElement.title = originalTitle;
            if (clickedElement.disabled !== undefined) {
                clickedElement.disabled = false;
            }
        }
    }

    // --- 事件监听器 ---
    dropZone.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', handleFileSelect);
    dropZone.addEventListener('dragover', handleDragOver);
    dropZone.addEventListener('dragleave', handleDragLeave);
    dropZone.addEventListener('drop', handleDrop);
    flPane.addEventListener('paste', handlePaste);
    removeImageBtn.addEventListener('click', resetInput);
    startBtn.addEventListener('click', handleStartProcessing);

    // 新增美颜相关事件监听器
    beautyStrengthRange.addEventListener('input', function() {
        const value = this.value;
        beautyStrengthValue.textContent = value;
    });

    if (flTab) {
        flTab.addEventListener('shown.bs.tab', handleTabShown);
    }
    if (loadMoreHistoryBtn) {
        loadMoreHistoryBtn.addEventListener('click', () => {
            if (!isLoadingFlHistory && currentFlHistoryPage < totalFlHistoryPages) {
                loadFlHistory(currentFlHistoryPage + 1, true);
            }
        });
    }

    // +++ 自定义下拉框处理函数 +++
    function setupCustomDropdown(buttonElement, menuElement, valueInputElement, callbackOnChange) {
        menuElement.addEventListener('click', (event) => {
            if (event.target.classList.contains('dropdown-item')) {
                event.preventDefault();
                const selectedValue = event.target.dataset.value;
                const selectedText = event.target.textContent;

                valueInputElement.value = selectedValue;
                buttonElement.textContent = selectedText;

                // 更新 active状态
                menuElement.querySelectorAll('.dropdown-item').forEach(item => {
                    item.classList.remove('active');
                });
                event.target.classList.add('active');

                if (callbackOnChange) {
                    callbackOnChange(selectedValue);
                }
            }
        });
    }

    // 初始化功能类型下拉框
    setupCustomDropdown(
        functionTypeSelectBtn, 
        functionTypeSelectMenu, 
        functionTypeSelectValue, 
        handleFunctionTypeChange
    );

    // 处理功能类型变更
    function handleFunctionTypeChange(newType) {
        currentFunctionType = newType;
        
        // 更新UI
        if (newType === 'beauty') {
            beautyParamsContainer.style.display = 'block';
            flProcessDescription.textContent = '美颜，让照片人物更加精致。';
            startBtn.innerHTML = '<i class="bi bi-magic me-1"></i>开始美颜';
            // 更新美颜功能的成本
            updateButtonWithCost('beauty');
        } else {
            beautyParamsContainer.style.display = 'none';
            flProcessDescription.textContent = '开始补光，拯救废片。';
            startBtn.innerHTML = '<i class="bi bi-lightning me-1"></i>开始补光，拯救废片';
            // 更新补光功能的成本
            updateButtonWithCost('flash_light');
        }
        
        // 重置结果区
        resetResultState();
    }

    async function fetchFeatureCost(featureKey = 'flash_light') {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('未找到认证令牌，无法获取功能成本');
                return null;
            }
            const response = await fetch(`https://caca.yzycolour.top/api/features/cost?key=${featureKey}`, {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            if (!response.ok) {
                console.error(`获取功能 ${featureKey} 成本失败:`, response.status);
                return null;
            }
            const data = await response.json();
            if (data.success && data.cost !== undefined) {
                return data.cost;
            }
            return null;
        } catch (error) {
            console.error(`获取功能 ${featureKey} 成本出错:`, error);
            return null;
        }
    }
    
    async function updateButtonWithCost(featureKey = 'flash_light') {
        const cost = await fetchFeatureCost(featureKey);
        if (startBtn && cost !== null) {
            // 移除旧的成本显示 (如果存在)
            const existingBadge = startBtn.querySelector('.cost-badge');
            if (existingBadge) {
                existingBadge.remove();
            }
            
            // 查找主文本节点
            let textNode = Array.from(startBtn.childNodes)
                .find(node => node.nodeType === Node.TEXT_NODE && node.textContent.trim().length > 0);

            if (!textNode) {
                 const iconNode = startBtn.querySelector('i.bi-lightning, i.bi-magic');
                 if (iconNode && iconNode.nextSibling && iconNode.nextSibling.nodeType === Node.TEXT_NODE) {
                    textNode = iconNode.nextSibling;
                 }
            }
            
            // 如果还是找不到，就直接在按钮末尾添加
            if (!textNode && startBtn.firstChild && startBtn.firstChild.nodeType === Node.TEXT_NODE) {
                 textNode = startBtn.firstChild;
            }

            let baseButtonText = currentFunctionType === 'beauty' ? '开始美颜' : '开始补光';
            const newButtonText = `${baseButtonText} (${cost}积分)`;

            if (textNode) {
                 // 清理可能存在的旧积分文本
                textNode.textContent = textNode.textContent.replace(/\s*\([^)]*积分\)/, '').trim();
                if (textNode.textContent.trim() === '正在处理...') { 
                     // 徽章更新在下面处理
                } else {
                    textNode.textContent = ` ${baseButtonText}`;
                }
            } else {
                const newTextNode = document.createTextNode(` ${baseButtonText}`);
                const icon = startBtn.querySelector('i');
                if (icon && icon.nextSibling) {
                    startBtn.insertBefore(newTextNode, icon.nextSibling);
                } else if (icon) {
                    startBtn.appendChild(newTextNode);
                } else {
                    startBtn.prepend(newTextNode);
                }
                textNode = newTextNode;
            }
            
            // 创建并添加成本徽章
            const costBadge = document.createElement('span');
            costBadge.className = 'ms-1 badge bg-secondary cost-badge';
            costBadge.textContent = `${cost}积分`;
            
            if (textNode && textNode.nextSibling) {
                startBtn.insertBefore(costBadge, textNode.nextSibling);
            } else if (textNode) {
                startBtn.appendChild(costBadge);
            } else {
                const icon = startBtn.querySelector('i');
                if (icon && icon.nextSibling) {
                    startBtn.insertBefore(costBadge, icon.nextSibling);
                } else if (icon) {
                    startBtn.appendChild(costBadge);
                } else {
                    startBtn.appendChild(costBadge);
                }
            }
        }
    }

    function handleFileSelect(event) {
        const files = event.target.files;
        if (files && files.length > 0) {
            processFile(files[0]);
        }
        fileInput.value = null;
    }

    function handleDragOver(event) {
        event.preventDefault();
        dropZone.classList.add('drag-over');
    }

    function handleDragLeave(event) {
        event.preventDefault();
        dropZone.classList.remove('drag-over');
    }

    function handleDrop(event) {
        event.preventDefault();
        dropZone.classList.remove('drag-over');
        const files = event.dataTransfer.files;
        if (files && files.length > 0) {
            processFile(files[0]);
        }
    }

    function handlePaste(event) {
        const items = (event.clipboardData || window.clipboardData).items;
        for (let item of items) {
            if (item.kind === 'file' && item.type.startsWith('image/')) {
                const file = item.getAsFile();
                processFile(file);
                event.preventDefault();
                break;
            }
        }
    }

    function processFile(file) {
        if (!file.type.startsWith('image/')) {
            showError('请选择图片文件。');
            return;
        }
        if (file.size > 5 * 1024 * 1024) {
            showError('图片文件大小不能超过 5MB。');
            return;
        }
        currentFile = file;
        const reader = new FileReader();
        reader.onload = (e) => {
            previewImg.src = e.target.result;
            previewContainer.style.display = 'block';
            dropZone.style.display = 'none';
            startBtn.disabled = false;
            resetResultState();
        };
        reader.readAsDataURL(currentFile);
    }

    function resetInput() {
        currentFile = null;
        fileInput.value = null;
        previewImg.src = '';
        previewContainer.style.display = 'none';
        dropZone.style.display = 'flex';
        startBtn.disabled = true;
        resetResultState();
    }

    function resetResultState() {
        resultContainer.style.display = 'none';
        resultLoading.style.display = 'none';
        errorAlert.style.display = 'none';
        resultsPlaceholder.style.display = 'block';
        
        // 释放旧的 blob URL
        if (resultImage.src && resultImage.src.startsWith('blob:')) {
            URL.revokeObjectURL(resultImage.src);
        }
        resultImage.src = '#';

        const newResultLink = resultLink.cloneNode(true);
        if (resultLink.parentNode) {
             resultLink.parentNode.replaceChild(newResultLink, resultLink);
        }
        resultLink = newResultLink;
    }

    function showError(message) {
        errorAlert.textContent = message;
        errorAlert.style.display = 'block';
        resultContainer.style.display = 'none';
        resultLoading.style.display = 'none';
        resultsPlaceholder.style.display = 'block';
    }

    function setProcessingState(isProcessing) {
        if (isProcessing) {
            startBtn.disabled = true;
            const icon = startBtn.querySelector('i');
            if (icon) icon.style.display = 'none';
            const textNode = Array.from(startBtn.childNodes).find(node => node.nodeType === Node.TEXT_NODE && node.textContent.trim().length > 0);
            if (textNode) textNode.textContent = ' 正在处理...';
            
            resultLoading.style.display = 'flex';
            resultLoading.innerHTML = `
                <div class="loading-dots">
                    <span></span><span></span><span></span>
                </div>
                <p>正在处理图片，请稍候...</p>
            `;
            resultContainer.style.display = 'none';
            errorAlert.style.display = 'none';
            resultsPlaceholder.style.display = 'none';
        } else {
            startBtn.disabled = !currentFile;
            const icon = startBtn.querySelector('i');
            if (icon) icon.style.display = 'inline-block';
            const textNode = Array.from(startBtn.childNodes).find(node => node.nodeType === Node.TEXT_NODE && node.textContent.trim().length > 0);
            if (currentFunctionType === 'beauty') {
                if (textNode) textNode.textContent = ' 开始美颜';
            } else {
                if (textNode) textNode.textContent = ' 开始补光';
            }
            resultLoading.style.display = 'none';
            // 停止队列轮询
            stopQueuePolling();
        }
    }

    async function handleStartProcessing() {
        if (!currentFile) {
            showError('请先选择一个图片文件。');
            return;
        }

        setProcessingState(true);

        const formData = new FormData();
        formData.append('file', currentFile);
        
        // 根据当前选择的功能类型设置请求参数
        if (currentFunctionType === 'beauty') {
            formData.append('workflowName', WORKFLOW_BEAUTY); 
            formData.append('beautyStrength', beautyStrengthRange.value);
            formData.append('enhancedBeauty', enhancedBeautyCheck.checked ? "1" : "0");
            formData.append('slimFace', slimFaceCheck.checked ? "1" : "0");
            resultLoading.innerHTML = `
                <div class="loading-dots">
                    <span></span><span></span><span></span>
                </div>
                <p class="text-center">正在美颜中，请稍候...</p>
            `;
        } else {
            formData.append('workflowName', WORKFLOW_FLASH_LIGHT);
            resultLoading.innerHTML = `
                <div class="loading-dots">
                    <span></span><span></span><span></span>
                </div>
                <p class="text-center">正在添加AI提亮...</p>
            `;
        }
        
        const endpoint = 'https://caca.yzycolour.top/api/images/flash-beauty';

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                showError('用户未登录，请先登录。');
                setProcessingState(false);
                return;
            }

            // 开始前检查队列状态
            resultLoading.innerHTML = `
                <div class="loading-dots">
                    <span></span><span></span><span></span>
                </div>
                <p class="text-center">正在获取队列信息...</p>
            `;
            
            const queueData = await getComfyUIQueueStatus();
            const queueRemaining = queueData?.exec_info?.queue_remaining ?? 0;
            const queuePosition = queueRemaining + 1;
            
            // 立即更新队列信息显示
            if (queueRemaining > 0) {
                resultLoading.innerHTML = `
                    <div class="loading-dots"><span></span><span></span><span></span></div>
                    <p class="text-center">服务器繁忙，前面队列中有 ${queueRemaining} 个任务正在等待。</p>
                    <p class="text-center">您的任务排在第 ${queuePosition} 位，正在提交...</p>`;
            } else {
                resultLoading.innerHTML = `
                    <div class="loading-dots"><span></span><span></span><span></span></div>
                    <p class="text-center">队列空闲，您的任务即将开始处理...</p>`;
            }

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ message: `请求失败，状态码: ${response.status}` }));
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                // 启动队列状态轮询
                startQueuePolling();
                
                // 使用代理后的URL
                const imageUrlToDisplay = data.proxiedImageUrl;
                
                if (!imageUrlToDisplay) {
                    throw new Error('未能获取处理后的图片URL。');
                }
                
                // 显示图片
                await displayImageWithToken(imageUrlToDisplay, resultImage);

                resultContainer.style.display = 'block';
                
                // 更新下载链接
                resultLink.onclick = (e) => {
                    e.preventDefault();
                    initiateImageDownload(imageUrlToDisplay, resultLink);
                };
                resultLink.href = '#';

                // 更新积分显示
                if (typeof updateUserCredits === 'function' && data.newCredits !== undefined) {
                    updateUserCredits(data.newCredits, data.newDailyFreeUsed);
                }
                
                // 刷新历史记录
                loadFlHistory(1, false);
            } else {
                showError(data.message || '处理图片失败，请稍后再试。');
            }

        } catch (error) {
            console.error('处理过程中发生错误:', error);
            showError(`处理失败: ${error.message}`);
        } finally {
            // 在隐藏加载卡片前，如果卡片当前是显示的，则播放一次性扫描动画
            if (resultLoading.style.display === 'flex') {
                resultLoading.classList.add('play-scan-once');
                resultLoading.addEventListener('animationend', () => {
                    resultLoading.classList.remove('play-scan-once');
                }, { once: true });
            }
            setProcessingState(false);
            updateButtonWithCost(currentFunctionType === 'beauty' ? 'beauty' : 'flash_light');
            // 停止队列轮询
            stopQueuePolling();
        }
    }
    
    // 队列轮询定时器
    let queuePollingInterval = null;
    
    // 启动队列状态轮询
    function startQueuePolling() {
        // 确保先清理之前的定时器
        stopQueuePolling();
        
        // 设置新的定时器，每3秒轮询一次队列状态
        queuePollingInterval = setInterval(async () => {
            try {
                const queueData = await getComfyUIQueueStatus();
                const queueRemaining = queueData?.exec_info?.queue_remaining ?? 0;
                
                // 如果队列中还有任务，更新队列信息显示
                if (queueRemaining > 0) {
                    resultLoading.innerHTML = `
                        <div class="loading-dots"><span></span><span></span><span></span></div>
                        <p class="text-center">服务器繁忙，当前队列中有 ${queueRemaining} 个任务在等待处理。</p>
                        <p class="text-center">您的任务正在排队中，请耐心等待...</p>`;
                } else {
                    // 队列为空，可能正在处理或即将开始处理
                    resultLoading.innerHTML = `
                        <div class="loading-dots"><span></span><span></span><span></span></div>
                        <p class="text-center">您的任务正在处理中，请稍候...</p>`;
                }
            } catch (error) {
                console.warn('[FlashLight] 轮询队列状态失败:', error);
            }
        }, 3000);
    }
    
    // 停止队列状态轮询
    function stopQueuePolling() {
        if (queuePollingInterval) {
            clearInterval(queuePollingInterval);
            queuePollingInterval = null;
        }
    }

    async function loadFlHistory(page = 1, append = false) {
        if (isLoadingFlHistory) return;
        isLoadingFlHistory = true;

        if (!append) {
            historyListContainer.innerHTML = '';
            historyPlaceholder.textContent = '加载历史记录中...';
            historyPlaceholder.style.display = 'block';
        } else {
            if(loadMoreHistoryBtn) {
            loadMoreHistoryBtn.disabled = true;
            loadMoreHistoryBtn.textContent = '加载中...';
            }
        }
        if(historyLoadMoreContainer) historyLoadMoreContainer.style.display = 'none';

        const token = localStorage.getItem('token');
        if (!token) {
            historyPlaceholder.textContent = '请先登录以查看历史记录。';
            isLoadingFlHistory = false;
            return;
        }

        try {
            const apiBaseUrl = window.API_URL || 'https://caca.yzycolour.top';
            const historyUrl = `${apiBaseUrl}/flash-beauty/history?page=${page}&limit=8`;
            const response = await fetch(historyUrl, {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();

            if (data && data.history) {
                historyPlaceholder.style.display = 'none';
                renderFlHistory(data.history);
                currentFlHistoryPage = data.pagination.currentPage;
                totalFlHistoryPages = data.pagination.totalPages;
                updateLoadMoreFlButtonState();
            } else {
                if (!append) {
                    historyPlaceholder.textContent = '暂无处理历史记录。';
                    historyPlaceholder.style.display = 'block';
                }
                updateLoadMoreFlButtonState();
            }
        } catch (error) {
            console.error('[Flash Light History] 加载历史记录失败:', error);
            if (!append) {
                historyPlaceholder.textContent = '加载历史记录失败，请稍后重试。';
                historyPlaceholder.style.display = 'block';
            } else {
                if(window.showToast) window.showToast('加载更多历史记录失败', 'error');
            }
            updateLoadMoreFlButtonState();
        } finally {
            isLoadingFlHistory = false;
            if (append && loadMoreHistoryBtn) {
                loadMoreHistoryBtn.disabled = false;
                loadMoreHistoryBtn.textContent = '加载更多';
            }
        }
    }

    function renderFlHistory(historyItems) {
        if (!historyListContainer.classList.contains('history-list-flex-applied')) {
             historyListContainer.style.display = 'flex';
             historyListContainer.style.flexWrap = 'wrap';
             historyListContainer.style.gap = '1rem';
            historyListContainer.classList.add('history-list-flex-applied');
        }

        if (historyItems.length === 0 && historyListContainer.children.length === 0) {
             historyPlaceholder.textContent = '暂无处理历史记录。';
             historyPlaceholder.style.display = 'block';
             return;
        }
        historyPlaceholder.style.display = 'none';

        historyItems.forEach(item => {
            const historyCard = document.createElement('div');
            historyCard.className = 'upscale-history-item text-center'; 
            historyCard.style.flex = '0 0 auto';
            historyCard.style.maxWidth = '144px'; 

            const timeSmall = document.createElement('small');
            timeSmall.className = 'd-block text-muted mb-1';
            timeSmall.textContent = formatFullDateTime(item.created_at);
            historyCard.appendChild(timeSmall);

            // 显示工作流类型
            const workflowType = document.createElement('small');
            workflowType.className = 'd-block text-info mb-1';
            workflowType.textContent = item.workflow_name === WORKFLOW_BEAUTY ? 'AI美颜' : 'AI补光';
            historyCard.appendChild(workflowType);

            const imgLink = document.createElement('a');
            imgLink.href = item.result_image_url;
            imgLink.title = `点击下载图片 (原始文件名: ${item.original_image_filename || 'N/A'})`;

            const img = document.createElement('img');
            img.alt = 'Flash light image history';
            img.loading = 'lazy'; 
            img.className = 'img-fluid rounded border'; 
            img.style.objectFit = 'cover';
            img.style.aspectRatio = '1 / 1';
            
            // 处理图片加载
            if (item.result_image_url && item.result_image_url.includes('caca.yzycolour.top/api/')) {
                displayImageWithToken(item.result_image_url, img);
            } else if (item.result_image_url) {
                img.src = item.result_image_url; 
            } else {
                img.src = '#';
            }

            imgLink.appendChild(img);

            imgLink.addEventListener('click', (event) => {
                event.preventDefault();
                initiateImageDownload(item.result_image_url, imgLink);
            });

            historyCard.appendChild(imgLink);
            historyListContainer.appendChild(historyCard);
        });
    }

    function updateLoadMoreFlButtonState() {
        if (!loadMoreHistoryBtn || !historyLoadMoreContainer) return;
        if (currentFlHistoryPage < totalFlHistoryPages) {
            historyLoadMoreContainer.style.display = 'block';
            loadMoreHistoryBtn.disabled = false;
            loadMoreHistoryBtn.textContent = '加载更多';
        } else {
            historyLoadMoreContainer.style.display = 'none';
        }
    }

    function formatFullDateTime(dateString) {
        if (!dateString) return '未知时间';
        try {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric', month: '2-digit', day: '2-digit', 
                hour: '2-digit', minute: '2-digit', hour12: false
            });
        } catch (e) { return '日期无效'; }
    }

    function handleTabShown() {
        // 确保打开 tab 时总是尝试加载第一页历史记录
        if (!isLoadingFlHistory) { 
            loadFlHistory(1, false);
        } else {
        }
        // 根据当前选择的功能类型更新按钮成本
        updateButtonWithCost(currentFunctionType === 'beauty' ? 'beauty' : 'flash_light');
    }

    // 页面加载时，如果该tab当前是激活的，主动调用handleTabShown
    if (flTab && flPane && flTab.classList.contains('active') && flPane.classList.contains('active')) {
        handleTabShown();
    }

    // 初始化加载功能成本
    fetchFeatureCost('flash_light');
    fetchFeatureCost('beauty');
}); 