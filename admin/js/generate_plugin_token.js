// --- generate_plugin_token.js ---
const jwt = require('jsonwebtoken');

// --- 请根据你的实际情况修改以下变量 ---
const pluginUserId = 2; // 替换为你的 'plugin_reader' 用户的数据库 ID
const pluginUsername = '<EMAIL>'; // 替换为你的插件用户名
const pluginUserRole = 'user'; // 必须是 'user'
const yourJwtSecret = 'A1b2C3d4E5f6G7h8I9j0K1l2M3n4O5p6Q7r8S9t0U1v2W3x4Y5z6@#$'; // 替换为你的 .env 文件中的 JWT_SECRET
const expiresIn = '365d'; // 设置有效期，例如 '365d' (1年), '730d' (2年), '1825d' (5年), '3650d' (10年)
// --- 修改结束 ---

// 安全检查
if (yourJwtSecret === 'your-secret-key' || !yourJwtSecret) {
    console.error("错误：请务必将 'your-secret-key' 替换为你真实的 JWT_SECRET！");
    process.exit(1); // 退出脚本
}
if (pluginUserRole !== 'user') {
     console.error("错误：插件用户的角色必须是 'user'！");
     process.exit(1); // 退出脚本
}
 if (typeof pluginUserId !== 'number' || pluginUserId <= 0) {
     console.error("错误：请提供有效的 pluginUserId (数据库中的用户ID)！");
     process.exit(1); // 退出脚本
 }

try {
    // 生成 Token
    const token = jwt.sign(
        {
            userId: pluginUserId,     // 用户 ID
            username: pluginUsername, // 用户名
            role: pluginUserRole      // 用户角色
        },
        yourJwtSecret,           // 你的密钥
        { expiresIn: expiresIn } // 设置过期时间
    );

    console.log('--- 为 Figma 插件生成的长效 Token ---');
    console.log(token);
    console.log('------------------------------------');
    console.log(`Token 有效期: ${expiresIn}`);
    console.log(`请将此 Token 复制到 jimeng.html 文件中的 PLUGIN_API_TOKEN 常量处。`);

} catch (error) {
    console.error("生成 Token 时出错:", error);
    process.exit(1);
}
// --- 脚本结束 ---