// js/api-key-manager.js
// 负责管理需要 API Key 的模型及其设置模态框

// --- 配置需要 API Key 的模型 --- 
// 结构: 
// { 
//   id: '内部标识符', 
//   name: '显示名称', 
//   localStorageKey: '存储在 localStorage 中的键名', 
//   placeholder: '输入框占位符', 
//   helpLink: '获取 Key 的帮助链接 (可选)' 
// }
const modelsRequiringKeys = [
    {
        id: 'kimi', // 用于生成元素 ID 和查找
        name: '<PERSON><PERSON> (Moonshot)',
        localStorageKey: 'kimiApiKey',
        placeholder: 'sk-...',
        helpLink: 'https://platform.moonshot.cn/console/api-keys'
    },
    {
        id: 'runninghub',
        name: 'RunningHub',
        localStorageKey: 'runningHubApiKey',
        placeholder: '输入你的 RunningHub API Key',
        helpLink: 'https://www.runninghub.cn/pricing' // 你可以在这里放获取API Key的真实链接
    },
    {
        id: 'flux',
        name: 'Flux Kontext Pro',
        localStorageKey: 'flux_api_key',
        placeholder: '输入你的 Flux API Key',
        helpLink: 'https://bfl.ai/docs/api/flux-kontext-pro'
    },
    // {
    //     id: 'some_other_model',
    //     name: '其他模型',
    //     localStorageKey: 'otherModelApiKey',
    //     placeholder: '输入其他模型的 Key',
    //     // helpLink: '...'
    // }
    // --- 未来在此处添加更多模型 ---
];

document.addEventListener('DOMContentLoaded', () => {

    const apiKeyModalElement = document.getElementById('apiKeyModal');
    const apiKeyModalBody = document.getElementById('apiKeyModalBody');
    // 注意：触发按钮不需要在这里获取，因为 Bootstrap 会处理点击事件

    if (!apiKeyModalElement || !apiKeyModalBody) {
        console.error('API Key Modal elements not found!');
        return;
    }

    // --- 模态框显示时触发 --- 
    apiKeyModalElement.addEventListener('show.bs.modal', () => {
        renderApiKeyInputs();
    });

    // --- 动态渲染模态框内容 --- 
    function renderApiKeyInputs() {
        apiKeyModalBody.innerHTML = ''; // 清空旧内容

        if (modelsRequiringKeys.length === 0) {
            apiKeyModalBody.innerHTML = '<p class="text-muted">当前没有需要配置 API Key 的模型。</p>';
            return;
        }

        modelsRequiringKeys.forEach(model => {
            const keySection = document.createElement('div');
            keySection.className = 'mb-4'; // 每个 Key 的间距

            const label = document.createElement('label');
            label.htmlFor = `key-input-${model.id}`;
            label.className = 'form-label fw-bold';
            label.textContent = `${model.name} API Key:`;

            const inputGroup = document.createElement('div');
            inputGroup.className = 'input-group input-group-sm';

            const input = document.createElement('input');
            input.type = 'password';
            input.className = 'form-control form-control-sm bg-dark text-light border-secondary';
            input.id = `key-input-${model.id}`;
            input.placeholder = model.placeholder || '输入 API Key';

            const saveButton = document.createElement('button');
            saveButton.className = 'btn btn-outline-secondary btn-sm';
            saveButton.type = 'button';
            saveButton.id = `save-key-${model.id}`;
            saveButton.textContent = '保存';
            saveButton.dataset.modelId = model.id; // 存储模型 ID

            const toggleButton = document.createElement('button');
            toggleButton.className = 'btn btn-outline-info btn-sm';
            toggleButton.type = 'button';
            toggleButton.id = `toggle-key-${model.id}`;
            toggleButton.title = '显示/隐藏 Key';
            toggleButton.innerHTML = '<i class="bi bi-eye"></i>';
            toggleButton.dataset.modelId = model.id; // 存储模型 ID

            inputGroup.appendChild(input);
            inputGroup.appendChild(saveButton);
            inputGroup.appendChild(toggleButton);

            const helpTextDiv = document.createElement('div');
            helpTextDiv.className = 'form-text small text-muted mt-1';
            let helpHtml = 'API Key 将保存在你的浏览器本地。';
            if (model.helpLink) {
                helpHtml += ` <a href="${model.helpLink}" target="_blank" rel="noopener noreferrer">如何获取?</a>`;
            }
            helpTextDiv.innerHTML = helpHtml;

            const statusDiv = document.createElement('div');
            statusDiv.id = `status-key-${model.id}`;
            statusDiv.className = 'small mt-1';

            keySection.appendChild(label);
            keySection.appendChild(inputGroup);
            keySection.appendChild(helpTextDiv);
            keySection.appendChild(statusDiv);

            apiKeyModalBody.appendChild(keySection);

            // --- 为动态生成的按钮添加事件监听 ---
            saveButton.addEventListener('click', () => saveApiKey(model.id));
            toggleButton.addEventListener('click', () => toggleApiKeyVisibility(model.id));
        });

        // 渲染完成后，加载已保存的 Key
        loadApiKeys();
    }

    // --- 加载所有模型的 Key --- 
    function loadApiKeys() {
        modelsRequiringKeys.forEach(model => {
            const input = document.getElementById(`key-input-${model.id}`);
            const statusDiv = document.getElementById(`status-key-${model.id}`);
            const savedKey = localStorage.getItem(model.localStorageKey);

            if (input) {
                input.value = savedKey || '';
            }
            if (statusDiv) {
                if (savedKey) {
                    statusDiv.textContent = '已加载保存的 API Key。';
                    statusDiv.className = 'small mt-1 text-success';
                    if (model.id === 'runninghub') { // 新增：加载时也设置全局变量
                        window.RUNNINGHUB_API_KEY = savedKey;
                    }
                } else {
                    statusDiv.textContent = '尚未保存此模型的 API Key。';
                    statusDiv.className = 'small mt-1 text-warning';
                }
            }
        });
    }

    // --- 保存指定模型的 Key --- 
    function saveApiKey(modelId) {
        const model = modelsRequiringKeys.find(m => m.id === modelId);
        if (!model) return;

        const input = document.getElementById(`key-input-${model.id}`);
        const statusDiv = document.getElementById(`status-key-${model.id}`);
        if (!input || !statusDiv) return;

        const newKey = input.value.trim();

        // 可选：添加更严格的验证逻辑
        // if (model.id === 'kimi' && !newKey.startsWith('sk-')) { ... }

        if (newKey) {
            localStorage.setItem(model.localStorageKey, newKey);
            statusDiv.textContent = 'API Key 已保存！';
            statusDiv.className = 'small mt-1 text-success';
            input.type = 'password'; // 保存后自动隐藏
            const toggleBtn = document.getElementById(`toggle-key-${model.id}`);
            if (toggleBtn) toggleBtn.innerHTML = '<i class="bi bi-eye"></i>';
            if (model.id === 'runninghub') { // 新增：保存时也设置全局变量
                window.RUNNINGHUB_API_KEY = newKey;
            }
        } else {
            localStorage.removeItem(model.localStorageKey);
            statusDiv.textContent = 'API Key 已清除。';
            statusDiv.className = 'small mt-1 text-info';
            if (model.id === 'runninghub') { // 新增：清除时也清除全局变量
                window.RUNNINGHUB_API_KEY = '';
            }
        }
    }

    // --- 切换指定模型 Key 的可见性 --- 
    function toggleApiKeyVisibility(modelId) {
        const input = document.getElementById(`key-input-${modelId}`);
        const toggleButton = document.getElementById(`toggle-key-${modelId}`);
        if (!input || !toggleButton) return;

        if (input.type === 'password') {
            input.type = 'text';
            toggleButton.innerHTML = '<i class="bi bi-eye-slash"></i>';
        } else {
            input.type = 'password';
            toggleButton.innerHTML = '<i class="bi bi-eye"></i>';
        }
    }

}); // End of DOMContentLoaded 