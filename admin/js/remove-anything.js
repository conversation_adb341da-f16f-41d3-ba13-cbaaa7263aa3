// 移除万物 Tab 前端主逻辑
// 样式和布局完全参考 GPT-4o 图片编辑 Tab

document.addEventListener('DOMContentLoaded', () => {
    // --- 元素引用（与GPT-4o Tab风格一致） ---
    const form = document.getElementById('removeAnythingForm');
    // 主图片上传区
    const mainImageDropZone = document.getElementById('removeAnythingImageDropZone');
    const mainImageInput = document.getElementById('removeImageInput');
    const mainImagePreviewContainer = document.getElementById('removeAnythingImagePreviewContainer');
    const mainImagePreviewImg = document.getElementById('removeAnythingImagePreviewImg');
    const removeMainImageBtn = document.getElementById('removeAnythingMainImageBtn');
    // Mask图片上传区
    // const maskImageDropZone = document.getElementById('removeAnythingMaskDropZone');
    // const maskImageInput = document.getElementById('removeMaskInput');
    // const maskImagePreviewContainer = document.getElementById('removeAnythingMaskPreviewContainer');
    // const maskImagePreviewImg = document.getElementById('removeAnythingMaskPreviewImg');
    // const removeMaskImageBtn = document.getElementById('removeAnythingMaskImageBtn');
    // 参数输入
    const promptInput = document.getElementById('removePromptInput');
    const expandInput = document.getElementById('removeExpandInput');
    const strengthInput = document.getElementById('removeStrengthInput');
    const outputMPInput = document.getElementById('removeOutputMPInput');
    const startBtn = document.getElementById('startRemoveAnythingBtn');
    // 结果区
    const resultPlaceholder = document.getElementById('removeAnythingResultPlaceholder');
    const resultLoadingIndicator = document.getElementById('removeAnythingResultLoadingIndicator');
    const resultContent = document.getElementById('removeAnythingResultContent');
    const resultImage = document.getElementById('removeAnythingResultImage');
    const resultDownloadLink = document.getElementById('removeAnythingResultDownloadLink');
    const errorAlert = document.getElementById('removeAnythingErrorAlert');
    // 历史记录区
    const historyListContainer = document.getElementById('removeAnythingHistoryList');
    const historyPaginationContainer = document.getElementById('removeAnythingHistoryPagination');
    const historyPlaceholder = document.getElementById('removeAnythingHistoryPlaceholder');

    let mainUploadedFile = null; // 存储主图片File对象
    // let maskUploadedFile = null; // 存储Mask图片File对象 (移除)
    let drawnMaskDataUrl = null; // 新增：存储绘制的蒙版 Data URL
    let currentResultImageUrl = '';
    let currentHistoryPage = 1;
    let totalHistoryPages = 1;
    let isLoadingHistory = false;
    let loadMoreHistoryBtn = null; // 动态创建
    let removeAnythingCost = null; // 新增：存储功能成本

    // Removed block of unused/conflicting variable declarations
    // const maskEditModal = document.getElementById('maskEditModal');
    // const maskEditorCanvas = document.getElementById('maskEditorCanvas');
    // const maskEditorBaseImage = document.getElementById('maskEditorBaseImage');
    // const maskEditorBrushSize = document.getElementById('maskEditorBrushSize');
    // const maskEditorBrushSizeValue = document.getElementById('maskEditorBrushSizeValue');
    // const startMaskEditBtn = document.getElementById('startMaskEditBtn');
    // const saveMaskEditBtn = document.getElementById('saveMaskEditBtn');
    // const maskEditorClearBtn = document.getElementById('maskEditorClearBtn');

    // let isDrawing = false;
    // let currentTool = 'brush'; // 默认工具为画笔
    // let brushSize = parseInt(maskEditorBrushSize.value, 10); // ERROR LINE
    // const ctx = maskEditorCanvas.getContext('2d');

    // --- 新增：蒙版绘制模态框元素 ---
    const maskDrawingModalElement = document.getElementById('maskDrawingModal');
    const maskDrawingCanvas = document.getElementById('maskDrawingCanvas');
    const maskDrawingTools = document.getElementById('maskDrawingTools');
    const maskDrawingBrushSizeSlider = document.getElementById('maskDrawingBrushSize');
    const maskDrawingBrushSizeValue = document.getElementById('maskDrawingBrushSizeValue');
    const clearMaskDrawingBtn = document.getElementById('clearMaskDrawingBtn');
    const saveDrawnMaskBtn = document.getElementById('saveDrawnMaskBtn');
    let maskDrawingModalInstance = null;
    let maskDrawingCtx = null;
    let isMaskDrawing = false;
    let currentMaskTool = 'brush';
    let currentMaskBrushSize = 10; // This will now be calculated based on percentage
    let baseImageForMasking = new Image(); // 用于在canvas上绘制主图
    let imageSmallerDim = 1; // To store the smaller dimension of the image for percentage calculation

    // +++ 新增：用于蒙版绘制的离屏Canvas +++
    let maskLayerCanvas = null;
    let maskLayerCtx = null;
    let baseImageSnapshot = null; // 用于存储基础图片的ImageData，避免重复绘制image对象

    // --- 结束：蒙版绘制模态框元素 ---

    // --- 可复用的图片上传处理 (参考GPT-4o) ---
    function setupImageUpload(dropZone, input, previewContainer, previewImg, removeBtn, fileVariableSetter) {
        function handleFile(file) {
            if (file && file.type.startsWith('image/')) {
                fileVariableSetter(file);
                const reader = new FileReader();
                reader.onload = (e) => {
                    previewImg.src = e.target.result;
                    previewContainer.style.display = 'block';
                    dropZone.style.display = 'none';
                    checkFormValidity();
                };
                reader.readAsDataURL(file);
            } else {
                fileVariableSetter(null);
            }
        }
        // 点击上传
        dropZone.addEventListener('click', () => input.click());
        input.addEventListener('change', (e) => {
            if (e.target.files && e.target.files[0]) handleFile(e.target.files[0]);
            input.value = null;
        });
        // 拖拽高亮
        dropZone.addEventListener('dragover', (e) => { e.preventDefault(); dropZone.classList.add('dragging'); });
        dropZone.addEventListener('dragleave', () => dropZone.classList.remove('dragging'));
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault(); dropZone.classList.remove('dragging');
            if (e.dataTransfer.files && e.dataTransfer.files[0]) handleFile(e.dataTransfer.files[0]);
        });
        // 粘贴上传
        dropZone.addEventListener('paste', (e) => {
            e.preventDefault();
            const items = (e.clipboardData || window.clipboardData).items;
            for (let i = 0; i < items.length; i++) {
                if (items[i].type.indexOf('image') !== -1) {
                    const blob = items[i].getAsFile();
                    const file = new File([blob], `pasted_image_${Date.now()}.${blob.type.split('/')[1] || 'png'}`, { type: blob.type });
                    handleFile(file);
                    break;
                }
            }
        });
        // 移除按钮
        removeBtn.addEventListener('click', () => {
            fileVariableSetter(null);
            previewContainer.style.display = 'none';
            previewImg.src = '';
            dropZone.style.display = 'flex';
            input.value = null;
            checkFormValidity();
        });
    }
    setupImageUpload(mainImageDropZone, mainImageInput, mainImagePreviewContainer, mainImagePreviewImg, removeMainImageBtn, (file) => mainUploadedFile = file);
    // setupImageUpload(maskImageDropZone, maskImageInput, maskImagePreviewContainer, maskImagePreviewImg, removeMaskImageBtn, (file) => maskUploadedFile = file); // (移除)

    // --- 检查表单有效性 (参考GPT-4o) ---
    function checkFormValidity() {
        // startBtn.disabled = !(mainUploadedFile && maskUploadedFile && promptInput.value.trim()); // (修改)
        startBtn.disabled = !(mainUploadedFile && drawnMaskDataUrl && promptInput.value.trim());

        // 控制创建蒙版按钮的可用性
        if (createMaskBtn) {
            createMaskBtn.disabled = !mainUploadedFile;
        }
    }
    promptInput.addEventListener('input', checkFormValidity);

    // --- 新增：获取功能成本并更新按钮文本 ---
    async function fetchRemoveAnythingCost() {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('[Remove Anything] 未找到认证令牌，无法获取功能成本');
                updateStartButtonText(); // Update button even if cost fetch fails (to show base text)
                return;
            }
            const response = await fetch(`https://caca.yzycolour.top/api/features/cost?key=remove_anything`, {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            if (!response.ok) {
                console.error(`[Remove Anything] 获取功能成本失败:`, response.status);
                removeAnythingCost = null; // Ensure cost is null on failure
                updateStartButtonText();
                return;
            }
            const data = await response.json();
            if (data.success && data.cost !== undefined) {
                removeAnythingCost = data.cost;
            } else {
                console.warn('[Remove Anything] 获取功能成本数据格式不正确。');
                removeAnythingCost = null; // Ensure cost is null on bad format
            }
        } catch (error) {
            console.error(`[Remove Anything] 获取功能成本出错:`, error);
            removeAnythingCost = null; // Ensure cost is null on error
        }
        updateStartButtonText(); // Always update button text after attempt
    }

    function updateStartButtonText() {
        if (!startBtn) return;

        let buttonHTML = '<i class="bi bi-magic me-2"></i>开始移除'; // Icon and base text
        if (removeAnythingCost !== null && removeAnythingCost !== undefined) {
            buttonHTML += ` <span class="ms-1 badge bg-secondary cost-badge">${removeAnythingCost}积分</span>`;
        }
        startBtn.innerHTML = buttonHTML;
    }

    // --- 新增：获取用户积分余额，并刷新功能成本显示 ---
    async function refreshCreditDataAndCost() {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('[Remove Anything] 未找到认证令牌，无法获取用户积分');
                // Proceed to fetch cost anyway, as it doesn't strictly depend on user balance for display
                await fetchRemoveAnythingCost();
                return;
            }
            const balanceResponse = await fetch(`https://caca.yzycolour.top/api/credits/balance`, {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            if (balanceResponse.ok) {
                const balanceData = await balanceResponse.json();
                if (balanceData.success) {
                    // You can store/use balanceData.cumulativeCredits, balanceData.remainingDailyFreeCredits if needed for UI display in the future
                } else {
                    console.warn('[Remove Anything] 获取用户积分余额API成功但返回数据表明失败:', balanceData.error);
                }
            } else {
                console.warn('[Remove Anything] 获取用户积分余额API请求失败:', balanceResponse.status);
            }
        } catch (error) {
            console.error('[Remove Anything] 请求用户积分余额接口出错:', error);
        }
        // Always try to refresh the feature cost on the button afterwards
        await fetchRemoveAnythingCost();
    }

    // --- 图片下载 (参考GPT-4o) ---
    async function initiateImageDownload(imageUrl, clickedElement) {
        const originalTitle = clickedElement.title;
        clickedElement.title = '下载中...';
        if (clickedElement.disabled !== undefined) clickedElement.disabled = true;
        try {
            const response = await fetch(imageUrl);
            if (!response.ok) throw new Error(`下载失败: ${response.status}`);
            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob);
            const tempLink = document.createElement('a');
            tempLink.href = blobUrl;
            tempLink.download = `remove_anything_${Date.now()}.png`;
            document.body.appendChild(tempLink);
            tempLink.click();
            document.body.removeChild(tempLink);
            URL.revokeObjectURL(blobUrl);
        } catch (error) {
            showError(`下载失败: ${error.message}`);
        } finally {
            clickedElement.title = originalTitle;
            if (clickedElement.disabled !== undefined) clickedElement.disabled = false;
        }
    }
    resultDownloadLink.addEventListener('click', (e) => {
        e.preventDefault();
        if (currentResultImageUrl) initiateImageDownload(currentResultImageUrl, resultDownloadLink);
    });

    // --- 显示错误 (参考GPT-4o) ---
    function showError(message) {
        errorAlert.textContent = message;
        errorAlert.style.display = 'block';
    }

    // --- 表单提交 ---
    form.addEventListener('submit', async (e) => {
        e.preventDefault();

        // +++ 先刷新用户积分和功能成本显示 +++
        await refreshCreditDataAndCost();

        resultPlaceholder.style.display = 'none';
        resultContent.style.display = 'none';
        resultLoadingIndicator.style.display = 'flex';
        errorAlert.style.display = 'none';
        startBtn.disabled = true;
        startBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>请稍候...'; // Initial generic state
        currentResultImageUrl = '';

        // 构造 FormData
        const formData = new FormData();
        formData.append('mainImage', mainUploadedFile);
        if (drawnMaskDataUrl) {
            const blob = await fetch(drawnMaskDataUrl).then(res => res.blob());
            formData.append('maskImage', blob, 'drawn_mask.png');
        }

        let userPrompt = promptInput.value.trim();
        let finalPromptForApi = userPrompt; // 默认情况下，如果翻译失败或不需要翻译

        if (userPrompt && /[\u4e00-\u9fa5]/.test(userPrompt)) { // 简单检测是否包含中文
            if (window.imageUtils && typeof window.imageUtils.translateTextZhipuFrontend === 'function') {
                try {
                    // 显示一个通用的处理中提示给用户，因为翻译可能需要时间
                    startBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>正在翻译...';
                    const translatedText = await window.imageUtils.translateTextZhipuFrontend(userPrompt);
                    if (translatedText && translatedText !== userPrompt) {
                        finalPromptForApi = "remove the " + translatedText;
                    } else {
                        console.warn('[Remove Anything] Frontend translation did not change the prompt or returned empty, using original with prefix.');
                        finalPromptForApi = "remove the " + userPrompt; // 如果翻译结果与原样相同或为空，则还是用原提示词加前缀
                    }
                } catch (translateError) {
                    console.error('[Remove Anything] Frontend translation error:', translateError);
                    // 翻译失败，我们选择继续，并使用原始提示词加上前缀
                    finalPromptForApi = "remove the " + userPrompt;
                    alert('提示词前端翻译失败，将尝试使用原始提示词进行处理。');
                } 
                // No finally block that changes button text here
            } else {
                console.warn('[Remove Anything] Frontend translation function (translateTextZhipuFrontend) not found. Sending original prompt with prefix.');
                finalPromptForApi = "remove the " + userPrompt;
            }
        } else if (userPrompt) { // 如果是英文或其他非中文，直接加前缀
            finalPromptForApi = "remove the " + userPrompt;
        }
        
        // After prompt processing (translation or not), update button text to "Generating..."
        startBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>正在生成...';

        formData.append('prompt', finalPromptForApi); // 使用处理后的提示词
        formData.append('expand_value', expandInput.value);
        formData.append('strength_value', strengthInput.value);
        formData.append('output_mp_value', outputMPInput.value);

        const token = localStorage.getItem('token');
        try {
            const baseUrl = 'https://caca.yzycolour.top';
            const resp = await fetch(`${baseUrl}/api/remove-anything/run`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                    // 不要加 Content-Type
                },
                body: formData
            });
            const data = await resp.json();
            if (resp.ok && data.result_image_url) {
                resultImage.src = data.result_image_url;
                resultContent.style.display = 'block';
                currentResultImageUrl = data.result_image_url;
                
                // +++ 处理后端返回的最新积分信息 (用于日志或特定UI，可选) +++
                if (data.updatedBalance) {
                    // 例如: 更新此功能模块特有的积分显示 (如果需要)
                    // const specificCreditsDisplay = document.getElementById('removeAnythingModuleCredits');
                    // if (specificCreditsDisplay) {
                    //     specificCreditsDisplay.textContent = data.updatedBalance.cumulativeCredits;
                    // }
                }

                // +++ 调用全局积分刷新函数，更新通用积分显示 +++
                if (typeof window.fetchCredits === 'function') {
                    window.fetchCredits(); 
                } else {
                    console.warn('[Remove Anything] Global window.fetchCredits function not found. General balance display may not update immediately.');
                }
                
                loadRemoveAnythingHistory(1, false);
            } else {
                showError(data.error || '生成失败');
            }
        } catch (err) {
            showError('请求失败: ' + err.message);
        } finally {
            resultLoadingIndicator.style.display = 'none';
            startBtn.disabled = false;
            updateStartButtonText(); // Restore original button text with points
        }
    });

    // --- 历史记录 (参考GPT-4o风格) ---
    async function loadRemoveAnythingHistory(page = 1, append = false) {
        if (isLoadingHistory) return;
        isLoadingHistory = true;
        if (!append) {
            historyListContainer.innerHTML = '';
            historyPlaceholder.textContent = '加载历史记录中...';
            historyPlaceholder.style.display = 'block';
        } else {
            if (loadMoreHistoryBtn) {
                loadMoreHistoryBtn.disabled = true;
                loadMoreHistoryBtn.textContent = '加载中...';
            }
        }
        try {
            const token = localStorage.getItem('token');
            const baseUrl = 'https://caca.yzycolour.top'; // 定义正确的 API 基础 URL
            const resp = await fetch(`${baseUrl}/api/remove-anything/history?page=${page}&limit=6`, { // limit改为6适配3列布局
                headers: { 'Authorization': `Bearer ${token}` }
            });
            if (!resp.ok) throw new Error('历史记录获取失败: ' + resp.statusText); // 加上状态文本
            const data = await resp.json();
            if (data && data.history && data.history.length > 0) {
                historyPlaceholder.style.display = 'none';
                renderRemoveAnythingHistoryList(data.history, append);
                currentHistoryPage = data.pagination.currentPage;
                totalHistoryPages = data.pagination.totalPages;
            } else if (!append) {
                historyPlaceholder.textContent = '暂无历史记录。';
            }
            updateLoadMoreHistoryButtonState();
        } catch (e) {
            if (!append) historyPlaceholder.textContent = '加载历史记录失败';
            updateLoadMoreHistoryButtonState();
        } finally {
            isLoadingHistory = false;
            if (append && loadMoreHistoryBtn) {
                loadMoreHistoryBtn.disabled = false;
                loadMoreHistoryBtn.textContent = '加载更多';
            }
        }
    }

    function renderRemoveAnythingHistoryList(items, append) {
        if (!append) historyListContainer.innerHTML = '';
        items.forEach(item => {
            const colDiv = document.createElement('div');
            colDiv.className = 'col';
            const cardDiv = document.createElement('div');
            cardDiv.className = 'card h-100 shadow-sm upscale-history-item'; // 复用放大历史的样式
            if (item.result_image_url) {
                const imgLink = document.createElement('a');
                imgLink.href = item.result_image_url;
                imgLink.title = '点击下载结果';
                imgLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    initiateImageDownload(item.result_image_url, imgLink);
                });
                const img = document.createElement('img');
                img.src = item.result_image_url;
                img.className = 'card-img-top';
                img.style.aspectRatio = '1 / 1';
                img.style.objectFit = 'cover';
                img.alt = `移除结果: ${item.prompt ? item.prompt.substring(0,30) : '无提示'}`;
                imgLink.appendChild(img);
                cardDiv.appendChild(imgLink);
            }
            const cardBody = document.createElement('div');
            cardBody.className = 'card-body small p-2';
            if (item.prompt) {
                const promptEl = document.createElement('p');
                promptEl.className = 'card-text mb-1 text-truncate';
                promptEl.textContent = `提示: ${item.prompt}`;
                promptEl.title = item.prompt;
                cardBody.appendChild(promptEl);
            }
            const paramEl = document.createElement('p');
            paramEl.className = 'card-text text-muted small mb-1';
            paramEl.textContent = `扩展: ${item.expand_value || 'N/A'} | 强度: ${item.strength_value || 'N/A'} | 分辨率: ${item.output_mp_value || 'N/A'}`;
            cardBody.appendChild(paramEl);
            const metaText = document.createElement('p');
            metaText.className = 'card-text text-muted';
            metaText.style.whiteSpace = 'nowrap';
            metaText.textContent = formatFullDateTime(item.created_at);
            cardBody.appendChild(metaText);
            cardDiv.appendChild(cardBody);
            colDiv.appendChild(cardDiv);
            historyListContainer.appendChild(colDiv);
        });
    }

    function setupLoadMoreHistoryButton() {
        if (!historyPaginationContainer) return;
        if (!loadMoreHistoryBtn) {
            loadMoreHistoryBtn = document.createElement('button');
            loadMoreHistoryBtn.id = 'loadMoreRemoveAnythingHistoryBtn';
            loadMoreHistoryBtn.className = 'btn btn-outline-secondary btn-sm mt-3';
            loadMoreHistoryBtn.textContent = '加载更多';
            loadMoreHistoryBtn.style.display = 'none';
            historyPaginationContainer.appendChild(loadMoreHistoryBtn);
            loadMoreHistoryBtn.addEventListener('click', () => {
                if (!isLoadingHistory && currentHistoryPage < totalHistoryPages) {
                    loadRemoveAnythingHistory(currentHistoryPage + 1, true);
                }
            });
        }
    }

    function updateLoadMoreHistoryButtonState() {
        if (!loadMoreHistoryBtn) setupLoadMoreHistoryButton();
        if (!loadMoreHistoryBtn) return;
        const hasItems = historyListContainer && historyListContainer.children.length > 0;
        if (currentHistoryPage < totalHistoryPages) {
            loadMoreHistoryBtn.disabled = false;
            loadMoreHistoryBtn.textContent = '加载更多';
            loadMoreHistoryBtn.style.display = 'block';
        } else {
            loadMoreHistoryBtn.disabled = true;
            loadMoreHistoryBtn.textContent = hasItems ? '没有更多了' : '暂无记录';
            if (!hasItems && currentHistoryPage === 1) {
                loadMoreHistoryBtn.style.display = 'none';
                if (historyPlaceholder) {
                    historyPlaceholder.textContent = '暂无历史记录。';
                    historyPlaceholder.style.display = 'block';
                }
            } else {
                loadMoreHistoryBtn.style.display = 'block';
            }
        }
    }

    function formatFullDateTime(dateString) {
        if (!dateString) return '未知时间';
        try {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric', month: '2-digit', day: '2-digit',
                hour: '2-digit', minute: '2-digit', hour12: false
            });
        } catch (e) { return '日期无效'; }
    }

    // --- Tab激活时自动加载历史 (与之前版本相同) ---
    const removeAnythingTab = document.getElementById('remove-anything-tab');
    if (removeAnythingTab) {
        removeAnythingTab.addEventListener('shown.bs.tab', () => {
            loadRemoveAnythingHistory(1, false);
            fetchRemoveAnythingCost(); // 新增：Tab显示时也获取成本（以防之前失败或需要刷新）
        });
    }
    const removeAnythingPane = document.getElementById('remove-anything-pane'); // 确保获取到pane元素
    if (removeAnythingTab && removeAnythingPane && removeAnythingTab.classList.contains('active') && removeAnythingPane.classList.contains('active')) {
        loadRemoveAnythingHistory(1, false);
    }
    checkFormValidity(); // 初始检查按钮状态
    fetchRemoveAnythingCost(); // 新增：页面加载时获取成本

    // --- 新增：蒙版绘制功能 ---
    if (maskDrawingModalElement) {
        maskDrawingModalInstance = new bootstrap.Modal(maskDrawingModalElement);
        maskDrawingCtx = maskDrawingCanvas.getContext('2d');

        // +++ 初始化离屏Canvas +++
        maskLayerCanvas = document.createElement('canvas');
        maskLayerCtx = maskLayerCanvas.getContext('2d');

        createMaskBtn.addEventListener('click', () => {
            if (!mainUploadedFile) {
                alert('请先上传主图片。');
                return;
            }
            const reader = new FileReader();
            reader.onload = (e) => {
                baseImageForMasking.onload = () => {
                    maskDrawingCanvas.width = baseImageForMasking.naturalWidth;
                    maskDrawingCanvas.height = baseImageForMasking.naturalHeight;
                    
                    // 初始化主显示Canvas和获取基础图片快照
                    maskDrawingCtx.clearRect(0, 0, maskDrawingCanvas.width, maskDrawingCanvas.height);
                    maskDrawingCtx.drawImage(baseImageForMasking, 0, 0, maskDrawingCanvas.width, maskDrawingCanvas.height);
                    baseImageSnapshot = maskDrawingCtx.getImageData(0, 0, maskDrawingCanvas.width, maskDrawingCanvas.height);

                    // 初始化离屏绘制Canvas
                    maskLayerCanvas.width = maskDrawingCanvas.width;
                    maskLayerCanvas.height = maskDrawingCanvas.height;
                    maskLayerCtx.clearRect(0, 0, maskLayerCanvas.width, maskLayerCanvas.height); // 确保开始时是透明的
                    
                    // Calculate image's smaller dimension for responsive brush size
                    imageSmallerDim = Math.min(baseImageForMasking.naturalWidth, baseImageForMasking.naturalHeight);
                    
                    // Setup brush slider for percentage
                    maskDrawingBrushSizeSlider.min = "1"; // 1%
                    maskDrawingBrushSizeSlider.max = "15"; // 15%
                    maskDrawingBrushSizeSlider.value = "5"; // Default 5%
                    
                    const initialBrushPercentage = parseInt(maskDrawingBrushSizeSlider.value, 10);
                    currentMaskBrushSize = (initialBrushPercentage / 100) * imageSmallerDim;
                    maskDrawingBrushSizeValue.textContent = initialBrushPercentage + "%";

                    maskDrawingModalInstance.show();
                };
                baseImageForMasking.src = e.target.result;
            };
            reader.readAsDataURL(mainUploadedFile);
        });

        maskDrawingTools.addEventListener('click', (e) => {
            if (e.target.closest('button[data-tool]')) {
                const toolButton = e.target.closest('button[data-tool]');
                currentMaskTool = toolButton.dataset.tool;
                maskDrawingTools.querySelectorAll('button').forEach(btn => btn.classList.remove('active'));
                toolButton.classList.add('active');
            }
        });

        maskDrawingBrushSizeSlider.addEventListener('input', (e) => {
            const percentage = parseInt(e.target.value, 10);
            currentMaskBrushSize = (percentage / 100) * imageSmallerDim;
            maskDrawingBrushSizeValue.textContent = percentage + "%";
        });

        clearMaskDrawingBtn.addEventListener('click', () => {
            if (maskLayerCtx) {
                maskLayerCtx.clearRect(0, 0, maskLayerCanvas.width, maskLayerCanvas.height);
                redrawVisibleCanvas(); // 重绘显示Canvas
            }
        });

        saveDrawnMaskBtn.addEventListener('click', () => {
            const canvasWidth = maskLayerCanvas.width; // 使用离屏Canvas的尺寸
            const canvasHeight = maskLayerCanvas.height;

            // 从离屏Canvas获取绘制数据
            const drawingImageData = maskLayerCtx.getImageData(0, 0, canvasWidth, canvasHeight);
            const drawingData = drawingImageData.data;

            // Create new ImageData for the black and white mask
            const finalMaskImageData = maskDrawingCtx.createImageData(canvasWidth, canvasHeight);
            const finalMaskData = finalMaskImageData.data;

            const brushRed = 255, brushGreen = 0, brushBlue = 0, brushAlpha = 255;

            for (let i = 0; i < drawingData.length; i += 4) {
                const r = drawingData[i];
                const g = drawingData[i + 1];
                const b = drawingData[i + 2];
                const a = drawingData[i + 3];

                // Check if the pixel is our specific brush color (red and opaque)
                if (r === brushRed && g === brushGreen && b === brushBlue && a === brushAlpha) {
                    // Brushed area: make it WHITE in the final mask
                    finalMaskData[i] = 255;     // R
                    finalMaskData[i + 1] = 255; // G
                    finalMaskData[i + 2] = 255; // B
                    finalMaskData[i + 3] = 255; // A (opaque)
                } else {
                    // Unbrushed, erased, or base image area: make it BLACK in the final mask
                    finalMaskData[i] = 0;       // R
                    finalMaskData[i + 1] = 0;   // G
                    finalMaskData[i + 2] = 0;   // B
                    finalMaskData[i + 3] = 255; // A (opaque)
                }
            }

            // Put the processed mask data onto a temporary canvas to get Data URL
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = canvasWidth;
            tempCanvas.height = canvasHeight;
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.putImageData(finalMaskImageData, 0, 0);

            drawnMaskDataUrl = tempCanvas.toDataURL('image/png');
            
            generatedMaskPreviewImg.src = drawnMaskDataUrl;
            generatedMaskPreviewContainer.style.display = 'block';
            noMaskGeneratedText.style.display = 'none';
            maskDrawingModalInstance.hide();
            checkFormValidity();
        });

        function getMaskDrawingPosition(event) {
            const rect = maskDrawingCanvas.getBoundingClientRect();
            let x, y;

            // Calculate scaling factors
            const scaleX = maskDrawingCanvas.width / rect.width;
            const scaleY = maskDrawingCanvas.height / rect.height;

            if (event.touches && event.touches.length > 0) {
                x = (event.touches[0].clientX - rect.left) * scaleX;
                y = (event.touches[0].clientY - rect.top) * scaleY;
            } else {
                x = (event.clientX - rect.left) * scaleX;
                y = (event.clientY - rect.top) * scaleY;
            }
            return { x, y };
        }

        function startMaskDrawing(e) {
            e.preventDefault();
            isMaskDrawing = true;
            const { x, y } = getMaskDrawingPosition(e);
            // 在离屏Canvas上开始路径
            maskLayerCtx.beginPath();
            maskLayerCtx.moveTo(x, y);
        }

        function drawMask(e) {
            e.preventDefault();
            if (!isMaskDrawing) return;
            const { x, y } = getMaskDrawingPosition(e);

            // 所有绘制操作都在离屏Canvas (maskLayerCtx) 上进行
            maskLayerCtx.lineWidth = currentMaskBrushSize;
            maskLayerCtx.lineCap = 'round';
            maskLayerCtx.lineJoin = 'round';

            if (currentMaskTool === 'brush') {
                maskLayerCtx.globalCompositeOperation = 'source-over';
                maskLayerCtx.strokeStyle = 'rgba(255, 0, 0, 1)'; // Draw with RED for brush
            } else { // eraser
                maskLayerCtx.globalCompositeOperation = 'destination-out';
                maskLayerCtx.strokeStyle = 'rgba(0,0,0,1)'; // For destination-out, color doesn't matter, just needs to be opaque
            }
            maskLayerCtx.lineTo(x, y);
            maskLayerCtx.stroke();

            // 重绘显示Canvas以反映离屏Canvas的变化
            redrawVisibleCanvas();
        }

        function stopMaskDrawing() {
            if (isMaskDrawing) {
                maskLayerCtx.closePath(); // 在离屏Canvas上闭合路径
                isMaskDrawing = false;
            }
        }

        // +++ 新增：重绘主显示Canvas的函数 +++
        function redrawVisibleCanvas() {
            if (!baseImageSnapshot) return; // 如果没有基础图片快照，则不执行
            // 1. 恢复基础图片快照到主Canvas
            maskDrawingCtx.putImageData(baseImageSnapshot, 0, 0);
            // 2. 将离屏Canvas（蒙版层）绘制到主Canvas上
            maskDrawingCtx.drawImage(maskLayerCanvas, 0, 0);
        }

        maskDrawingCanvas.addEventListener('mousedown', startMaskDrawing);
        maskDrawingCanvas.addEventListener('mousemove', drawMask);
        maskDrawingCanvas.addEventListener('mouseup', stopMaskDrawing);
        maskDrawingCanvas.addEventListener('mouseleave', stopMaskDrawing);

        // Touch events for mobile
        maskDrawingCanvas.addEventListener('touchstart', startMaskDrawing);
        maskDrawingCanvas.addEventListener('touchmove', drawMask);
        maskDrawingCanvas.addEventListener('touchend', stopMaskDrawing);
        maskDrawingCanvas.addEventListener('touchcancel', stopMaskDrawing);

    } // End of蒙版绘制功能

    // 打开模态框时加载主图片到 Canvas (这部分是旧的，可移除或重构，当前已包含在新的蒙版绘制逻辑中)
    // startMaskEditBtn.addEventListener('click', () => {
    //     const mainImageSrc = document.getElementById('removeAnythingImagePreviewImg').src;
    //     maskEditorBaseImage.src = mainImageSrc; // 设置基础图片
    //     maskEditorBaseImage.onload = () => {
    //         maskEditorCanvas.width = maskEditorBaseImage.width;
    //         maskEditorCanvas.height = maskEditorBaseImage.height;
    //         ctx.drawImage(maskEditorBaseImage, 0, 0); // 绘制基础图片
    //     };
    //     // 显示模态框
    //     const modal = new bootstrap.Modal(maskEditModal);
    //     modal.show();
    // });

    // 绘图逻辑 (这部分是旧的，可移除或重构)
    // maskEditorCanvas.addEventListener('mousedown', (e) => {
    //     isDrawing = true;
    //     ctx.lineWidth = brushSize;
    //     ctx.lineCap = 'round';
    //     ctx.strokeStyle = currentTool === 'brush' ? 'black' : 'white'; // 画笔颜色
    //     ctx.beginPath();
    //     ctx.moveTo(e.offsetX, e.offsetY);
    // });

    // maskEditorCanvas.addEventListener('mousemove', (e) => {
    //     if (isDrawing) {
    //         ctx.lineTo(e.offsetX, e.offsetY);
    //         ctx.stroke();
    //     }
    // });

    // maskEditorCanvas.addEventListener('mouseup', () => {
    //     isDrawing = false;
    //     ctx.closePath();
    // });

    // // 切换工具
    // document.querySelectorAll('#maskEditorTools .btn').forEach(button => {
    //     button.addEventListener('click', () => {
    //         currentTool = button.getAttribute('data-tool');
    //         document.querySelectorAll('#maskEditorTools .btn').forEach(btn => btn.classList.remove('active'));
    //         button.classList.add('active');
    //     });
    // });

    // // 画笔大小控制
    // maskEditorBrushSize.addEventListener('input', () => {
    //     brushSize = parseInt(maskEditorBrushSize.value, 10);
    //     maskEditorBrushSizeValue.textContent = brushSize;
    // });

    // // 清空 Canvas
    // maskEditorClearBtn.addEventListener('click', () => {
    //     ctx.clearRect(0, 0, maskEditorCanvas.width, maskEditorCanvas.height);
    //     ctx.drawImage(maskEditorBaseImage, 0, 0); // 重新绘制基础图片
    // });

    // // 保存编辑后的 Mask
    // saveMaskEditBtn.addEventListener('click', () => {
    //     maskEditorCanvas.toBlob((blob) => {
    //         // 这里可以将 blob 添加到 FormData 中进行上传
    //         const formData = new FormData();
    //         formData.append('mask', blob, 'mask.png'); // 作为 Mask 图片上传
    //         // 发送请求的逻辑...
    //     }, 'image/png');
    //     // 关闭模态框
    //     const modal = bootstrap.Modal.getInstance(maskEditModal);
    //     modal.hide();
    // });

    // 初始化涂抹蒙版功能 (旧的，可移除)
    // document.getElementById('createMaskBtn').addEventListener('click', function() {
    //     const mainImage = document.getElementById('removeAnythingImagePreviewImg');
    //     const canvas = document.getElementById('maskDrawingCanvas');
    //     const ctx = canvas.getContext('2d');

    //     // 设置画布尺寸与主图一致
    //     canvas.width = mainImage.naturalWidth;
    //     canvas.height = mainImage.naturalHeight;

    //     // 绘制主图到画布
    //     ctx.drawImage(mainImage, 0, 0, canvas.width, canvas.height);

    //     // 显示模态框
    //     const modal = new bootstrap.Modal(document.getElementById('maskDrawingModal'));
    //     modal.show();

    //     // 初始化涂抹逻辑
    //     let isDrawing = false;
    //     let currentTool = 'brush';

    //     // 工具切换
    //     document.querySelectorAll('[data-tool]').forEach(btn => {
    //         btn.addEventListener('click', function() {
    //             document.querySelectorAll('[data-tool]').forEach(b => b.classList.remove('active'));
    //             this.classList.add('active');
    //             currentTool = this.dataset.tool;
    //         });
    //     });

    //     // 画笔大小调整
    //     document.getElementById('brushSize').addEventListener('input', function() {
    //         document.getElementById('brushSizeValue').textContent = this.value;
    //     });

    //     // 画布事件
    //     canvas.addEventListener('mousedown', startDrawing);
    //     canvas.addEventListener('mousemove', draw);
    //     canvas.addEventListener('mouseup', stopDrawing);
    //     canvas.addEventListener('mouseout', stopDrawing);

    //     function startDrawing(e) {
    //         isDrawing = true;
    //         draw(e);
    //     }

    //     function draw(e) {
    //         if (!isDrawing) return;
    //         const rect = canvas.getBoundingClientRect();
    //         const x = e.clientX - rect.left;
    //         const y = e.clientY - rect.top;
    //         const size = parseInt(document.getElementById('brushSize').value);

    //         ctx.globalCompositeOperation = currentTool === 'brush' ? 'source-over' : 'destination-out';
    //         ctx.beginPath();
    //         ctx.arc(x, y, size, 0, Math.PI * 2);
    //         ctx.fill();
    //     }

    //     function stopDrawing() {
    //         isDrawing = false;
    //     }

    //     // 保存蒙版
    //     document.getElementById('saveMaskBtn').addEventListener('click', function() {
    //         // 将画布内容转换为图片并设置为蒙版
    //         const maskDataUrl = canvas.toDataURL('image/png');
    //         const maskPreview = document.getElementById('removeAnythingMaskPreviewImg');
    //         const maskCanvas = document.getElementById('removeAnythingMaskCanvas');

    //         maskPreview.src = maskDataUrl;
    //         maskPreview.style.display = 'block';
    //         maskCanvas.style.display = 'none';

    //         // 隐藏模态框
    //         modal.hide();
    //     });
    // });
});

document.getElementById('removeAnythingMainImageBtn').onclick = function() {
    // 清空图片、隐藏预览、显示上传区域等
    document.getElementById('removeAnythingImagePreviewContainer').style.display = 'none';
    document.getElementById('removeAnythingImagePreviewImg').src = '';
    // 还原上传区域显示
    document.getElementById('removeAnythingImageDropZone').style.display = 'flex';
    // 其他清理逻辑...
    mainUploadedFile = null; // 清理已上传文件
    drawnMaskDataUrl = null; // 清理已绘制的蒙版
    generatedMaskPreviewContainer.style.display = 'none'; // 隐藏蒙版预览
    generatedMaskPreviewImg.src = '';
    noMaskGeneratedText.style.display = 'block'; // 显示提示
    checkFormValidity(); // 重新检查表单状态
};