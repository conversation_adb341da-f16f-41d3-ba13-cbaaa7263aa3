// example-detail-modal.js

// 将所有逻辑封装到一个初始化函数中
function initExampleDetailModal() {
    // Modal Element
    const detailModalElement = document.getElementById('exampleDetailModal');
    if (!detailModalElement) {
        console.error('Example Detail Modal element #exampleDetailModal not found upon init!');
        return;
    }
    // 确保 Modal 实例只创建一次，或者在需要时重新获取
    let detailModal = bootstrap.Modal.getInstance(detailModalElement);
    if (!detailModal) {
        detailModal = new bootstrap.Modal(detailModalElement);
    }

    // DOM Elements within the modal
    const detailModalLabel = document.getElementById('exampleDetailModalLabel');
    const detailGalleryMainImage = document.getElementById('detailGalleryMainImage');
    const detailGalleryMainImagePlaceholder = detailModalElement.querySelector('.example-detail-placeholder-main-image');
    
    const gpt4oEditImagesSection = document.getElementById('detailGpt4oEditImagesSection');
    const detailSourceImage = document.getElementById('detailSourceImage');
    const detailPlaceholderSourceImage = gpt4oEditImagesSection ? gpt4oEditImagesSection.querySelector('.example-detail-placeholder-secondary-image.source') : null;
    const detailReferenceImage = document.getElementById('detailReferenceImage');
    const detailPlaceholderReferenceImage = gpt4oEditImagesSection ? gpt4oEditImagesSection.querySelector('.example-detail-placeholder-secondary-image.reference') : null;

    const detailTitle = document.getElementById('detailTitle');
    const detailModel = document.getElementById('detailModel');
    const detailGenerateSameBtnFooter = document.getElementById('detailGenerateSameBtn_footer');
    const detailCategory = document.getElementById('detailCategory');
    
    // 更新和新增的DOM元素引用
    const detailAuthorAvatar = document.getElementById('detailAuthorAvatar');
    const detailAuthorName = document.getElementById('detailAuthorName');

    const detailPrompt = document.getElementById('detailPrompt');
    const detailCopyPromptBtn = document.getElementById('detailCopyPromptBtn');
    const detailTagsContainer = document.getElementById('detailTagsContainer');
    
    const detailLikeBtn = document.getElementById('detailLikeBtn');
    const detailLikeCount = document.getElementById('detailLikeCount');
    const detailShareBtn = document.getElementById('detailShareBtn');

    const detailCommentsList = document.getElementById('detailCommentsList');
    const detailCommentForm = document.getElementById('detailCommentForm');
    const detailCommentText = document.getElementById('detailCommentText');
    const detailCommentCount = document.getElementById('detailCommentCount');

    // 评论图片上传与预览逻辑
    const commentImageInput = document.getElementById('detailCommentImage');
    const commentImagePreview = document.getElementById('detailCommentImagePreview');
    const commentImagePreviewImg = commentImagePreview ? commentImagePreview.querySelector('img') : null;
    const removeCommentImageBtn = document.getElementById('removeCommentImageBtn');
    const commentImageLabel = document.querySelector('label[for="detailCommentImage"]');
    let commentImageFile = null;

    // 加载 browser-image-compression
    // 假设已在页面引入 window.imageCompression

    // 确保所有关键元素都存在
    const criticalElements = {
        detailModalLabel, detailGalleryMainImage, /*gpt4oEditImagesSection, // 暂时允许为空，因为不是所有模板都有*/ detailTitle, 
        detailModel, detailGenerateSameBtnFooter, detailCategory, detailAuthorAvatar, 
        detailAuthorName, detailPrompt, detailCopyPromptBtn, detailTagsContainer, 
        detailLikeBtn, detailLikeCount, detailShareBtn, detailCommentsList, 
        detailCommentForm, detailCommentText,
        detailCommentCount
    };

    for (const key in criticalElements) {
        if (!criticalElements[key]) {
            console.warn(`[Detail Modal Init] Critical DOM element #${key} is missing.`);
            // 不再中止初始化，允许部分功能缺失，但记录警告
        }
    }
    
    let currentExampleId = null; 
    let currentImageDimensions = null; // 新增：存储当前主图的尺寸 { width, height }
    const API_BASE_URL = 'https://caca.yzycolour.top/api'; 
    const IMAGE_BASE_URL = 'https://www.yzycolour.top/prompt-examples/server/uploads/'; 
    const DEFAULT_AVATAR = 'images/default-avatar.png'; 

    // 图片查看模态框初始化
    const commentImageViewerModal = document.getElementById('commentImageViewerModal');
    let imageViewerModalInstance = null;
    if (commentImageViewerModal) {
        imageViewerModalInstance = new bootstrap.Modal(commentImageViewerModal);
        // 确保modal关闭时清空图片src，避免内存问题
        commentImageViewerModal.addEventListener('hidden.bs.modal', function () {
            const viewerImg = document.getElementById('commentImageViewerImg');
            if (viewerImg) viewerImg.src = '';
        });
    }
    
    // 全局函数: 打开图片查看器
    window.openCommentImageViewer = function(imageUrl) {
        const viewerImg = document.getElementById('commentImageViewerImg');
        if (viewerImg) viewerImg.src = imageUrl;
        if (imageViewerModalInstance) imageViewerModalInstance.show();
    };

    async function showExampleDetail(exampleId) {
        currentExampleId = exampleId;

        try {
            resetModalContent();
            
            // 确保在显示模态框前，tab已重置到详情页
            const detailsTab = document.getElementById('details-tab');
            if (detailsTab) {
                // 强制激活详情标签页
                document.querySelectorAll('#detailTabs .nav-link').forEach(tab => {
                    tab.classList.remove('active');
                });
                detailsTab.classList.add('active');
                
                document.querySelectorAll('#detailTabsContent .tab-pane').forEach(pane => {
                    pane.classList.remove('show', 'active');
                });
                const detailsPane = document.getElementById('details-tab-pane');
                if (detailsPane) {
                    detailsPane.classList.add('show', 'active');
                }
            }
            
            initTabSwitching();
            
            detailModal.show();

        } catch (e) {
            console.error("[Detail Modal] Error during initial modal setup (reset, tabs, or show call):", e);
            if(detailTitle) detailTitle.textContent = '加载模态框失败';
            if(detailPrompt) detailPrompt.textContent = e.message || '未知错误';
            // 可以考虑不在这里重新抛出，因为后续的 try-catch 可能不应该执行
            // 如果这里出错，通常意味着模态框本身无法正确展示
            return; // 提前退出，避免执行后续的 fetch
        }

        try {
            const response = await fetch(`${API_BASE_URL}/examples/${exampleId}`, {
                headers: getAuthHeadersFromMain() // 改为调用 main.js 的函数
            });
            if (!response.ok) {
                if (response.status === 404) throw new Error('案例未找到');
                throw new Error(`获取案例详情失败: ${response.status}`);
            }
            const example = await response.json();
            populateModalContent(example);
        } catch (error) {
            console.error('[Detail Modal] Error fetching example details:', error);
            if(detailTitle) detailTitle.textContent = '加载失败';
            if(detailPrompt) detailPrompt.textContent = error.message;
            if(detailGalleryMainImage) detailGalleryMainImage.style.display = 'none';
            if(detailGalleryMainImagePlaceholder) {
                detailGalleryMainImagePlaceholder.style.display = 'flex';
                if(detailGalleryMainImagePlaceholder.querySelector('p')) detailGalleryMainImagePlaceholder.querySelector('p').textContent = '加载案例信息失败';
            }
        }
    }

    function resetModalContent() {
        try {
            if(detailModalLabel) detailModalLabel.textContent = '案例详情加载中...';
            if(detailTitle) detailTitle.textContent = '案例标题加载中...';
            
            if(detailGalleryMainImage) {
                detailGalleryMainImage.src = '';
                detailGalleryMainImage.style.display = 'none';
            }
            if(detailGalleryMainImagePlaceholder) {
                detailGalleryMainImagePlaceholder.style.display = 'flex';
                if(detailGalleryMainImagePlaceholder.querySelector('p')) detailGalleryMainImagePlaceholder.querySelector('p').textContent = '加载中...';
            }

            if(gpt4oEditImagesSection) gpt4oEditImagesSection.style.display = 'none';
            if(detailSourceImage) {
                detailSourceImage.src = '';
                detailSourceImage.style.display = 'none';
            }
            if(detailPlaceholderSourceImage) {
                detailPlaceholderSourceImage.style.display = 'flex';
                if(detailPlaceholderSourceImage.querySelector('p')) detailPlaceholderSourceImage.querySelector('p').textContent = '加载中...';
            }
            if(detailReferenceImage) {
                detailReferenceImage.src = '';
                detailReferenceImage.style.display = 'none';
            }
            if(detailPlaceholderReferenceImage) {
                detailPlaceholderReferenceImage.style.display = 'flex';
                if(detailPlaceholderReferenceImage.querySelector('p')) detailPlaceholderReferenceImage.querySelector('p').textContent = '加载中...';
            }

            if(detailModel) {
                detailModel.textContent = 'N/A';
            }
            if(detailGenerateSameBtnFooter) detailGenerateSameBtnFooter.style.display = 'block';
            if(detailCategory) {
                detailCategory.textContent = '加载中...';
            }
            if(detailAuthorAvatar) detailAuthorAvatar.src = DEFAULT_AVATAR;
            if(detailAuthorName) detailAuthorName.textContent = '加载中...';

            if(detailPrompt) detailPrompt.textContent = '加载中...';
            if(detailTagsContainer) detailTagsContainer.innerHTML = '<span class="text-muted">加载中...</span>';
            
            if(detailLikeBtn) {
                detailLikeBtn.classList.remove('liked');
                const likeIcon = detailLikeBtn.querySelector('i');
                if(likeIcon) likeIcon.className = 'bi bi-heart me-1';
                detailLikeBtn.title = '点赞';
            }
            if(detailLikeCount) detailLikeCount.textContent = '0';
            
            if(detailCommentsList) detailCommentsList.innerHTML = '<p class="text-muted text-center">评论加载中...</p>';
            if(detailCommentText) detailCommentText.value = '';
            if(detailCommentCount) detailCommentCount.textContent = '0';

            currentImageDimensions = null; // 确保在这里重置图片尺寸信息
        } catch (e) {
            console.error("[Detail Modal] Error INSIDE resetModalContent:", e);
            throw e; // 重新抛出错误，以保持原有的promise拒绝行为
        }
    }

    function populateModalContent(example) {
        if(detailModalLabel) detailModalLabel.textContent = `案例详情: ${example.title || '无标题'}`;
        if(detailTitle) detailTitle.textContent = example.title || '无标题';

        if (example.image_file) {
            if(detailGalleryMainImage) {
                detailGalleryMainImage.src = IMAGE_BASE_URL + example.image_file;
                detailGalleryMainImage.style.display = 'block';
            }
            if(detailGalleryMainImagePlaceholder) detailGalleryMainImagePlaceholder.style.display = 'none';
        } else {
            if(detailGalleryMainImage) detailGalleryMainImage.style.display = 'none';
            if(detailGalleryMainImagePlaceholder) {
                detailGalleryMainImagePlaceholder.style.display = 'flex';
                if(detailGalleryMainImagePlaceholder.querySelector('p')) detailGalleryMainImagePlaceholder.querySelector('p').textContent = '暂无主图片';
            }
        }

        // 更新模型和分类信息，适配新布局
        if(detailModel) detailModel.textContent = example.target_model || '未指定';
        if(detailCategory) detailCategory.textContent = window.getCategoryNameFromMain ? window.getCategoryNameFromMain(example.category) : (example.category || '未分类');
        
        // Logic for the generate same button (now only footer one matters for click)
        if (example.target_model === '超级生图' || example.target_model === 'GPT4O' || example.target_model === '高级生图' || example.target_model === 'MJ' || example.target_model === 'Midjourney') {
            if(detailGenerateSameBtnFooter) {
                detailGenerateSameBtnFooter.style.display = 'block'; 
                detailGenerateSameBtnFooter.onclick = () => {
                    if (window.populatePromptForSameGeneration) {
                        const promptToUse = example.prompt;
                        let imageDimensionsStr = null;

                        // 优先使用已通过 onload/complete 获取的 currentImageDimensions
                        if (currentImageDimensions && currentImageDimensions.width && currentImageDimensions.height) {
                            imageDimensionsStr = `${currentImageDimensions.width}x${currentImageDimensions.height}`;
                        } else {
                            // 如果 currentImageDimensions 未设置，尝试从主图元素直接获取
                            const mainImgElement = document.getElementById('detailGalleryMainImage');
                            if (mainImgElement && mainImgElement.complete && mainImgElement.naturalWidth > 0 && mainImgElement.naturalHeight > 0) {
                                currentImageDimensions = { width: mainImgElement.naturalWidth, height: mainImgElement.naturalHeight }; // 更新全局变量
                                imageDimensionsStr = `${currentImageDimensions.width}x${currentImageDimensions.height}`;
                            } else {
                                console.warn('[Detail Modal] Could not determine image dimensions on click. Main image complete state:', mainImgElement ? mainImgElement.complete : 'N/A', 'Natural W/H:', mainImgElement ? `${mainImgElement.naturalWidth}x${mainImgElement.naturalHeight}`: 'N/A');
                            }
                        }
                        
                        
                        // 超级生图、高级生图、MJ和GPT4O都不需要源图和参考图，直接传null
                        window.populatePromptForSameGeneration(promptToUse, example.target_model, example.category, null, null, imageDimensionsStr);
                        detailModal.hide(); 
                    } else {
                        alert('无法将提示词填充到生成区域，请手动复制。');
                    }
                };
            }
        } else if (example.target_model === 'GPT4O-编辑') { // 新增：处理GPT4O-编辑类型
            if(detailGenerateSameBtnFooter) {
                detailGenerateSameBtnFooter.style.display = 'block'; 
                detailGenerateSameBtnFooter.onclick = () => {
                    if (window.populatePromptForSameGeneration) {
                        const promptToUse = example.prompt;
                        
                        // 获取源图和参考图的URL
                        const sourceImageUrl = example.source_image_file ? IMAGE_BASE_URL + example.source_image_file : null;
                        const referenceImageUrl = example.reference_image_file ? IMAGE_BASE_URL + example.reference_image_file : null;
                        
                        
                        // 传递源图和参考图的URL给populatePromptForSameGeneration函数
                        window.populatePromptForSameGeneration(promptToUse, example.target_model, example.category, sourceImageUrl, referenceImageUrl, null);
                        detailModal.hide();
                    } else {
                        alert('无法将提示词填充到生成区域，请手动复制。');
                    }
                };
            }
        } else if (example.target_model === 'FLUX-多图') { // 新增：处理FLUX-多图类型
            if(detailGenerateSameBtnFooter) {
                detailGenerateSameBtnFooter.style.display = 'block'; 
                detailGenerateSameBtnFooter.onclick = () => {
                    if (window.populatePromptForSameGeneration) {
                        const promptToUse = example.prompt;
                        
                        // 获取图1和图2的URL
                        const image1Url = example.flux_second_image_file ? IMAGE_BASE_URL + example.flux_second_image_file : null;
                        const image2Url = example.flux_result_image_file ? IMAGE_BASE_URL + example.flux_result_image_file : null;
                        
                        
                        // 传递图1和图2的URL给populatePromptForSameGeneration函数
                        // 注意：对于FLUX-多图，我们使用sourceImageUrl参数传递图1，referenceImageUrl参数传递图2
                        window.populatePromptForSameGeneration(promptToUse, example.target_model, example.category, image1Url, image2Url, null);
                        detailModal.hide();
                    } else {
                        alert('无法将提示词填充到生成区域，请手动复制。');
                    }
                };
            }
        } else { // GPT4O-编辑 和其他所有不支持的模型都隐藏按钮
            if(detailGenerateSameBtnFooter) detailGenerateSameBtnFooter.style.display = 'none';
        }

        // 填充作者信息 (假设后端 example.author 对象包含这些信息)
        if (example.author) {
            if(detailAuthorAvatar) {
                detailAuthorAvatar.src = example.author.avatar_url ? IMAGE_BASE_URL + example.author.avatar_url : DEFAULT_AVATAR;
                detailAuthorAvatar.alt = example.author.nickname || example.author.username || '作者头像';
            }
            if(detailAuthorName) detailAuthorName.textContent = example.author.nickname || example.author.username || '匿名作者';
        } else {
            if(detailAuthorAvatar) detailAuthorAvatar.src = DEFAULT_AVATAR;
            if(detailAuthorName) detailAuthorName.textContent = '未知作者';
        }
        if(detailAuthorAvatar) detailAuthorAvatar.onerror = () => { if(detailAuthorAvatar) {detailAuthorAvatar.src = DEFAULT_AVATAR; detailAuthorAvatar.onerror = null;} };

        if(detailPrompt) detailPrompt.textContent = example.prompt || '无提示词';

        if (example.tags && example.tags.length > 0) {
            if(detailTagsContainer) detailTagsContainer.innerHTML = example.tags.map(tag => `<span class="tag">${escapeHTML(tag)}</span>`).join('');
        } else {
            if(detailTagsContainer) detailTagsContainer.innerHTML = '<span class="text-muted">暂无标签</span>';
        }

        if (example.target_model === 'GPT4O-编辑') {
            if(gpt4oEditImagesSection) gpt4oEditImagesSection.style.display = 'none';
            if (example.source_image_file) {
                if(detailSourceImage) {
                    detailSourceImage.src = IMAGE_BASE_URL + example.source_image_file;
                    detailSourceImage.style.display = 'block';
                }
                if(detailPlaceholderSourceImage) detailPlaceholderSourceImage.style.display = 'none';
            } else {
                if(detailSourceImage) detailSourceImage.style.display = 'none';
                if(detailPlaceholderSourceImage) {
                    detailPlaceholderSourceImage.style.display = 'flex';
                    if(detailPlaceholderSourceImage.querySelector('p')) detailPlaceholderSourceImage.querySelector('p').textContent = '无源图';
                }
            }
            if (example.reference_image_file) {
                if(detailReferenceImage) {
                    detailReferenceImage.src = IMAGE_BASE_URL + example.reference_image_file;
                    detailReferenceImage.style.display = 'block';
                }
                if(detailPlaceholderReferenceImage) detailPlaceholderReferenceImage.style.display = 'none';
            } else {
                if(detailReferenceImage) detailReferenceImage.style.display = 'none';
                if(detailPlaceholderReferenceImage) {
                    detailPlaceholderReferenceImage.style.display = 'flex';
                    if(detailPlaceholderReferenceImage.querySelector('p')) detailPlaceholderReferenceImage.querySelector('p').textContent = '无参考图';
                }
            }
        } else {
            if(gpt4oEditImagesSection) gpt4oEditImagesSection.style.display = 'none';
        }

        if(detailLikeCount) detailLikeCount.textContent = example.likes_count || '0';
        if(detailLikeBtn) {
            const likeIcon = detailLikeBtn.querySelector('i');
            if (example.is_liked_by_current_user) {
                detailLikeBtn.classList.add('liked');
                if(likeIcon) likeIcon.className = 'bi bi-heart-fill me-1';
                detailLikeBtn.title = '取消点赞';
            } else {
                detailLikeBtn.classList.remove('liked');
                if(likeIcon) likeIcon.className = 'bi bi-heart me-1';
                detailLikeBtn.title = '点赞';
            }
        }
        loadComments(example.id);

        // 多图切换逻辑
        const images = [];
        
        // 根据模型类型处理不同的图片展示逻辑
        if (example.target_model === 'FLUX-多图') {
            // FLUX-多图模式下：
            // image_file 是结果图
            // flux_second_image_file 是输入图片1
            // flux_result_image_file 是输入图片2
            if (example.image_file) images.push({ url: IMAGE_BASE_URL + example.image_file, label: '结果图' });
            if (example.flux_second_image_file) images.push({ url: IMAGE_BASE_URL + example.flux_second_image_file, label: '图1' });
            if (example.flux_result_image_file) images.push({ url: IMAGE_BASE_URL + example.flux_result_image_file, label: '图2' });
        } else if (example.target_model === 'GPT4O-编辑') {
            // GPT4O-编辑模式正常处理
            if (example.image_file) images.push({ url: IMAGE_BASE_URL + example.image_file, label: '结果图' });
            if (example.source_image_file) images.push({ url: IMAGE_BASE_URL + example.source_image_file, label: '源图片' });
            if (example.reference_image_file) images.push({ url: IMAGE_BASE_URL + example.reference_image_file, label: '参考图' });
        } else {
            // 其他模型正常处理
            if (example.image_file) images.push({ url: IMAGE_BASE_URL + example.image_file, label: '结果图' });
            if (example.source_image_file) images.push({ url: IMAGE_BASE_URL + example.source_image_file, label: '源图片' });
            if (example.reference_image_file) images.push({ url: IMAGE_BASE_URL + example.reference_image_file, label: '参考图' });
        }
        
        renderGalleryImages(images, example.target_model);
    }

    function renderGalleryImages(images, modelType) {
        const mainImg = document.getElementById('detailGalleryMainImage');
        const mainPlaceholder = document.getElementById('detailGalleryMainImagePlaceholder');
        const thumbs = document.getElementById('detailGalleryThumbnails');
        if (!mainImg || !mainPlaceholder || !thumbs) return;

        currentImageDimensions = null; // 每次渲染画廊时首先重置

        if (!images || images.length === 0) {
            mainImg.style.display = 'none';
            mainPlaceholder.style.display = 'flex';
            thumbs.style.display = 'none';
            return;
        }
        let currentIdx = 0;
        function showImage(idx) {
            mainImg.src = images[idx].url;
            mainImg.alt = images[idx].label;
            mainImg.style.display = 'block';
            mainPlaceholder.style.display = 'none';
            // 动态设置主图区和图片的max-height
            if (modelType === 'GPT4O-编辑' || modelType === 'FLUX-多图') {
                mainImg.style.maxHeight = '45vh';
            } else {
                mainImg.style.maxHeight = '62vh';
            }
            // 高亮当前缩略图
            thumbs.querySelectorAll('.gallery-thumb').forEach((el, i) => {
                el.classList.toggle('active', i === idx);
            });
            mainImg.onload = () => {
                if (mainImg.naturalWidth > 0 && mainImg.naturalHeight > 0) {
                    currentImageDimensions = { width: mainImg.naturalWidth, height: mainImg.naturalHeight };
                }
                mainImg.onload = null; // 移除事件处理器，防止多次触发
            };
            // 处理图片已缓存的情况
            if (mainImg.complete && mainImg.naturalWidth > 0 && mainImg.naturalHeight > 0) {
                // 如果图片已加载完成，立即获取尺寸
                // 只有当 currentImageDimensions 尚未通过 onload 设置时才执行
                if (!currentImageDimensions || (currentImageDimensions.width !== mainImg.naturalWidth || currentImageDimensions.height !== mainImg.naturalHeight) ) {
                    currentImageDimensions = { width: mainImg.naturalWidth, height: mainImg.naturalHeight };
                }
            }
        }
        // 渲染缩略图
        if (images.length > 1) {
            thumbs.innerHTML = images.map((img, i) =>
                `<div class="gallery-thumb${i === 0 ? ' active' : ''}" style="cursor:pointer; margin:0 6px; text-align:center;">
                    <img src="${img.url}" alt="${img.label}" style="width:48px;height:48px;object-fit:contain;border-radius:6px;border:2px solid #333;">
                    <div style="font-size:0.75em;color:#aaa;">${img.label}</div>
                </div>`
            ).join('');
            thumbs.style.display = 'flex';
            thumbs.querySelectorAll('.gallery-thumb').forEach((el, i) => {
                el.onclick = () => {
                    currentIdx = i;
                    showImage(i);
                };
            });
        } else {
            thumbs.innerHTML = '';
            thumbs.style.display = 'none';
        }
        showImage(0);
    }
    
    // Event Listeners
    if(detailCopyPromptBtn) {
        detailCopyPromptBtn.addEventListener('click', () => {
            if (navigator.clipboard && detailPrompt && detailPrompt.textContent) {
                navigator.clipboard.writeText(detailPrompt.textContent)
                    .then(() => showToastFromMain('提示词已复制到剪贴板!')) 
                    .catch(err => {
                        console.error('[Detail Modal] Failed to copy prompt:', err);
                        alert('复制失败，请手动复制。');
                    });
            } else {
                alert('无法复制提示词。');
            }
        });
    }

    if(detailLikeBtn) {
        detailLikeBtn.addEventListener('click', async () => {
            if (!currentExampleId) return;
            try {
                if (window.toggleLikeDetailModal) { 
                    await window.toggleLikeDetailModal(currentExampleId, detailLikeBtn, detailLikeCount);
                } else {
                    console.warn('toggleLikeDetailModal function not found on window.');
                }
            } catch (error) {
                console.error('[Detail Modal] Error toggling like:', error);
                alert('点赞/取消点赞失败');
            }
        });
    }
    
    if(detailShareBtn) {
        detailShareBtn.addEventListener('click', () => {
            if (!currentExampleId) return; // Still good to check if an example is loaded

            const promptText = detailPrompt.textContent || '（未知提示词）';
            const authorName = detailAuthorName.textContent || '（匿名作者）';
            
            const promotionalText = "\n\n---\n✨ 发现更多 AI 绘画灵感，请访问 https://www.yzycolour.top/prompt-examples/admin ✨";
            const attributionText = `\n作者：${authorName}`;
            
            const finalTextToCopy = promptText + attributionText + promotionalText;

            navigator.clipboard.writeText(finalTextToCopy).then(() => {
                showToastFromMain('提示词及作者信息已复制 (含来源)!'); // 更新成功提示
            }).catch(err => {
                 console.error('[Detail Modal] Failed to copy prompt and author info:', err);
                 alert(`复制以下内容失败，请手动复制:\n\n${finalTextToCopy}`); // 更新失败提示
            });
        });
    }

    if(detailCommentForm) {
        // 字数统计功能
        const commentText = document.getElementById('detailCommentText');
        const charCount = document.getElementById('commentCharCount');
        const submitBtn = document.getElementById('commentSubmitBtn');
        const MAX_COMMENT_LENGTH = 200;
        
        if(commentText && charCount) {
            commentText.addEventListener('input', function() {
                const currentLength = this.value.length;
                charCount.textContent = currentLength;
                
                // 超过字数限制时禁用提交按钮
                if(submitBtn) {
                    if(currentLength > MAX_COMMENT_LENGTH) {
                        submitBtn.disabled = true;
                        charCount.classList.add('text-danger');
                    } else {
                        submitBtn.disabled = false;
                        charCount.classList.remove('text-danger');
                    }
                }
            });
        }
        
        // 评论提交处理
        detailCommentForm.addEventListener('submit', async (event) => {
            event.preventDefault();
            if (!currentExampleId || !detailCommentText || !detailCommentText.value.trim()) return;
            // 检查评论长度
            if (detailCommentText.value.trim().length > MAX_COMMENT_LENGTH) {
                showCommentError(`评论不能超过${MAX_COMMENT_LENGTH}个字符`);
                return;
            }
            // 禁用提交按钮，显示加载状态
            if(submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 提交中...';
            }
            // 隐藏之前的错误信息
            hideCommentError();
            // 构建FormData
            const formData = new FormData();
            formData.append('content', detailCommentText.value.trim());
            if (commentImageFile) {
                formData.append('image', commentImageFile);
            }
            try {
                const token = window.getToken ? window.getToken() : localStorage.getItem('token');
                const response = await fetch(`${API_BASE_URL}/examples/${currentExampleId}/comments`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }, // 移除Content-Type，让浏览器自动处理FormData
                    body: formData
                });
                if (!response.ok) {
                    let errorMessage = '发表评论失败';
                    try {
                        const errorData = await response.json();
                        errorMessage = errorData.error || errorData.message || `评论失败: ${response.status}`;
                    } catch(e) {
                        errorMessage = `评论失败: ${response.status}`;
                    }
                    throw new Error(errorMessage);
                }
                // 清空评论框、图片选择和预览
                if(detailCommentText) {
                    detailCommentText.value = '';
                    if(charCount) charCount.textContent = '0';
                }
                if(commentImageInput) commentImageInput.value = '';
                if(commentImagePreview) commentImagePreview.style.display = 'none';
                if(commentImagePreviewImg) commentImagePreviewImg.src = '';
                if(commentImageLabel) commentImageLabel.style.display = '';
                commentImageFile = null;
                // 重新加载评论
                await loadComments(currentExampleId);
                // 显示成功提示
                showToastFromMain('评论发表成功!');
            } catch (error) {
                console.error('[Detail Modal] Error submitting comment:', error);
                showCommentError(error.message || '发表评论失败');
            } finally {
                // 恢复提交按钮状态
                if(submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="bi bi-send me-1"></i> 发表评论';
                }
            }
        });
    }

    // 添加重试按钮事件监听
    const retryCommentsBtn = document.getElementById('retryCommentsBtn');
    if(retryCommentsBtn) {
        retryCommentsBtn.addEventListener('click', () => {
            if(currentExampleId) {
                loadComments(currentExampleId);
            }
        });
    }

    async function loadComments(exampleId) {
        if(!detailCommentsList) return;
        
        // 显示加载状态
        document.getElementById('detailCommentsLoading').style.display = 'flex';
        document.getElementById('detailCommentsList').style.display = 'none';
        document.getElementById('detailCommentsError').style.display = 'none';
        
        
        if(detailCommentCount) detailCommentCount.textContent = '...'; // 更新评论数状态
        
        try {
            const response = await fetch(`${API_BASE_URL}/examples/${exampleId}/comments`, {
                headers: getAuthHeadersFromMain() 
            });
            
            if (!response.ok) {
                if (response.status === 404) {
                    // 不需要显示错误，只是没有评论
                    if(detailCommentsList) {
                        detailCommentsList.innerHTML = '<p class="text-muted text-center py-4">暂无评论，快来抢沙发吧！</p>';
                        detailCommentsList.style.display = 'block';
                    }
                    
                    if(detailCommentCount) detailCommentCount.textContent = '0';
                    
                    // 隐藏加载和错误状态
                    document.getElementById('detailCommentsLoading').style.display = 'none';
                    return;
                }
                
                throw new Error(`获取评论失败: ${response.status}`);
            }
            
            const comments = await response.json();
            renderComments(comments);
            
        } catch (error) {
            console.error('[Detail Modal] Error loading comments:', error);
            
            // 显示错误状态
            document.getElementById('detailCommentsError').style.display = 'flex';
            
            if(detailCommentCount) detailCommentCount.textContent = 'X'; // 表示加载失败
        } finally {
            // 隐藏加载状态
            document.getElementById('detailCommentsLoading').style.display = 'none';
        }
    }

    // 获取当前用户信息（从 localStorage）
    function getCurrentUser() {
        try {
            const userStr = localStorage.getItem('user');
            if (!userStr) return null;
            return JSON.parse(userStr);
        } catch (e) {
            return null;
        }
    }

    function renderComments(comments) {
        if(!detailCommentsList) return;
        const currentUser = getCurrentUser();
        const currentUserId = currentUser ? currentUser.id : null;
        const currentUserRole = currentUser ? currentUser.role : null;
        if (!comments || comments.length === 0) {
            detailCommentsList.innerHTML = '<p class="text-muted text-center py-4">暂无评论，快来抢沙发吧！</p>';
            detailCommentsList.style.display = 'block';
            if(detailCommentCount) detailCommentCount.textContent = '0';
            return;
        }
        detailCommentsList.innerHTML = comments.map(comment => {
            const authorName = escapeHTML(comment.author_nickname || comment.author_username || '匿名用户');
            const avatarSrc = comment.author_avatar_url ? comment.author_avatar_url : DEFAULT_AVATAR;
            const dateFormatted = formatCommentDate(comment.created_at);
            // 判断是否有删除/编辑权限
            const canDelete = currentUserId && (currentUserId === comment.author_id || currentUserRole === 'admin');
            const canEdit = currentUserId && (currentUserId === comment.author_id);
            // 评论图片处理
            const hasImage = comment.image_url ? true : false;
            const imageHtml = hasImage ? `
                <div class="comment-image mt-2">
                    <img src="${comment.image_url}" alt="评论图片" class="img-fluid rounded comment-attached-image" style="max-height: 100px; cursor: pointer;" onclick="window.openCommentImageViewer('${comment.image_url}')">
                </div>` : '';
            return `
            <div class="comment-item d-flex justify-content-between align-items-start" data-comment-id="${comment.id}">
                <div style="flex:1;">
                    <div class="comment-header">
                        <img src="${avatarSrc}" alt="${authorName}" class="comment-avatar" onerror="this.src='${DEFAULT_AVATAR}'; this.onerror=null;">
                        <span class="comment-author">${authorName}</span>
                        <span class="comment-date">${dateFormatted}</span>
                    </div>
                    <p class="comment-text" data-comment-text="${comment.id}">${escapeHTML(comment.content)}</p>
                    ${imageHtml}
                </div>
                <div class="comment-actions d-flex align-items-center">
                    ${canEdit ? `<span class="comment-edit-icon" title="编辑评论"><i class="bi bi-pencil"></i></span>` : ''}
                    ${canDelete ? `<span class="comment-delete-icon" title="删除评论"><i class="bi bi-trash"></i></span>` : ''}
                </div>
            </div>`;
        }).join('');
        // 显示评论列表
        detailCommentsList.style.display = 'block';
        if(detailCommentCount) detailCommentCount.textContent = comments.length.toString();
        // 绑定删除事件
        detailCommentsList.querySelectorAll('.comment-delete-icon').forEach(icon => {
            icon.addEventListener('click', async function(e) {
                const commentItem = this.closest('.comment-item');
                const commentId = commentItem ? commentItem.getAttribute('data-comment-id') : null;
                if (!commentId || !currentExampleId) return;
                if (!window.confirm('确定要删除这条评论吗？')) return;
                try {
                    const response = await fetch(`${API_BASE_URL}/examples/${currentExampleId}/comments/${commentId}`, {
                        method: 'DELETE',
                        headers: getAuthHeadersFromMain()
                    });
                    if (!response.ok) {
                        let errorMsg = '删除评论失败';
                        try {
                            const errData = await response.json();
                            errorMsg = errData.error || errData.message || errorMsg;
                        } catch {}
                        showToastFromMain(errorMsg, 'danger');
                        return;
                    }
                    showToastFromMain('评论已删除', 'success');
                    await loadComments(currentExampleId);
                } catch (err) {
                    showToastFromMain('网络错误，删除失败', 'danger');
                }
            });
        });
        // 绑定编辑事件
        detailCommentsList.querySelectorAll('.comment-edit-icon').forEach(icon => {
            icon.addEventListener('click', function(e) {
                const commentItem = this.closest('.comment-item');
                const commentId = commentItem ? commentItem.getAttribute('data-comment-id') : null;
                if (!commentId || !currentExampleId) return;
                const textP = commentItem.querySelector(`[data-comment-text="${commentId}"]`);
                const oldText = textP ? textP.textContent : '';
                // 替换为编辑输入框
                textP.innerHTML = `<textarea class='form-control comment-edit-textarea' maxlength='200' rows='2'>${escapeHTML(oldText)}</textarea>`;
                // 隐藏编辑icon
                icon.style.display = 'none';
                // 添加保存/取消icon
                let actionsDiv = commentItem.querySelector('.comment-actions');
                if (!actionsDiv) return;
                // 防止重复添加
                if (actionsDiv.querySelector('.comment-save-icon')) return;
                const saveIcon = document.createElement('span');
                saveIcon.className = 'comment-save-icon';
                saveIcon.title = '保存';
                saveIcon.innerHTML = '<i class="bi bi-check2"></i>';
                const cancelIcon = document.createElement('span');
                cancelIcon.className = 'comment-cancel-icon';
                cancelIcon.title = '取消';
                cancelIcon.innerHTML = '<i class="bi bi-x"></i>';
                actionsDiv.appendChild(saveIcon);
                actionsDiv.appendChild(cancelIcon);
                // 保存事件
                saveIcon.addEventListener('click', async function() {
                    const textarea = commentItem.querySelector('.comment-edit-textarea');
                    if (!textarea) return;
                    const newText = textarea.value.trim();
                    if (!newText) {
                        showToastFromMain('评论内容不能为空', 'danger');
                        return;
                    }
                    if (newText.length > 200) {
                        showToastFromMain('评论不能超过200字', 'danger');
                        return;
                    }
                    try {
                        const response = await fetch(`${API_BASE_URL}/examples/${currentExampleId}/comments/${commentId}`, {
                            method: 'PUT',
                            headers: getAuthHeadersFromMain(),
                            body: JSON.stringify({ content: newText })
                        });
                        if (!response.ok) {
                            let errorMsg = '编辑评论失败';
                            try {
                                const errData = await response.json();
                                errorMsg = errData.error || errData.message || errorMsg;
                            } catch {}
                            showToastFromMain(errorMsg, 'danger');
                            return;
                        }
                        showToastFromMain('评论已更新', 'success');
                        await loadComments(currentExampleId);
                    } catch (err) {
                        showToastFromMain('网络错误，编辑失败', 'danger');
                    }
                });
                // 取消事件
                cancelIcon.addEventListener('click', function() {
                    // 恢复原内容
                    textP.textContent = oldText;
                    icon.style.display = '';
                    saveIcon.remove();
                    cancelIcon.remove();
                });
            });
        });
    }
    
    // 显示评论提交错误
    function showCommentError(message) {
        const errorElement = document.getElementById('commentSubmitError');
        if(errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        } else {
            alert(message);
        }
    }
    
    // 隐藏评论提交错误
    function hideCommentError() {
        const errorElement = document.getElementById('commentSubmitError');
        if(errorElement) {
            errorElement.style.display = 'none';
        }
    }
    
    // 格式化评论日期
    function formatCommentDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffSec = Math.floor(diffMs / 1000);
        const diffMin = Math.floor(diffSec / 60);
        const diffHour = Math.floor(diffMin / 60);
        const diffDay = Math.floor(diffHour / 24);
        
        // 不同时间间隔的显示方式
        if (diffSec < 60) {
            return '刚刚';
        } else if (diffMin < 60) {
            return `${diffMin}分钟前`;
        } else if (diffHour < 24) {
            return `${diffHour}小时前`;
        } else if (diffDay < 30) {
            return `${diffDay}天前`;
        } else {
            // 超过30天显示具体日期，如：2023-10-15
            return date.toLocaleDateString();
        }
    }

    // 使用 main.js 中暴露的函数
    function getAuthHeadersFromMain() {
        if (window.getAuthHeaders) {
            return window.getAuthHeaders();
        } 
        console.warn('getAuthHeaders from main.js not found, using local fallback.');
        const token = localStorage.getItem('token');
        return { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' };
    }

    function showToastFromMain(message, type = 'success') {
        if (window.showToast) {
            window.showToast(message, type);
        } else {
            alert(message);
        }
    }
    
    function escapeHTML(str) {
        if (str === null || str === undefined) return '';
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#39;',
            '`': '&#x60;'
        };
        return str.replace(/[&<>"'`]/g, function (match) { return map[match]; });
    }
    
    window.showExampleDetailModal = showExampleDetail;

    // 添加一个初始化标签切换功能的方法
    function initTabSwitching() {
        const tabElements = document.querySelectorAll('#detailTabs .nav-link');
        tabElements.forEach((tabEl) => {
            // 移除已存在的事件处理器，避免重复绑定
            tabEl.removeEventListener('click', handleTabClick);
            tabEl.addEventListener('click', handleTabClick);
        });
    }
    
    // 标签切换事件处理函数
    function handleTabClick(event) {
        event.preventDefault();
        
        // 移除所有标签的活动状态
        document.querySelectorAll('#detailTabs .nav-link').forEach(tab => {
            tab.classList.remove('active');
        });
        
        // 为当前点击的标签添加活动状态
        this.classList.add('active');
        
        // 获取目标面板ID
        const targetId = this.getAttribute('data-bs-target').substring(1);
        
        // 隐藏所有标签面板
        document.querySelectorAll('#detailTabsContent .tab-pane').forEach(pane => {
            pane.classList.remove('show', 'active');
        });
        
        // 显示目标面板
        const targetPane = document.getElementById(targetId);
        if (targetPane) {
            targetPane.classList.add('show', 'active');
        }
    }

    if (commentImageInput) {
        commentImageInput.addEventListener('change', async function() {
            const file = this.files && this.files[0];
            if (!file) {
                if(commentImagePreview) commentImagePreview.style.display = 'none';
                if(commentImageLabel) commentImageLabel.style.display = '';
                commentImageFile = null;
                return;
            }
            // 只允许图片
            if (!file.type.startsWith('image/')) {
                alert('请选择图片文件');
                commentImageInput.value = '';
                if(commentImagePreview) commentImagePreview.style.display = 'none';
                if(commentImageLabel) commentImageLabel.style.display = '';
                commentImageFile = null;
                return;
            }
            // 压缩图片到200kb以内 - 使用更激进的压缩设置
            try {
                
                // 根据文件大小和类型决定压缩参数
                const sizeInMB = file.size / (1024 * 1024);
                
                // 更激进的压缩设置，参考main.js中的配置
                const compressionOptions = {
                    maxSizeMB: 0.1, // 严格控制在100KB内（实际会预留一些空间）
                    maxWidthOrHeight: 1200,
                    useWebWorker: true,
                    fileType: 'image/jpeg', // 强制转为jpeg格式，更利于压缩
                    initialQuality: 0.7  // 降低初始质量
                };
                
                // 针对非常大的图片，使用更激进的压缩
                if (sizeInMB > 5) compressionOptions.initialQuality = 0.5;
                else if (sizeInMB > 2) compressionOptions.initialQuality = 0.6;
                
                const compressedFile = await window.imageCompression(file, compressionOptions);
                
                if (compressedFile.size > 204800) { // 200KB
                    alert('图片压缩后仍超过200KB，请尝试使用更简单的图片或降低分辨率的图片');
                    commentImageInput.value = '';
                    if(commentImagePreview) commentImagePreview.style.display = 'none';
                    if(commentImageLabel) commentImageLabel.style.display = '';
                    commentImageFile = null;
                    return;
                }
                
                // 获取正确的扩展名并创建新文件
                let fileExt = 'jpg'; // 默认使用jpg，因为我们指定了输出为jpeg
                let newType = 'image/jpeg';
                
                // 创建新文件对象，确保压缩后的文件有正确的MIME类型和扩展名
                const fixedFile = new File(
                    [compressedFile], 
                    `comment_img_${Date.now()}.${fileExt}`, 
                    {type: newType}
                );
                commentImageFile = fixedFile;

                // 预览
                const reader = new FileReader();
                reader.onload = function(e) {
                    if (commentImagePreviewImg) {
                        commentImagePreviewImg.src = e.target.result;
                        if(commentImagePreview) commentImagePreview.style.display = 'flex';
                        if(commentImageLabel) commentImageLabel.style.display = 'none';
                    }
                };
                reader.readAsDataURL(fixedFile);
            } catch (err) {
                console.error('图片压缩失败:', err);
                alert('图片压缩失败，请尝试使用更小或更简单的图片');
                commentImageInput.value = '';
                if(commentImagePreview) commentImagePreview.style.display = 'none';
                if(commentImageLabel) commentImageLabel.style.display = '';
                commentImageFile = null;
            }
        });
    }
    if (removeCommentImageBtn) {
        removeCommentImageBtn.addEventListener('click', function() {
            if(commentImageInput) commentImageInput.value = '';
            if(commentImagePreview) commentImagePreview.style.display = 'none';
            if (commentImagePreviewImg) commentImagePreviewImg.src = '';
            if(commentImageLabel) commentImageLabel.style.display = '';
            commentImageFile = null;
        });
    }
}

window.initExampleDetailModal = initExampleDetailModal; 