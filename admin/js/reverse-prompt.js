// js/reverse-prompt.js
// 此文件用于处理"提示词反推"标签页的特定交互逻辑

// --- 模型数据结构 ---
let modelData; // *** 修改：将 modelData 声明在外部
if (typeof modelData === 'undefined') {
    modelData = { // *** 修改：在这里为它赋值
        "智谱 (Zhipu)": [
            { value: "zhipu", text: "glm-4v-flash" } // 注意：原先的 value 是 zhipu，保持一致
        ],
        "月之暗面 (Moonshot)": [
            { value: "kimi-8k", text: "Kimi Vision (8k)" },
            { value: "kimi-32k", text: "Kimi Vision (32k)" },
            { value: "kimi-128k", text: "Kimi Vision (128k)" }
        ],
        // 可以添加更多分类和模型
        // "其他模型": [
        //     { value: "other-model-1", text: "模型一" }
        // ]
    };
}

// --- API 常量 (如果需要从 main.js 迁移) ---
const ZHIPU_API_KEY = '755372cb0cde4da9855848ec5a8fb635.Cta0linzDIQgluUg';
const ZHIPU_API_URL = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
const KIMI_API_URL = 'https://api.moonshot.cn/v1/chat/completions';
// --- 全局变量 (如果需要) ---
let selectedReverseFile = null; // 用于反推功能的图片文件

document.addEventListener('DOMContentLoaded', () => {

    // --- 获取 DOM 元素 ---
    const cascadeModelSelectBtn = document.getElementById('cascadeModelSelectBtn');
    const cascadeModelSelectMenu = document.getElementById('cascadeModelSelectMenu');
    const selectedModelValueInput = document.getElementById('selectedModelValueInput');

    const reverseImageDropZone = document.getElementById('reverseImageDropZone');
    const reverseImageInput = document.getElementById('reverseImageInput');
    const reverseImagePreview = document.getElementById('reverseImagePreview');
    const reversePreviewImg = document.getElementById('reversePreviewImg');
    const removeReverseImageButton = document.getElementById('removeReverseImage');
    const generateReversePromptBtn = document.getElementById('generateReversePromptBtn');
    const reversePromptLoading = document.getElementById('reversePromptLoading');
    const reversePromptError = document.getElementById('reversePromptError');
    const reversePromptResultsContainer = document.getElementById('reversePromptResults');
    const resultsPlaceholder = document.getElementById('resultsPlaceholder');
    // const reverseModelSelect = document.getElementById('reverseModelSelect'); // 不再需要原生 select

    // --- 自定义级联选择器逻辑 ---
    function updateSelection(value, text) {
        if (cascadeModelSelectBtn && selectedModelValueInput) {
            cascadeModelSelectBtn.textContent = text; // 更新按钮文本
            selectedModelValueInput.value = value; // 更新隐藏输入的值

            // 可以在这里添加当模型改变时需要触发的其他逻辑
        }
    }

    function renderLevelOne() {
        if (!cascadeModelSelectMenu) return;
        cascadeModelSelectMenu.innerHTML = ''; // 清空菜单
        Object.keys(modelData).forEach(category => {
            const li = document.createElement('li');
            const a = document.createElement('a');
            a.className = 'dropdown-item';
            a.href = '#';
            a.textContent = category;
            a.dataset.category = category; // 标记为分类
            li.appendChild(a);
            cascadeModelSelectMenu.appendChild(li);
        });
    }

    function renderLevelTwo(category) {
        if (!cascadeModelSelectMenu || !modelData[category]) return;
        cascadeModelSelectMenu.innerHTML = ''; // 清空菜单

        // 添加返回按钮
        const backLi = document.createElement('li');
        const backA = document.createElement('a');
        backA.className = 'dropdown-item';
        backA.href = '#';
        backA.innerHTML = '<i class="bi bi-arrow-left me-2"></i>返回分类';
        backA.dataset.action = 'back'; // 标记为返回操作
        backLi.appendChild(backA);
        cascadeModelSelectMenu.appendChild(backLi);
        cascadeModelSelectMenu.appendChild(document.createElement('hr')); // 添加分隔线

        // 添加模型选项
        modelData[category].forEach(model => {
            const li = document.createElement('li');
            const a = document.createElement('a');
            a.className = 'dropdown-item';
            a.href = '#';
            a.textContent = model.text;
            a.dataset.value = model.value; // 标记为模型值
            a.dataset.text = model.text; // 存储显示文本
            li.appendChild(a);
            cascadeModelSelectMenu.appendChild(li);
        });
    }

    // 事件委托：处理下拉菜单点击
    if (cascadeModelSelectMenu) {
        cascadeModelSelectMenu.addEventListener('click', (event) => {
            const target = event.target.closest('a.dropdown-item'); //确保点击的是a标签或其内部元素
            if (!target) return;

            event.preventDefault(); // 阻止 a 标签的默认跳转行为

            const category = target.dataset.category;
            const value = target.dataset.value;
            const text = target.dataset.text;
            const action = target.dataset.action;

            if (category) {
                // 点击的是一级分类，渲染二级菜单，并阻止下拉菜单关闭
                renderLevelTwo(category);
                event.stopPropagation(); // *** 新增：阻止事件冒泡，防止 dropdown 关闭 ***
            } else if (value && text) {
                // 点击的是二级模型，更新选择，允许下拉菜单关闭
                updateSelection(value, text);
                // Bootstrap 5 dropdowns should close automatically on item click
                // Manually close if needed (find the button and hide its dropdown)
                // const dropdownInstance = bootstrap.Dropdown.getInstance(cascadeModelSelectBtn);
                //  if (dropdownInstance) {
                //      dropdownInstance.hide();
                //  }
            } else if (action === 'back') {
                // 点击的是返回按钮，渲染一级菜单，并阻止下拉菜单关闭
                renderLevelOne();
                event.stopPropagation(); // *** 新增：阻止事件冒泡，防止 dropdown 关闭 ***
            }
        });
    }

    // 初始化
    renderLevelOne(); // 初始渲染一级菜单
    // 设置默认值 (例如，智谱的第一个模型)
    const defaultCategory = Object.keys(modelData)[0];
    if (defaultCategory && modelData[defaultCategory] && modelData[defaultCategory].length > 0) {
        const defaultModel = modelData[defaultCategory][0];
        updateSelection(defaultModel.value, defaultModel.text);
    } else {
        // 如果没有默认模型或数据为空，设置一个提示
         if (cascadeModelSelectBtn && selectedModelValueInput){
             cascadeModelSelectBtn.textContent = "无可用模型";
             selectedModelValueInput.value = "";
         }
    }

    // --- 其他反推功能逻辑 (已迁移) ---

    // readFileAsBase64 函数
    function readFileAsBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const base64String = reader.result;
                resolve(base64String);
            };
            reader.onerror = (error) => {
                reject(error);
            };
            reader.readAsDataURL(file);
        });
    }

     // 图片处理函数
    function handleReverseFileSelect(file) {
        if (!file || !file.type.match('image.*')) {
            alert('请选择有效的图片文件');
            if(reverseImageInput) reverseImageInput.value = '';
            return;
        }
        const maxSizeMB = 10;
        if (file.size > maxSizeMB * 1024 * 1024) {
            alert(`图片大小不能超过 ${maxSizeMB}MB`);
             if(reverseImageInput) reverseImageInput.value = '';
            return;
        }

        selectedReverseFile = file;
        displayReverseImagePreview();
        if(generateReversePromptBtn) generateReversePromptBtn.disabled = false;
        if(reversePromptResultsContainer) reversePromptResultsContainer.innerHTML = '';
        if(resultsPlaceholder) resultsPlaceholder.style.display = 'block';
        if(reversePromptError) reversePromptError.style.display = 'none';
    }

    function displayReverseImagePreview() {
        if (!selectedReverseFile || !reverseImagePreview || !reversePreviewImg || !reverseImageDropZone) {
            console.error('无法显示反推图片预览，缺少必要的 DOM 元素。');
            if(reverseImagePreview) reverseImagePreview.style.display = 'none';
            if(reversePreviewImg) reversePreviewImg.src = '';
            if(reverseImageDropZone) reverseImageDropZone.style.display = 'block';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            reversePreviewImg.src = e.target.result;
            reverseImagePreview.style.display = 'block';
            reverseImageDropZone.style.display = 'none';
        };
        reader.onerror = function(e) {
            console.error("反推图片文件读取错误:", e);
            alert('读取图片预览失败');
            removeReverseImage();
        };
        reader.readAsDataURL(selectedReverseFile);
    }

    function removeReverseImage() {
        selectedReverseFile = null;
        if(reverseImageInput) reverseImageInput.value = '';
        if(reversePreviewImg) reversePreviewImg.src = '';
        if(reverseImagePreview) reverseImagePreview.style.display = 'none';
        if(reverseImageDropZone) reverseImageDropZone.style.display = 'block';
        if(generateReversePromptBtn) generateReversePromptBtn.disabled = true;
    }

    // 图片相关事件监听
    if (reverseImageDropZone) {
        reverseImageDropZone.addEventListener('click', () => reverseImageInput && reverseImageInput.click());
        reverseImageDropZone.addEventListener('dragover', (event) => {
            event.preventDefault();
            reverseImageDropZone.classList.add('drag-over');
        });
        reverseImageDropZone.addEventListener('dragleave', () => {
            reverseImageDropZone.classList.remove('drag-over');
        });
        reverseImageDropZone.addEventListener('drop', (event) => {
            event.preventDefault();
            reverseImageDropZone.classList.remove('drag-over');
            if (event.dataTransfer.files && event.dataTransfer.files[0]) {
                handleReverseFileSelect(event.dataTransfer.files[0]);
            }
        });
        const reversePane = document.getElementById('reverse-prompt-tab-pane');
        if(reversePane) {
            reversePane.addEventListener('paste', (event) => {
                 const items = (event.clipboardData || event.originalEvent.clipboardData).items;
                 for (let index in items) {
                     const item = items[index];
                     if (item.kind === 'file' && item.type.startsWith('image/')) {
                         const blob = item.getAsFile();
                         if (blob) {
                             const timestamp = Date.now();
                             const file = new File([blob], `pasted-reverse-${timestamp}.${blob.type.split('/')[1]}`, { type: blob.type });
                             handleReverseFileSelect(file);
                             event.preventDefault();
                             break;
                         }
                     }
                 }
            });
        }
    }
    if (reverseImageInput) {
        reverseImageInput.addEventListener('change', (event) => {
            if (event.target.files && event.target.files[0]) {
                handleReverseFileSelect(event.target.files[0]);
            }
        });
    }
    if (removeReverseImageButton) {
        removeReverseImageButton.addEventListener('click', removeReverseImage);
    }

    // AI 反推调用逻辑
    async function getReversePrompts(imageFile, targetTypes) {
        if (!generateReversePromptBtn || !reversePromptLoading || !reversePromptError || !resultsPlaceholder || !reversePromptResultsContainer || !selectedModelValueInput) {
            console.error("缺少必要的 DOM 元素来执行反推。");
            return;
        }
        generateReversePromptBtn.disabled = true;
        reversePromptLoading.style.display = 'flex';
        reversePromptError.style.display = 'none';
        resultsPlaceholder.style.display = 'none';
        reversePromptResultsContainer.innerHTML = '';

        try {
            const base64Image = await readFileAsBase64(imageFile);
            const selectedModelValue = selectedModelValueInput.value;
            if (!selectedModelValue) {
                 throw new Error('请先通过下拉菜单选择一个有效的 AI 模型。');
            }

            let promptInstruction = `你是一个专业的图像分析师和提示词工程师。\n请仔细分析用户提供的这张图片，然后根据用户选择的类型生成对应的提示词。\n**重要：你必须为用户选择的 *每一个* 类型都生成对应的提示词。**\n用户选择的类型是: ${targetTypes.join(', ')}。\n\n请严格按照以下 JSON 格式返回结果，**确保 JSON 结构完整且只包含用户请求的字段**：\n{\n`;
            if (targetTypes.includes('general')) {
                promptInstruction += `  "general_description": "这里是对图片的非常详细的描述，请涵盖主体、对象、细节、环境、构图、光线、色彩、情感氛围、可能的艺术风格或媒介（照片、插画、3D渲染等）。",\n`;
            }
            if (targetTypes.includes('mj')) {
                promptInstruction += `  "midjourney_prompt": "这里是适用于 Midjourney 的提示词。请使用逗号分隔的英文关键词和短语，着重于视觉元素、艺术风格、艺术家名称（如果适用）、画面氛围和常用的 MJ 参数（例如 --ar 16:9 --v 6）。例如：'blue location pin icon on abstract map background, paper airplane, red heart symbols, flat design, vector art, minimalist, tech style --ar 16:9 --v 6'",\n`;
            }
            if (targetTypes.includes('sd')) {
                promptInstruction += `  "stable_diffusion_prompt": "这里是适用于 Stable Diffusion 的提示词。请使用英文关键词，可以包含正面提示 (positive prompt) 和负面提示 (negative prompt)，用逗号分隔。关注图像内容、风格、质量词 (e.g., best quality, masterpiece) 和可能的触发词。例如：'positive prompt: blue map marker icon, simple vector map background with lines, paper plane, small red hearts, flat illustration, modern design, soft colors, best quality. negative prompt: blurry, low quality, text, letters, noisy'",\n`;
            }
            promptInstruction = promptInstruction.trim().replace(/,$/, '');
            promptInstruction += `\n}\n**再次强调：请确保输出是严格的 JSON 格式，包含所有请求的字段 (${targetTypes.join(', ')})，不要遗漏任何一个。**`;

            let apiUrl = '';
            let apiKey = '';
            let modelName = '';
            let requestBody = {};
            let headers = {};
            let isKimiModel = selectedModelValue.startsWith('kimi-');

            const messages = [
                {
                    role: "user",
                    content: [
                        { type: "text", text: promptInstruction },
                        { type: "image_url", image_url: { url: base64Image } }
                    ]
                }
            ];

            if (isKimiModel) {
                apiKey = localStorage.getItem('kimiApiKey');
                if (!apiKey) {
                    alert('Kimi 模型需要设置 API Key 才能使用。即将打开 API Key 设置窗口。');
                    const apiKeyModalElement = document.getElementById('apiKeyModal');
                    if (apiKeyModalElement) {
                        const apiKeyModal = bootstrap.Modal.getOrCreateInstance(apiKeyModalElement);
                        apiKeyModal.show();
                    } else {
                        console.error('无法找到 API Key 模态框元素！');
                        throw new Error('请点击模型选择旁边的钥匙图标，设置 Kimi API Key。');
                    }
                    return;
                }
                apiUrl = KIMI_API_URL;
                const kValueMatch = selectedModelValue.match(/kimi-(\d+k)/);
                const kValue = kValueMatch ? kValueMatch[1] : '8k';
                modelName = `moonshot-v1-${kValue}-vision-preview`;
                headers = {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                };
                requestBody = JSON.stringify({
                    model: modelName,
                    messages: messages,
                    max_tokens: 1024,
                    temperature: 0.3,
                    stream: false
                });
            } else if (selectedModelValue === 'zhipu') {
                apiUrl = ZHIPU_API_URL;
                apiKey = ZHIPU_API_KEY;
                modelName = "glm-4v-flash";
                headers = {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                };
                requestBody = JSON.stringify({
                    model: modelName,
                    messages: messages,
                    max_tokens: 1024,
                    temperature: 0.7,
                    stream: false
                });
            } else {
                 throw new Error(`错误: 不支持的模型选择 "${selectedModelValue}"`);
            }

            // API 调用
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: headers,
                body: requestBody
            });

            if (!response.ok) {
                let errorText = '';
                try {
                     const errorData = await response.json();
                     errorText = errorData.error?.message || errorData.error?.code || JSON.stringify(errorData);
                } catch (e) {
                     errorText = await response.text();
                }
                console.error(`${isKimiModel ? 'Kimi' : '智谱'} AI 反推请求失败:`, response.status, errorText);
                throw new Error(`AI 服务请求失败 (${response.status}): ${errorText}`);
            }

            const result = await response.json();
            const aiContent = result.choices[0]?.message?.content;

            if (!aiContent) {
                 throw new Error('AI 未返回有效内容');
            }

            let parsedResult = {};
            try {
                const jsonMatch = aiContent.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                     parsedResult = JSON.parse(jsonMatch[0]);
                     displayReversePromptResults(parsedResult);
                } else {
                     console.error('AI 响应中未找到有效的 JSON 格式:', aiContent);
                     displayReversePromptResults({ raw_text: aiContent });
                }
            } catch (parseError) {
                console.error('解析 AI 响应 JSON 失败:', parseError);
                 displayReversePromptResults({ raw_text: aiContent });
            }


        } catch (error) {
            console.error('反推提示词时出错:', error);
            if(reversePromptError) {
                reversePromptError.textContent = `生成失败: ${error.message || '未知错误'}`;
                reversePromptError.style.display = 'block';
            }
        } finally {
            generateReversePromptBtn.disabled = false;
            reversePromptLoading.style.display = 'none';
        }
    }

    // 反推结果显示函数
    function displayReversePromptResults(results) {
        if (!reversePromptResultsContainer) return;
        reversePromptResultsContainer.innerHTML = '';

        const typeMapping = {
            general_description: '通用描述',
            midjourney_prompt: 'Midjourney 提示词',
            stable_diffusion_prompt: 'Stable Diffusion 提示词',
            raw_text: 'AI 原始响应'
        };

        let hasContent = false;
        for (const key in results) {
            if (results.hasOwnProperty(key) && results[key]) {
                hasContent = true;
                const title = typeMapping[key] || key;
                const content = results[key];
                const resultBlock = document.createElement('div');
                resultBlock.className = 'mb-4 result-block';
                const header = document.createElement('div');
                header.className = 'd-flex justify-content-between align-items-center mb-2';
                const heading = document.createElement('h6');
                heading.className = 'mb-0 text-light';
                heading.textContent = title;
                const copyButton = document.createElement('button');
                copyButton.className = 'btn btn-sm btn-outline-secondary copy-btn';
                copyButton.innerHTML = '<i class="bi bi-clipboard me-1"></i> 复制';
                copyButton.onclick = () => copyTextToClipboard(content, copyButton);
                header.appendChild(heading);
                header.appendChild(copyButton);
                const pre = document.createElement('pre');
                pre.className = 'bg-dark p-3 rounded border border-secondary';
                pre.style.whiteSpace = 'pre-wrap';
                pre.style.wordBreak = 'break-word';
                pre.style.maxHeight = '300px';
                pre.style.overflowY = 'auto';
                pre.textContent = content;
                resultBlock.appendChild(header);
                resultBlock.appendChild(pre);
                reversePromptResultsContainer.appendChild(resultBlock);
            }
        }

        if (!hasContent) {
            reversePromptResultsContainer.innerHTML = '<p class="text-muted text-center">AI 未能生成有效的提示词内容。</p>';
        }
    }

     // 简单的文本复制函数
    async function copyTextToClipboard(text, buttonElement) {
        try {
            await navigator.clipboard.writeText(text);
            if (buttonElement) {
                const originalText = buttonElement.innerHTML;
                buttonElement.innerHTML = '<i class="bi bi-check-lg me-1"></i> 已复制';
                buttonElement.disabled = true;
                setTimeout(() => {
                    buttonElement.innerHTML = originalText;
                    buttonElement.disabled = false;
                }, 1500);
            }
        } catch (error) {
            console.error('复制文本错误:', error);
            alert('复制失败: ' + (error.message || '未知错误'));
        }
    }

    // 生成按钮点击事件
    if (generateReversePromptBtn) {
        generateReversePromptBtn.addEventListener('click', async () => {
            if (!selectedReverseFile) {
                alert('请先上传图片');
                return;
            }
            const targetTypes = [];
            document.querySelectorAll('#reverse-prompt-tab-pane input[type="checkbox"]:checked').forEach(checkbox => {
                targetTypes.push(checkbox.value);
            });
            if (targetTypes.length === 0) {
                alert('请至少选择一种要生成的提示词类型');
                return;
            }
            await getReversePrompts(selectedReverseFile, targetTypes);
        });
    }

}); // End of DOMContentLoaded
 