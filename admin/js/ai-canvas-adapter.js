/**
 * AI画布系统适配器
 * 用于从您的网站向AI画布系统传递token
 * 
 * 使用方法:
 * 1. 在您的网页中引入此脚本
 * 2. 添加一个带有id="jump-to-ai-canvas"的按钮
 * 3. 在您的登录逻辑中设置token存储
 * 4. 适配器会自动处理跳转逻辑
 */

// 在脚本开头设置调试模式
window.AI_CANVAS_DEBUG = true;

// 确保不会与其他脚本冲突
(function() {
    // 等待DOM加载完成
    document.addEventListener('DOMContentLoaded', function() {
        // 获取跳转按钮元素
        const jumpToAiCanvasBtn = document.getElementById('jump-to-ai-canvas');
        
        // 添加点击事件监听器
        if (jumpToAiCanvasBtn) {
            jumpToAiCanvasBtn.addEventListener('click', jumpToJaazCanvas);
        } else {
            console.warn('[AI画布适配器] 未找到跳转按钮，请确保页面中有id为jump-to-ai-canvas的元素');
        }
    });

    // 配置选项 - 可以在引入脚本前设置全局变量来覆盖这些默认值
    const config = {
        tokenKey: window.AI_CANVAS_TOKEN_KEY || 'token', // 存储token的localStorage键名
        ssoApiUrl: window.AI_CANVAS_SSO_API_URL || 'https://caca.yzycolour.top/api/sso/generate-token', // SSO API地址
        targetSystem: window.AI_CANVAS_TARGET_SYSTEM || 'jaaz', // 目标系统标识
        buttonLoadingText: window.AI_CANVAS_BUTTON_LOADING_TEXT || '<span class="ai-canvas-spinner"></span>跳转中...', // 加载中显示的文本
        buttonDefaultText: window.AI_CANVAS_BUTTON_DEFAULT_TEXT || 'AI画布', // 默认按钮文本
        openInNewTab: window.AI_CANVAS_OPEN_IN_NEW_TAB !== undefined ? window.AI_CANVAS_OPEN_IN_NEW_TAB : true, // 是否在新标签页打开
        debug: window.AI_CANVAS_DEBUG || false // 是否开启调试模式
    };

    // 添加必要的CSS样式
    if (!document.getElementById('ai-canvas-adapter-styles')) {
        const style = document.createElement('style');
        style.id = 'ai-canvas-adapter-styles';
        style.innerHTML = `
            .ai-canvas-spinner {
                display: inline-block;
                width: 15px;
                height: 15px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                border-top-color: #fff;
                animation: ai-canvas-spin 1s ease-in-out infinite;
                margin-right: 8px;
                vertical-align: middle;
            }
            @keyframes ai-canvas-spin {
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    // 跳转到JAAZ画布系统
    async function jumpToJaazCanvas() {
        let btn = null;
        try {
            // 获取按钮元素
            btn = document.getElementById('jump-to-ai-canvas');
            if (!btn) {
                throw new Error('跳转按钮元素不存在');
            }

            // 显示加载状态
            const originalText = btn.innerHTML;
            btn.innerHTML = config.buttonLoadingText;
            btn.disabled = true;

            // 从localStorage获取token
            const token = localStorage.getItem(config.tokenKey);
            if (!token) {
                throw new Error('未找到认证令牌，请先登录');
            }

            // 添加令牌解析和检查
            try {
                const tokenParts = token.split('.');
                if (tokenParts.length === 3) {
                    const payloadBase64 = tokenParts[1];
                    const payload = JSON.parse(atob(payloadBase64));
                    
                    // 检查令牌是否已过期
                    if (payload.exp < Math.floor(Date.now() / 1000)) {
                        console.error('[AI画布适配器] 令牌已过期!');
                    }
                }
            } catch (e) {
                console.error('[AI画布适配器] 令牌解析失败:', e);
            }

            // 调用AI画布系统的SSO接口
            const response = await fetch(config.ssoApiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    target_system: config.targetSystem
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                if (config.debug) {
                    console.error(`[AI画布适配器] API响应错误(${response.status}): ${errorText}`);
                }
                throw new Error(`生成跳转令牌失败(${response.status})`);
            }

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.message || '生成跳转令牌失败');
            }

            // 获取跳转URL
            const jaazUrl = data.redirect_url;

            // 执行跳转
            if (config.openInNewTab) {
                window.open(jaazUrl, '_blank');
            } else {
                window.location.href = jaazUrl;
            }

            return { success: true, redirectUrl: jaazUrl };

        } catch (error) {
            console.error('[AI画布适配器] 跳转失败:', error);
            alert(`跳转到AI画布失败: ${error.message}`);
            return { success: false, error: error.message };
        } finally {
            // 恢复按钮状态
            if (btn) {
                btn.innerHTML = config.buttonDefaultText || 'AI画布';
                btn.disabled = false;
            }
        }
    }

    // 导出全局函数，允许手动调用
    window.jumpToAICanvas = jumpToJaazCanvas;
})();