/**
 * 自定义UI组件
 * 包含可在整个管理界面中复用的UI组件
 */

/**
 * 创建自定义下拉选择器
 * @param {Object} options - 配置选项
 * @param {string} options.containerId - 容器元素的ID
 * @param {string} options.labelText - 标签文本
 * @param {Array} options.options - 选项数组，每个选项格式为 {value: '值', text: '显示文本'}
 * @param {string} options.defaultValue - 默认选中的值
 * @param {function} options.onChange - 选项变更时的回调函数 (value, text) => {}
 * @param {string} options.labelForId - (可选) label的for属性，默认为 containerId + 'Btn'
 * @returns {Object} - 包含组件引用和方法的对象
 */
function createCustomDropdown(options) {
    const {
        containerId,
        labelText,
        options: dropdownOptions,
        defaultValue,
        onChange,
        labelForId = containerId + 'Btn'
    } = options;

    // 获取或创建容器
    let container = document.getElementById(containerId);
    if (!container) {
        console.error(`容器 #${containerId} 不存在`);
        return null;
    }

    // 清空容器
    container.innerHTML = '';

    // 创建label
    if (labelText) {
        const label = document.createElement('label');
        label.setAttribute('for', labelForId);
        label.className = 'form-label';
        label.textContent = labelText;
        container.appendChild(label);
    }

    // 创建dropdown容器
    const dropdownDiv = document.createElement('div');
    dropdownDiv.className = 'dropdown';

    // 创建按钮
    const button = document.createElement('button');
    button.className = 'form-select text-start dropdown-toggle';
    button.setAttribute('type', 'button');
    button.setAttribute('id', labelForId);
    button.setAttribute('data-bs-toggle', 'dropdown');
    button.setAttribute('aria-expanded', 'false');
    
    // 默认文本
    const defaultOption = dropdownOptions.find(opt => opt.value === defaultValue) || dropdownOptions[0];
    button.textContent = defaultOption ? defaultOption.text : '';
    
    // 创建下拉菜单
    const dropdownMenu = document.createElement('ul');
    const menuId = containerId + 'Menu';
    dropdownMenu.className = 'dropdown-menu dropdown-menu-dark w-100 custom-filter-dropdown';
    dropdownMenu.setAttribute('aria-labelledby', labelForId);
    dropdownMenu.setAttribute('id', menuId);

    // 创建隐藏的input
    const hiddenInput = document.createElement('input');
    const valueId = containerId + 'Value';
    hiddenInput.setAttribute('type', 'hidden');
    hiddenInput.setAttribute('id', valueId);
    hiddenInput.value = defaultValue || (defaultOption ? defaultOption.value : '');

    // 添加选项
    dropdownOptions.forEach(option => {
        const li = document.createElement('li');
        const a = document.createElement('a');
        a.className = 'dropdown-item' + (option.value === hiddenInput.value ? ' active' : '');
        a.setAttribute('href', '#');
        a.setAttribute('data-value', option.value);
        a.textContent = option.text;

        // 点击事件
        a.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();
            
            const selectedValue = this.dataset.value;
            const selectedText = this.textContent;
            
            hiddenInput.value = selectedValue;
            button.textContent = selectedText;

            // 更新active状态
            dropdownMenu.querySelector('.dropdown-item.active')?.classList.remove('active');
            this.classList.add('active');

            // 调用回调
            if (typeof onChange === 'function') {
                onChange(selectedValue, selectedText);
            }
            
            // 手动关闭下拉菜单
            const dropdownInstance = bootstrap.Dropdown.getInstance(button);
            if (dropdownInstance) {
                dropdownInstance.hide();
            }
            
            return false; // 额外确保事件不会继续传播
        });

        li.appendChild(a);
        dropdownMenu.appendChild(li);
    });

    // 组装组件
    dropdownDiv.appendChild(button);
    dropdownDiv.appendChild(dropdownMenu);
    dropdownDiv.appendChild(hiddenInput);
    container.appendChild(dropdownDiv);

    // 返回组件引用和方法
    return {
        container,
        button,
        menu: dropdownMenu,
        input: hiddenInput,
        
        // 获取当前值
        getValue: () => hiddenInput.value,
        
        // 设置值
        setValue: (value) => {
            const option = dropdownOptions.find(opt => opt.value === value);
            if (option) {
                hiddenInput.value = value;
                button.textContent = option.text;
                
                // 更新active状态
                dropdownMenu.querySelector('.dropdown-item.active')?.classList.remove('active');
                const activeItem = dropdownMenu.querySelector(`[data-value="${value}"]`);
                if (activeItem) {
                    activeItem.classList.add('active');
                }
                
                // 调用回调
                if (typeof onChange === 'function') {
                    onChange(value, option.text);
                }
            }
        }
    };
}

/**
 * 初始化已存在的自定义下拉选择器
 * 用于处理已经在HTML中定义的下拉选择器
 * @param {HTMLElement|string} buttonElement - 下拉按钮元素或元素ID
 * @param {HTMLElement|string} menuElement - 下拉菜单元素或元素ID
 * @param {HTMLElement|string} valueElement - 隐藏值输入元素或元素ID
 * @param {function} [onChange] - 选项变更时的回调函数 (value, text) => {}
 * @returns {Object} - 包含组件引用和方法的对象
 */
function initializeCustomDropdown(buttonElement, menuElement, valueElement, onChange) {
    // 检查并获取DOM元素
    if (typeof buttonElement === 'string') {
        buttonElement = document.getElementById(buttonElement);
    }
    if (typeof menuElement === 'string') {
        menuElement = document.getElementById(menuElement);
    }
    if (typeof valueElement === 'string') {
        valueElement = document.getElementById(valueElement);
    }
    
    if (!buttonElement || !menuElement || !valueElement) {
        console.error('初始化下拉选择器失败：缺少必要元素', {buttonElement, menuElement, valueElement});
        return null;
    }
    
    // 绑定点击事件
    const dropdownItems = menuElement.querySelectorAll('.dropdown-item');
    dropdownItems.forEach(item => {
        item.addEventListener('click', function(event) {
            // 完全阻止事件传播
            event.preventDefault();
            event.stopPropagation();
            
            const selectedValue = this.dataset.value;
            const selectedText = this.textContent;
            
            valueElement.value = selectedValue;
            buttonElement.textContent = selectedText;

            // 更新active状态
            menuElement.querySelector('.dropdown-item.active')?.classList.remove('active');
            this.classList.add('active');
            
            // 调用回调
            if (typeof onChange === 'function') {
                onChange(selectedValue, selectedText);
            }
            
            // 手动关闭下拉菜单
            const dropdownInstance = bootstrap.Dropdown.getInstance(buttonElement);
            if (dropdownInstance) {
                dropdownInstance.hide();
            }
            
            return false; // 额外的保险措施，确保事件不会继续传播
        });
    });
    
    // 返回组件引用和方法
    return {
        button: buttonElement,
        menu: menuElement,
        input: valueElement,
        
        // 获取当前值
        getValue: () => valueElement.value,
        
        // 设置值
        setValue: (value) => {
            const item = menuElement.querySelector(`.dropdown-item[data-value="${value}"]`);
            if (item) {
                valueElement.value = value;
                buttonElement.textContent = item.textContent;
                
                // 更新active状态
                menuElement.querySelector('.dropdown-item.active')?.classList.remove('active');
                item.classList.add('active');
                
                // 调用回调
                if (typeof onChange === 'function') {
                    onChange(value, item.textContent);
                }
            }
        }
    };
}

// 导出公共函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        createCustomDropdown,
        initializeCustomDropdown
    };
}
