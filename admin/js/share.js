// Share Functionality Module

// Global variables needed (assuming defined in main.js or globally):
// API_URL, imageBaseUrl, getAuthHeaders, showToast, logout, bootstrap, QRCode

let shareModalInstance = null;
let currentShareExampleId = null;
let isShareModalLoaded = false; // Flag to track if modal HTML is loaded

// Function to load share modal HTML dynamically
async function loadShareModalHTML() {
    if (isShareModalLoaded) return; // Already loaded
    try {
        const response = await fetch('share-modal.html'); // Fetch the separate HTML file
        if (!response.ok) {
            throw new Error(`HTTP error ${response.status} fetching share-modal.html`);
        }
        const html = await response.text();
        const placeholder = document.getElementById('shareModalPlaceholder');
        if (!placeholder) {
            throw new Error('Share modal placeholder element not found in index.html');
        }
        placeholder.innerHTML = html;
        isShareModalLoaded = true;
        console.log('Share modal HTML dynamically loaded.');
    } catch (error) {
        console.error('Error loading share modal HTML:', error);
        // Display an error to the user if needed, e.g., using a toast or alert
        showToast('无法加载分享功能，请稍后重试。', 'error');
        // Optionally re-throw or handle the error to prevent modal opening
        throw error; // Re-throw to stop showShareModal execution
    }
}

async function showShareModal(exampleId) {
    // Ensure the modal HTML is loaded before proceeding
    try {
        await loadShareModalHTML();
    } catch (error) {
        // Loading failed, error already logged/shown by loadShareModalHTML
        return; // Stop execution
    }

    currentShareExampleId = exampleId;
    const shareModalElement = document.getElementById('shareModal');
    if (!shareModalElement) {
        console.error('Share modal element not found after loading!');
        showToast('分享功能组件加载失败。', 'error');
        return;
    }
    // Ensure a Bootstrap Modal instance exists for the newly added element
    shareModalInstance = bootstrap.Modal.getOrCreateInstance(shareModalElement);

    // Get references to modal elements
    const imageContainer = document.getElementById('shareImageContainer'); // Get image container
    const exampleImage = document.getElementById('shareExampleImage'); // Get image element
    const authorAvatar = document.getElementById('shareAuthorAvatar');
    const authorName = document.getElementById('shareAuthorName');
    const exampleTitle = document.getElementById('shareExampleTitle');
    const exampleDate = document.getElementById('shareExampleDate');
    const promptDisplay = document.getElementById('sharePromptDisplay');
    const modelNameSpan = document.getElementById('shareModelName');
    const copyLinkBtn = document.getElementById('copyLinkBtn');
    const copyPosterBtn = document.getElementById('copyPosterBtn'); // Restore button reference
    const qrCodeContainer = document.getElementById('shareQrCodeContainer');

    // Reset state
    exampleImage.src = '';
    exampleImage.style.display = 'none';
    authorAvatar.src = 'images/default-avatar.png';
    authorName.textContent = '...';
    exampleTitle.textContent = '加载中...';
    exampleDate.textContent = '';
    promptDisplay.textContent = '加载中...';
    modelNameSpan.textContent = '-';
    copyLinkBtn.onclick = null;
    copyLinkBtn.disabled = true;
    copyPosterBtn.onclick = null; // Reset poster button listener
    copyPosterBtn.disabled = true; // Disable poster button initially
    qrCodeContainer.innerHTML = ''; // Clear previous QR code

    shareModalInstance.show();

    try {
        // Fetch example details
        const response = await fetch(`${API_URL}/examples/${exampleId}`, {
            headers: getAuthHeaders()
        });
        if (!response.ok) {
            // Handle specific errors like 401/403, potentially calling logout()
            if (response.status === 401 || response.status === 403) {
                 logout(); // Assuming logout() is globally available
                 throw new Error('认证失败，请重新登录');
             }
            throw new Error(`获取案例详情失败 (${response.status})`);
        }
        const example = await response.json();

        if (!example) {
            throw new Error('无法获取案例数据。');
        }

        // Populate Image
        const imageFilename = example.image_file;
        const fullImageUrl = imageFilename ? imageBaseUrl + imageFilename : '';
        if (fullImageUrl) {
            exampleImage.src = fullImageUrl;
            exampleImage.style.display = 'block';
        } else {
            exampleImage.style.display = 'none';
        }

        // Populate Author
        if (example.author) {
            const authorAvatarFilename = example.author.avatar_url;
            const fullAuthorAvatarUrl = authorAvatarFilename
                ? (authorAvatarFilename.startsWith('http') ? authorAvatarFilename : imageBaseUrl + authorAvatarFilename)
                : 'images/default-avatar.png';
            authorAvatar.src = fullAuthorAvatarUrl;
            authorName.textContent = example.author.nickname || example.author.username || '匿名';
        } else {
             authorAvatar.src = 'images/default-avatar.png';
             authorName.textContent = '匿名';
        }

        // Populate Title
        exampleTitle.textContent = example.title || '无标题';

        // Populate Date
        if (example.createdAt) {
            try {
                const date = new Date(example.createdAt);
                exampleDate.textContent = date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
            } catch (e) {
                console.error('Error parsing date:', e);
                exampleDate.textContent = '';
            }
        } else {
             exampleDate.textContent = '';
        }

        // Populate Prompt
        promptDisplay.textContent = example.prompt || '无提示词';

        // Populate Model
        modelNameSpan.textContent = example.target_model || '-';

        /* --- Modified QR Code Generation --- START ---
        const currentUrlForQR = window.location.origin + window.location.pathname + '?example=' + exampleId; // Add example ID to QR URL
        qrCodeContainer.innerHTML = ''; // Clear previous content
        try {
            // Create a temporary container for QRCode library, as it needs a DOM element
            const tempQrElement = document.createElement('div');
            const qrcodeInstance = new QRCode(tempQrElement, {
                text: currentUrlForQR,
                width: 128, // Use a slightly larger size for better quality in data URL
                height: 128,
                colorDark : "#000000", // Standard black/white for better canvas conversion
                colorLight : "#ffffff",
                correctLevel : QRCode.CorrectLevel.H
            });

            // Access the generated canvas (might be implementation-dependent for qrcode.js)
            // Common ways: qrcodeInstance._el.firstChild or tempQrElement.querySelector('canvas')
            const qrCanvas = tempQrElement.querySelector('canvas');

            if (qrCanvas) {
                // Delay slightly to ensure canvas is drawn
                setTimeout(() => {
                    const qrDataURL = qrCanvas.toDataURL('image/png');
                    const qrImage = document.createElement('img');
                    qrImage.src = qrDataURL;
                    qrImage.style.width = '60px'; // Scale down the display size with CSS
                    qrImage.style.height = '60px';
                    qrImage.style.display = 'block';
                    qrImage.style.borderRadius = '4px';
                    qrImage.style.backgroundColor = '#2a2a2e'; // Add background like before
                    qrCodeContainer.appendChild(qrImage);
                 }, 50); // 50ms delay might be enough
            } else {
                 console.error("QR Code canvas element not found after generation.");
                 qrCodeContainer.innerHTML = '<small class="text-danger">二维码生成失败 (canvas)</small>';
            }
        } catch (qrError) {
            console.error("QR Code generation failed:", qrError);
            qrCodeContainer.innerHTML = '<small class="text-danger">二维码生成失败</small>';
        }
        --- Modified QR Code Generation --- END --- */

        // Configure Buttons
        copyLinkBtn.onclick = () => copyShareLink(exampleId, copyLinkBtn);
        copyPosterBtn.onclick = () => copySharePoster(copyPosterBtn); // Restore listener

        // Enable buttons
        copyLinkBtn.disabled = false;
        copyPosterBtn.disabled = false; // Enable poster button

    } catch (error) {
        console.error('显示分享模态框时出错:', error);
        // Display error state in the right panel
        exampleTitle.textContent = '加载失败';
        promptDisplay.textContent = error.message;
        copyLinkBtn.disabled = true;
        copyPosterBtn.disabled = true; // Disable poster button on error
    }
}

// --- 复制分享链接 --- 
async function copyShareLink(exampleId, buttonElement) { 
    if (!exampleId) {
        showToast('无法获取案例ID', 'error');
        return;
    }

    const originalButtonText = '复制链接';
    buttonElement.disabled = true;
    buttonElement.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 加载中...';

    try {
        // 1. 获取案例详情 (只需要 prompt)
        const response = await fetch(`${API_URL}/examples/${exampleId}`, {
            headers: getAuthHeaders()
        });
        if (!response.ok) throw new Error('无法获取案例详情');
        const example = await response.json();
        const promptText = example.prompt || '无提示词';

        // 2. 获取当前页面链接 (使用更干净的 URL，去掉参数和哈希)
        const shareUrl = window.location.origin + window.location.pathname; 

        // 3. 组合文案
        const promoText = "我在 [智绘 Prompt] 发现了一个不错的提示词，快来看看吧！"; // 你的宣传文案
        const combinedText = `${promoText}\n\n🎨 提示词:\n${promptText}\n\n🔗 链接: ${shareUrl}?example=${exampleId}`; // 可以添加 exampleId 参数方便直接定位

        // 4. 复制到剪贴板
        await navigator.clipboard.writeText(combinedText);

        // 5. 反馈
        buttonElement.innerHTML = '<i class="bi bi-check-lg"></i> 已复制';
        showToast('分享链接已复制!');

        // 延迟恢复按钮文字
        setTimeout(() => {
            buttonElement.innerHTML = originalButtonText;
            buttonElement.disabled = false;
        }, 2000);

    } catch (error) {
        console.error('复制分享链接失败:', error);
        showToast(`复制失败: ${error.message}`, 'error');
        buttonElement.innerHTML = originalButtonText; // 出错时恢复按钮
        buttonElement.disabled = false;
    }
} 

// Helper function to fetch an image and convert it to Data URL
async function imageToDataUrl(url) {
    if (!url || !url.startsWith('http')) {
        // If URL is invalid or already a data URL (or local path we can't fetch easily), return null
        return null;
    }
    try {
        const response = await fetch(url); // Fetch the image
        if (!response.ok) {
            console.warn(`Failed to fetch image for Data URL conversion: ${url}, Status: ${response.status}`);
            return null; // Return null if fetch fails
        }
        const blob = await response.blob(); // Get the image data as a Blob
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result); // Resolve with the Data URL string
            reader.onerror = reject;
            reader.readAsDataURL(blob); // Read the Blob as Data URL
        });
    } catch (error) {
        console.error(`Error converting image to Data URL (${url}):`, error);
        return null; // Return null on any error
    }
}

// --- Restore Copy Share Poster Function ---
async function copySharePoster(buttonElement) {
    const posterContentElement = document.getElementById('sharePosterContent');
    if (!posterContentElement) {
        showToast('找不到分享内容区域', 'error');
        return;
    }
    // Ensure domtoimage is loaded (check if it exists globally)
    if (typeof domtoimage !== 'object' || typeof domtoimage.toBlob !== 'function') { // Check for domtoimage object and toBlob function
        showToast('复制海报功能需要 dom-to-image-more 库', 'error');
        console.error('dom-to-image-more is not loaded or not available.');
        return;
    }

    const originalButtonText = '复制海报';
    // --- Delay changing button state --- Remove initial change
    // buttonElement.disabled = true;
    // buttonElement.innerHTML = '<span class="spinner-border ..."></span> 生成中...';

    try {
        // --- Fetch and convert images to Data URLs --- START ---
        console.log("Attempting to convert images to Data URLs...");
        const shareImageElement = posterContentElement.querySelector('#shareExampleImage');
        const avatarImageElement = posterContentElement.querySelector('#shareAuthorAvatar');

        const originalShareImageSrc = shareImageElement ? shareImageElement.src : null;
        const originalAvatarSrc = avatarImageElement ? avatarImageElement.src : null;

        const dataUrlPromises = [
            originalShareImageSrc ? imageToDataUrl(originalShareImageSrc) : Promise.resolve(null),
            originalAvatarSrc ? imageToDataUrl(originalAvatarSrc) : Promise.resolve(null)
        ];

        const [shareImageDataUrl, avatarDataUrl] = await Promise.all(dataUrlPromises);

        // Update image elements only if Data URL conversion was successful
        if (shareImageElement && shareImageDataUrl) {
            shareImageElement.src = shareImageDataUrl;
            console.log("Main image src updated to Data URL.");
        }
        if (avatarImageElement && avatarDataUrl) {
            avatarImageElement.src = avatarDataUrl;
            console.log("Avatar image src updated to Data URL.");
        }
        console.log("Image to Data URL conversion step finished.");
        // --- Fetch and convert images to Data URLs --- END ---

        // Ensure images are loaded (Keep this logic)
        const images = posterContentElement.querySelectorAll('img');
        const promises = Array.from(images).map(img => {
            if (img.src && img.src !== window.location.href && !img.complete) {
                return new Promise((resolve) => {
                     img.onload = resolve;
                     img.onerror = () => { console.warn('Image failed loading:', img.src); resolve(); }; // Resolve on error too
                     setTimeout(() => resolve(), 3000); // 3 second timeout per image
                 });
             }
            return Promise.resolve();
        });
        await Promise.all(promises);
        console.log('Image loading check complete.');

        const elementToRender = posterContentElement;
        console.log("--- Rendering target element for dom-to-image-more ---", elementToRender);

        // --- Change button state *right before* calling domtoimage --- START ---
        buttonElement.disabled = true;
        buttonElement.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 生成中...';
        // --- Change button state --- END ---

        domtoimage.toBlob(elementToRender, { quality: 0.95 })
            .then(async (blob) => {
                if (!blob) {
                    throw new Error('无法生成海报图片 Blob (dom-to-image)');
                }
                console.log("Blob generated successfully (dom-to-image), size:", blob.size);
                try {
                    await navigator.clipboard.write([new ClipboardItem({ 'image/png': blob })]);
                    // Restore button text *after* successful copy
                    buttonElement.innerHTML = '<i class="bi bi-check-lg"></i> 已复制';
                    showToast('分享海报已复制到剪贴板!');
                    setTimeout(() => {
                        buttonElement.innerHTML = originalButtonText;
                        buttonElement.disabled = false;
                    }, 2000);
                } catch (clipboardError) {
                    console.error('无法将海报复制到剪贴板:', clipboardError);
                    try {
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `prompt_example_${currentShareExampleId || 'poster'}.png`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                        showToast('无法复制到剪贴板，已尝试下载海报。下载未开始请检查浏览器设置。', 'warning', 5000);
                        // Restore button text *after* attempting download
                        // buttonElement.innerHTML = originalButtonText;
                        // buttonElement.disabled = false;
                        // Note: button state is restored in download fallback's success/error path now
                    } catch (downloadError) {
                        console.error('下载海报也失败了:', downloadError);
                        throw new Error('无法复制到剪贴板或下载海报。请检查浏览器权限。'); // Modified error
                    }
                }
            })
            .catch((error) => {
                 console.error('dom-to-image-more 转换失败:', error);
                 throw new Error(`生成海报失败: ${error.message}`);
            })
            .finally(() => {
                 // Restore original image srcs
                 if (shareImageElement && originalShareImageSrc) {
                     shareImageElement.src = originalShareImageSrc;
                 }
                 if (avatarImageElement && originalAvatarSrc) {
                     avatarImageElement.src = originalAvatarSrc;
                 }
                 console.log("Restored original image sources.");
                 // --- Remove button state restoration from finally --- 
                 // Button state is handled in .then/.catch or outer catch now
            });

    } catch (error) {
        console.error('复制分享海报操作失败:', error);
        showToast(`复制失败: ${error.message}`, 'error');
        // Restore button state immediately on outer error
        buttonElement.innerHTML = originalButtonText;
        buttonElement.disabled = false;
        // Restore original image sources in case of early error
        const shareImageElement = posterContentElement.querySelector('#shareExampleImage');
        const avatarImageElement = posterContentElement.querySelector('#shareAuthorAvatar');
        if (shareImageElement && originalShareImageSrc) shareImageElement.src = originalShareImageSrc;
        if (avatarImageElement && originalAvatarSrc) avatarImageElement.src = originalAvatarSrc;
    }
} 