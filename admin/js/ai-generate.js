document.addEventListener('DOMContentLoaded', () => {

    // +++ 新增：定义 API 基础 URL +++
    const API_BASE_URL = 'https://caca.yzycolour.top';
    // +++ 结束新增 +++

    // --- 获取 DOM 元素 ---
    const aiPromptInput = document.getElementById('aiPromptInput');
    // const aiModelSelect = document.getElementById('aiModelSelect'); // 已废弃
    const aiModelSelectValue = document.getElementById('aiModelSelectValue'); // 新增：自定义下拉菜单隐藏input
    const aiImageWidthInput = document.getElementById('aiImageWidth');
    const aiImageHeightInput = document.getElementById('aiImageHeight');
    const aiRatioSelector = document.getElementById('aiRatioSelector');
    const aiRatioLockBtn = document.getElementById('aiRatioLockBtn');
    const ratioButtons = aiRatioSelector ? aiRatioSelector.querySelectorAll('.ratio-button') : [];
    const startAIGenerationBtn = document.getElementById('startAIGenerationBtn');
    // const aiGenerationResult = document.getElementById('aiGenerationResult'); // This ID is no longer used for display
    const aiGenerationLoading = document.getElementById('aiGenerationLoading');
    const aiGenerationError = document.getElementById('aiGenerationError');
    const aiHistoryListContainer = document.getElementById('aiHistoryList');
    // const aiHistoryPaginationContainer = document.getElementById('aiHistoryPagination'); // 已移除

    // +++ 新增：获取 ComfyUI 相关的 DOM 元素 +++
    const comfyAspectRatioGroup = document.getElementById('comfyAspectRatioGroup');
    const comfyAspectRatioSelect = document.getElementById('comfyAspectRatioSelect');
    const comfyNumImagesGroup = document.getElementById('comfyNumImagesGroup'); // Added
    const comfyNumImagesInput = document.getElementById('comfyNumImagesInput'); // Added

    // +++ 新增：声明 comfyAspectRatioDropdown 变量 +++
    let comfyAspectRatioDropdown;
    // +++ 结束新增 +++

    // 获取旧版比例选择器和尺寸输入框的父容器，用于显隐控制
    // aiRatioSelector 的直接父级 div (class="mb-3")
    const legacyRatioGroup = aiRatioSelector ? aiRatioSelector.parentElement : null; 
    // aiImageWidthInput 的父级 div (class="row g-2 mb-3 align-items-center")
    const legacyDimensionsGroup = aiImageWidthInput ? aiImageWidthInput.closest('.row.g-2.mb-3.align-items-center') : null;
    // +++ 结束新增 +++

    // 新增：获取历史记录占位符
    const historyPlaceholder = document.getElementById('historyPlaceholder');

    // 新增：获取图片详情模态框及其元素
    const imageDetailModalElement = document.getElementById('imageDetailModal');
    const imageDetailModal = imageDetailModalElement ? new bootstrap.Modal(imageDetailModalElement) : null;
    const modalImageView = document.getElementById('modalImageView');
    const modalPromptDisplay = document.getElementById('modalPromptDisplay');
    const uploadToExamplesBtn = document.getElementById('uploadToExamplesBtn');
    const modalDataPrompt = document.getElementById('modalDataPrompt');
    const modalDataModel = document.getElementById('modalDataModel');
    const modalDataWidth = document.getElementById('modalDataWidth');
    const modalDataHeight = document.getElementById('modalDataHeight');
    const modalDataImageUrl = document.getElementById('modalDataImageUrl');

    // --- 全局变量，用于临时存储从详情传过来的图片信息 ---
    let featureCost = null; // 添加变量存储功能成本

    // --- !! 调试：检查按钮元素并立即绑定监听器 !! ---
    if (startAIGenerationBtn) {
        startAIGenerationBtn.addEventListener('click', () => {
            const prompt = aiPromptInput.value.trim();
            
            // 检查提示词长度，如果超过800字符则自动截断
            let finalPrompt = prompt;
            if (prompt.length > 800) {
                finalPrompt = prompt.substring(0, 800);
                console.warn(`[AI Generate] Prompt exceeds 800 characters limit. Trimming to ${finalPrompt.length} characters.`);
                // 显示提示，但不阻止生成过程
                const warningMessage = document.createElement('div');
                warningMessage.className = 'alert alert-warning alert-dismissible fade show';
                warningMessage.innerHTML = `
                    <strong>提示:</strong> 您的提示词超过800字符限制，已自动截断至800字符。
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;
                const container = aiGenerationError.parentElement;
                container.insertBefore(warningMessage, aiGenerationError);
                setTimeout(() => {
                    warningMessage.classList.remove('show');
                    setTimeout(() => {
                        warningMessage.remove();
                    }, 150);
                }, 5000);
                
                // 更新输入框内容
                aiPromptInput.value = finalPrompt;
            }
            
            const model = aiModelSelectValue ? aiModelSelectValue.value : '';
            const width = aiImageWidthInput.value;
            const height = aiImageHeightInput.value;
            const aspect_ratio_string = comfyAspectRatioDropdown 
                ? comfyAspectRatioDropdown.getValue() 
                : (comfyAspectRatioSelect ? comfyAspectRatioSelect.value : null); // 获取高级生图的比例
    
            if (!finalPrompt) {
                showError('请输入提示词！');
                aiPromptInput.focus();
                return;
            }
            
            if (model === 'comfyui_imagefx_advanced') {
                // Removed erroneous direct assignments to 'params'
                if (!aspect_ratio_string) {
                    showError('高级生图模式缺少 aspect_ratio_string 参数。');
                    // showLoading is typically handled by startGeneration or its callees
                    return;
                }
                const numImagesForComfy = comfyNumImagesInput ? (parseInt(comfyNumImagesInput.value, 10) || 1) : 1;
                // Call startGeneration with all necessary parameters, including num_images for ComfyUI
                // Width and height are null for ComfyUI advanced as per its logic
                startGeneration(finalPrompt, model, null, null, aspect_ratio_string, numImagesForComfy);
            } else {
                if ((width && isNaN(parseInt(width))) || (height && isNaN(parseInt(height))) || parseInt(width) <= 0 || parseInt(height) <= 0) {
                    showError('宽度和高度必须是有效的正数。');
                    return;
                }
                // Call startGeneration for other models (num_images is not applicable here or handled differently)
                startGeneration(finalPrompt, model, width, height, null, null); // Pass null for num_images if not used
            }
        });
    } else {
        console.error('[Debug] Error: Could not find the startAIGenerationBtn element!');
    }
    // --- !! 结束调试代码 !! ---

    // --- 状态变量 ---
    let pollingInterval = null; 
    let currentHistoryId = null;
    let currentHistoryPage = 1; 
    let totalHistoryPages = 1; // 新增：存储总页数
    let isLoadingHistory = false; // 新增：历史记录加载状态
    let isRatioLocked = true; // 新增：比例锁定状态，默认为 true

    // 新增：相关案例Tab的状态变量
    let relevantExamplesCurrentPage = 1;
    let relevantExamplesTotalPages = 1;
    let isLoadingRelevantExamples = false;
    let currentRelevantExamplesQuery = ''; // 用于存储当前相关案例的查询条件，避免重复加载相同内容

    // --- 预设比例数据 ---
    const ratios = {
        '21:9': { w: 2016, h: 864 },
        '16:9': { w: 1664, h: 936 },
        '3:2':  { w: 1584, h: 1056 },
        '4:3':  { w: 1472, h: 1104 },
        '1:1':  { w: 1328, h: 1328 },
        '3:4':  { w: 1104, h: 1472 },
        '2:3':  { w: 1056, h: 1584 },
        '9:16': { w: 936,  h: 1664 },
    };

    // --- 获取按钮内部元素 (确保在按钮存在后再获取) ---
    let btnSpinner = null;
    let btnIcon = null;
    let btnTextSpan = null; // +++ 新增，用于按钮文本 +++
    if (startAIGenerationBtn) {
        btnSpinner = startAIGenerationBtn.querySelector('.spinner-border');
        btnIcon = startAIGenerationBtn.querySelector('.bi-stars');
        // +++ 获取按钮文本的 span (如果存在的话)，或者直接操作按钮的 textContent +++
        // 假设按钮文本是直接在 <button> 标签内，而不是特定 span
    } else {
         console.error('[Debug] Cannot find spinner/icon because start button was not found.');
    }

    // --- 新增：尺寸计算和 UI 更新辅助函数 ---
    function calculateDimensions(ratioId) {
        const ratioInfo = ratios[ratioId];
        if (!ratioInfo) return null;

        // 直接使用预设的宽度和高度
        const targetWidth = ratioInfo.w;
        const targetHeight = ratioInfo.h;

        return { width: targetWidth, height: targetHeight };
    }

    function updateSizeInputs(width, height) {
        if (aiImageWidthInput) aiImageWidthInput.value = width;
        if (aiImageHeightInput) aiImageHeightInput.value = height;
    }

    function updateRatioSelection(selectedRatioId) {
        ratioButtons.forEach(button => {
            if (button.dataset.ratio === selectedRatioId) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
        // 如果选中了某个比例，自动锁定
        if (selectedRatioId && !isRatioLocked) {
             toggleRatioLock(true); // 传入 true 表示强制锁定
        } else if (!selectedRatioId && isRatioLocked) {
             // 如果没有选中比例（例如手动输入导致不匹配），则解锁
             // 注意：手动输入导致不匹配的解锁逻辑在 handleSizeInputChange 中处理
             // 这里只处理直接调用 updateRatioSelection(null) 的情况
             // toggleRatioLock(false); // 暂时注释掉，避免干扰手动锁定逻辑
        }
    }

    function toggleRatioLock(forceState = null) {
        const lockIcon = aiRatioLockBtn ? aiRatioLockBtn.querySelector('i') : null;
        if (!aiRatioLockBtn || !lockIcon) return;

        if (forceState !== null) {
            isRatioLocked = forceState;
        } else {
            isRatioLocked = !isRatioLocked; // 切换状态
        }

        if (isRatioLocked) {
            aiRatioLockBtn.classList.add('active'); // 使用 'active' 类表示锁定
            aiRatioLockBtn.title = '比例已锁定';
            lockIcon.classList.remove('bi-unlock');
            lockIcon.classList.add('bi-link-45deg');
             // 锁定后，根据当前尺寸尝试匹配一个比例
             updateRatioSelectionFromInputs();
        } else {
            aiRatioLockBtn.classList.remove('active');
            aiRatioLockBtn.title = '比例已解锁';
            lockIcon.classList.remove('bi-link-45deg');
            lockIcon.classList.add('bi-unlock');
             // 解锁后，取消比例按钮的选中状态
             updateRatioSelection(null);
        }
    }

    function updateRatioSelectionFromInputs() {
        if (!aiImageWidthInput || !aiImageHeightInput) return null;
        
        const currentWidth = parseInt(aiImageWidthInput.value, 10);
        const currentHeight = parseInt(aiImageHeightInput.value, 10);

        if (isNaN(currentWidth) || isNaN(currentHeight) || currentWidth <= 0 || currentHeight <= 0) {
            updateRatioSelection(null); // 无效尺寸，取消选择
            return null;
        }
        
        let bestMatchRatio = null;
        let minDiff = Infinity;

        // 寻找最接近的预设比例
        for (const ratioId in ratios) {
            const { w, h } = ratios[ratioId];
            const expectedHeight = (currentWidth * h) / w;
            const diff = Math.abs(currentHeight - expectedHeight);
            
             // 设置一个容忍度，比如允许 5 像素的误差
             const tolerance = 5; 
             if (diff < minDiff && diff <= tolerance) {
                 minDiff = diff;
                 bestMatchRatio = ratioId;
             }
        }
        
        updateRatioSelection(bestMatchRatio);
        return bestMatchRatio; // 返回匹配到的比例 ID 或 null
    }
    // --- 结束：辅助函数定义 ---

    // +++ 新增：获取模型友好显示名称的函数 +++
    function getModelDisplayName(modelValue) {
        const modelMap = {
            'super_image_3': '超级生图 V3.0',
            'super_image_2': '超级生图 V2.1',
            '3.0': '超级生图 V3.0',
            '2.1': '超级生图 V2.1',
            'comfyui_imagefx_advanced': '高级生图',
            'midjourney': 'Midjourney'
        };
        // 返回映射的名称，如果找不到则返回原始值
        return modelMap[modelValue] || modelValue;
    }
    // +++ 结束新增 +++

    // +++ 新增：根据模型值设置下拉框显示 +++
    function setModelSelection(modelValue) {
        const aiModelSelectBtn = document.getElementById('aiModelSelectBtn');
        const aiModelSelectMenu = document.getElementById('aiModelSelectMenu');
        const aiModelSelectValue = document.getElementById('aiModelSelectValue');

        if (!aiModelSelectBtn || !aiModelSelectMenu || !aiModelSelectValue) {
            console.error('设置模型选择失败：缺少相关的DOM元素。');
            return;
        }

        // 查找与modelValue匹配的菜单项
        const targetItem = aiModelSelectMenu.querySelector(`a[data-value="${modelValue}"]`);

        if (targetItem) {
            // 1. 更新隐藏input的值
            aiModelSelectValue.value = modelValue;
            
            // 2. 更新按钮上显示的文本
            aiModelSelectBtn.textContent = targetItem.textContent;

            // 3. 更新下拉菜单中的选中状态 (active class)
            aiModelSelectMenu.querySelectorAll('a.dropdown-item').forEach(item => {
                item.classList.remove('active');
            });
            targetItem.classList.add('active');

            // 4. 触发change事件，以便其他依赖此选择器的UI（如参数显隐）可以更新
            aiModelSelectValue.dispatchEvent(new Event('change', { bubbles: true }));

        } else {
            console.warn(`尝试设置一个不存在的模型值: "${modelValue}"。下拉框未改变。`);
        }
    }
    // +++ 结束新增 +++

    // --- UI 更新函数 ---
    function showLoading(isLoading, message = '正在生成图片...') { // Modified to accept a message
        if (isLoading) {
            if(startAIGenerationBtn) {
                // 只在按钮当前不是加载状态时（即第一次进入加载状态时）保存原始文本
                if (!startAIGenerationBtn.disabled) { // disabled 通常在进入加载时设置为 true
                    const currentBtnHTML = startAIGenerationBtn.innerHTML;
                    let textToSave = currentBtnHTML.replace(/<span class="spinner-border.*?<\/span>\s*/, '');
                    textToSave = textToSave.replace(/<i class="bi bi-stars.*?<\/i>\s*/, '');
                    textToSave = textToSave.replace(/<span class="badge.*?<\/span>/, '').trim();
                    startAIGenerationBtn.setAttribute('data-original-text', textToSave || '开始生成');
                }
                startAIGenerationBtn.disabled = true; // 确保设置为 disabled
                startAIGenerationBtn.innerHTML = `<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>${message}`;
            }
            if(aiGenerationLoading) {
                aiGenerationLoading.style.display = 'flex'; 
                const loadingTextElement = aiGenerationLoading.querySelector('p');
                if (loadingTextElement) loadingTextElement.textContent = message;
            }
            if(aiGenerationError) aiGenerationError.style.display = 'none';
        } else {
            if(startAIGenerationBtn) {
                startAIGenerationBtn.disabled = false;
                const originalButtonText = startAIGenerationBtn.getAttribute('data-original-text') || '开始生成';
                startAIGenerationBtn.innerHTML = `<i class="bi bi-stars me-2"></i> ${originalButtonText}`;
                // 只恢复原始内容，不插入 badge
                updateButtonWithCost(); // 统一由这个函数插入积分badge
            }
            if(aiGenerationLoading) aiGenerationLoading.style.display = 'none';
            // 在这里清除轮询是安全的，因为无论是成功还是失败，showLoading(false) 都会被调用
            if (pollingInterval) {
                 clearInterval(pollingInterval);
                 pollingInterval = null;
            }
        }
    }

    function showError(message) {
        console.error('[AI Generate] showError called with message:', message);
        // 恢复按钮状态
        showLoading(false);
        
        // 检查元素是否存在
        if (aiGenerationError) {
            aiGenerationError.textContent = message;
            aiGenerationError.style.display = 'block';
        } else {
            console.error('[AI Generate] showError: aiGenerationError element not found in the DOM.');
            // 可以在这里添加一个全局的错误提示，或者只是在控制台记录
            alert(`AI 生成出错: ${message} (无法找到页面上的错误提示区域)`); 
        }
        // 同样检查 loading 元素
        if (aiGenerationLoading) {
             aiGenerationLoading.style.display = 'none'; 
        } else {
            console.error('[AI Generate] showError: aiGenerationLoading element not found in the DOM.');
        }
    }

    function displayResult(imageUrls, prompt, model, width, height, existingHistoryId = null) {
        const generationTime = new Date();
        showLoading(false);
        aiGenerationError.style.display = 'none';
        aiGenerationLoading.style.display = 'none';

        // --- 成功获取结果后，尝试更新积分显示 ---
        if (typeof window.fetchCredits === 'function') {
            window.fetchCredits();
        } else {
            console.warn('[AI Generate] window.fetchCredits function not found.');
        }
        // --- 结束：更新积分显示 ---

        if (imageUrls && imageUrls.length > 0) {
            // --- 创建新的历史记录项元素 (与 displayHistory 逻辑同步) ---
            const historyItemDiv = document.createElement('div');
            historyItemDiv.className = 'history-item';
            historyItemDiv.classList.add('newly-generated-item'); // 应用新的动画类

            // 1. Header
            const headerDiv = document.createElement('div');
            headerDiv.className = 'history-item-header';
            const titleSpan = document.createElement('span');
            titleSpan.className = 'history-item-title';
            
            // +++ 修改：使用友好的模型显示名称 +++
            let modelDisplayName = getModelDisplayName(model);
            // +++ 结束修改 +++
            
            titleSpan.innerHTML = `<i class="bi bi-stars"></i> ${modelDisplayName}-图片生成`;
            const dateSpan = document.createElement('span');
            dateSpan.className = 'history-item-date';
            dateSpan.textContent = generationTime.toLocaleString('zh-CN', { month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', hour12: false }); // 格式化日期
            headerDiv.appendChild(titleSpan);
            headerDiv.appendChild(dateSpan);

            // 2. Prompt + Badges
            const promptP = document.createElement('p');
            promptP.className = 'history-prompt';
            promptP.textContent = prompt || '无提示词'; // Set text content first
            
            // +++ 修改：显示友好的模型名称Badge +++
            // Add badges
            if (model) {
                const modelBadge = document.createElement('span');
                modelBadge.className = 'badge text-bg-secondary'; // Use bootstrap badge class
                // 根据模型值显示友好的名称
                let displayModelName = model;
                if (model === 'comfyui_imagefx_advanced') {
                    displayModelName = '高级生图';
                } else if (model === '3.0' || model === '2.1') {
                    displayModelName = '超级生图';
                } else if (model === 'midjourney') {
                    displayModelName = 'Midjourney';
                }
                modelBadge.textContent = `图片 ${escapeHTML(displayModelName)}`;
                promptP.appendChild(document.createTextNode(' ')); // Add space before badge
                promptP.appendChild(modelBadge);
            }
            // +++ 结束修改 +++
            
            if (width && height) {
                const sizeBadge = document.createElement('span');
                sizeBadge.className = 'badge text-bg-secondary';
                // --- 修改：使用输入框的值 ---
                const displayWidth = escapeHTML(width);
                const displayHeight = escapeHTML(height);
                sizeBadge.textContent = `${displayWidth}x${displayHeight}`;
                // --- 结束修改 ---
                promptP.appendChild(document.createTextNode(' '));
                promptP.appendChild(sizeBadge);
            }
            // You could add more badges here (like aspect ratio if calculated differently)

            // 3. Image Grid
            const imagesDiv = document.createElement('div');
            imagesDiv.className = 'history-images';
            imageUrls.slice(0, 4).forEach(url => {
                const wrapperDiv = document.createElement('div');
                wrapperDiv.className = 'ai-history-image-wrapper';

                const img = document.createElement('img');
                img.src = url;
                img.alt = '生成的图片';
                img.loading = 'lazy';
                img.className = 'ai-history-image';
                img.title = '点击查看详情';

                const actionsDivHover = document.createElement('div');
                actionsDivHover.className = 'ai-history-actions';

                // 按钮1: 清晰放大 (示例)
                const upscaleButton = document.createElement('button');
                upscaleButton.className = 'btn btn-sm btn-action';
                upscaleButton.innerHTML = '<i class="bi bi-arrows-angle-expand"></i>';
                upscaleButton.title = '发送到清晰放大';
                upscaleButton.dataset.action = 'sendToUpscale';
                upscaleButton.dataset.imageUrl = url;
                upscaleButton.dataset.targetPane = 'creative-upscale-pane';
                upscaleButton.dataset.targetTab = 'creative-upscale-tab';
                actionsDivHover.appendChild(upscaleButton);

                // 按钮2: 背景移除 (示例)
                const removeBgButton = document.createElement('button');
                removeBgButton.className = 'btn btn-sm btn-action';
                removeBgButton.innerHTML = '<i class="bi bi-eraser-fill"></i>';
                removeBgButton.title = '发送到背景移除';
                removeBgButton.dataset.action = 'sendToRemoveBackground';
                removeBgButton.dataset.imageUrl = url;
                removeBgButton.dataset.targetPane = 'remove-background-pane';
                removeBgButton.dataset.targetTab = 'remove-background-tab';
                actionsDivHover.appendChild(removeBgButton);

                 // 按钮3: 位图转 SVG (示例)
                const vectorizeButton = document.createElement('button');
                vectorizeButton.className = 'btn btn-sm btn-action';
                vectorizeButton.innerHTML = '<i class="bi bi-bounding-box-circles"></i>';
                vectorizeButton.title = '发送到位图转 SVG';
                vectorizeButton.dataset.action = 'sendToVectorize';
                vectorizeButton.dataset.imageUrl = url;
                vectorizeButton.dataset.targetPane = 'vectorize-pane';
                vectorizeButton.dataset.targetTab = 'vectorize-tab';
                actionsDivHover.appendChild(vectorizeButton);

                // 新增：按钮4 转3D
                const to3dButton = document.createElement('button');
                to3dButton.className = 'btn btn-sm btn-action';
                to3dButton.innerHTML = '<i class="bi bi-box"></i>';
                to3dButton.title = '发送到转3D';
                to3dButton.dataset.action = 'sendTo3d';
                to3dButton.dataset.imageUrl = url;
                to3dButton.dataset.targetPane = 'image-to-3d-pane';
                to3dButton.dataset.targetTab = 'image-to-3d-tab';
                actionsDivHover.appendChild(to3dButton);

                // 原始图片点击事件 (保持查看详情功能)
                img.addEventListener('click', () => {
                    const imageData = {
                        imageUrl: url,
                        prompt: prompt,
                        model: model,
                        width: width,
                        height: height,
                        createdAt: generationTime
                    };
                    openImageDetailModal(imageData);
                });

                // 将图片和动作条添加到包装器
                wrapperDiv.appendChild(img);
                wrapperDiv.appendChild(actionsDivHover);

                // 将包装器添加到图片网格
                imagesDiv.appendChild(wrapperDiv);
            });

            // 4. Actions
            const actionsDiv = document.createElement('div');
            actionsDiv.className = 'history-actions'; // Use new class name
            const reuseButton = document.createElement('button');
            reuseButton.className = 'btn btn-sm btn-outline-secondary me-2';
            reuseButton.innerHTML = '<i class="bi bi-clipboard"></i> 使用提示词';
            reuseButton.title = '将此提示词填入上方输入框';
            reuseButton.addEventListener('click', () => {
                aiPromptInput.value = prompt;
                setModelSelection(model);
                if(width) aiImageWidthInput.value = width;
                if(height) aiImageHeightInput.value = height;
                updateRatioSelectionFromInputs(); // 根据填入的宽高更新比例按钮
                aiPromptInput.focus();
            });
            actionsDiv.appendChild(reuseButton);
            // Add more actions here if needed

            // Assemble the item
            historyItemDiv.appendChild(headerDiv);
            historyItemDiv.appendChild(promptP);
            historyItemDiv.appendChild(imagesDiv);
            historyItemDiv.appendChild(actionsDiv);
            // --- 结束：创建元素 ---

            const placeholder = document.getElementById('historyPlaceholder');
            if (placeholder) placeholder.style.display = 'none';

            aiHistoryListContainer.prepend(historyItemDiv);
            aiHistoryListContainer.style.display = 'block';

            saveGenerationToHistory(prompt, imageUrls, model, width, height, generationTime, existingHistoryId); // Pass existingHistoryId
            
        } else {
            // 结果无效时，也需要确保历史列表可见，并可能显示占位符
            aiHistoryListContainer.style.display = 'block';
            if (aiHistoryListContainer.children.length === 1 && historyPlaceholder) {
                historyPlaceholder.style.display = 'block'; // 如果列表为空（只有placeholder），显示它
            }
             showError('生成任务完成，但未返回有效图片。');
        }
    }

    // --- 保存历史记录到后端的函数 --- 
    async function saveGenerationToHistory(prompt, imageUrls, model, width, height, timestamp, existingHistoryId = null) { // Added existingHistoryId
        // If this save operation is for a ComfyUI task that already has a history ID from the initial submission,
        // we assume the backend is responsible for updating that record. So, frontend should not create a new one.
        const isComfyUIPolledTask = existingHistoryId && 
                                    (model === 'comfyui_imagefx_advanced' || model === 'sdxl_comfy' || model === 'sd3_comfy');

        if (isComfyUIPolledTask) {
            // Optionally trigger a history reload here to ensure the UI reflects the backend update if needed.
            // loadHistory(currentHistoryPage, false); // Consider if this is necessary or if the backend update is reflected on next load.
            return;
        }

        const historyData = {
            prompt,
            imageUrls,
            model,
            width,
            height,
            generationTime: timestamp.getTime() // 发送毫秒时间戳
        };

        try {
            const response = await fetch(`${API_BASE_URL}/api/ai/history`, { // <<< 修改了 URL
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(historyData)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error(`保存历史记录失败 (${response.status}):`, errorData.error || response.statusText);
                // 可选：给用户一个非阻塞的提示
                 // showToast('无法保存到历史记录', 'warning'); 
            } else {
                // --- REMOVED: 不再需要调用 loadHistory(1) --- 
                // loadHistory(1); // 保存成功后不应重新加载整个历史，新项已预置
            }
        } catch (error) {
            console.error('调用保存历史记录接口失败:', error);
             // showToast('无法连接服务器保存历史记录', 'warning');
        }
    }
    
    // --- 修改：加载历史记录函数 (支持懒加载) ---
    async function loadHistory(page = 1, append = false) {
        if (isLoadingHistory) {
            return;
        }
        isLoadingHistory = true;
        
        const placeholder = document.getElementById('historyPlaceholder'); // 重新获取，因为它可能已被移除

        // 初始加载或刷新时显示提示
        if (page === 1 && !append) {
            if (placeholder) placeholder.style.display = 'none'; // 隐藏占位符，准备显示加载提示
            aiHistoryListContainer.innerHTML = '<p class="text-muted text-center py-3"><span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>正在加载历史记录...</p>';
        } else if (append) {
            // 显示加载更多指示器（如果需要）
            showLoadMoreIndicator(true);
        }
        
        currentHistoryPage = page;

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_BASE_URL}/api/ai/history?page=${page}&limit=5`, { // <<< 修改了 URL
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            const data = await response.json();
            
            if (page === 1 && !append) { // 确保在显示前清空（如果不是追加模式）
                aiHistoryListContainer.innerHTML = ''; 
            }
            
            displayHistory(data.history, data.pagination, append);
            totalHistoryPages = data.pagination.totalPages || 1; // 更新总页数

        } catch (error) {
            console.error('加载历史记录失败:', error);
            if (page === 1 && !append) {
                aiHistoryListContainer.innerHTML = `<p class="text-danger text-center py-3">无法加载历史记录: ${error.message}</p>`;
            } else if (append) {
                // 可以在列表底部显示一个错误提示
                const errorP = document.createElement('p');
                errorP.className = 'text-danger text-center small py-2';
                errorP.textContent = '加载更多失败。';
                aiHistoryListContainer.appendChild(errorP);
            }
        } finally {
            isLoadingHistory = false;
            if (append) {
                showLoadMoreIndicator(false); // 隐藏加载更多指示器
            }
            // 检查是否应该显示初始占位符
            checkAndShowHistoryPlaceholder();
        }
    }

    // --- 修改：显示历史记录函数 (支持追加) ---
    function displayHistory(history, pagination, append = false) {
        const placeholder = document.getElementById('historyPlaceholder'); // 再次获取，以防万一
        if (placeholder && !append && (history && history.length > 0)) { // 只有在非追加且有数据时才移除
            placeholder.remove();
        }

        if (!append) { // 如果不是追加模式，并且是第一页加载
            // aiHistoryListContainer.innerHTML = ''; // 已在 loadHistory 中处理
            if (!history || history.length === 0) {
                // checkAndShowHistoryPlaceholder() 会处理这个问题
                // aiHistoryListContainer.innerHTML = '<p class="text-muted text-center py-3" id="historyPlaceholder">还没有生成历史记录。</p>';
            }
        } else {
            // 如果是追加模式，移除可能存在的"加载更多失败"提示
            const existingErrorP = aiHistoryListContainer.querySelector('p.text-danger.text-center.small');
            if (existingErrorP) existingErrorP.remove();
        }


        if (!history || history.length === 0) {
            if (!append && currentHistoryPage === 1 && !aiHistoryListContainer.hasChildNodes()) {
                // 只有在第一页且列表为空时，才显示"没有历史记录"
                // 这部分逻辑由 checkAndShowHistoryPlaceholder 统一处理
            }
            // 对于懒加载，不再需要显式设置分页容器为空
            // aiHistoryPaginationContainer.innerHTML = ''; 
            checkAndShowHistoryPlaceholder();
            return;
        }

        history.forEach(item => {
            const historyItemDiv = document.createElement('div');
            historyItemDiv.className = 'history-item';
            historyItemDiv.dataset.id = item.id; // 添加记录ID作为数据属性
            if (item.mj_task_id) { // 如果是MJ任务，也存储MJ任务ID
                historyItemDiv.dataset.mjTaskId = item.mj_task_id;
            }

            // 1. Header (Copied and adapted from displayResult)
            const headerDiv = document.createElement('div');
            headerDiv.className = 'history-item-header';
            const titleSpan = document.createElement('span');
            titleSpan.className = 'history-item-title';
            
            // +++ 使用 item.display_model 进行显示 +++
            const modelDisplayName = getModelDisplayName(item.model);
            // +++ 结束修改 +++
            
            // +++ 修改：根据任务状态设置不同的图标 +++
            let statusIcon = '<i class="bi bi-check-circle"></i>';
            if (item.status === 'PROCESSING' || item.status === 'PENDING' || item.status === 'IN_PROGRESS') { // 增加了 PENDING 和 IN_PROGRESS
                statusIcon = '<i class="bi bi-hourglass-split text-warning"></i>';
            } else if (item.status === 'FAILED') {
                statusIcon = '<i class="bi bi-exclamation-circle text-danger"></i>';
            }
            titleSpan.innerHTML = `${statusIcon} ${modelDisplayName}-图片生成`;
            // +++ 结束修改 +++
            
            const dateSpan = document.createElement('span');
            dateSpan.className = 'history-item-date';
            // Format date from item.created_at
            const itemDate = new Date(item.created_at);
            dateSpan.textContent = itemDate.toLocaleString('zh-CN', { month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', hour12: false });
            headerDiv.appendChild(titleSpan);
            headerDiv.appendChild(dateSpan);

            // 2. Prompt + Badges (Copied and adapted from displayResult)
            const promptP = document.createElement('p');
            promptP.className = 'history-prompt';
            promptP.textContent = item.prompt || '无提示词';
            
            // +++ 使用 item.display_model 进行显示 +++
            if (item.model) { // 这里仍然可以用 item.model 来判断是否有模型信息，但显示用 display_model
                const modelBadge = document.createElement('span');
                modelBadge.className = 'badge text-bg-secondary';
                modelBadge.textContent = `图片 ${escapeHTML(modelDisplayName)}`; // 使用处理过的 modelDisplayName
                promptP.appendChild(document.createTextNode(' '));
                promptP.appendChild(modelBadge);
            }
            // +++ 结束修改 +++
            
            if (item.width && item.height) {
                const sizeBadge = document.createElement('span');
                sizeBadge.className = 'badge text-bg-secondary';
                const displayWidth = escapeHTML(item.width);
                const displayHeight = escapeHTML(item.height);
                sizeBadge.textContent = `${displayWidth}x${displayHeight}`;
                promptP.appendChild(document.createTextNode(' '));
                promptP.appendChild(sizeBadge);
            }
            
            // +++ 修改：显示任务状态Badge +++
            const statusBadge = document.createElement('span');
            if (item.status === 'PROCESSING' || item.status === 'PENDING' || item.status === 'IN_PROGRESS') { // 增加了 PENDING 和 IN_PROGRESS
                statusBadge.className = 'badge bg-warning text-dark';
                statusBadge.textContent = '处理中';
            } else if (item.status === 'FAILED') {
                statusBadge.className = 'badge bg-danger';
                statusBadge.textContent = '失败';
            } else { // SUCCESS 或其他已完成状态
                statusBadge.className = 'badge bg-success';
                statusBadge.textContent = '已完成';
            }
            promptP.appendChild(document.createTextNode(' '));
            promptP.appendChild(statusBadge);
            // +++ 结束修改 +++
            
            // Display generation time if available
            if (item.generation_time_ms !== null && item.generation_time_ms !== undefined) {
                const timeBadge = document.createElement('span');
                timeBadge.className = 'badge text-bg-secondary';
                timeBadge.textContent = `≈${(item.generation_time_ms / 1000).toFixed(1)}s`;
                promptP.appendChild(document.createTextNode(' '));
                promptP.appendChild(timeBadge);
            }

            // 3. Image Grid (Copied and adapted from displayResult)
            const imagesDiv = document.createElement('div');
            imagesDiv.className = 'history-images';
            
            // +++ 修改：根据不同状态显示图片或加载提示 +++
            // 特别处理 Midjourney 的 PENDING/IN_PROGRESS 状态
            if (item.display_model === 'Midjourney' &&
                (item.status === 'PENDING' || item.status === 'IN_PROGRESS' || item.status === 'PROCESSING') &&
                (!item.image_urls || item.image_urls.length === 0) && 
                item.mj_task_id // 确保有 mj_task_id 才显示刷新按钮
            ) {
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'history-loading-indicator';
                // 只显示加载动画和提示文字，按钮会统一添加到 actionsDivBelow
                loadingDiv.innerHTML = `
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">Midjourney任务正在处理中，请耐心等候...</p>
                    <p class="text-muted small">您可以关闭页面，稍后再来查看结果</p>
                `;
                imagesDiv.appendChild(loadingDiv);
            } else if (item.status === 'PROCESSING' || item.status === 'PENDING' || item.status === 'IN_PROGRESS') { // 其他模型的处理中状态
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'history-loading-indicator';
                
                const loadingHtml = (item.model === 'comfyui_imagefx_advanced' || item.display_model === '高级生图') ? 
                    `
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">任务正在排队等待处理，请耐心等候...</p>
                    <p class="text-muted small">您可以关闭页面，稍后再来查看结果</p>
                    ` : 
                    `
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">任务正在处理中，请稍候...</p>
                    <p class="text-muted small">您可以关闭页面，稍后再来查看结果</p>
                    `;
                
                loadingDiv.innerHTML = loadingHtml;
                imagesDiv.appendChild(loadingDiv);
            } else if (item.status === 'FAILED') {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'history-error-indicator';
                // +++ 新增：从 item 对象获取错误信息 +++
                const errorMessage = item.fail_reason || item.error_message || item.error || '未知错误';
                // +++ 结束新增 +++
                errorDiv.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        生成失败: ${errorMessage ? '(' + escapeHTML(errorMessage) + ')' : ''}
                    </div>
                `;
                imagesDiv.appendChild(errorDiv);
            } else if (item.image_urls && item.image_urls.length > 0) {
                // 成功状态显示图片
                item.image_urls.slice(0, 4).forEach(url => {
                    const wrapperDiv = document.createElement('div');
                    wrapperDiv.className = 'ai-history-image-wrapper';

                    const img = document.createElement('img');
                    img.src = url;
                    img.alt = '历史图片预览';
                    img.loading = 'lazy';
                    img.className = 'ai-history-image';
                    img.title = '点击查看详情';

                    const actionsDivHover = document.createElement('div');
                    actionsDivHover.className = 'ai-history-actions';

                    // 按钮1: 清晰放大 (示例)
                    const upscaleButton = document.createElement('button');
                    upscaleButton.className = 'btn btn-sm btn-action';
                    upscaleButton.innerHTML = '<i class="bi bi-arrows-angle-expand"></i>';
                    upscaleButton.title = '发送到清晰放大';
                    upscaleButton.dataset.action = 'sendToUpscale';
                    upscaleButton.dataset.imageUrl = url;
                    upscaleButton.dataset.targetPane = 'creative-upscale-pane';
                    upscaleButton.dataset.targetTab = 'creative-upscale-tab';
                    actionsDivHover.appendChild(upscaleButton);

                    // 按钮2: 背景移除 (示例)
                    const removeBgButton = document.createElement('button');
                    removeBgButton.className = 'btn btn-sm btn-action';
                    removeBgButton.innerHTML = '<i class="bi bi-eraser-fill"></i>';
                    removeBgButton.title = '发送到背景移除';
                    removeBgButton.dataset.action = 'sendToRemoveBackground';
                    removeBgButton.dataset.imageUrl = url;
                    removeBgButton.dataset.targetPane = 'remove-background-pane';
                    removeBgButton.dataset.targetTab = 'remove-background-tab';
                    actionsDivHover.appendChild(removeBgButton);

                        // 按钮3: 位图转 SVG (示例)
                    const vectorizeButton = document.createElement('button');
                    vectorizeButton.className = 'btn btn-sm btn-action';
                    vectorizeButton.innerHTML = '<i class="bi bi-bounding-box-circles"></i>';
                    vectorizeButton.title = '发送到位图转 SVG';
                    vectorizeButton.dataset.action = 'sendToVectorize';
                    vectorizeButton.dataset.imageUrl = url;
                    vectorizeButton.dataset.targetPane = 'vectorize-pane';
                    vectorizeButton.dataset.targetTab = 'vectorize-tab';
                    actionsDivHover.appendChild(vectorizeButton);

                    // 新增：按钮4 转3D
                    const to3dButton = document.createElement('button');
                    to3dButton.className = 'btn btn-sm btn-action';
                    to3dButton.innerHTML = '<i class="bi bi-box"></i>';
                    to3dButton.title = '发送到转3D';
                    to3dButton.dataset.action = 'sendTo3d';
                    to3dButton.dataset.imageUrl = url;
                    to3dButton.dataset.targetPane = 'image-to-3d-pane';
                    to3dButton.dataset.targetTab = 'image-to-3d-tab';
                    actionsDivHover.appendChild(to3dButton);

                    // 原始图片点击事件 (保持查看详情功能)
                    img.addEventListener('click', () => {
                        const imageData = {
                            imageUrl: url,
                            prompt: item.prompt,
                            model: item.model, // 传原始 model 值
                            display_model: modelDisplayName, // 传显示用的模型名
                            width: item.width,
                            height: item.height,
                            createdAt: item.created_at
                        };
                        openImageDetailModal(imageData);
                    });

                    // 将图片和动作条添加到包装器
                    wrapperDiv.appendChild(img);
                    wrapperDiv.appendChild(actionsDivHover);

                    // 将包装器添加到图片网格
                    imagesDiv.appendChild(wrapperDiv);
                });
            } else { // 其他情况（例如，已完成但无图）
                imagesDiv.innerHTML = '<span class="text-muted small">无可用图片</span>';
            }
            // +++ 结束修改 +++

            // 4. Actions (Copied and adapted from displayResult - these are below the image grid)
            const actionsDivBelow = document.createElement('div'); 
            actionsDivBelow.className = 'history-item-actions'; // 添加类名
            const reuseButton = document.createElement('button');
            reuseButton.className = 'btn btn-sm btn-outline-secondary me-2';
            reuseButton.innerHTML = '<i class="bi bi-clipboard"></i> 使用提示词';
            reuseButton.title = '将此提示词填入上方输入框';
            reuseButton.addEventListener('click', () => {
                aiPromptInput.value = item.prompt;
                setModelSelection(item.model);
                if(item.width) aiImageWidthInput.value = item.width;
                if(item.height) aiImageHeightInput.value = item.height;
                updateRatioSelectionFromInputs(); 
                aiPromptInput.focus();
            });
            actionsDivBelow.appendChild(reuseButton);

            // "刷新状态"按钮 (如果适用)
            if ((item.display_model === 'Midjourney' && item.mj_task_id && (item.status === 'PENDING' || item.status === 'IN_PROGRESS' || item.status === 'PROCESSING')) || 
                (item.display_model !== 'Midjourney' && (item.status === 'PROCESSING' || item.status === 'PENDING' || item.status === 'IN_PROGRESS'))) {
                const refreshButton = document.createElement('button');
                refreshButton.className = 'btn btn-sm btn-outline-primary me-2'; 
                refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新状态';
                refreshButton.title = '手动刷新此任务的状态';
                if (item.display_model === 'Midjourney') {
                    refreshButton.classList.add('refresh-mj-status-btn'); 
                    refreshButton.dataset.mjTaskId = item.mj_task_id;
                    refreshButton.dataset.historyId = item.id; 
                } else {
                    refreshButton.addEventListener('click', () => {
                        loadHistory(currentHistoryPage, false);
                    });
                }
                actionsDivBelow.appendChild(refreshButton);
            }

            // "删除记录"按钮 - 移到MJ按钮组之前
            const deleteButton = document.createElement('button');
            // 如果后面没有MJ按钮组，它就是最后一个，不需要me-2。如果后面有MJ按钮组，则需要me-2。
            // 这个判断需要在MJ按钮组的条件之后进行，或者先添加，然后按需修改class。
            // 为简单起见，我们先假设它后面可能有MJ按钮，所以加上 me-2，如果它最终是最后一个，这个 me-2 不会有负面影响。
            deleteButton.className = 'btn btn-sm btn-outline-danger me-2'; 
            deleteButton.innerHTML = '<i class="bi bi-trash"></i> 删除记录';
            deleteButton.title = '删除此历史记录';
            deleteButton.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                showDeleteConfirmDialog(item.id, historyItemDiv);
            });
            actionsDivBelow.appendChild(deleteButton);

            // Midjourney 操作按钮组 (如果适用)
            if (item.display_model === 'Midjourney' && item.mj_buttons && Array.isArray(item.mj_buttons) && item.mj_buttons.length > 0) {
                const mjButtonsGroup = document.createElement('div');
                mjButtonsGroup.className = 'btn-group btn-group-sm d-flex flex-wrap'; // 允许按钮组折行
                mjButtonsGroup.setAttribute('role', 'group');
                mjButtonsGroup.setAttribute('aria-label', 'Midjourney Actions');
                mjButtonsGroup.style.marginTop = '12px'; 

                item.mj_buttons.forEach(mjButton => {
                    if (mjButton.label || mjButton.emoji) { 
                        const buttonEl = document.createElement('button');
                        buttonEl.type = 'button';
                        buttonEl.className = 'btn btn-sm btn-outline-secondary me-1 mb-1'; // 为每个按钮添加边距以便换行时有间隙
                        buttonEl.textContent = mjButton.label || mjButton.emoji;
                        buttonEl.title = mjButton.label || mjButton.customId; 
                        buttonEl.dataset.customId = mjButton.customId;
                        buttonEl.dataset.taskId = item.mj_task_id; 
                        buttonEl.addEventListener('click', (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            if (typeof handleMjActionButtonClick === 'function') {
                                handleMjActionButtonClick(mjButton.customId, item.mj_task_id, item.id, mjButton.label || mjButton.emoji);
                            } else {
                                alert('Midjourney 操作功能暂未实现。');
                            }
                        });
                        mjButtonsGroup.appendChild(buttonEl);
                    }
                });
                actionsDivBelow.appendChild(mjButtonsGroup);
                
                // 由于MJ按钮组现在是最后一个（如果存在），删除按钮就不应该有 me-2
                // 如果MJ按钮组被添加了，那么移除删除按钮的 me-2（如果它有的话）
                if (deleteButton.classList.contains('me-2')) {
                    deleteButton.classList.remove('me-2');
                }
            } else {
                // 如果没有MJ按钮组，删除按钮就是最后一个，它之前设置的 me-2 需要移除
                 if (deleteButton.classList.contains('me-2')) {
                    deleteButton.classList.remove('me-2');
                }
            }

            // Assemble the item
            historyItemDiv.appendChild(headerDiv);
            historyItemDiv.appendChild(promptP);
            historyItemDiv.appendChild(imagesDiv);
            historyItemDiv.appendChild(actionsDivBelow);
            
            aiHistoryListContainer.appendChild(historyItemDiv);
        });

        // createPagination(pagination); // 移除分页按钮创建
        checkAndShowHistoryPlaceholder(); // 每次更新后检查是否需要显示占位符

        // 为新添加的 Midjourney 刷新按钮绑定事件
        bindMjRefreshButtons();
    }

    // --- 新增：为MJ刷新按钮绑定事件的独立函数 ---
    function bindMjRefreshButtons() {
        aiHistoryListContainer.querySelectorAll('.refresh-mj-status-btn').forEach(btn => {
            if (btn.dataset.listenerAttached === 'true') return;
            btn.dataset.listenerAttached = 'true';
            btn.addEventListener('click', async function() {
                const mjTaskId = this.getAttribute('data-mj-task-id');
                this.disabled = true;
                this.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>刷新中...';
                try {
                    await fetch(`${API_BASE_URL}/api/mj/task/${mjTaskId}/fetch`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('token')}`
                        }
                    });
                } catch (e) {
                    // 可以加错误提示
                    console.error('刷新MJ任务状态失败:', e);
                } finally {
                    // 无论成功失败都刷新历史记录，并恢复按钮
                    loadHistory(currentHistoryPage, false);
                    this.disabled = false;
                    this.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新状态';
                }
            });
        });
    }

    // --- 新增：检查并显示历史记录占位符 ---
    function checkAndShowHistoryPlaceholder() {
        const placeholder = document.getElementById('historyPlaceholder');
        if (aiHistoryListContainer.children.length === 0) {
            if (placeholder) {
                placeholder.style.display = 'block';
            } else {
                // 如果占位符不存在（可能已被移除），重新创建并添加
                const newPlaceholder = document.createElement('p');
                newPlaceholder.className = 'text-muted text-center py-3';
                newPlaceholder.id = 'historyPlaceholder';
                newPlaceholder.textContent = '还没有生成历史记录。';
                // 确保它被添加到正确的位置，例如，如果列表容器是父级
                const parent = aiHistoryListContainer.parentElement; // 假设card-body是父级
                if (parent) {
                     // 插入到aiHistoryListContainer之前或之后，取决于布局
                     // 这里我们假设如果列表为空，可以直接在列表内显示
                    aiHistoryListContainer.appendChild(newPlaceholder);
                }
            }
            aiHistoryListContainer.style.display = 'block'; // 确保列表容器可见以显示占位符
        } else {
            if (placeholder) {
                placeholder.style.display = 'none';
            }
             aiHistoryListContainer.style.display = 'block'; // 确保列表容器可见
        }
    }

    // --- 新增：显示/隐藏加载更多指示器 ---
    function showLoadMoreIndicator(show) {
        let indicator = document.getElementById('historyLoadMoreIndicator');
        if (show) {
            if (!indicator) {
                indicator = document.createElement('div');
                indicator.id = 'historyLoadMoreIndicator';
                indicator.className = 'text-center py-3 text-muted';
                indicator.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>正在加载更多...';
                // 将加载指示器添加到历史记录列表末尾，而不是卡片底部
                aiHistoryListContainer.appendChild(indicator);
            }
            indicator.style.display = 'block';
        } else {
            if (indicator) {
                indicator.style.display = 'none';
            }
        }
    }
    
    // --- 新增：滚动加载事件监听器 ---
    // 修改：选择更合适的滚动容器，历史记录卡片的 card-body
    const scrollableHistoryContainer = document.querySelector('#ai-generate-tab-pane .col-md-7 .card-body');

    if (scrollableHistoryContainer) {
        scrollableHistoryContainer.addEventListener('scroll', () => {
            // 检查滚动条是否接近底部
            const threshold = 100; // 减小阈值，适应更小的容器
            if (!isLoadingHistory && currentHistoryPage < totalHistoryPages &&
                (scrollableHistoryContainer.scrollTop + scrollableHistoryContainer.clientHeight >= scrollableHistoryContainer.scrollHeight - threshold)) {
                loadHistory(currentHistoryPage + 1, true); // 加载下一页并追加
            }
        });
    } else {
        console.warn('AI 生成历史记录的滚动容器未找到，懒加载可能无法正常工作。');
    }

    // --- API 调用函数 ---
    async function startGeneration(prompt, model, width, height, aspect_ratio_string, num_images) { // Added num_images to signature
        // 检查提示词长度，确保不超过800字符
        if (prompt && prompt.length > 800) {
            console.warn(`[AI Generate] Prompt exceeds 800 characters limit in startGeneration. Trimming from ${prompt.length} to 800 characters.`);
            prompt = prompt.substring(0, 800);
        }
        
        
        // Store the model used for this specific generation task
        const modelForThisGeneration = model; 

        let loadingMessage = '正在提交生成请求...';
        if (model === 'comfyui_imagefx_advanced') {
            loadingMessage = '正在提交高级生图请求...';
        } else if (model === 'midjourney') {
            loadingMessage = '正在提交Midjourney请求...';
        }
        showLoading(true, loadingMessage);
        aiGenerationError.style.display = 'none';

        try {
            // 特殊处理 Midjourney 模型
            if (model === 'midjourney') {
                // 获取 Midjourney 特有参数
                const mjMode = document.getElementById('mjModeSelect')?.value || 'fast';
                
                // 构建请求体
                const requestBody = {
                    prompt: prompt, // prompt已在startGeneration开头检查并限制为800字符
                    mode: mjMode
                };
                
                // 如果有垫图，添加 base64 数据
                if (typeof mjImagineBase64 === 'string' && mjImagineBase64.startsWith('data:image/')) {
                    // 提取纯 base64 数据（去掉前缀）
                    const base64Data = mjImagineBase64.split(',')[1];
                    requestBody.base64Array = [base64Data]; // API期望数组格式
                }
                
                
                // 调用后端API
                const response = await fetch(`${API_BASE_URL}/api/mj/submit/imagine`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify(requestBody)
                });
                
                if (!response.ok) {
                    let errorData;
                    try {
                        errorData = await response.json();
                    } catch (e) {
                        errorData = { error: response.statusText, details: `服务器返回状态 ${response.status}` };
                    }
                    throw new Error(errorData.error || 'Midjourney请求失败', { cause: errorData.details });
                }
                
                const data = await response.json();
                
                // 从响应中获取任务ID和历史记录ID
                if (data.success === true && data.taskId) {
                    // 使用任务ID进行轮询
                    const mjTaskId = data.taskId;
                    const historyId = data.historyId || null; // 可能存在的历史记录ID
                    
                    // 显示进度并开始轮询
                    showLoading(true, 'Midjourney任务已提交，等待结果...');
                    startPollingMjResult(mjTaskId, prompt, model, mjMode, historyId);
                } else {
        console.error('[AI Generate] Midjourney响应缺少必要字段:', JSON.stringify(data));
        throw new Error(data.error || 'Midjourney返回无效结果，缺少taskId字段');
                }
                
                return; // 退出函数
            }
            
            // 原有逻辑（非Midjourney模型）
            const payload = {
                prompt,
                model
            };

            if (model === 'comfyui_imagefx_advanced') {
                if (aspect_ratio_string) {
                    payload.aspect_ratio_string = aspect_ratio_string;
                }
                // Correctly get and add num_images for comfyui_imagefx_advanced
                // const numImages = parseInt(comfyNumImagesInput.value, 10); // This is now passed as an argument
                if (num_images && num_images > 0) { // Use the passed num_images argument
                    payload.num_images = num_images;
                } else {
                    payload.num_images = 1; // Default to 1 if input is invalid or not provided
                }
            } else {
                if (width && height) {
                    payload.width = parseInt(width);
                    payload.height = parseInt(height);
                }
            }
            

            const response = await fetch(`${API_BASE_URL}/api/ai/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                let errorData;
                try {
                    errorData = await response.json();
                } catch (e) {
                    // If response is not JSON, use statusText
                    errorData = { error: response.statusText, details: `服务器返回状态 ${response.status}` };
                }
                console.error('[AI Generate] Generation request failed:', errorData);
                throw new Error(errorData.error || '生成请求失败', { cause: errorData.details });
            }

            const data = await response.json();

            // Path 1: Ideal response from OUR backend (task submitted, history created, got our history_id)
            if (data.success === true && data.aigc_data && data.aigc_data.history_record_id) {
                currentHistoryId = data.aigc_data.history_record_id;
                showLoading(true, '任务已提交，等待结果...');
                startPollingForResult(currentHistoryId, prompt, modelForThisGeneration, width, height); // Pass modelForThisGeneration
                } 
            // Path 2: Backend forwards Jimeng's response directly, AND Jimeng accepted the task (ret: '0')
            // This is likely the current scenario based on logs.
            else if (data.ret === '0' && typeof data.errmsg === 'string') { // Check errmsg too for structure
                // Problem: Backend did not provide its own history_record_id for polling.
                // This is a critical issue in the backend/frontend contract.
                console.error('[AI Generate] Jimeng task was submitted successfully (ret: \'0\'), but the backend did not return a local history_record_id for polling. This prevents status checking. Response:', data);
                throw new Error('AI任务已提交，但后端未能按预期处理并返回轮询ID。请联系管理员。');
                } 
            // Path 3: Backend forwards Jimeng's response, AND Jimeng reported an error (ret is non-'0')
            else if (typeof data.ret === 'string' && data.ret !== '0' && typeof data.errmsg === 'string') {
                console.error('[AI Generate] Jimeng API reported an error directly during submission. Response:', data);
                let userMessage = `AI服务错误: ${data.errmsg} (代码: ${data.ret})`;
                if (data.ret === '22113') { // Ensure string comparison for ret codes
                    userMessage = '提示词可能包含敏感内容，请修改后重试。';
                } else if (data.ret === '22107') {
                    userMessage = '当前系统繁忙（AI任务过多），请稍后再试。';
                }
                // Add more specific Jimeng error mappings here if needed
                throw new Error(userMessage);
            }
            // Path 4: Our backend explicitly reports an error (e.g., { success: false, error: "..." })
            else if (data.success === false && data.error) {
                console.error('[AI Generate] Backend explicitly reported an error:', data.error, data.details);
                throw new Error(data.error, { cause: data.details });
            }
            // Path 5: Completely unexpected response structure
            else {
                console.error('[AI Generate] Unexpected or unhandled response structure from backend. Response:', data);
                throw new Error('从后端收到无法处理的响应格式。请检查API响应或联系管理员。');
            }
        } catch (error) {
            console.error('[AI Generate] Error in startGeneration:', error);
            showError(`生成失败: ${error.message}${error.cause ? ' (' + error.cause + ')' : ''}`);
            // 如果有 currentHistoryId，表示任务已提交但后续出错，可能不需要清除
            // currentHistoryId = null; 
        }
    }

    // +++ 新增：获取 ComfyUI 队列状态的函数 +++
    async function fetchComfyUIQueueStatus() {
        // 这个 URL 假设您的后端会提供一个代理到 ComfyUI GET /prompt 端点的接口
        const queueStatusUrl = `${API_BASE_URL}/api/ai/comfy_queue_status`; 
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(queueStatusUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                console.error('获取 ComfyUI 队列状态失败:', response.status, await response.text());
                return null;
            }
            const data = await response.json();
            // 根据 ComfyUI 文档: { "exec_info": { "queue_remaining": N } }
            if (data && data.exec_info && typeof data.exec_info.queue_remaining !== 'undefined') {
                return data.exec_info.queue_remaining;
            }
            console.warn('ComfyUI 队列状态响应格式不符合预期:', data);
            return null;
        } catch (error) {
            console.error('请求 ComfyUI 队列状态时出错:', error);
            return null;
        }
    }
    // +++ 结束新增 +++

    async function queryResult(historyId) {
        try {
            
             const token = localStorage.getItem('token'); // 获取 token
             
             // 注意：这里的 URL 已更新为指向正确的后端代理地址
            const requestBody = { history_id: historyId };
            const requestUrl = `${API_BASE_URL}/api/ai/query`;
            

            
            const response = await fetch(requestUrl, { // <<< 修改了 URL
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    // 新增：添加认证头
                    'Authorization': `Bearer ${token}`                },
                body: JSON.stringify(requestBody)
            });


            if (!response.ok) {
                 const errorData = await response.json().catch(() => ({ error: `HTTP 错误: ${response.status}` }));
                 
                 // 查询接口的错误不直接停止轮询，但可以在控制台记录
                 console.error('查询结果失败:', errorData.error || `HTTP ${response.status}`);
                 return null; // 返回 null 表示查询失败或未完成
            }

            const data = await response.json();
            

            if (data.error) {
                console.error(`  错误信息: ${data.error}`);
            }

            // 检查 jimeng.js 中定义的 /api/query 响应结构
            if (data.success === true && data.imageUrls && data.imageUrls.length > 0) {
                // 成功获取到图片 URL
                return data.imageUrls;
            } else if (data.success === true && (!data.imageUrls || data.imageUrls.length === 0)){
                 // 任务可能仍在进行中，或者已完成但无结果 (例如被审核)
                 return null; // 继续轮询
            } else if (data.success === false) {
                 // 任务失败，返回特殊标识以停止轮询
                 console.error(`[前端查询] 任务失败: ${data.error}`);
                 return { error: data.error }; // 返回错误对象而不是null，让轮询逻辑知道应该停止
            } else {
                 // 其他未知状态
                 console.error(`[前端查询] 查询 History ID ${historyId} 返回未知状态:`, data);
                 return null; // 继续轮询，或者根据错误类型决定是否停止
            }

        } catch (error) {
            // +++ 新增：记录异常详情 +++
            console.error(`[前端查询] 查询异常详情:`);
            console.error(`  Error type: ${error.constructor.name}`);
            console.error(`  Error message: ${error.message}`);
            console.error(`  Error stack:`, error.stack);
            // +++ 结束新增 +++
            
            console.error(`轮询 History ID ${historyId} 时发生错误:`, error);
            // 网络错误等，继续轮询
            return null;
        }
    }

    async function startPollingForResult(historyId, originalPrompt, originalModel, originalWidth, originalHeight) { // Added original params
        if (pollingInterval) {
            clearInterval(pollingInterval);
        }

        let attempts = 0;
        const maxAttempts = 180; // 最多轮询 180 次 (约 15 分钟)
        const intervalTime = 5000; // 每 5 秒查询一次



        // 立即执行一次轮询内容，然后再设置interval
        const pollOnce = async () => {
            attempts++;
            


            const imageUrls = await queryResult(historyId);
            

            
            let queueRemaining = null;
            let currentModelForPolling = aiModelSelectValue ? aiModelSelectValue.value : null;



            if (currentModelForPolling === 'comfyui_imagefx_advanced') {
                queueRemaining = await fetchComfyUIQueueStatus();
            }

            if (imageUrls && imageUrls.error) {
                // 任务失败，停止轮询并显示错误
                console.error(`[前端轮询] ===== 任务失败，停止轮询 =====`);
                console.error(`  错误信息: ${imageUrls.error}`);
                console.error(`  总轮询次数: ${attempts}`);
                
                if (pollingInterval) clearInterval(pollingInterval);
                pollingInterval = null;
                
                if(aiHistoryListContainer) aiHistoryListContainer.style.display = 'block';
                checkAndShowHistoryPlaceholder(); 
                showError(imageUrls.error);
            } else if (imageUrls && Array.isArray(imageUrls)) {

                
                if (pollingInterval) clearInterval(pollingInterval);
                pollingInterval = null;
                
                // Use originalPrompt, originalModel, etc. instead of fetching from UI again
                // const currentPrompt = aiPromptInput.value.trim(); 
                // const currentModelForPolling = aiModelSelect ? aiModelSelect.value : null;
                // const currentWidth = aiImageWidthInput.value;
                // const currentHeight = aiImageHeightInput.value;
               
                if(aiHistoryListContainer) aiHistoryListContainer.style.display = 'block';
                if(historyPlaceholder) historyPlaceholder.style.display = 'none';

                displayResult(imageUrls, originalPrompt, originalModel, originalWidth, originalHeight, historyId); // Use originalModel
            } else if (attempts >= maxAttempts) {

                
                if (pollingInterval) clearInterval(pollingInterval);
                pollingInterval = null;
                
                if(aiHistoryListContainer) aiHistoryListContainer.style.display = 'block';
                checkAndShowHistoryPlaceholder(); 
                showError('生成超时，您可以稍后在历史记录中查看结果或重新提交任务。');
            } else {
                // 任务仍在进行中，更新加载消息
                let loadingMessage = `处理中 (尝试 ${attempts}/${maxAttempts})...`;
                
                
                // Use originalModel for checking if it's a ComfyUI task for loading message purposes
                if (originalModel === 'comfyui_imagefx_advanced') { 
                    if (queueRemaining !== null) {
                        // +++ 修改：更友好且更准确的队列等待提示 +++
                        if (queueRemaining > 0) {
                            loadingMessage = `前面还有 ${queueRemaining} 个任务在等待，请耐心等候 (尝试 ${attempts}/${maxAttempts})...`;
                        } else {
                            loadingMessage = `任务正在处理中，请稍候 (尝试 ${attempts}/${maxAttempts})...`;
                        }
                        // +++ 结束修改 +++
                    } else {
                        loadingMessage = `正在查询任务状态，请稍候 (尝试 ${attempts}/${maxAttempts})...`;
                    }
                }
                
                
                
                showLoading(true, loadingMessage);

                // 如果轮询器尚未设置或已被清除（例如在 pollOnce 完成后但在 clearInterval 前），则重新设置它
                if (!pollingInterval && attempts < maxAttempts) {
                    pollingInterval = setInterval(pollOnce, intervalTime);
                }
            }
            
            
        };

        // 首次调用
        pollOnce();
        // 设置后续的定时轮询 (如果第一次pollOnce没有立即完成或超时)
        // 注意：如果pollOnce是异步的，并且我们希望严格按照intervalTime轮询，
        // setInterval应该在pollOnce内部的else分支中重新设置，或者在外部确保只设置一次。
        // 上面的逻辑中，如果任务未完成且未超时，会重新启动interval。
        // 为确保只有一个interval，清除旧的（如果有），然后在需要继续轮询时设置新的。
        if (pollingInterval) clearInterval(pollingInterval); // 清除可能由之前调用遗留的
        if (attempts < maxAttempts) { // 只有在未达到最大尝试次数时才设置 interval
            pollingInterval = setInterval(pollOnce, intervalTime);
        }
    }

    // --- 事件监听器 ---
    // REMOVED: Event listener moved to the top for debugging
    // startAIGenerationBtn.addEventListener('click', () => { ... });

    // --- 监听 AI 图片生成 Tab 是否被激活 --- 
    const tabButton = document.getElementById('ai-generate-tab');
    if (tabButton) {
        tabButton.addEventListener('shown.bs.tab', event => {
            // 切换到此 Tab 时加载第一页历史记录
            // loadHistory(1); // 初始加载现在由 handleTabShown 处理
            handleTabShown(); // 调用新的处理函数
        });
        // 可选：首次加载时，如果此 Tab 默认激活，也加载历史记录
        if (tabButton.classList.contains('active')) {
            // loadHistory(1); // 初始加载现在由 handleTabShown 处理
             handleTabShown(); // 调用新的处理函数
        }
    }

    // 清理轮询的逻辑保持不变
    const tabPane = document.getElementById('ai-generate-tab-pane');
    const observer = new MutationObserver(mutations => {
         mutations.forEach(mutation => {
             if (mutation.attributeName === 'class') {
                 const isActive = tabPane.classList.contains('active');
                 if (!isActive && pollingInterval) {
                     clearInterval(pollingInterval);
                     pollingInterval = null;
                 }
             }
         });
     });
     if (tabPane) {
          observer.observe(tabPane, { attributes: true });
     } else {
         console.warn('无法找到 AI Generate Tab Pane 元素');
     }

    // --- 新增：打开图片详情模态框的函数 ---
    function openImageDetailModal(imageData) {
        const modal = new bootstrap.Modal(document.getElementById('imageDetailModal'));
        const imageView = document.getElementById('modalImageView');
        const promptDisplay = document.getElementById('modalPromptDisplay');
        const modelDisplay = document.getElementById('modalModelDisplay');
        const sizeDisplay = document.getElementById('modalSizeDisplay');
        const dateDisplay = document.getElementById('modalDateDisplay');

        // Hidden inputs for data storage
        const dataPrompt = document.getElementById('modalDataPrompt');
        const dataModel = document.getElementById('modalDataModel'); // 这个存储原始 model value
        const dataWidth = document.getElementById('modalDataWidth');
        const dataHeight = document.getElementById('modalDataHeight');
        const dataImageUrl = document.getElementById('modalDataImageUrl');
        const dataCreatedAt = document.getElementById('modalDataCreatedAt');
        
        // +++ 使用 imageData.display_model (如果有) 或 imageData.model 进行显示 +++
        let modelValueToShow = imageData.display_model || imageData.model || '-';
        // +++ 结束修改 +++
        
        if (modelDisplay) modelDisplay.textContent = modelValueToShow;
        else console.error('[Modal Error] modelDisplay element not found!');

        const sizeValue = (imageData.width && imageData.height) ? `${imageData.width} x ${imageData.height}` : '-';
        if (sizeDisplay) sizeDisplay.textContent = sizeValue;
        else console.error('[Modal Error] sizeDisplay element not found!');

        let dateValue = '-';
        if (imageData.createdAt) {
            try {
                const date = new Date(isNaN(imageData.createdAt) ? imageData.createdAt : Number(imageData.createdAt));
                if (!isNaN(date)) {
                     dateValue = date.toLocaleString('zh-CN', {
                        year: 'numeric', month: '2-digit', day: '2-digit',
                        hour: '2-digit', minute: '2-digit', second: '2-digit',
                        hour12: false
                    }).replace(/\//g, '-');
                }
            } catch (e) {
                console.error('[Modal Error] Error parsing date:', e);
            }
        }
        if (dateDisplay) dateDisplay.textContent = dateValue;
        else console.error('[Modal Error] dateDisplay element not found!');

        if(imageView) imageView.src = imageData.imageUrl || '';
        else console.error('[Modal Error] imageView element not found!');
        
        if (promptDisplay) promptDisplay.textContent = imageData.prompt || '无';
        else console.error('[Modal Error] promptDisplay element not found!');
        
        if(dataPrompt) dataPrompt.value = imageData.prompt || '';
        if(dataImageUrl) dataImageUrl.value = imageData.imageUrl || '';
        if(dataModel) dataModel.value = imageData.model || ''; // 存储原始 model value
        if(dataWidth) dataWidth.value = imageData.width || '';
        if(dataHeight) dataHeight.value = imageData.height || '';
        if(dataCreatedAt) dataCreatedAt.value = imageData.createdAt || ''; 

        modal.show();
    }
    
    // --- 修改：\"上传到案例库\"按钮的事件监听器 ---
    if (uploadToExamplesBtn) {
        uploadToExamplesBtn.addEventListener('click', async () => {

            // 1. 保存原始状态并设置处理中
            const originalButtonHtml = uploadToExamplesBtn.innerHTML;
            uploadToExamplesBtn.disabled = true;
            uploadToExamplesBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>处理中...';

            try {
                // 2. 获取所有必要的数据 (与之前相同)
                const promptElement = document.getElementById('modalDataPrompt');
                const modelElement = document.getElementById('modalDataModel');
                const imageUrlElement = document.getElementById('modalDataImageUrl');

                if (!promptElement || !modelElement || !imageUrlElement) {
                    throw new Error('无法获取必要的数据元素');
                }

                // +++ 修改：将技术模型名称转换为友好名称 +++
                const originalModel = modelElement.value;
                let friendlyModelName = originalModel;
                
                // 转换为友好的模型名称
                if (originalModel === 'comfyui_imagefx_advanced') {
                    friendlyModelName = '高级生图';
                } else if (originalModel === 'super_image_3' || originalModel === 'super_image_2') {
                    friendlyModelName = '超级生图';
                } else if (originalModel === '3.0' || originalModel === '2.1') {
                    friendlyModelName = '超级生图';
                }
                // +++ 结束修改 +++

                // 3. 获取图片文件 (与之前相同)
                const response = await fetch(imageUrlElement.value);
                const blob = await response.blob();
                // 尝试从URL或响应头获取文件名和类型，或使用默认值
                let filename = 'generated_image';
                let filetype = blob.type || 'image/png'; // 优先使用 blob 的类型
                const urlParts = imageUrlElement.value.split('/').pop().split('.');
                if (urlParts.length > 1) {
                    filename = urlParts.slice(0, -1).join('.');
                    const ext = urlParts.pop().toLowerCase();
                    // 简单的MIME类型映射
                    if (ext === 'jpg' || ext === 'jpeg') filetype = 'image/jpeg';
                    else if (ext === 'png') filetype = 'image/png';
                    else if (ext === 'gif') filetype = 'image/gif';
                    else if (ext === 'webp') filetype = 'image/webp';
                }
                 // 如果 blob 类型是 application/octet-stream，尝试从文件名推断
                 if (filetype === 'application/octet-stream' && urlParts.length > 1) {
                    const ext = urlParts.pop().toLowerCase();
                    if (ext === 'jpg' || ext === 'jpeg') filetype = 'image/jpeg';
                    else if (ext === 'png') filetype = 'image/png';
                    else if (ext === 'gif') filetype = 'image/gif';
                    else if (ext === 'webp') filetype = 'image/webp';
                 }


                const file = new File([blob], `${filename}.${filetype.split('/')[1] || 'png'}`, { type: filetype });


                // 4. 调用AI分析 (与之前相同)
                let aiAnalysisResult = { title: '', tags: [] };
                try {
                    if (window.imageUtils && window.imageUtils.analyzeImageWithAI && file) { // 修正路径
                         // 添加视觉提示，告知用户正在分析
                         uploadToExamplesBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>AI分析中...';
                         aiAnalysisResult = await window.imageUtils.analyzeImageWithAI(file); // 修正路径
                         // 分析完成后恢复"处理中"
                         uploadToExamplesBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>处理中...';
                    } else {
                        console.warn('[AI Upload Prep] analyzeImageWithAI function not found or file not ready.');
                    }
                } catch (analyzeError) {
                    console.error('[AI Upload Prep] Error during analyzeImageWithAI call:', analyzeError);
                     // 即使分析失败，也恢复按钮状态
                     uploadToExamplesBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>处理中...';
                    alert('AI图片分析失败，请稍后手动编辑标题和标签。');
                    // 分析失败不应阻止上传，继续使用默认值
                }

                // 5. 准备数据对象 (与之前相同)
                const dataForMain = {
                    title: aiAnalysisResult.title || '', // 如果AI失败，标题留空让用户填写
                    prompt: promptElement.value,
                    model: friendlyModelName, // +++ 修改：使用友好的模型名称 +++
                    tags: aiAnalysisResult.tags || [], // AI失败则标签为空
                    imageUrl: imageUrlElement.value,
                    imageFile: file // 传递 File 对象
                };

                // 如果AI分析没有生成标签,则尝试从提示词中提取 (保留)
                if (dataForMain.tags && dataForMain.tags.length === 0 && promptElement.value) {
                   const keywords = promptElement.value.match(/[a-zA-Z0-9\u4e00-\u9fa5]+/g) || []; // 匹配字母、数字、中文
                   // 过滤掉太短的词（可选）
                   dataForMain.tags = [...new Set(keywords.filter(kw => kw.length > 1))].slice(0, 5);
                }


                // 6. 调用 main.js 的函数来准备并显示模态框 (与之前相同)
                if (window.exampleFormHandler && typeof window.exampleFormHandler.prepareAndShowAddExampleModal === 'function') { // 修正路径
                    // 先隐藏详情模态框
                    if (imageDetailModalElement) {
                        const detailModalInstance = bootstrap.Modal.getInstance(imageDetailModalElement);
                        if (detailModalInstance) {
                            // 使用 Promise 确保模态框隐藏后才调用 prepare
                            await new Promise(resolve => {
                                imageDetailModalElement.addEventListener('hidden.bs.modal', resolve, { once: true });
                                detailModalInstance.hide();
                            });
                            window.exampleFormHandler.prepareAndShowAddExampleModal(dataForMain); // 修正路径

                        } else {
                            console.warn('[AI Upload Prep] Could not get instance of detail modal. Calling prepare function directly.');
                            window.exampleFormHandler.prepareAndShowAddExampleModal(dataForMain); // 修正路径
                        }
                    } else {
                        console.warn('[AI Upload Prep] Detail modal element not found. Calling prepare function directly.');
                        window.exampleFormHandler.prepareAndShowAddExampleModal(dataForMain); // 修正路径
                    }
                } else {
                    console.error('[AI Upload Prep] window.prepareAndShowAddExampleModal function not found in main.js!');
                    throw new Error('无法打开添加案例窗口，请联系管理员。'); // 抛出错误以便 finally 块捕获并重置按钮
                }

            } catch (error) {
                // 7. 错误处理 (简化，只显示 alert)
                console.error('[AI Upload Prep] Error preparing Add Example modal data:', error);
                alert(`准备上传失败: ${error.message}`);
                // 错误现在会在 finally 中处理按钮重置
            } finally {
                // 8. 使用 finally 确保按钮状态总是被重置
                uploadToExamplesBtn.disabled = false;
                uploadToExamplesBtn.innerHTML = originalButtonHtml; // 恢复按钮的原始内容
            }
        });
    } else {
        console.error('无法找到 uploadToExamplesBtn 按钮。');
    }

    // --- 新增：比例按钮点击事件 ---
    if (aiRatioSelector) {
        ratioButtons.forEach(button => {
            button.addEventListener('click', () => {
                const selectedRatioId = button.dataset.ratio;
                const dimensions = calculateDimensions(selectedRatioId);
                if (dimensions) {
                    updateSizeInputs(dimensions.width, dimensions.height);
                    updateRatioSelection(selectedRatioId); // 会自动锁定
                }
            });
        });
    } else {
        console.warn('Ratio selector container not found.');
    }

    // --- 新增：锁定按钮点击事件 ---
     if (aiRatioLockBtn) {
        aiRatioLockBtn.addEventListener('click', () => {
            toggleRatioLock(); // 切换状态
        });
    } else {
        console.warn('Ratio lock button not found.');
    }

    // --- 新增：宽高输入框联动事件 ---
    function handleSizeInputChange(event) {
        if (!isRatioLocked) {
            // 如果未锁定，用户修改任何一个值都会取消比例选择
            updateRatioSelection(null);
            return; // 不进行联动计算
        }

        const changedInput = event.target;
        const otherInput = changedInput === aiImageWidthInput ? aiImageHeightInput : aiImageWidthInput;
        if (!otherInput) return;

        const changedValue = parseInt(changedInput.value, 10);
        if (isNaN(changedValue) || changedValue <= 0) {
            // 输入无效，不清空另一个，但取消比例选择
            updateRatioSelection(null);
            return;
        }

        // 尝试找到当前最匹配的比例
        const activeRatioId = updateRatioSelectionFromInputs(); // 这个函数会根据当前 W/H 更新按钮状态

        if (activeRatioId) {
             const ratioInfo = ratios[activeRatioId];
             let newOtherValue;

             if (changedInput === aiImageWidthInput) { // 修改了宽度
                 newOtherValue = Math.round((changedValue * ratioInfo.h) / ratioInfo.w);
             } else { // 修改了高度
                 newOtherValue = Math.round((changedValue * ratioInfo.w) / ratioInfo.h);
             }
             
             // 调整为 8 的倍数
             newOtherValue = Math.max(8, Math.floor(newOtherValue / 8) * 8);

             otherInput.value = newOtherValue;
             
             // 因为联动修改了另一个值，需要再次确认比例是否仍然匹配（理论上应该匹配）
             updateRatioSelectionFromInputs();

        } else {
            // 即使锁定了，如果当前 W/H 不匹配任何预设比例，则不进行联动
            // 但保持锁定状态，直到用户点击解锁或选择一个新比例
        }
    }

    if (aiImageWidthInput) {
        aiImageWidthInput.addEventListener('input', handleSizeInputChange);
    }
    if (aiImageHeightInput) {
        aiImageHeightInput.addEventListener('input', handleSizeInputChange);
    }

    // --- Add this event listener in the main scope of ai-generate.js --- 
    // (Ensure aiHistoryListContainer is defined in this scope)
    if (aiHistoryListContainer) {
        aiHistoryListContainer.addEventListener('click', (event) => {
            const button = event.target.closest('.btn-action');
            if (!button) return; 

            event.preventDefault();
            event.stopPropagation();

            const action = button.dataset.action;
            const imageUrl = button.dataset.imageUrl;
            const targetPaneId = button.dataset.targetPane;
            const targetTabId = button.dataset.targetTab; // Get tab ID from data attribute

            if (!action || !imageUrl || !targetPaneId || !targetTabId) { // Check all required data
                console.error('Action button missing data attributes:', button.dataset);
                if(window.showToast) window.showToast('无法执行操作，按钮数据缺失', 'error');
                return;
            }


            // 新增：转3D逻辑
            if (action === 'sendTo3d') {
                window.pendingImageData = { targetPaneId: targetPaneId, imageUrl: imageUrl };
                // 切换到转3D的tab
                const targetTabElement = document.getElementById(targetTabId);
                if (targetTabElement) {
                    try {
                        const tab = new bootstrap.Tab(targetTabElement);
                        tab.show();
                    } catch (e) {
                        console.error(`Error switching tab to ${targetTabId}:`, e);
                        if(window.showToast) window.showToast('切换标签页时出错', 'error');
                        delete window.pendingImageData;
                    }
                } else {
                    console.error(`Target tab element not found: #${targetTabId}`);
                    if(window.showToast) window.showToast('找不到目标标签页', 'error');
                    delete window.pendingImageData;
                }
                return;
            }

            // 其他 action 保持原有逻辑
            window.pendingImageData = { targetPaneId: targetPaneId, imageUrl: imageUrl };

            // Switch tab
            const targetTabElement = document.getElementById(targetTabId);
            if (targetTabElement) {
                try {
                     const tab = new bootstrap.Tab(targetTabElement);
                     tab.show();
                } catch (e) {
                     console.error(`Error switching tab to ${targetTabId}:`, e);
                     if(window.showToast) window.showToast('切换标签页时出错', 'error');
                     delete window.pendingImageData; // Clear pending data on error
                }
            } else {
                console.error(`Target tab element not found: #${targetTabId}`);
                if(window.showToast) window.showToast('找不到目标标签页', 'error');
                delete window.pendingImageData; // Clear pending data if tab doesn't exist
            }
        });
    } else {
        console.warn('AI History list container not found for attaching action listener.');
    }

    // --- 新增：获取功能成本的函数 --- START ---
    async function fetchFeatureCost() {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('未找到认证令牌，无法获取功能成本');
                return;
            }
            
            const response = await fetch(`${API_BASE_URL}/api/features/cost?key=ai_generate`, { // <<< 修改了 URL
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (!response.ok) {
                console.error('获取功能成本失败:', response.status);
                return;
            }
            
            const data = await response.json();
            if (data.success && data.cost !== undefined) {
                featureCost = data.cost;
                
                // 更新按钮文本
                updateButtonWithCost();
            }
        } catch (error) {
            console.error('获取功能成本出错:', error);
        }
    }
    
    function updateButtonWithCost() {
        if (startAIGenerationBtn && featureCost !== null) {
            // 统一重建按钮内容，避免重复
            let originalText = startAIGenerationBtn.getAttribute('data-original-text') || '开始生成';
            // 去掉原有的积分信息
            originalText = originalText.replace(/\s*\d+积分\s*/g, '').trim();

            startAIGenerationBtn.innerHTML = `<i class="bi bi-stars me-2"></i> ${originalText} <span class="ms-1 badge bg-secondary">${featureCost}积分</span>`;
        }
    }
    // --- 新增：获取功能成本的函数 --- END ---

    // --- 新增：Tab 显示处理函数 --- START ---
    function handleTabShown() {
        if (window.pendingImageData && window.pendingImageData.targetPaneId === 'vectorize-pane') {
            // ... existing code ...
        } else {
            // --- 新增：加载历史记录 --- START ---
            // 仅当历史记录为空，或者不是因为图片发送操作切换过来的时候才加载
            // 并且当前 Tab 是 'ai-generate-tab-pane'
            const currentActiveTabPaneId = document.querySelector('.tab-pane.active')?.id;
            if (currentActiveTabPaneId === 'ai-generate-tab-pane' && (!aiHistoryListContainer.hasChildNodes() || aiHistoryListContainer.querySelector('#historyPlaceholder'))) {
                 loadHistory(1, false); // 加载第一页，非追加
            } else if (currentActiveTabPaneId === 'ai-generate-tab-pane') {
                 // 修改：滚动容器改为历史记录卡片的 card-body
                 if (scrollableHistoryContainer) {
                    scrollableHistoryContainer.scrollTop = 0;
                 }
            }
            // --- 新增：加载历史记录 --- END ---
        }

        // 获取功能成本（如果尚未获取）
        if (featureCost === null) {
            fetchFeatureCost();
        }
    }
    // --- 新增：Tab 显示处理函数 --- END ---

    // --- 初始加载 ---
    // 页面加载时获取功能成本
    fetchFeatureCost();
    // 初始时不直接调用 loadHistory(1)，而是由 tab 'shown' 事件或默认激活状态来触发
    // 如果 #ai-generate-tab 默认是 active, 则上面的逻辑会处理首次加载
    // 确保 checkAndShowHistoryPlaceholder 在 DOMContentLoaded 后至少运行一次，以处理初始占位符状态
    checkAndShowHistoryPlaceholder();

    // 新增：获取"相关案例"Tab和其容器
    const relevantExamplesTab = document.getElementById('relevant-examples-tab');
    const relevantExamplesContainer = document.getElementById('aiGenerateRelevantExamplesContainer');
    const relevantExamplesPlaceholder = relevantExamplesContainer ? relevantExamplesContainer.querySelector('.relevant-examples-placeholder') : null;
    // 新增：获取新的相关案例搜索框
    const relevantExamplesSearchInput = document.getElementById('relevantExamplesSearchInput');

    // --- 重新添加：相关案例图片基础URL和默认头像路径 ---
    const RELEVANT_EXAMPLES_IMAGE_BASE_URL = 'https://www.yzycolour.top/prompt-examples/server/uploads/';
    const DEFAULT_AVATAR_PATH = 'images/default-avatar.png'; // 确保 admin/images/ 目录下有此文件

    // --- 新增：相关案例搜索的防抖计时器 ---
    let relevantExamplesSearchDebounceTimer = null;

    // --- 新增：获取并渲染相关案例 ---
    async function fetchAndRenderRelevantExamples(page = 1, append = false) {
        if (isLoadingRelevantExamples) return;
        isLoadingRelevantExamples = true;

        if (!relevantExamplesContainer) {
            console.error('相关案例容器未找到 (aiGenerateRelevantExamplesContainer)');
            isLoadingRelevantExamples = false;
            return;
        }
        
        // 优先级：1. 新的独立搜索框 relevantExamplesSearchInput, 2. 左侧的 aiPromptInput
        const dedicatedSearchQuery = relevantExamplesSearchInput ? relevantExamplesSearchInput.value.trim() : '';
        const fallbackPromptQuery = aiPromptInput.value.trim();
        const finalPromptQuery = dedicatedSearchQuery || fallbackPromptQuery;

        // 获取选定模型的文本名称，例如 "超级生图 V3.0"
        let selectedModelNameForAPI = null;
        let originalSelectedModelText = null; // 存储原始选择的文本，用于指纹
        if (aiModelSelectValue && aiModelSelectValue.selectedIndex >= 0) {
            const selectedOption = aiModelSelectValue.options[aiModelSelectValue.selectedIndex];
            if (selectedOption && selectedOption.text) {
                originalSelectedModelText = selectedOption.text.trim();
                if (originalSelectedModelText.includes('超级生图')) {
                    selectedModelNameForAPI = '超级生图'; // 统一使用 "超级生图"进行API查询
                } else {
                    selectedModelNameForAPI = originalSelectedModelText; // 其他模型使用原始文本
                }
            }
        }

        // 构建查询参数
        let queryParams = new URLSearchParams({
            page: page.toString(),
            limit: '6' // 每次加载6个案例 (2列 x 3行)
        });

        let apiEndpoint = `${API_BASE_URL}/api/examples`;
        let newQueryFingerprint = '';

        if (finalPromptQuery) { // 使用最终确定的搜索词
            apiEndpoint = `${API_BASE_URL}/api/examples/search`;
            queryParams.append('query', finalPromptQuery);
            newQueryFingerprint = `prompt:${finalPromptQuery}`;
        } else {
             apiEndpoint = `${API_BASE_URL}/api/examples`; // <<< 修改了 URL (确保无搜索词时也使用新基地址)
        }

        // 如果选定了模型，则添加模型筛选
        if (selectedModelNameForAPI) {
            queryParams.append('target_model', selectedModelNameForAPI);
            // 指纹部分使用原始选择的文本，以区分 V3.0 和 V2.1 触发的重新加载（如果未来API支持更细致筛选）
            // 或者，如果希望 V3.0 和 V2.1 视为同一筛选条件不触发刷新，则用 selectedModelNameForAPI
            newQueryFingerprint += (newQueryFingerprint ? '|' : '') + `model:${originalSelectedModelText || selectedModelNameForAPI}`;
        }

        // 如果既没有提示词也没有选定模型，则按日期降序加载所有案例
        // 如果只有模型，则按日期降序加载该模型的案例
        // 如果有提示词（无论是否有模型），则通常由搜索接口的默认排序（相关性）决定，但也可以显式指定
        if (!finalPromptQuery) { // 包括 (无提示词+无模型) 和 (无提示词+有模型) 的情况
            queryParams.append('sort', 'date_desc');
            if (!selectedModelNameForAPI) { // 特指 无提示词+无模型
                 if (!newQueryFingerprint) newQueryFingerprint = 'latest_all'; // 确保指纹不为空
            }
        }
        // 如果 newQueryFingerprint 仍然为空 (例如，selectedModelNameForAPI 为 null 或空字符串且无 prompt)
        // 确保它有一个默认值，以避免与之前的 'latest' (当模型筛选未实现时) 混淆
        if (!newQueryFingerprint) {
            newQueryFingerprint = 'all_models_default_sort'; // 或者更具体的默认状态指纹
        }


        // 如果查询条件改变，重置页码和容器内容
        if (currentRelevantExamplesQuery !== newQueryFingerprint && !append) {
            relevantExamplesCurrentPage = 1;
            relevantExamplesTotalPages = 1;
            if(relevantExamplesContainer) relevantExamplesContainer.innerHTML = ''; // 清空现有内容
            currentRelevantExamplesQuery = newQueryFingerprint;
             if (relevantExamplesPlaceholder) relevantExamplesPlaceholder.textContent = '加载案例中...';
        } else if (!append) {
            if(relevantExamplesContainer) relevantExamplesContainer.innerHTML = ''; // 非追加模式也清空
             if (relevantExamplesPlaceholder) relevantExamplesPlaceholder.textContent = '加载案例中...';
        }


        if (relevantExamplesPlaceholder) relevantExamplesPlaceholder.style.display = (page === 1 && !append) ? 'block' : 'none';
        if (append) showRelevantExamplesLoadMoreIndicator(true);


        try {
            // 如果 window.getAuthHeaders 存在并且是函数，则使用它，否则回退到默认的 Content-Type
            const headers = window.getAuthHeaders && typeof window.getAuthHeaders === 'function'
                            ? window.getAuthHeaders()
                            : { 'Content-Type': 'application/json' };
            
            const response = await fetch(`${apiEndpoint}?${queryParams.toString()}`, { // apiEndpoint 已经包含 API_BASE_URL
                headers: headers
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || `获取相关案例失败: ${response.status}`);
            }

            const data = await response.json();
            renderRelevantExampleCards(data.examples || [], append);
            relevantExamplesCurrentPage = data.pagination.currentPage || page;
            relevantExamplesTotalPages = data.pagination.totalPages || 1;

        } catch (error) {
            console.error('获取或渲染相关案例失败:', error);
            if (relevantExamplesContainer) {
                 if (!append || relevantExamplesContainer.children.length === 0) {
                    relevantExamplesContainer.innerHTML = `<p class="text-danger text-center w-100">加载案例失败: ${error.message}</p>`;
                 } else {
                    // 可以在末尾追加一个小的错误提示
                    const errorP = document.createElement('p');
                    errorP.className = 'text-danger text-center w-100 small py-2';
                    errorP.textContent = '加载更多案例失败。';
                    relevantExamplesContainer.appendChild(errorP);
                 }
            }
        } finally {
            isLoadingRelevantExamples = false;
            if (relevantExamplesPlaceholder && relevantExamplesContainer.children.length > 0) relevantExamplesPlaceholder.style.display = 'none';
            else if (relevantExamplesPlaceholder && relevantExamplesContainer.children.length === 0 && !isLoadingRelevantExamples) relevantExamplesPlaceholder.textContent = '没有找到相关案例。';

            if (append) showRelevantExamplesLoadMoreIndicator(false);
        }
    }

    function renderRelevantExampleCards(examples, append = false) {
        if (!relevantExamplesContainer) return;

        if (!append) {
            // relevantExamplesContainer.innerHTML = ''; // 已在 fetch 函数中处理
        }
        
        // 移除可能存在的加载指示器或错误提示（如果是追加模式）
        if (append) {
            const existingIndicator = relevantExamplesContainer.querySelector('.loading-more-relevant');
            if (existingIndicator) existingIndicator.remove();
            const existingError = relevantExamplesContainer.querySelector('p.text-danger');
            if(existingError) existingError.remove();
        }


        if (!examples || examples.length === 0) {
            if (!append && relevantExamplesContainer.children.length === 0) {
                 if (relevantExamplesPlaceholder) {
                    relevantExamplesPlaceholder.textContent = '没有找到符合条件的案例。';
                    relevantExamplesPlaceholder.style.display = 'block';
                } else {
                    relevantExamplesContainer.innerHTML = '<p class="text-muted text-center w-100 relevant-examples-placeholder">没有找到相关案例。</p>';
                }
            }
            return;
        }

        if (relevantExamplesPlaceholder) relevantExamplesPlaceholder.style.display = 'none';

        const fragment = document.createDocumentFragment();
        const imageBaseUrl = window.imageBaseUrl || 'https://www.yzycolour.top/prompt-examples/server/uploads/';

        examples.forEach(example => {
            const cardCol = document.createElement('div');
            // 卡片使用 Bootstrap 列定义，父容器已经是 .row .g-3
            cardCol.className = 'col'; //  确保 cardCol 是一个列元素，配合父容器的 row-cols-md-2

            const card = document.createElement('div');
            card.className = 'example-card relevant-example-card'; // 移除了 h-100
            card.dataset.id = example.id;

            const imageUrl = example.image_file ? imageBaseUrl + example.image_file : '';
            const categoryName = (window.getCategoryNameFromMain && example.category_slug) ? window.getCategoryNameFromMain(example.category_slug) : (example.category_name || '未分类');
            
            // 从 example.author 对象中获取作者信息
            const authorNickname = example.author && example.author.nickname ? example.author.nickname : '匿名作者';
            const relativeAuthorAvatarUrl = example.author && example.author.avatar_url ? example.author.avatar_url : null;

            // 修正：拼接基础URL，并提供默认头像
            const finalAuthorAvatarUrl = relativeAuthorAvatarUrl
                ? RELEVANT_EXAMPLES_IMAGE_BASE_URL + relativeAuthorAvatarUrl
                : RELEVANT_EXAMPLES_IMAGE_BASE_URL + DEFAULT_AVATAR_PATH; // 或者直接是一个完整的默认头像URL

            let categoryColorStyle = '';
            if (window.getCategoryColor && example.category_slug) {
                const colors = window.getCategoryColor(example.category_slug);
                categoryColorStyle = `background-color: ${colors.backgroundColor}; border-color: ${colors.borderColor};`;
            }


            card.innerHTML = `
                ${imageUrl ? `<img src="${imageUrl}" class="example-img" alt="${example.title || '案例图片'}" loading="lazy">` : '<div class="example-img-placeholder"></div>'}
                ${example.category_slug ? `<span class="category-badge" style="${categoryColorStyle}">${categoryName}</span>` : ''}
                ${example.target_model ? `<span class="model-badge">${example.target_model}</span>` : ''}
                <!-- <div class="card-body"> -->
                    <!-- <h6 class="relevant-card-title">${example.title || '无标题'}</h6> -->
                    <!-- <p class="relevant-card-prompt">${example.prompt ? example.prompt.substring(0, 60) + (example.prompt.length > 60 ? '...' : '') : '无提示词'}</p> -->
                <!-- </div> -->
                <div class="card-footer relevant-card-actions d-flex align-items-center">
                    ${relativeAuthorAvatarUrl ? `<img src="${finalAuthorAvatarUrl}" alt="${authorNickname}" class="relevant-author-avatar me-2" onerror="this.onerror=null; this.src='${RELEVANT_EXAMPLES_IMAGE_BASE_URL + DEFAULT_AVATAR_PATH}';">` : `<img src="${finalAuthorAvatarUrl}" alt="默认头像" class="relevant-author-avatar me-2">`}
                    <span class="relevant-author-name">${authorNickname}</span>
                    <!-- 应用按钮已移除 -->
                    <!-- 
                    <button class="btn btn-sm btn-outline-secondary copy-prompt-to-input" title="使用此提示词和参数">
                        <i class="bi bi-clipboard-plus"></i> 应用
                    </button>
                    -->
                </div>
            `;
            
            // 卡片点击事件 - 打开主案例详情
            card.addEventListener('click', (event) => {
                // 移除打开详情模态框的逻辑
                // if (window.showExampleDetailModal) {
                //     window.showExampleDetailModal(example.id);
                // } else {
                //     console.error('showExampleDetailModal function not available.');
                // }

                // 新增：填充提示词和模型，并切换 Tab 的逻辑
                if (aiPromptInput) {
                    aiPromptInput.value = example.prompt || '';
                }

                if (example.target_model) setModelSelection(example.target_model);
                // 切换回"生成参数"tab
                const paramsTabButton = document.getElementById('generate-params-tab');
                if (paramsTabButton) {
                    const tab = new bootstrap.Tab(paramsTabButton);
                    tab.show();
                }
                if (aiPromptInput) {
                    aiPromptInput.focus();
                }
                if(window.showToast) {
                    window.showToast('提示词和模型已填充！', 'success');
                }
            });

            // "应用"按钮事件 (此按钮已在UI上移除，但逻辑可参考或被卡片点击事件复用)
            const copyButton = card.querySelector('.copy-prompt-to-input');
            if (copyButton) {
                copyButton.addEventListener('click', (e) => {
                    e.stopPropagation(); // 阻止事件冒泡到卡片点击
                    aiPromptInput.value = example.prompt || '';
                    // 尝试设置模型 (如果 aiModelSelect 的 option value 与 example.target_model 匹配)
                    if (example.target_model) {
                        let modelFound = false;
                        for (let option of aiModelSelectValue.options) {
                            // 简单匹配，例如 "超级生图 V3.0" vs "3.0"
                            if (option.value === example.target_model || option.text === example.target_model || (example.target_model.includes('3.0') && option.value === '3.0') || (example.target_model.includes('2.1') && option.value === '2.1') ) {
                                aiModelSelectValue.value = option.value;
                                modelFound = true;
                                break;
                            }
                        }
                        if (!modelFound) console.warn(`模型 "${example.target_model}" 在下拉列表中未完全匹配。`);
                    }
                    
                    // 切换回"生成参数"tab
                    const paramsTabButton = document.getElementById('generate-params-tab');
                    if (paramsTabButton) {
                        const tab = new bootstrap.Tab(paramsTabButton);
                        tab.show();
                    }
                    aiPromptInput.focus();
                    if(window.showToast) window.showToast('提示词和模型已应用！', 'success');
                });
            }

            cardCol.appendChild(card);
            fragment.appendChild(cardCol);
        });
        relevantExamplesContainer.appendChild(fragment);
    }

    function showRelevantExamplesLoadMoreIndicator(show) {
        let indicator = relevantExamplesContainer.querySelector('.loading-more-relevant');
        if (show) {
            if (!indicator) {
                indicator = document.createElement('div');
                indicator.className = 'loading-more-relevant text-center py-3 text-muted col-12'; // col-12 确保占满整行
                indicator.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>加载更多案例...';
                relevantExamplesContainer.appendChild(indicator);
            }
            indicator.style.display = 'block';
        } else {
            if (indicator) {
                indicator.remove(); // 直接移除，下次需要时重建
            }
        }
    }


    // 事件监听：切换到"相关案例"Tab 时加载数据
    if (relevantExamplesTab) {
        relevantExamplesTab.addEventListener('shown.bs.tab', () => {
            // 只有在内容为空或占位符可见时才加载第一页，或者查询条件已更改
            const promptValue = aiPromptInput.value.trim();
            let originalSelectedModelText = null;
            if (aiModelSelectValue && aiModelSelectValue.selectedIndex >= 0) {
                const selectedOption = aiModelSelectValue.options[aiModelSelectValue.selectedIndex];
                if (selectedOption && selectedOption.text) {
                    originalSelectedModelText = selectedOption.text.trim();
                }
            }
            let newQueryFingerprint = '';
            if (promptValue) newQueryFingerprint = `prompt:${promptValue}`;
            if (originalSelectedModelText) newQueryFingerprint += (newQueryFingerprint ? '|' : '') + `model:${originalSelectedModelText}`;
            if (!newQueryFingerprint) newQueryFingerprint = 'all_models_default_sort'; // 与 fetch 函数中保持一致

            if (relevantExamplesContainer && 
                (relevantExamplesContainer.children.length === 0 || 
                 (relevantExamplesPlaceholder && relevantExamplesPlaceholder.style.display !== 'none') ||
                 currentRelevantExamplesQuery !== newQueryFingerprint)
            ) {
                 fetchAndRenderRelevantExamples(1, false);
            } else {
            }
        });
    }

    // 新增：监听新的相关案例搜索框输入（带防抖）
    if (relevantExamplesSearchInput) {
        relevantExamplesSearchInput.addEventListener('input', () => {
            clearTimeout(relevantExamplesSearchDebounceTimer);
            relevantExamplesSearchDebounceTimer = setTimeout(() => {
                fetchAndRenderRelevantExamples(1, false);
            }, 500); // 500毫秒防抖
        });
    }

    // 修改：监听 Prompt 输入框变化时，只有当新的相关案例搜索框为空时，才触发相关案例搜索
    if (aiPromptInput) {
        aiPromptInput.addEventListener('input', () => {
            clearTimeout(relevantExamplesSearchDebounceTimer);
            relevantExamplesSearchDebounceTimer = setTimeout(() => {
                // 仅当专属搜索框为空时，aiPromptInput 的变化才影响案例搜索
                if (relevantExamplesSearchInput && !relevantExamplesSearchInput.value.trim()) {
                    fetchAndRenderRelevantExamples(1, false);
                }
            }, 500); // 500毫秒防抖
        });
    }

    // 新增：监听模型选择变化以触发相关案例更新
    if (aiModelSelectValue) {
        aiModelSelectValue.addEventListener('change', () => {
            fetchAndRenderRelevantExamples(1, false);
        });
    }

    // 事件监听：相关案例容器滚动加载
    if (relevantExamplesContainer && relevantExamplesContainer.parentElement.classList.contains('relevant-examples-scroll-container')) {
        // 如果滚动是直接在 #aiGenerateRelevantExamplesContainer 上（因为它有 max-height 和 overflow）
        const scrollContainerForRelevant = relevantExamplesContainer;
         scrollContainerForRelevant.addEventListener('scroll', () => {
            const threshold = 100;
            if (!isLoadingRelevantExamples && relevantExamplesCurrentPage < relevantExamplesTotalPages &&
                (scrollContainerForRelevant.scrollTop + scrollContainerForRelevant.clientHeight >= scrollContainerForRelevant.scrollHeight - threshold)) {
                fetchAndRenderRelevantExamples(relevantExamplesCurrentPage + 1, true);
            }
        });
    } else if (document.querySelector('.relevant-examples-scroll-container')) { // 备用，如果滚动在父级
        const scrollContainerForRelevant = document.querySelector('.relevant-examples-scroll-container');
        if (scrollContainerForRelevant){
            scrollContainerForRelevant.addEventListener('scroll', () => {
                const threshold = 100;
                if (!isLoadingRelevantExamples && relevantExamplesCurrentPage < relevantExamplesTotalPages &&
                    (scrollContainerForRelevant.scrollTop + scrollContainerForRelevant.clientHeight >= scrollContainerForRelevant.scrollHeight - threshold)) {
                    fetchAndRenderRelevantExamples(relevantExamplesCurrentPage + 1, true);
                }
            });
        }
    } else {
        console.warn('相关案例的滚动容器未正确配置，懒加载可能无法工作。');
    }

    // =======================================================================
    // >>> 在 DOMContentLoaded 的末尾，但在结束的 `});` 之前，添加以下代码 <<<
    // =======================================================================
    if (typeof window.aiGenerateJsApi === 'undefined') {
        window.aiGenerateJsApi = {};
    }
    
    // 添加公共API - selectRatio
    window.aiGenerateJsApi.selectRatio = function(ratioId) {
        const dimensions = calculateDimensions(ratioId);
        if (dimensions) {
            updateSizeInputs(dimensions.width, dimensions.height);
            updateRatioSelection(ratioId);
            return true;
        }
        return false;
    };
    
    window.aiGenerateJsApi._internal = {
        // DOM 元素引用
        ratioButtonsNodeList: ratioButtons, // 'ratioButtons' 应该在此作用域已定义
        getAiImageWidthInput: () => aiImageWidthInput,     // 'aiImageWidthInput' 已定义
        getAiImageHeightInput: () => aiImageHeightInput,   // 'aiImageHeightInput' 已定义
        getAiRatioLockBtn: () => aiRatioLockBtn,         // 'aiRatioLockBtn' 已定义

        // 核心功能函数
        calculateDimensionsFunc: calculateDimensions,     // 'calculateDimensions' 已定义
        updateSizeInputsFunc: updateSizeInputs,           // 'updateSizeInputs' 已定义
        updateRatioSelectionFunc: updateRatioSelection,   // 'updateRatioSelection' 已定义
        toggleRatioLockFunc: toggleRatioLock,             // 'toggleRatioLock' 已定义
        isRatioLockedState: () => isRatioLocked,          // 'isRatioLocked' (状态变量)
        
        // Ratios data (如果 calculateDimensionsFunc 依赖外部的 ratios)
        // 如果 ratios 是在 DOMContentLoaded 外部定义的全局常量，则不需要在这里暴露
        // 如果 ratios 定义在 DOMContentLoaded 内部，则需要暴露:
        // ratiosData: ratios 
    };
    // =======================================================================
    // >>> 添加结束 <<<
    // =======================================================================



    // +++ 新增：处理模型选择变化的函数 +++
    function handleModelChange() {
        const selectedModel = aiModelSelectValue ? aiModelSelectValue.value : '';
        // 下面的逻辑与原来一致，只是把aiModelSelect.value换成aiModelSelectValue.value
        const midjourneyParamsGroup = document.getElementById('midjourneyParamsGroup');
        const comfyAspectRatioGroup = document.getElementById('comfyAspectRatioGroup');
        const comfyNumImagesGroup = document.getElementById('comfyNumImagesGroup');
        const aiRatioSelectorContainer = document.getElementById('aiRatioSelector') ? document.getElementById('aiRatioSelector').closest('.mb-3') : null;
        const aiImageDimensionsRow = document.getElementById('aiImageDimensionsRow');

        // Hide all conditional parameter groups first
        if (midjourneyParamsGroup) midjourneyParamsGroup.style.display = 'none';
        if (comfyAspectRatioGroup) comfyAspectRatioGroup.style.display = 'none';
        if (comfyNumImagesGroup) comfyNumImagesGroup.style.display = 'none';
        if (aiRatioSelectorContainer) aiRatioSelectorContainer.style.display = 'none';
        if (aiImageDimensionsRow) aiImageDimensionsRow.style.display = 'none';

        if (selectedModel === 'midjourney') {
            if (midjourneyParamsGroup) midjourneyParamsGroup.style.display = 'block';
            initializeMjImageUploaderIfNeeded && initializeMjImageUploaderIfNeeded();
        } else if (selectedModel === 'comfyui_imagefx_advanced') {
            if (comfyAspectRatioGroup) comfyAspectRatioGroup.style.display = 'block';
            if (comfyNumImagesGroup) comfyNumImagesGroup.style.display = 'block';
        } else {
            if (aiRatioSelectorContainer) aiRatioSelectorContainer.style.display = 'block';
            if (aiImageDimensionsRow) aiImageDimensionsRow.style.display = 'block';
        }
        // 相关案例和按钮等后续逻辑
        if (typeof fetchAndRenderRelevantExamples === 'function') {
            window.currentRelevantExamplesPage = 1;
            fetchAndRenderRelevantExamples(window.currentRelevantExamplesPage, false);
        }
        if (typeof updateButtonWithCost === 'function') {
            updateButtonWithCost();
        }
    }
    // +++ 结束新增 +++

    // +++ 新增：为模型选择器添加事件监听器 +++
    if (aiModelSelectValue) {
        aiModelSelectValue.addEventListener('change', handleModelChange);
        // 初始化时也调用一次，以确保UI基于默认选中的模型正确显示
        handleModelChange(); 
    } else {
        console.warn('[AI Generate] aiModelSelectValue element not found. UI switching for models will not work.');
    }
    // +++ 结束新增 +++

    // 新增：删除历史记录的函数
    async function deleteHistoryRecord(recordId) {
        if (!recordId) {
            console.error('[AI Generate] 删除失败：缺少记录ID');
            if (window.showToast) window.showToast('删除失败：未指定记录', 'error');
            return false;
        }

        try {
            const response = await fetch(`${API_BASE_URL}/api/ai/history/${recordId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error(`[AI Generate] 删除记录失败 (${response.status}):`, errorData.error || response.statusText);
                if (window.showToast) window.showToast(errorData.error || '删除失败，请重试', 'error');
                return false;
            }

            const data = await response.json();
            if (window.showToast) window.showToast('历史记录已删除', 'success');
            return true;
        } catch (error) {
            console.error('[AI Generate] 调用删除API失败:', error);
            if (window.showToast) window.showToast('网络错误，无法删除记录', 'error');
            return false;
        }
    }

    // 新增：显示删除确认对话框
    function showDeleteConfirmDialog(recordId, historyItemElement) {
        // 检查是否已存在确认对话框
        let confirmModal = document.getElementById('deleteHistoryConfirmModal');
        let bootstrapModal;
        
        if (!confirmModal) {
            // 如果模态对话框不存在，则创建一个
            const modalHtml = `
            <div class="modal fade" id="deleteHistoryConfirmModal" tabindex="-1" aria-labelledby="deleteHistoryConfirmLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header" style="border-top-left-radius: 8px; border-top-right-radius: 8px;">
                            <h5 class="modal-title" id="deleteHistoryConfirmLabel">确认删除</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            确定要删除这条历史记录吗？此操作无法撤销。
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                                <i class="bi bi-trash"></i> 确认删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>`;
            
            // 将模态框HTML添加到文档中
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            confirmModal = document.getElementById('deleteHistoryConfirmModal');
        } else {
            // 如果模态框已存在，恢复确认按钮的初始状态
            const existingBtn = confirmModal.querySelector('#confirmDeleteBtn');
            if (existingBtn) {
                existingBtn.disabled = false;
                existingBtn.innerHTML = '<i class="bi bi-trash"></i> 确认删除';
            }
        }
        
        // 获取或创建模态实例
        try {
            bootstrapModal = new bootstrap.Modal(confirmModal, {
                backdrop: true,
                keyboard: true,
                focus: true
            });
        } catch (error) {
            console.error('创建Bootstrap模态框实例失败:', error);
            // 如果无法创建Bootstrap模态框，可以使用简单的confirm对话框代替
            if (confirm('确定要删除这条历史记录吗？此操作无法撤销。')) {
                deleteHistoryRecord(recordId).then(success => {
                    if (success && historyItemElement) {
                        historyItemElement.style.opacity = '0';
                        setTimeout(() => historyItemElement.remove(), 300);
                        checkAndShowHistoryPlaceholder();
                    }
                });
            }
        return;
    }

        // 获取确认按钮并绑定事件
        const confirmBtn = document.getElementById('confirmDeleteBtn');
        
        // 确保按钮处于初始状态
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = '<i class="bi bi-trash"></i> 确认删除';
        
        // 移除可能存在的旧事件处理器
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
        
        // 绑定新的事件处理器
        newConfirmBtn.addEventListener('click', async () => {
            // 显示加载状态
            newConfirmBtn.disabled = true;
            newConfirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>删除中...';
            
            const success = await deleteHistoryRecord(recordId);
            if (success && historyItemElement) {
                // 添加渐变消失动画
                historyItemElement.style.transition = 'opacity 0.3s ease, transform 0.3s ease, max-height 0.5s ease';
                historyItemElement.style.opacity = '0';
                historyItemElement.style.transform = 'translateY(-20px)';
                historyItemElement.style.overflow = 'hidden';
                
                // 等待动画完成后移除元素
                setTimeout(() => {
                    historyItemElement.style.maxHeight = '0';
                    setTimeout(() => {
                        historyItemElement.remove();
                        // 检查是否需要显示占位符
                        checkAndShowHistoryPlaceholder();
                    }, 500);
                }, 300);
            }
            
            // 无论成功与否，都恢复按钮状态（虽然模态框会关闭，但为了安全起见）
            newConfirmBtn.disabled = false;
            newConfirmBtn.innerHTML = '<i class="bi bi-trash"></i> 确认删除';
            
            try {
                // 尝试关闭模态框
                bootstrapModal.hide();
            } catch (e) {
                console.error('关闭模态框时出错:', e);
                // 如果无法通过Bootstrap API关闭，则尝试直接隐藏元素
                confirmModal.style.display = 'none';
                document.body.classList.remove('modal-open');
                const backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach(backdrop => backdrop.remove());
            }
        });
        
        // 监听模态框隐藏事件，确保按钮状态重置
        confirmModal.addEventListener('hidden.bs.modal', function () {
            const btn = document.getElementById('confirmDeleteBtn');
            if (btn) {
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-trash"></i> 确认删除';
            }
        });
        
        // 显示模态框
        try {
            bootstrapModal.show();
        } catch (e) {
            console.error('显示模态框时出错:', e);
            // 如果无法显示模态框，回退到原生confirm
            if (confirm('确定要删除这条历史记录吗？此操作无法撤销。')) {
                deleteHistoryRecord(recordId).then(success => {
                    if (success && historyItemElement) {
                        historyItemElement.style.opacity = '0';
                        setTimeout(() => historyItemElement.remove(), 300);
                        checkAndShowHistoryPlaceholder();
                    }
                });
            }
        }
    }

    // Midjourney specific elements (ensure these IDs match your HTML)
    // const mjImagineBaseImageDropZone = document.getElementById('mjImagineBaseImageDropZone'); // 已提前
    // const mjImagineBaseImageInput = document.getElementById('mjImagineBaseImageInput'); // 已提前
    // const mjImagineBaseImagePreviewContainer = document.getElementById('mjImagineBaseImagePreviewContainer'); // 已提前
    // const mjImagineBaseImagePreviewImg = document.getElementById('mjImagineBaseImagePreviewImg'); // 已提前
    // const mjRemoveImagineBaseImageBtn = document.getElementById('mjRemoveImagineBaseImageBtn'); // 已提前
    // let mjImagineBase64 = null; // 已提前

    // Helper function to initialize Midjourney Image Uploader
    function escapeHTML(str) {
        if (!str) return '';
        const div = document.createElement('div');
        div.appendChild(document.createTextNode(str));
        return div.innerHTML;
    }

    // ========== 新增：从API动态加载模型选择器 ==========
    async function populateModelSelectorFromAPI() {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('未找到认证令牌，无法加载模型列表。');
                        return;
            }

            const response = await fetch(`${API_BASE_URL}/api/ui-components/ai_generate`, {
                headers: { 'Authorization': `Bearer ${token}` }
            });

            if (!response.ok) throw new Error('从服务器获取模型数据失败');

                    const data = await response.json();
            if (!data.success || !data.components) throw new Error('服务器返回的模型数据格式不正确');

            const modelComponent = data.components.find(c => c.component_key === 'ai_generate_model_selector');

            if (!modelComponent || !modelComponent.options) {
                console.error('API响应中未找到模型选择器的配置信息。');
                return;
            }

            const aiModelSelectBtn = document.getElementById('aiModelSelectBtn');
            const aiModelSelectMenu = document.getElementById('aiModelSelectMenu');
            const aiModelSelectValue = document.getElementById('aiModelSelectValue');

            if (!aiModelSelectBtn || !aiModelSelectMenu || !aiModelSelectValue) {
                console.error('模型选择器的相关DOM元素未找到。');
                return;
            }

            // 清空现有的硬编码选项
            aiModelSelectMenu.innerHTML = '';

            // 使用从数据库获取的数据填充选项
            modelComponent.options.forEach(option => {
                const li = document.createElement('li');
                const a = document.createElement('a');
                a.className = 'dropdown-item';
                a.href = '#';
                a.dataset.value = option.value;
                a.textContent = option.label;
                li.appendChild(a);
                aiModelSelectMenu.appendChild(li);
            });

            // 设置默认值
            const defaultOption = modelComponent.options.find(opt => opt.value === modelComponent.default_value) || modelComponent.options[0];
            if (defaultOption) {
                aiModelSelectBtn.textContent = defaultOption.label;
                aiModelSelectValue.value = defaultOption.value;
                const defaultMenuItem = aiModelSelectMenu.querySelector(`a[data-value="${defaultOption.value}"]`);
                if (defaultMenuItem) {
                    defaultMenuItem.classList.add('active');
                }
            }
            
            // 手动触发change事件，以确保UI的其他部分（如参数显隐）能够根据默认模型正确更新
            aiModelSelectValue.dispatchEvent(new Event('change', { bubbles: true }));

        } catch (error) {
            console.error('动态加载模型选择器失败:', error);
            const aiModelSelectBtn = document.getElementById('aiModelSelectBtn');
            if (aiModelSelectBtn) {
                aiModelSelectBtn.textContent = '模型加载失败';
                aiModelSelectBtn.disabled = true;
            }
        }
    }
    // ========== 结束新增 ==========

    // 监听所有刷新按钮
    aiHistoryListContainer.querySelectorAll('.refresh-mj-status-btn').forEach(btn => {
        btn.addEventListener('click', async function() {
            const mjTaskId = this.getAttribute('data-mj-task-id');
            const historyId = this.getAttribute('data-history-id');
            if (!mjTaskId) return;

            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>刷新中...';

            try {
                const response = await fetch(`${API_BASE_URL}/api/mj/task/${mjTaskId}/fetch`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                const data = await response.json();
                // 如果有图片，刷新整个历史记录列表（或只刷新当前卡片）
                if (data.status === "SUCCESS" && data.imageUrl) {
                    // 推荐：直接重新加载历史记录
                    loadHistory(currentHistoryPage, false);
                } else if (data.status === "FAILED") {
                    // 推荐：直接重新加载历史记录
                    loadHistory(currentHistoryPage, false);
                } else {
                    // 还在处理中，恢复按钮
                    this.disabled = false;
                    this.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新状态';
                }
            } catch (e) {
                this.disabled = false;
                this.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新状态';
                alert('刷新失败，请稍后重试');
            }
        });
    });

    // 旧的、硬编码的函数，我们不再需要它，所以注释掉
    /*
    function initializeModelOptions() {
        const aiModelSelect = document.getElementById('aiModelSelect');
        if (!aiModelSelect) {
            console.error('未找到模型选择器元素');
            return;
        }

        // 定义所有可用的模型选项
        const modelOptions = [
            { value: 'super_image_3', text: '超级生图 V3.0' },
            { value: 'comfyui_imagefx_advanced', text: '高级生图' },
            { value: 'midjourney', text: 'Midjourney' },
            { value: 'super_image_2', text: '超级生图 V2.1' }
        ];

        // 清除现有的选项（包括默认的placeholder选项）
        while (aiModelSelect.options.length > 0) {
            aiModelSelect.remove(0);
        }

        // 添加所有模型选项
        modelOptions.forEach((option, index) => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.text = option.text;
            // 设置超级生图 3.0 为默认选中
            if (option.value === 'super_image_3') {
                optionElement.selected = true;
            }
            aiModelSelect.appendChild(optionElement);
        });

        
        // 触发 change 事件以更新相关 UI
        const event = new Event('change');
        aiModelSelect.dispatchEvent(event);
    }
    */

    // 初始化时调用新的API函数，而不是旧的
    populateModelSelectorFromAPI();
    
    // +++ 新增：将高级生图比例选择下拉组件的初始化移入主逻辑作用域 +++
    if (typeof initializeCustomDropdown === 'function') {
        comfyAspectRatioDropdown = initializeCustomDropdown(
            'comfyAspectRatioBtn',
            'comfyAspectRatioMenu',
            'comfyAspectRatioValue',
            (value, text) => {
                // 如果有其他需要处理的逻辑可以放在这里
            }
        );
    }
    // +++ 结束新增 +++
    
});
// ========== AI提示词助手功能 ========== //
(function() {
    // DOM元素
    const helperBtn = document.getElementById('aiPromptHelperBtn');
    const helperModal = document.getElementById('aiPromptHelperModal');
    const helperChatArea = document.getElementById('aiPromptHelperChatArea');
    const helperInput = document.getElementById('aiPromptHelperInput');
    const helperSendBtn = document.getElementById('aiPromptHelperSendBtn');
    const helperFillBtn = document.getElementById('aiPromptHelperFillBtn');
    const aiPromptInput = document.getElementById('aiPromptInput');

    if (!helperBtn || !helperModal || !helperChatArea || !helperInput || !helperSendBtn || !helperFillBtn || !aiPromptInput) return;

    let chatHistory = [];
    let lastAIContent = '';
    let isLoading = false;

    // 打开弹窗
    helperBtn.addEventListener('click', () => {
        helperInput.value = aiPromptInput.value.trim();
        helperChatArea.innerHTML = '<div class="text-muted text-center">和AI助手对话，让它帮你优化、扩写、翻译提示词...</div>';
        chatHistory = [];
        lastAIContent = '';
        helperFillBtn.disabled = true;
        const modal = bootstrap.Modal.getOrCreateInstance(helperModal);
        modal.show();
    });

    // 发送消息
    async function sendMessage() {
        const userMsg = helperInput.value.trim();
        if (!userMsg || isLoading) return;
        isLoading = true;
        helperSendBtn.disabled = true;
        helperInput.disabled = true;
        renderChat([...chatHistory, {role: 'user', content: userMsg}], true);
        chatHistory.push({role: 'user', content: userMsg});
        helperInput.value = '';
        try {
            const res = await fetch(window.ZHIPU_API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + window.ZHIPU_API_KEY
                },
                body: JSON.stringify({
                    model: 'glm-4-flash',
                    messages: [
                        { role: 'system', content: '你是一个AI提示词助手。你的主要任务是帮助用户优化、扩写或翻译用于AI绘画的提示词。请始终围绕这个核心功能进行对话和提供帮助。当提供最终优化后的提示词时，请使用特殊标记将其包裹，格式为：【优化后提示词】这里是纯粹的、可以直接使用的优化后提示词，不包含任何解释、编号或额外对话【结束提示词】。如果用户的问题与提示词优化无关，请委婉地引导用户回到主题。' },
                        ...chatHistory.map(m => ({role: m.role, content: m.content}))
                    ],
                    temperature: 0.7
                })
            });
            if (!res.ok) throw new Error('AI接口请求失败');
            const data = await res.json();
            const aiMsg = data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content ? data.choices[0].message.content : 'AI未返回内容';
            
            // 新增：提取被特殊标记包裹的优化后提示词
            let extractedPrompt = aiMsg; // 默认为完整消息
            const regex = /【优化后提示词】([\s\S]*?)【结束提示词】/g;
            let match;
            let lastMatchContent = null;
            while ((match = regex.exec(aiMsg)) !== null) {
               lastMatchContent = match[1].trim(); // 获取捕获组1的内容并去除首尾空格
            }
            if (lastMatchContent !== null) {
               extractedPrompt = lastMatchContent;
            }
            // 更新 lastAIContent 为提取到的提示词（或完整消息，如果未找到标记）
            lastAIContent = extractedPrompt; 
            
            // chatHistory 仍然使用完整的 AI 回复，以便在聊天区域显示全部内容
            chatHistory.push({role: 'assistant', content: aiMsg}); 
            renderChat(chatHistory);
            helperFillBtn.disabled = false;
        } catch (e) {
            chatHistory.push({role: 'assistant', content: '【AI助手出错】' + e.message});
            renderChat(chatHistory);
        } finally {
            isLoading = false;
            helperSendBtn.disabled = false;
            helperInput.disabled = false;
            helperInput.focus();
        }
    }

    // 渲染对话
    function renderChat(history, loading=false) {
        helperChatArea.innerHTML = '';
        if (!history.length) {
            helperChatArea.innerHTML = '<div class="text-muted text-center">和AI助手对话，让它帮你优化、扩写、翻译提示词...</div>';
            return;
        }
        history.forEach(msg => {
            const div = document.createElement('div');
            div.className = 'mb-2';
            if (msg.role === 'user') {
                div.innerHTML = `<div class="text-end"><span class="badge bg-primary">我</span> <span class="ms-2">${escapeHTML(msg.content)}</span></div>`;
            } else {
                div.innerHTML = `<div class="text-start"><span class="badge bg-success">AI</span> <span class="ms-2">${escapeHTML(msg.content)}</span></div>`;
            }
            helperChatArea.appendChild(div);
        });
        if (loading) {
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'text-center text-info';
            loadingDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>AI助手思考中...';
            helperChatArea.appendChild(loadingDiv);
        }
        helperChatArea.scrollTop = helperChatArea.scrollHeight;
    }

    // 发送按钮/回车
    helperSendBtn.addEventListener('click', sendMessage);
    
    // 新增：标记是否处于中文输入状态
    let isComposing = false;
    
    // 监听中文输入法开始输入
    helperInput.addEventListener('compositionstart', () => {
        isComposing = true;
    });
    
    // 监听中文输入法结束输入
    helperInput.addEventListener('compositionend', () => {
        isComposing = false;
    });
    
    // 修改回车键处理逻辑，添加对中文输入状态的判断
    helperInput.addEventListener('keydown', e => {
        if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
            e.preventDefault();
            sendMessage();
        }
    });

    // 一键填入
    helperFillBtn.addEventListener('click', () => {
        if (lastAIContent) {
            aiPromptInput.value = lastAIContent;
            const modal = bootstrap.Modal.getOrCreateInstance(helperModal);
            modal.hide();
        }
    });

    // 关闭弹窗时清空历史
    helperModal.addEventListener('hidden.bs.modal', () => {
        chatHistory = [];
        lastAIContent = '';
        helperFillBtn.disabled = true;
        helperChatArea.innerHTML = '<div class="text-muted text-center">和AI助手对话，让它帮你优化、扩写、翻译提示词...</div>';
    });

    // HTML转义
    function escapeHTML(str) {
        return str.replace(/[&<>"']/g, function (c) {
            return {'&':'&amp;','<':'&lt;','>':'&gt;','"':'&quot;','\'':'&#39;'}[c];
        });
    }
})();
// ========== AI提示词助手功能 END ========== //
