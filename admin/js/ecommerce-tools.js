/**
 * 电商专栏工具集
 * 包含印花提取等电商相关AI功能
 */

document.addEventListener('DOMContentLoaded', function() {
    // API基础URL
    const API_BASE_URL = 'https://caca.yzycolour.top';
    
    // 获取认证令牌
    function getAuthToken() {
        return localStorage.getItem('token') || '';
    }
    
    // 创建带认证的请求头
    function createAuthHeaders() {
        const token = getAuthToken();
        return {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
    }
    
    // DOM元素
    const ecommerceToolsGrid = document.getElementById('ecommerce-tools-grid');
    const ecommerceToolContent = document.getElementById('ecommerce-tool-content');
    const backToToolsBtn = document.getElementById('back-to-ecommerce-tools');
    const toolContentPanels = document.querySelectorAll('.ecommerce-tool-content-panel');
    
    // 印花提取相关元素
    const patternExtractionDropZone = document.getElementById('patternExtractionDropZone');
    const patternExtractionInput = document.getElementById('patternExtractionInput');
    const patternExtractionPreviewContainer = document.getElementById('patternExtractionPreviewContainer');
    const patternExtractionPreviewImg = document.getElementById('patternExtractionPreviewImg');
    const removePatternExtractionImageBtn = document.getElementById('removePatternExtractionImage');
    const extractPatternBtn = document.getElementById('extractPatternBtn');
    const patternExtractionLoading = document.getElementById('patternExtractionLoading');
    const patternExtractionResultContainer = document.getElementById('patternExtractionResultContainer');
    const patternExtractionResultImg = document.getElementById('patternExtractionResultImg');
    const downloadPatternBtn = document.getElementById('downloadPatternBtn');
    const patternExtractionError = document.getElementById('patternExtractionError');
    const patternExtractionPlaceholder = document.getElementById('patternExtractionPlaceholder');
    
    // 选项复选框
    const enhancePatternCheck = document.getElementById('enhancePatternCheck');
    const removeBackgroundCheck = document.getElementById('removeBackgroundCheck');
    const vectorizePatternCheck = document.getElementById('vectorizePatternCheck');
    
    // 历史记录相关元素
    const patternExtractionHistoryList = document.getElementById('patternExtractionHistoryList');
    const patternExtractionHistoryPlaceholder = document.getElementById('patternExtractionHistoryPlaceholder');
    const patternExtractionHistoryLoadMoreContainer = document.getElementById('patternExtractionHistoryLoadMoreContainer');
    const loadMorePatternExtractionHistoryBtn = document.getElementById('loadMorePatternExtractionHistoryBtn');
    
    // 状态变量
    let currentPatternImageFile = null;
    let currentPatternImageBase64 = null;
    let patternExtractionTaskId = null;
    let patternExtractionPollingInterval = null;
    let patternExtractionHistoryPage = 1;
    const patternExtractionHistoryPageSize = 6;
    
    // 初始化
    initEcommerceTools();
    
    /**
     * 初始化电商工具集
     */
    function initEcommerceTools() {
        loadEcommerceTools();
        
        // 绑定返回按钮事件
        if (backToToolsBtn) {
            backToToolsBtn.addEventListener('click', function() {
                hideAllToolContents();
            });
        }
        
        // 初始化印花提取功能
        initPatternExtraction();
    }
    
    /**
     * 显示指定工具的内容
     * @param {string} tool - 工具标识
     */
    function showToolContent(tool) {
        // 隐藏工具网格
        ecommerceToolsGrid.closest('.row').style.display = 'none';
        
        // 显示工具内容区域
        ecommerceToolContent.style.display = 'block';
        
        // 隐藏所有工具内容面板
        toolContentPanels.forEach(panel => {
            panel.style.display = 'none';
        });
        
        // 显示指定工具的内容面板
        const toolPanel = document.getElementById(`${tool}-content`);
        if (toolPanel) {
            toolPanel.style.display = 'block';
        }
    }
    
    /**
     * 隐藏所有工具内容，返回工具网格
     */
    function hideAllToolContents() {
        // 显示工具网格
        ecommerceToolsGrid.closest('.row').style.display = 'block';
        
        // 隐藏工具内容区域
        ecommerceToolContent.style.display = 'none';
        
        // 隐藏所有工具内容面板
        toolContentPanels.forEach(panel => {
            panel.style.display = 'none';
        });
    }
    
    /**
     * 初始化印花提取功能
     */
    function initPatternExtraction() {
        // 设置图片上传
        setupPatternImageUpload();
        
        // 设置提取按钮
        setupExtractPatternButton();
        
        // 设置尺寸选择下拉框
        setupPatternSizeDropdown();
        
        // 加载历史记录
        loadPatternExtractionHistory(1);
    }
    
    /**
     * 设置印花提取尺寸下拉框
     */
    function setupPatternSizeDropdown() {
        const patternSizeBtn = document.getElementById('patternSizeBtn');
        const patternSizeMenu = document.getElementById('patternSizeMenu');
        const patternSizeInput = document.getElementById('patternSize');
        
        if (patternSizeMenu) {
            // 为每个选项添加点击事件
            const sizeOptions = patternSizeMenu.querySelectorAll('.dropdown-item');
            sizeOptions.forEach(option => {
                option.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 更新按钮文本
                    const selectedText = this.textContent.trim();
                    patternSizeBtn.textContent = selectedText;
                    
                    // 更新隐藏输入值
                    const selectedValue = this.getAttribute('data-value');
                    patternSizeInput.value = selectedValue;
                    
                    // 更新活动状态
                    sizeOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        }
    }
    
    /**
     * 设置印花图片上传
     */
    function setupPatternImageUpload() {
        // 点击上传区域触发文件选择
        patternExtractionDropZone.addEventListener('click', () => {
            patternExtractionInput.click();
        });
        
        // 文件选择变化事件
        patternExtractionInput.addEventListener('change', handlePatternImageSelect);
        
        // 拖放事件
        patternExtractionDropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            patternExtractionDropZone.classList.add('dragging');
        });
        
        patternExtractionDropZone.addEventListener('dragleave', () => {
            patternExtractionDropZone.classList.remove('dragging');
        });
        
        patternExtractionDropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            patternExtractionDropZone.classList.remove('dragging');
            if (e.dataTransfer.files.length > 0) {
                handlePatternImageFile(e.dataTransfer.files[0]);
            }
        });
        
        // 粘贴事件
        document.addEventListener('paste', (e) => {
            // 只有当电商专栏标签页处于活动状态时才处理粘贴事件
            if (document.getElementById('ecommerce-tab').classList.contains('active')) {
                const items = e.clipboardData.items;
                for (let i = 0; i < items.length; i++) {
                    if (items[i].type.indexOf('image') !== -1) {
                        const file = items[i].getAsFile();
                        handlePatternImageFile(file);
                        break;
                    }
                }
            }
        });
        
        // 移除图片按钮
        removePatternExtractionImageBtn.addEventListener('click', () => {
            removePatternImage();
        });
    }
    
    /**
     * 处理选择的印花图片
     */
    function handlePatternImageSelect(e) {
        const file = e.target.files[0];
        if (file) {
            handlePatternImageFile(file);
        }
        // 清空input的值，确保下次选择相同文件仍能触发change事件
        e.target.value = '';
    }
    
    /**
     * 处理印花图片文件
     */
    async function handlePatternImageFile(file) {
        if (!file.type.startsWith('image/')) {
            showPatternExtractionError('请上传图片文件');
            return;
        }
        
        currentPatternImageFile = file;
        
        try {
            // 转换为Base64
            currentPatternImageBase64 = await toBase64(file);
            
            // 显示预览
            patternExtractionPreviewImg.src = currentPatternImageBase64;
            patternExtractionPreviewContainer.style.display = 'block';
            patternExtractionDropZone.classList.add('d-none');
            
            // 启用提取按钮
            extractPatternBtn.disabled = false;
            
            // 清除错误信息
            showPatternExtractionError('');
        } catch (error) {
            showPatternExtractionError('图片预览失败: ' + error.message);
            removePatternImage();
        }
    }
    
    /**
     * 移除印花图片
     */
    function removePatternImage() {
        currentPatternImageFile = null;
        currentPatternImageBase64 = null;
        patternExtractionPreviewContainer.style.display = 'none';
        patternExtractionPreviewImg.src = '';
        
        // 重新显示上传区域
        patternExtractionDropZone.classList.remove('d-none');
        
        // 禁用提取按钮
        extractPatternBtn.disabled = true;
    }
    
    /**
     * 设置提取印花按钮
     */
    function setupExtractPatternButton() {
        extractPatternBtn.addEventListener('click', async () => {
            if (!currentPatternImageFile) {
                showPatternExtractionError('请先上传图片');
                return;
            }
            
            // 获取勾选选项
            const enhance = enhancePatternCheck.checked;
            const removeBackground = removeBackgroundCheck.checked;
            const vectorize = vectorizePatternCheck.checked;

            // 显示加载状态
            patternExtractionPlaceholder.style.display = 'none';
            patternExtractionResultContainer.style.display = 'none';
            patternExtractionError.style.display = 'none';
            patternExtractionLoading.style.display = 'block';
            extractPatternBtn.disabled = true;
            extractPatternBtn.querySelector('.spinner-border').style.display = 'inline-block';
            
            // 获取尺寸设置
            const sizeOption = document.getElementById('patternSize').value;
            const [width, height] = sizeOption.split('x').map(Number);
            
            try {
                // 准备请求数据
                const formData = new FormData();
                formData.append('image', currentPatternImageFile);
                formData.append('enhance', enhance);
                formData.append('removeBackground', 'false'); // 总是使用false，我们会自己处理
                formData.append('vectorize', 'false'); // 总是使用false，我们会自己处理
                formData.append('width', width);
                formData.append('height', height);
                
                // 先发送印花提取请求，获取基本处理结果
                const response = await fetch(`${API_BASE_URL}/api/pattern-extraction/extract`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${getAuthToken()}`
                    },
                    body: formData
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || '提取请求失败');
                }
                
                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.message || '提取请求失败');
                }
                
                // 获取任务ID并开始轮询任务状态
                patternExtractionTaskId = result.task.id;
                
                // 开始轮询任务状态
                await processExtractedPattern(patternExtractionTaskId, removeBackground, vectorize);
                
            } catch (error) {
                showPatternExtractionError('提取印花失败: ' + (error.message || '未知错误'));
                // 恢复按钮状态
                extractPatternBtn.disabled = false;
                extractPatternBtn.querySelector('.spinner-border').style.display = 'none';
                patternExtractionLoading.style.display = 'none';
            }
        });
    }
    
    /**
     * 处理提取的印花，并根据选项执行抠图和矢量化
     */
    async function processExtractedPattern(taskId, shouldRemoveBackground, shouldVectorize) {
        patternExtractionLoading.querySelector('.progress-message').textContent = '印花提取中，请稍候...';
        
        // 轮询印花提取任务直到完成
        let extractedImageUrl = null;
        let completed = false;
        
        // 清除之前的轮询
        if (patternExtractionPollingInterval) {
            clearInterval(patternExtractionPollingInterval);
        }
        
        try {
            // 轮询直到任务完成
            while (!completed) {
                const response = await fetch(`${API_BASE_URL}/api/pattern-extraction/task/${taskId}`, {
                    headers: createAuthHeaders()
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || '获取任务状态失败');
                }
                
                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.message || '获取任务状态失败');
                }
                
                // 更新进度
                if (result.progress) {
                    const progressBar = patternExtractionLoading.querySelector('.progress-bar');
                    if (progressBar) {
                        progressBar.style.width = `${result.progress * 100}%`;
                        progressBar.setAttribute('aria-valuenow', Math.round(result.progress * 100));
                    }
                }
                
                // 根据任务状态处理
                if (result.status === 'completed') {
                    extractedImageUrl = result.result.image_url;
                    completed = true;
                } else if (result.status === 'error' || result.status === 'timeout') {
                    throw new Error('印花提取失败: ' + (result.message || '处理失败'));
                } else {
                    // 更新加载消息
                    patternExtractionLoading.querySelector('.progress-message').textContent = result.message || '印花提取中...';
                    // 等待3秒后再次检查
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }
            }
            
            // 基本印花提取完成，获取到了图片URL
            
            // 如果用户选择了去背景，则调用抠图服务
            if (shouldRemoveBackground && extractedImageUrl) {
                patternExtractionLoading.querySelector('.progress-message').textContent = '正在去除背景...';
                
                try {
                    // 使用ComfyUI去背景API
                    const response = await fetch(`${API_BASE_URL}/api/pattern-extraction/remove-background-comfyui`, {
                        method: 'POST',
                        headers: {
                            ...createAuthHeaders(),
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ imageUrl: extractedImageUrl })
                    });
                    
                    if (!response.ok) {
                        throw new Error('背景去除失败');
                    }
                    
                    const result = await response.json();
                    if (result.success && result.imageUrl) {
                        extractedImageUrl = result.imageUrl;
                    } else {
                        console.warn('背景去除处理失败，将使用原始图片');
                    }
                } catch (error) {
                    console.warn('背景去除处理失败:', error);
                    // 继续执行，不阻止用户体验
                }
            }
            
            // 如果用户选择了矢量化，则调用矢量化服务
            if (shouldVectorize && extractedImageUrl) {
                patternExtractionLoading.querySelector('.progress-message').textContent = '正在转换为矢量图...';
                
                // 获取图片文件
                const imageResponse = await fetch(extractedImageUrl);
                const imageBlob = await imageResponse.blob();
                const imageFile = new File([imageBlob], 'pattern_for_vectorize.png', { type: 'image/png' });
                
                // 调用矢量化服务
                const formData = new FormData();
                formData.append('file', imageFile);
                
                const vectorizeResponse = await fetch(`${API_BASE_URL}/api/images/vectorize`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${getAuthToken()}`
                    },
                    body: formData
                });
                
                if (!vectorizeResponse.ok) {
                    throw new Error('矢量化失败');
                }
                
                const vectorizeResult = await vectorizeResponse.json();
                if (vectorizeResult.svgUrl) {
                    extractedImageUrl = vectorizeResult.svgUrl;
                } else {
                    console.warn('矢量化处理失败，将使用原始图片');
                }
            }
            
            // 显示最终结果
            patternExtractionResultImg.src = extractedImageUrl;
            downloadPatternBtn.href = extractedImageUrl;
            
            // 根据最终图片类型设置下载文件名
            const fileExt = shouldVectorize ? 'svg' : 'png';
            downloadPatternBtn.download = `pattern-${Date.now()}.${fileExt}`;
            
            patternExtractionLoading.style.display = 'none';
            patternExtractionResultContainer.style.display = 'block';
            
            // 更新印花提取历史记录中的图像
            if (shouldRemoveBackground || shouldVectorize) {
                try {
                    // 构建更新请求
                    const updateData = {
                        taskId: taskId,
                        newImageUrl: extractedImageUrl,
                        processingInfo: {
                            removeBackground: shouldRemoveBackground,
                            vectorize: shouldVectorize
                        }
                    };
                    
                    // 发送更新请求
                    await fetch(`${API_BASE_URL}/api/pattern-extraction/update-result`, {
                        method: 'POST',
                        headers: {
                            ...createAuthHeaders(),
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(updateData)
                    });
                } catch (updateError) {
                    console.warn('更新印花提取结果失败:', updateError);
                    // 继续执行，不阻止用户体验
                }
            }
            
            // 刷新历史记录
            loadPatternExtractionHistory(1);
            
        } catch (error) {
            console.error('处理印花失败:', error);
            showPatternExtractionError('处理失败: ' + (error.message || '未知错误'));
        } finally {
            // 恢复按钮状态
            extractPatternBtn.disabled = false;
            extractPatternBtn.querySelector('.spinner-border').style.display = 'none';
        }
    }
    
    /**
     * 加载印花提取历史记录
     */
    async function loadPatternExtractionHistory(page, append = false) {
        try {
            // 显示加载状态
            if (!append) {
                patternExtractionHistoryPlaceholder.style.display = 'block';
                patternExtractionHistoryList.innerHTML = '';
            }
            
            const response = await fetch(`${API_BASE_URL}/api/pattern-extraction/history?page=${page}&pageSize=${patternExtractionHistoryPageSize}`, {
                headers: createAuthHeaders()
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || '加载历史记录失败');
            }
            
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || '加载历史记录失败');
            }
            
            // 隐藏占位符
            patternExtractionHistoryPlaceholder.style.display = 'none';
            
            // 渲染历史记录
            renderPatternExtractionHistory(data, append);
            
            // 更新当前页码
            patternExtractionHistoryPage = data.page;
            
            // 更新加载更多按钮状态
            patternExtractionHistoryLoadMoreContainer.style.display = data.hasMore ? 'block' : 'none';
            
            // 绑定加载更多按钮事件
            if (data.hasMore && loadMorePatternExtractionHistoryBtn) {
                loadMorePatternExtractionHistoryBtn.onclick = () => {
                    loadPatternExtractionHistory(data.page + 1, true);
                };
            }
        } catch (error) {
            if (!append) {
                patternExtractionHistoryList.innerHTML = '<p class="text-muted text-center">加载历史记录失败</p>';
                patternExtractionHistoryPlaceholder.style.display = 'none';
            }
            console.error('加载印花提取历史记录失败:', error);
        }
    }
    
    /**
     * 渲染印花提取历史记录
     */
    function renderPatternExtractionHistory(data, append) {
        // 如果没有数据且不是追加模式，显示空状态
        if (data.records.length === 0 && !append) {
            patternExtractionHistoryList.innerHTML = '<p class="text-muted text-center w-100">暂无印花提取历史记录</p>';
            return;
        }
        
        // 如果是第一页且不追加，清空列表
        if (data.page === 1 && !append) {
            patternExtractionHistoryList.innerHTML = '';
        }
        
        // 添加历史项
        data.records.forEach(item => {
            const historyItem = createPatternExtractionHistoryItem(item);
            patternExtractionHistoryList.appendChild(historyItem);
        });
    }
    
    /**
     * 创建印花提取历史记录项
     */
    function createPatternExtractionHistoryItem(item) {
        // 创建历史记录项元素
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';
        historyItem.id = `pattern-history-item-${item.id}`;
        
        // 解析处理信息
        let processingInfo = {};
        try {
            if (item.processing_info && typeof item.processing_info === 'string') {
                processingInfo = JSON.parse(item.processing_info);
            } else if (item.processing_info && typeof item.processing_info === 'object') {
                processingInfo = item.processing_info;
            }
        } catch (e) {
            console.warn('解析处理信息失败:', e);
        }
        
        // 添加处理标签
        let processingTags = '';
        if (processingInfo.removeBackground) {
            processingTags += '<span class="badge bg-success me-1">已去背</span>';
        }
        if (processingInfo.vectorize) {
            processingTags += '<span class="badge bg-info me-1">已矢量化</span>';
        }
        
        // 设置下载按钮的文件扩展名
        const fileExt = processingInfo.vectorize ? 'svg' : 'png';
        const downloadFilename = `pattern-${item.id}.${fileExt}`;
        
        // 设置历史记录项内容
        historyItem.innerHTML = `
            <div class="history-images">
                <img src="${item.result_image_url}" alt="提取的印花" class="img-fluid rounded cursor-pointer">
            </div>
            <div class="history-item-content">
                <p class="history-item-title">
                    印花图案
                    ${processingTags}
                </p>
                <span class="history-item-date">${formatDate(item.created_at)}</span>
            </div>
            <div class="history-actions">
                <a href="${item.result_image_url}" class="btn btn-sm btn-outline-primary me-1" download="${downloadFilename}">
                    <i class="bi bi-download"></i> 下载
                </a>
                <button class="btn btn-sm btn-outline-success me-1 remove-bg-btn" data-url="${item.result_image_url}" ${processingInfo.removeBackground ? 'disabled' : ''}>
                    <i class="bi bi-scissors"></i> ${processingInfo.removeBackground ? '已抠图' : '抠图'}
                </button>
                <button class="btn btn-sm btn-outline-info me-1 vectorize-btn" data-url="${item.result_image_url}" ${processingInfo.vectorize ? 'disabled' : ''}>
                    <i class="bi bi-bezier"></i> ${processingInfo.vectorize ? '已转矢量' : '转矢量'}
                </button>
                <button class="btn btn-sm btn-outline-danger delete-history-btn" data-id="${item.id}">
                    <i class="bi bi-trash"></i> 删除
                </button>
            </div>
        `;
        
        // 绑定事件
        const resultImage = historyItem.querySelector('.history-images img');
        resultImage.addEventListener('click', () => {
            window.open(item.result_image_url, '_blank');
        });
        
        const deleteBtn = historyItem.querySelector('.delete-history-btn');
        deleteBtn.addEventListener('click', () => {
            deletePatternExtractionHistory(item.id);
        });
        
        // 绑定抠图按钮事件
        const removeBgBtn = historyItem.querySelector('.remove-bg-btn');
        removeBgBtn.addEventListener('click', async () => {
            try {
                removeBgBtn.disabled = true;
                removeBgBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> 处理中...';
                
                // 获取图片URL
                const imageUrl = removeBgBtn.getAttribute('data-url');
                
                // 使用ComfyUI去背景API
                const response = await fetch(`${API_BASE_URL}/api/pattern-extraction/remove-background-comfyui`, {
                    method: 'POST',
                    headers: {
                        ...createAuthHeaders(),
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ imageUrl: imageUrl })
                });
                
                if (!response.ok) {
                    throw new Error('背景去除失败');
                }
                
                const result = await response.json();
                if (result.success && result.imageUrl) {
                    // 在新窗口打开抠图结果
                    window.open(result.imageUrl, '_blank');
                } else {
                    throw new Error('背景去除处理失败');
                }
            } catch (error) {
                console.error('抠图失败:', error);
                alert('抠图失败: ' + (error.message || '未知错误'));
            } finally {
                removeBgBtn.disabled = false;
                removeBgBtn.innerHTML = '<i class="bi bi-scissors"></i> 抠图';
            }
        });
        
        // 绑定矢量化按钮事件
        const vectorizeBtn = historyItem.querySelector('.vectorize-btn');
        vectorizeBtn.addEventListener('click', async () => {
            try {
                vectorizeBtn.disabled = true;
                vectorizeBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> 处理中...';
                
                // 获取图片文件
                const imageUrl = vectorizeBtn.getAttribute('data-url');
                const imageResponse = await fetch(imageUrl);
                const imageBlob = await imageResponse.blob();
                const imageFile = new File([imageBlob], 'pattern_for_vectorize.png', { type: 'image/png' });
                
                // 调用矢量化服务
                const formData = new FormData();
                formData.append('file', imageFile);
                
                const response = await fetch(`${API_BASE_URL}/api/images/vectorize`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${getAuthToken()}`
                    },
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error('矢量化失败');
                }
                
                const result = await response.json();
                if (result.svgUrl) {
                    // 在新窗口打开矢量化结果
                    window.open(result.svgUrl, '_blank');
                } else {
                    throw new Error('矢量化处理失败');
                }
            } catch (error) {
                console.error('矢量化失败:', error);
                alert('矢量化失败: ' + (error.message || '未知错误'));
            } finally {
                vectorizeBtn.disabled = false;
                vectorizeBtn.innerHTML = '<i class="bi bi-bezier"></i> 转矢量';
            }
        });
        
        return historyItem;
    }
    
    /**
     * 删除印花提取历史记录
     */
    async function deletePatternExtractionHistory(id) {
        if (!confirm('确定要删除这条历史记录吗？')) {
            return;
        }
        
        try {
            const response = await fetch(`${API_BASE_URL}/api/pattern-extraction/history/${id}`, {
                method: 'DELETE',
                headers: createAuthHeaders()
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || '删除历史记录失败');
            }
            
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.message || '删除历史记录失败');
            }
            
            // 从DOM中移除该记录
            const historyItem = document.getElementById(`pattern-history-item-${id}`);
            if (historyItem) {
                historyItem.remove();
            }
            
            // 如果列表为空，显示空状态
            if (patternExtractionHistoryList.children.length === 0) {
                patternExtractionHistoryList.innerHTML = '<p class="text-muted text-center w-100">暂无印花提取历史记录</p>';
            }
        } catch (error) {
            console.error('删除印花提取历史记录失败:', error);
            alert('删除历史记录失败: ' + (error.message || '未知错误'));
        }
    }
    
    /**
     * 显示印花提取错误消息
     */
    function showPatternExtractionError(message) {
        if (message && message.trim() !== '') {
            patternExtractionError.textContent = message;
            patternExtractionError.style.display = 'block';
        } else {
            patternExtractionError.style.display = 'none';
        }
    }
    
    /**
     * 将文件转换为Base64
     */
    function toBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }
    
    /**
     * 格式化日期
     */
    function formatDate(dateString) {
        if (!dateString) return '未知时间';
        
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    function loadEcommerceTools() {
        const toolsGrid = document.getElementById('ecommerce-tools-grid');
        const loadingElement = document.getElementById('ecommerce-tools-loading');
        
        // 获取电商工具列表
        fetch(`${API_BASE_URL}/api/ecommerce-tools`, {
            headers: createAuthHeaders()
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('获取电商工具列表失败');
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.tools && data.tools.length > 0) {
                // 移除加载指示器
                if (loadingElement) {
                    loadingElement.remove();
                }
                
                // 添加工具卡片
                data.tools.forEach(tool => {
                    const toolCard = createEcommerceToolCard(tool);
                    toolsGrid.appendChild(toolCard);
                });
            } else {
                // 显示空状态
                toolsGrid.innerHTML = '<div class="col-12 text-center py-5"><p class="text-light">暂无可用的电商工具</p></div>';
            }
        })
        .catch(error => {
            console.error('加载电商工具失败:', error);
            toolsGrid.innerHTML = '<div class="col-12 text-center py-5"><p class="text-danger">加载电商工具失败，请刷新页面重试</p></div>';
        });
    }

    // 创建电商工具卡片
    function createEcommerceToolCard(tool) {
        const col = document.createElement('div');
        col.className = 'col';
        
        col.innerHTML = `
            <div class="card h-100 ecommerce-tool-card cursor-pointer ecommerce-tool-btn" data-tool="${tool.tool_key}" style="background-color: rgba(0, 0, 0, 0.8); border: 1px solid #343a40; border-radius: 10px; overflow: hidden; transition: transform 0.3s ease, box-shadow 0.3s ease;">
                <div class="card-img-top-wrapper" style="position: relative; overflow: hidden;">
                    <img src="${tool.image_url}" class="card-img-top" alt="${tool.name}" style="object-fit: cover; height: 220px;">
                    <span class="position-absolute" style="bottom: 10px; left: 10px; background: rgba(0,0,0,0.6); color: white; padding: 2px 8px; border-radius: 4px;">电商工具</span>
                </div>
                <div class="card-body text-white" style="padding: 24px;">
                    <h5 class="card-title fw-bold" style="margin-bottom: 12px;">${tool.name}</h5>
                    <p class="card-text" style="opacity: 0.8;">${tool.description}</p>
                </div>
            </div>
        `;
        
        // 添加点击事件
        const cardElement = col.querySelector('.ecommerce-tool-btn');
        cardElement.addEventListener('click', function() {
            const toolKey = this.getAttribute('data-tool');
            showToolContent(toolKey);
        });
        
        return col;
    }
}); 