// admin/js/vectorize-image.js
document.addEventListener('DOMContentLoaded', () => {

    // --- Element References (Ensure all relevant ones are `let` if reassigned) ---
    const vectorizeTab = document.getElementById('vectorize-tab');
    const vectorizePane = document.getElementById('vectorize-pane');
    const dropZone = document.getElementById('vectorizeImageDropZone');
    const fileInput = document.getElementById('vectorizeImageInput');
    const previewContainer = document.getElementById('vectorizePreviewContainer');
    const previewImg = document.getElementById('vectorizePreviewImg');
    const removeImageBtn = document.getElementById('removeVectorizeImage');
    const startBtn = document.getElementById('startVectorizeBtn');
    // const loadingSpinner = startBtn.querySelector('.spinner-border'); // Not directly reassigned
    const resultContainer = document.getElementById('vectorizeResultContainer');
    const resultLoading = document.getElementById('vectorizeResultLoading');
    const resultContent = document.getElementById('vectorizeResultContent');
    let resultLink = document.getElementById('vectorizeResultLink'); // Changed to let
    const resultPreview = document.getElementById('vectorizeResultPreview');
    const errorAlert = document.getElementById('vectorizeErrorAlert');
    const resultsPlaceholder = document.getElementById('vectorizeResultsPlaceholder');
    const historyListContainer = document.getElementById('vectorizeHistoryList');
    const historyPlaceholder = document.getElementById('vectorizeHistoryPlaceholder');
    const historyLoadMoreContainer = document.getElementById('vectorizeHistoryLoadMoreContainer');
    const loadMoreHistoryBtn = document.getElementById('loadMoreVectorizeHistoryBtn');

    let currentFile = null;
    let featureCost = null;
    let currentVectorizeHistoryPage = 1;
    let totalVectorizeHistoryPages = 1;
    let isLoadingVectorizeHistory = false;

    // --- 新增：可复用的文件下载函数 (适配 SVG) --- START ---
    async function initiateFileDownload(fileUrl, clickedElement, defaultFilename = 'downloaded_file') {
        const originalLinkText = clickedElement.textContent;
        const originalTitle = clickedElement.title;
        let isMainResultLink = (clickedElement.id === 'vectorizeResultLink'); // Check if it's the main result link for this page

        if (isMainResultLink) {
            // For the main link, it might be an icon or specific text
            // For simplicity, we'll assume it can show "下载中..."
            clickedElement.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> 下载中...';
        } else {
            // For history links (which are images wrapped in <a>), just update title
             clickedElement.title = '下载中...';
        }
        if (clickedElement.disabled !== undefined) {
            clickedElement.disabled = true;
        }

        try {
            const response = await fetch(fileUrl);
            if (!response.ok) {
                throw new Error(`下载文件失败: ${response.status} ${response.statusText}`);
            }
            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob);
            const tempLink = document.createElement('a');
            tempLink.href = blobUrl;
            
            let filename = defaultFilename;
            if (defaultFilename.endsWith('.svg') && currentFile && currentFile.name) { // Better default for SVG based on original
                 const nameWithoutExt = currentFile.name.split('.').slice(0, -1).join('.');
                 if (nameWithoutExt) filename = `${nameWithoutExt}_vectorized.svg`;
            }
            // Try to get filename from ComfyUI URL if possible
            try {
                const urlObj = new URL(fileUrl);
                const nameFromParams = urlObj.searchParams.get("filename");
                if (nameFromParams) {
                    filename = nameFromParams; // This will have the timestamp and .svg from backend
                }
            } catch (e) {
                console.warn('无法从URL解析 ComfyUI 文件名，使用构造的文件名。');
            }
            tempLink.download = filename;

            document.body.appendChild(tempLink);
            tempLink.click();
            document.body.removeChild(tempLink);
            URL.revokeObjectURL(blobUrl);
        } catch (error) {
            console.error('下载文件时出错:', error);
            showError(`下载失败: ${error.message}`);
        } finally {
            if (isMainResultLink) {
                // Restore original innerHTML, assuming it was an icon + text
                // This needs to be robust if originalLinkText isn't reliable for complex HTML
                clickedElement.innerHTML = originalLinkText || '<i class="bi bi-download"></i> 下载SVG'; 
            }
            clickedElement.title = originalTitle;
            if (clickedElement.disabled !== undefined) {
                clickedElement.disabled = false;
            }
        }
    }
    // --- 可复用的文件下载函数 --- END ---

    // Event Listeners (no changes to these basic ones)
    dropZone.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', handleFileSelect);
    dropZone.addEventListener('dragover', handleDragOver);
    dropZone.addEventListener('dragleave', handleDragLeave);
    dropZone.addEventListener('drop', handleDrop);
    vectorizePane.addEventListener('paste', handlePaste);
    removeImageBtn.addEventListener('click', resetInput);
    startBtn.addEventListener('click', handleStartProcessing);
    // resultLink's listener is now set dynamically in handleStartProcessing

    if (vectorizeTab) {
        vectorizeTab.addEventListener('shown.bs.tab', handleTabShown);
    }
    if (loadMoreHistoryBtn) {
        loadMoreHistoryBtn.addEventListener('click', () => {
            if (!isLoadingVectorizeHistory && currentVectorizeHistoryPage < totalVectorizeHistoryPages) {
                loadVectorizeHistory(currentVectorizeHistoryPage + 1, true);
            }
        });
    }

    async function fetchFeatureCost() {
        try {
            const token = localStorage.getItem('token');
            if (!token) { console.warn('未找到认证令牌，无法获取功能成本'); return; }
            const response = await fetch('https://caca.yzycolour.top/api/features/cost?key=vectorize_image', {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            if (!response.ok) { console.error('获取功能成本失败:', response.status); return; }
            const data = await response.json();
            if (data.success && data.cost !== undefined) {
                featureCost = data.cost;
                updateButtonWithCost();
            }
        } catch (error) { console.error('获取功能成本出错:', error); }
    }
    
    function updateButtonWithCost() {
        if (startBtn && featureCost !== null) {
            const textNode = Array.from(startBtn.childNodes)
                .find(node => node.nodeType === Node.TEXT_NODE && node.textContent.trim().length > 0);
            if (textNode) {
                const originalText = textNode.textContent.trim();
                textNode.textContent = ` ${originalText} (${featureCost}积分)`;
            } else {
                const costBadge = document.createElement('span');
                costBadge.className = 'ms-1 badge bg-secondary';
                costBadge.textContent = `${featureCost}积分`;
                startBtn.appendChild(costBadge);
            }
        }
    }

    function handleFileSelect(event) {
        const files = event.target.files;
        if (files && files.length > 0) { processFile(files[0]); }
        fileInput.value = null;
    }
    function handleDragOver(event) { event.preventDefault(); dropZone.classList.add('drag-over'); }
    function handleDragLeave(event) { event.preventDefault(); dropZone.classList.remove('drag-over'); }
    function handleDrop(event) {
        event.preventDefault(); dropZone.classList.remove('drag-over');
        const files = event.dataTransfer.files;
        if (files && files.length > 0) { processFile(files[0]); }
    }
    function handlePaste(event) {
        const items = (event.clipboardData || window.clipboardData).items;
        for (let item of items) {
            if (item.kind === 'file' && item.type.startsWith('image/')) {
                const file = item.getAsFile(); processFile(file); event.preventDefault(); break;
            }
        }
    }

    function processFile(file) {
        if (!file.type.startsWith('image/')) { showError('请选择图片文件。'); return; }
        if (file.size > 5 * 1024 * 1024) { showError('图片文件大小不能超过 5MB。'); return; }
        currentFile = file;
        const reader = new FileReader();
        reader.onload = (e) => {
            previewImg.src = e.target.result;
            previewContainer.style.display = 'block';
            dropZone.style.display = 'none';
            startBtn.disabled = false;
            resetResultState();
        };
        reader.readAsDataURL(currentFile);
    }

    function resetInput() {
        currentFile = null; fileInput.value = null; previewImg.src = '';
        previewContainer.style.display = 'none'; dropZone.style.display = 'flex';
        startBtn.disabled = true; resetResultState();
    }

    function resetResultState() {
        resultContainer.style.display = 'none'; resultLoading.style.display = 'none';
        resultContent.style.display = 'none'; errorAlert.style.display = 'none';
        resultsPlaceholder.style.display = 'block';
        resultPreview.src = ''; resultPreview.style.display = 'none';
        // Clear previous event listener from resultLink
        const newResultLink = resultLink.cloneNode(true);
        if(resultLink.parentNode){
            resultLink.parentNode.replaceChild(newResultLink, resultLink);
        }
        resultLink = newResultLink; // Update the reference
        // Restore its original content if needed (e.g., text or icon)
        resultLink.innerHTML = '<i class="bi bi-download"></i> 下载SVG'; // Or from a data attribute
    }

    function showError(message) {
        errorAlert.textContent = message; errorAlert.style.display = 'block';
        resultContainer.style.display = 'none'; resultLoading.style.display = 'none';
        resultsPlaceholder.style.display = 'block';
    }

    function setProcessingState(isProcessing) {
        if (isProcessing) {
            startBtn.disabled = true;
            const icon = startBtn.querySelector('i'); if (icon) icon.style.display = 'none';
            const textNode = Array.from(startBtn.childNodes).find(node => node.nodeType === Node.TEXT_NODE && node.textContent.trim().length > 0);
            if (textNode) textNode.textContent = ' 正在转换...';
            errorAlert.style.display = 'none'; resultsPlaceholder.style.display = 'none';
        } else {
            startBtn.disabled = !currentFile;
            const icon = startBtn.querySelector('i'); if (icon) icon.style.display = 'inline-block';
            const textNode = Array.from(startBtn.childNodes).find(node => node.nodeType === Node.TEXT_NODE && node.textContent.trim().length > 0);
            if (textNode) textNode.textContent = '开始转换 SVG';
            resultLoading.style.display = 'none';
        }
    }

    // Removed old handleDownloadClick as its logic is now in initiateFileDownload

    async function loadImageFromUrl(imageUrl) {
        try {
            const response = await fetch(imageUrl);
            if (!response.ok) throw new Error(`Failed to fetch image: ${response.status}`);
            const blob = await response.blob();
            let filename = 'image_from_history.png'; // This is for raster image preview
            try { filename = new URL(imageUrl).pathname.split('/').pop() || filename; } catch (e) {}
            const imageFile = new File([blob], filename, { type: blob.type });
            processFile(imageFile);
        } catch (error) { console.error('[Vectorize] Error loading image from URL:', error); showError(`加载图片失败: ${error.message}`); }
    }

    function handleTabShown() {
        if (window.pendingImageData && window.pendingImageData.targetPaneId === 'vectorize-pane') {
            const imageUrl = window.pendingImageData.imageUrl;
            delete window.pendingImageData;
            loadImageFromUrl(imageUrl);
        } else {
            if (!historyListContainer.hasChildNodes() || historyPlaceholder.style.display !== 'none') {
                 loadVectorizeHistory(1, false);
            }
        }
        if (featureCost === null) { fetchFeatureCost(); }
    }

    async function handleStartProcessing() {
        if (!currentFile) { showError('请先选择一个图片文件。'); return; }
        setProcessingState(true);
        resultsPlaceholder.style.display = 'none'; errorAlert.style.display = 'none';
        resultContainer.style.display = 'block'; resultContent.style.display = 'none';
        resultPreview.style.display = 'none'; resultLoading.style.display = 'flex';

        const formData = new FormData(); formData.append('file', currentFile);
        try {
            const token = localStorage.getItem('token');
            if (!token) { showError('用户未登录，请先登录。'); setProcessingState(false); return; }
            const apiEndpoint = 'https://caca.yzycolour.top/api/images/vectorize';
            const response = await fetch(apiEndpoint, {
                method: 'POST', headers: { 'Authorization': `Bearer ${token}` }, body: formData
            });
            const data = await response.json();
            if (!response.ok) { console.error('[Vectorize] API Error:', data); throw new Error(data.error || `服务器错误: ${response.status}`); }

            if (data.svgUrl) { // This will be the ComfyUI URL
                resultPreview.src = data.svgUrl; // Set SVG preview source
                resultPreview.style.display = 'block';
                resultContent.style.display = 'block';
                resultLoading.style.display = 'none';

                // Setup download for the main result link
                const newResultLink = resultLink.cloneNode(true); // Clear old listeners
                if(resultLink.parentNode) {
                    resultLink.parentNode.replaceChild(newResultLink, resultLink);
                }
                resultLink = newResultLink;
                // Set default content if it was complex and not cloned properly
                resultLink.innerHTML = '<i class="bi bi-download"></i> 下载SVG'; 

                resultLink.addEventListener('click', (event) => {
                    event.preventDefault();
                    initiateFileDownload(data.svgUrl, resultLink, 'vectorized_image.svg');
                });

                if (typeof window.fetchCredits === 'function') { window.fetchCredits(); }
                loadVectorizeHistory(1, false);
            } else { throw new Error('API响应缺少svgUrl。'); }
        } catch (error) { console.error('[Vectorize] Error:', error); showError(`处理失败: ${error.message}`); resultsPlaceholder.style.display = 'block'; }
        finally { setProcessingState(false); }
    }

    async function loadVectorizeHistory(page = 1, append = false) {
        if (isLoadingVectorizeHistory) return;
        isLoadingVectorizeHistory = true;
        if (!append) {
            historyListContainer.innerHTML = ''; 
            historyPlaceholder.textContent = '加载历史记录中...';
            historyPlaceholder.style.display = 'block';
        } else { if(loadMoreHistoryBtn) { loadMoreHistoryBtn.disabled = true; loadMoreHistoryBtn.textContent = '加载中...'; }}
        if(historyLoadMoreContainer) historyLoadMoreContainer.style.display = 'none';
        const token = localStorage.getItem('token');
        if (!token) { historyPlaceholder.textContent = '请先登录以查看历史记录。'; isLoadingVectorizeHistory = false; return; }
        try {
            const apiBaseUrl = window.API_URL || 'https://caca.yzycolour.top'; 
            const historyUrl = `${apiBaseUrl}/vectorize/history?page=${page}&limit=8`;
            const response = await fetch(historyUrl, { headers: { 'Authorization': `Bearer ${token}` }});
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();
            if (data && data.history) {
                historyPlaceholder.style.display = 'none';
                renderVectorizeHistory(data.history);
                currentVectorizeHistoryPage = data.pagination.currentPage;
                totalVectorizeHistoryPages = data.pagination.totalPages;
                updateLoadMoreVectorizeButtonState();
            } else {
                if (!append) { historyPlaceholder.textContent = '暂无 SVG 转换历史记录。'; historyPlaceholder.style.display = 'block'; }
                updateLoadMoreVectorizeButtonState(); 
            }
        } catch (error) {
            console.error('[Vectorize History] Load error:', error);
            if (!append) { historyPlaceholder.textContent = '加载历史记录失败。'; historyPlaceholder.style.display = 'block'; }
            else { if(window.showToast) window.showToast('加载更多历史失败', 'error'); }
            updateLoadMoreVectorizeButtonState(); 
        } finally { isLoadingVectorizeHistory = false; if (append && loadMoreHistoryBtn) { loadMoreHistoryBtn.disabled = false; loadMoreHistoryBtn.textContent = '加载更多'; } }
    }

    function renderVectorizeHistory(historyItems) {
        if (!historyListContainer.classList.contains('history-list-flex-applied')) {
            historyListContainer.style.display = 'flex'; historyListContainer.style.flexWrap = 'wrap';
            historyListContainer.style.gap = '1rem'; historyListContainer.classList.add('history-list-flex-applied');
        }
        if (historyItems.length === 0 && historyListContainer.children.length === 0) {
            historyPlaceholder.textContent = '暂无 SVG 转换历史记录。'; historyPlaceholder.style.display = 'block'; return;
        }
        historyPlaceholder.style.display = 'none'; 
        historyItems.forEach(item => {
            const historyCard = document.createElement('div');
            historyCard.className = 'upscale-history-item text-center'; 
            historyCard.style.flex = '0 0 auto'; historyCard.style.maxWidth = '144px';
            const timeSmall = document.createElement('small');
            timeSmall.className = 'd-block text-muted mb-1'; 
            timeSmall.textContent = formatFullDateTime(item.created_at);
            historyCard.appendChild(timeSmall);
            
            const svgAnchor = document.createElement('a');
            svgAnchor.href = item.result_svg_url; // This will be ComfyUI URL
            svgAnchor.title = `点击下载SVG (原始: ${item.original_image_filename || 'N/A'})`;
            // No target="_blank"

            const imgPreview = document.createElement('img');
            imgPreview.src = item.result_svg_url; // SVG can be direct src for img
            imgPreview.alt = 'Vectorized SVG history preview';
            imgPreview.loading = 'lazy';
            imgPreview.className = 'img-fluid rounded border';
            imgPreview.style.objectFit = 'contain'; // Use contain for SVG to see whole image
            imgPreview.style.aspectRatio = '1 / 1';
            imgPreview.style.backgroundColor = 'white'; // BG for transparent SVGs
            svgAnchor.appendChild(imgPreview);

            svgAnchor.addEventListener('click', (event) => {
                event.preventDefault();
                initiateFileDownload(item.result_svg_url, svgAnchor, 'vectorized_history.svg');
            });
            historyCard.appendChild(svgAnchor);
            historyListContainer.appendChild(historyCard);
        });
    }

    function updateLoadMoreVectorizeButtonState() {
        if (!loadMoreHistoryBtn || !historyLoadMoreContainer) return;
        if (currentVectorizeHistoryPage < totalVectorizeHistoryPages) {
            historyLoadMoreContainer.style.display = 'block';
            loadMoreHistoryBtn.disabled = false; loadMoreHistoryBtn.textContent = '加载更多';
        } else {
            historyLoadMoreContainer.style.display = 'none';
        }
    }

    function formatFullDateTime(dateString) {
        if (!dateString) return '未知时间';
        try {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric', month: '2-digit', day: '2-digit', 
                hour: '2-digit', minute: '2-digit', hour12: false
            });
        } catch (e) { return '日期无效'; }
    }

    fetchFeatureCost();
}); 