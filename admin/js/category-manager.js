document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements for Category Management
    const categoriesModal = document.getElementById('categoriesModal');
    const categoriesTableBody = document.getElementById('categoriesTableBody');
    const addCategoryBtnInModal = categoriesModal ? categoriesModal.querySelector('#addCategoryBtn') : null; // Button inside the management modal

    const editCategoryModal = document.getElementById('editCategoryModal');
    const categoryForm = document.getElementById('categoryForm');
    const categoryIdInput = document.getElementById('categoryId');
    const categoryNameInput = document.getElementById('categoryName');
    const categorySlugInput = document.getElementById('categorySlug');
    const categoryDescriptionInput = document.getElementById('categoryDescription');
    const saveCategoryButton = document.getElementById('saveCategory');

    const deleteCategoryConfirmModal = document.getElementById('deleteCategoryConfirmModal');
    const confirmDeleteCategoryButton = document.getElementById('confirmDeleteCategory');

    // State Variable
    let deleteCategoryId = null;

    // Ensure API_URL, getAuthHeaders, logout, currentUser, loadCategories (from main.js) are available (e.g., via window object)
    const MAIN_API_URL = window.API_URL;
    const getMainAuthHeaders = window.getAuthHeaders;
    const mainLogout = window.logout;
    const getCurrentUser = window.getCurrentUser; // Access as a function to get latest
    const mainLoadCategories = window.loadCategories;


    // Function to load categories into the management modal
    async function loadCategoriesList() {
        if (!categoriesTableBody) {
            console.error('categoriesTableBody element not found.');
            return;
        }
        try {
            categoriesTableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center loading">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <span>加载中...</span>
                    </td>
                </tr>
            `;
            
            const response = await fetch(`${MAIN_API_URL}/categories`, {
                headers: getMainAuthHeaders()
            });
            
            if (!response.ok) {
                 if (response.status === 401 || response.status === 403) {
                     mainLogout();
                     return;
                 }
                 throw new Error(`获取分类列表失败: ${response.status}`);
            }

            const categories = await response.json();
            
            if (categories.length === 0) {
                categoriesTableBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center empty-state">
                            <span>暂无分类，点击"添加分类"按钮创建</span>
                        </td>
                    </tr>
                `;
                return;
            }
            
            let html = '';
            categories.forEach(category => {
                // Note: _id is used if your backend returns MongoDB-style IDs. Adjust if it's just 'id'.
                const catId = category._id || category.id; 
                html += `
                    <tr>
                        <td>${category.name}</td>
                        <td>${category.slug}</td>
                        <td>${category.description || '-'}</td>
                        <td>
                            <button class="btn btn-icon btn-outline-primary" onclick="window.editCategoryManager('${catId}')">
                                <i class="bi bi-pencil-square"></i>
                            </button>
                            <button class="btn btn-icon btn-danger" onclick="window.showDeleteCategoryConfirmManager('${catId}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            categoriesTableBody.innerHTML = html;
        } catch (error) {
            console.error('加载分类列表错误 (category-manager):', error);
            categoriesTableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center text-danger">
                        加载失败，请刷新重试
                    </td>
                </tr>
            `;
        }
    }

    // Function to show the add/edit category form (for adding)
    function showAddCategoryForm() {
        resetCategoryForm();
        if (editCategoryModal) {
            const title = editCategoryModal.querySelector('.modal-title');
            if(title) title.textContent = '添加分类';
            const modalInstance = bootstrap.Modal.getOrCreateInstance(editCategoryModal);
            modalInstance.show();
        }
    }

    // Function to reset the category form
    function resetCategoryForm() {
        if (categoryForm) categoryForm.reset();
        if (categoryIdInput) categoryIdInput.value = '';
    }

    // Make functions global for onclick attributes
    window.editCategoryManager = async (id) => {
        const currentUser = getCurrentUser();
        if (!currentUser || currentUser.role !== 'admin') {
            console.warn('普通用户尝试编辑分类，操作被阻止。');
            return; 
        }
        try {
            const response = await fetch(`${MAIN_API_URL}/categories/${id}`, {
                 headers: getMainAuthHeaders()
            });
            if (!response.ok) {
                 if (response.status === 401 || response.status === 403) {
                     alert('认证失败或已过期，请重新登录。');
                     mainLogout();
                     return;
                 }
                 throw new Error(`获取分类详情失败: ${response.status}`);
            }
            const category = await response.json();
            
            if(categoryIdInput) categoryIdInput.value = category.id || category._id;
            if(categoryNameInput) categoryNameInput.value = category.name;
            if(categorySlugInput) categorySlugInput.value = category.slug;
            if(categoryDescriptionInput) categoryDescriptionInput.value = category.description || '';
            
            if (editCategoryModal) {
                const title = editCategoryModal.querySelector('.modal-title');
                if(title) title.textContent = '编辑分类';
                const modalInstance = bootstrap.Modal.getOrCreateInstance(editCategoryModal);
                modalInstance.show();
            }
        } catch (error) {
            console.error('获取分类详情错误 (category-manager):', error);
            alert('加载分类数据失败，请重试');
        }
    };

    window.showDeleteCategoryConfirmManager = (id) => {
        const currentUser = getCurrentUser();
        if (!currentUser || currentUser.role !== 'admin') {
            console.warn('普通用户尝试删除分类，操作被阻止。');
            return; 
        }
        deleteCategoryId = id;
        if (deleteCategoryConfirmModal) {
            const modalInstance = bootstrap.Modal.getOrCreateInstance(deleteCategoryConfirmModal);
            modalInstance.show();
        }
    };

    // Function to save (add/update) a category
    async function saveCategory() {
        if (!categoryNameInput || !categorySlugInput || !saveCategoryButton) return;

        try {
            if (!categoryNameInput.value || !categorySlugInput.value) {
                alert('请填写必填字段 (名称和标识符)');
                return;
            }
            
            const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
            if (!slugRegex.test(categorySlugInput.value)) {
                alert('分类标识符只能包含小写字母、数字和短横线（不能连续出现或在开头结尾）');
                return;
            }
            
            const categoryData = {
                name: categoryNameInput.value,
                slug: categorySlugInput.value,
                description: categoryDescriptionInput.value
            };
            
            const isUpdate = categoryIdInput.value !== '';
            let url = `${MAIN_API_URL}/categories`;
            let method = 'POST';
            
            if (isUpdate) {
                url = `${MAIN_API_URL}/categories/${categoryIdInput.value}`;
                method = 'PUT';
            }
            
            saveCategoryButton.disabled = true;
            saveCategoryButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
            
            const response = await fetch(url, {
                method: method,
                headers: getMainAuthHeaders(),
                body: JSON.stringify(categoryData)
            });

            if (!response.ok) {
                if (response.status === 401 || response.status === 403) {
                     alert('认证失败或权限不足，请重新登录。');
                     mainLogout();
                     return;
                 }
                const errorData = await response.json().catch(() => ({ error: '请求失败' }));
                throw new Error(errorData.message || errorData.error || `HTTP error: ${response.status}`);
            }
            
            if (editCategoryModal) {
                const modalInstance = bootstrap.Modal.getInstance(editCategoryModal);
                if (modalInstance) modalInstance.hide();
            }
            loadCategoriesList(); 
            if (typeof mainLoadCategories === 'function') {
                mainLoadCategories(); 
            }
            
        } catch (error) {
            console.error('保存分类错误 (category-manager):', error);
            alert(`保存失败: ${error.message}`);
        } finally {
            saveCategoryButton.disabled = false;
            saveCategoryButton.innerHTML = '保存';
        }
    }

    // Function to delete a category
    async function deleteCategory() {
        if (!deleteCategoryId || !confirmDeleteCategoryButton) return;
        
        try {
            confirmDeleteCategoryButton.disabled = true;
            confirmDeleteCategoryButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 删除中...';
            
            const response = await fetch(`${MAIN_API_URL}/categories/${deleteCategoryId}`, {
                method: 'DELETE',
                headers: getMainAuthHeaders()
            });
            
            if (!response.ok) {
                if (response.status === 401 || response.status === 403) {
                     alert('认证失败或权限不足，请重新登录。');
                     mainLogout();
                     return;
                 }
                const errorData = await response.json().catch(() => ({error: '删除失败'}));
                 if (response.status === 400 && errorData.error && errorData.error.includes('案例正在使用此分类')) {
                     throw new Error('无法删除，该分类仍有案例在使用。');
                 }
                throw new Error(errorData.message || errorData.error || `HTTP error: ${response.status}`);
            }
            
            if (deleteCategoryConfirmModal) {
                const modalInstance = bootstrap.Modal.getInstance(deleteCategoryConfirmModal);
                if(modalInstance) modalInstance.hide();
            }
            loadCategoriesList();
            if (typeof mainLoadCategories === 'function') {
                mainLoadCategories();
            }
            
        } catch (error) {
            console.error('删除分类错误 (category-manager):', error);
            alert(`删除失败: ${error.message}`);
        } finally {
            confirmDeleteCategoryButton.disabled = false;
            confirmDeleteCategoryButton.innerHTML = '删除';
            deleteCategoryId = null;
        }
    }

    // Event Listeners
    if (categoriesModal) {
        categoriesModal.addEventListener('shown.bs.modal', loadCategoriesList);
    }

    if (addCategoryBtnInModal) { // Button to open the add/edit modal for adding
        addCategoryBtnInModal.addEventListener('click', showAddCategoryForm);
    }
    
    if (saveCategoryButton) {
        saveCategoryButton.addEventListener('click', saveCategory);
    }
    
    if (confirmDeleteCategoryButton) {
        confirmDeleteCategoryButton.addEventListener('click', deleteCategory);
    }

    if (editCategoryModal) {
        editCategoryModal.addEventListener('hidden.bs.modal', resetCategoryForm);
        // Setting title when modal is shown (for add/edit) is handled within showAddCategoryForm and window.editCategoryManager
    }
}); 