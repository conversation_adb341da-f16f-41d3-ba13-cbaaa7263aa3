window.exampleFormHandler = (function() {
    function resetForm() {
        const exampleForm = document.getElementById('exampleForm');
        const exampleIdInput = document.getElementById('exampleId');
        const titleInput = document.getElementById('title');
        const promptTextarea = document.getElementById('prompt');

        const imageInput = document.getElementById('image');
        const previewImg = document.getElementById('previewImg');
        const imagePreview = document.getElementById('imagePreview');
        const imageDropZone = document.getElementById('imageDropZone');

        const sourceImageInput = document.getElementById('source_image');
        const previewSourceImg = document.getElementById('previewSourceImg');
        const sourceImagePreview = document.getElementById('sourceImagePreview');
        const sourceImageDropZone = document.getElementById('sourceImageDropZone');
        const sourceImageSection = document.getElementById('sourceImageSection');

        const referenceImageInput = document.getElementById('reference_image');
        const previewReferenceImg = document.getElementById('previewReferenceImg');
        const referenceImagePreview = document.getElementById('referenceImagePreview');
        const referenceImageDropZone = document.getElementById('referenceImageDropZone');
        const referenceImageSection = document.getElementById('referenceImageSection');

        const modalCategoryFilterBtn = document.getElementById('modalCategoryFilterBtn');
        const modalCategoryFilterValue = document.getElementById('modalCategoryFilterValue');
        const modalCategoryFilterMenu = document.getElementById('modalCategoryFilterMenu');
        const modalModelFilterBtn = document.getElementById('modalModelFilterBtn');
        const modalModelFilterValue = document.getElementById('modalModelFilterValue');
        const modalModelFilterMenu = document.getElementById('modalModelFilterMenu');

        const fluxSecondImageSection = document.getElementById('fluxSecondImageSection');
        const fluxSecondImageDropZone = document.getElementById('fluxSecondImageDropZone');
        const fluxSecondImageInput = document.getElementById('flux_second_image');
        const fluxSecondImagePreview = document.getElementById('fluxSecondImagePreview');
        const previewFluxSecondImg = document.getElementById('previewFluxSecondImg');
        const removeFluxSecondImageBtn = document.getElementById('removeFluxSecondImage');

        const fluxResultImageSection = document.getElementById('fluxResultImageSection');
        const fluxResultImageDropZone = document.getElementById('fluxResultImageDropZone');
        const fluxResultImageInput = document.getElementById('flux_result_image');
        const fluxResultImagePreview = document.getElementById('fluxResultImagePreview');
        const previewFluxResultImg = document.getElementById('previewFluxResultImg');
        const removeFluxResultImageBtn = document.getElementById('removeFluxResultImage');

        if (exampleForm) exampleForm.reset();
        if (exampleIdInput) exampleIdInput.value = '';
        if (window.tagifyInstance) window.tagifyInstance.removeAllTags();
        
        if (window.imageUtils && typeof window.imageUtils.removeImageFunc === 'function') {
            window.imageUtils.removeImageFunc('selectedFile', imageInput, previewImg, imagePreview, imageDropZone);
            window.imageUtils.removeImageFunc('selectedSourceFile', sourceImageInput, previewSourceImg, sourceImagePreview, sourceImageDropZone);
            window.imageUtils.removeImageFunc('selectedReferenceFile', referenceImageInput, previewReferenceImg, referenceImagePreview, referenceImageDropZone);
        } else {
            console.warn('imageUtils.removeImageFunc not available during resetForm.');
            // Fallback manual clear
            window.selectedFile = null; if(imageInput) imageInput.value = ''; if(previewImg) previewImg.src = ''; if(imagePreview) imagePreview.style.display = 'none'; if(imageDropZone) imageDropZone.style.display = 'block';
            window.selectedSourceFile = null; if(sourceImageInput) sourceImageInput.value = ''; if(previewSourceImg) previewSourceImg.src = ''; if(sourceImagePreview) sourceImagePreview.style.display = 'none'; if(sourceImageDropZone) sourceImageDropZone.style.display = 'block';
            window.selectedReferenceFile = null; if(referenceImageInput) referenceImageInput.value = ''; if(previewReferenceImg) previewReferenceImg.src = ''; if(referenceImagePreview) referenceImagePreview.style.display = 'none'; if(referenceImageDropZone) referenceImageDropZone.style.display = 'block';
        }
        
        if (sourceImageSection) sourceImageSection.style.display = 'none';
        if (referenceImageSection) referenceImageSection.style.display = 'none';
        
        if (modalCategoryFilterBtn) modalCategoryFilterBtn.textContent = '选择分类';
        if (modalCategoryFilterValue) modalCategoryFilterValue.value = '';
        if (modalCategoryFilterMenu) {
            modalCategoryFilterMenu.querySelectorAll('.dropdown-item').forEach(item => {
                item.classList.toggle('active', item.dataset.value === '');
            });
        }
        
        if (modalModelFilterBtn) modalModelFilterBtn.textContent = '请选择或留空';
        if (modalModelFilterValue) modalModelFilterValue.value = '';
        if (modalModelFilterMenu) {
            modalModelFilterMenu.querySelectorAll('.dropdown-item').forEach(item => {
                item.classList.toggle('active', item.dataset.value === '');
            });
        }
        
        if (titleInput) titleInput.value = '';
        if (promptTextarea) promptTextarea.value = '';

        // 重置FLUX第二张图片
        if (fluxSecondImagePreview) fluxSecondImagePreview.style.display = 'none';
        if (fluxSecondImageDropZone) fluxSecondImageDropZone.style.display = 'block';
        if (previewFluxSecondImg) previewFluxSecondImg.src = '';
        window.selectedFluxSecondFile = null; // 清除选中的文件
        
        // 隐藏条件性上传区域
        if(fluxSecondImageSection) fluxSecondImageSection.style.display = 'none';

        // 重置FLUX结果图
        if (fluxResultImagePreview) fluxResultImagePreview.style.display = 'none';
        if (fluxResultImageDropZone) fluxResultImageDropZone.style.display = 'block';
        if (previewFluxResultImg) previewFluxResultImg.src = '';
        window.selectedFluxResultFile = null; // 清除选中的文件
        if(fluxResultImageSection) fluxResultImageSection.style.display = 'none';
    }

    /**
     * 准备并显示添加案例模态框
     */
    async function prepareAndShowAddExampleModal(data) {
        console.log('[FormHandler Prepare Modal] Received data for modal:', data);
        const addExampleModalElement = document.getElementById('addExampleModal');
        const titleInput = document.getElementById('title');
        const promptTextarea = document.getElementById('prompt');
        const modalModelFilterValue = document.getElementById('modalModelFilterValue');
        const modalModelFilterBtn = document.getElementById('modalModelFilterBtn');
        const modalModelFilterMenu = document.getElementById('modalModelFilterMenu');
        const imageInput = document.getElementById('image'); // Needed for removeImageFunc fallback

        // 添加缺失的FLUX相关DOM元素定义
        const fluxSecondImageSection = document.getElementById('fluxSecondImageSection');
        const fluxSecondImageDropZone = document.getElementById('fluxSecondImageDropZone');
        const fluxSecondImageInput = document.getElementById('flux_second_image');
        const fluxSecondImagePreview = document.getElementById('fluxSecondImagePreview');
        const previewFluxSecondImg = document.getElementById('previewFluxSecondImg');
        const removeFluxSecondImageBtn = document.getElementById('removeFluxSecondImage');

        const fluxResultImageSection = document.getElementById('fluxResultImageSection');
        const fluxResultImageDropZone = document.getElementById('fluxResultImageDropZone');
        const fluxResultImageInput = document.getElementById('flux_result_image');
        const fluxResultImagePreview = document.getElementById('fluxResultImagePreview');
        const previewFluxResultImg = document.getElementById('previewFluxResultImg');
        const removeFluxResultImageBtn = document.getElementById('removeFluxResultImage');

        if (!addExampleModalElement) {
            console.error('Add Example Modal element not found!');
            return;
        }

        resetForm(); // 重置表单
        const modalInstance = bootstrap.Modal.getOrCreateInstance(addExampleModalElement);

        // 移除之前可能存在的监听器，避免重复执行
        // window.fillModalWithDataHandler 作用域问题，这里直接在显示后处理
        // addExampleModalElement.removeEventListener('shown.bs.modal', window.fillModalWithDataHandler);

        const fillModalAction = async () => {
            console.log('[FormHandler Prepare Modal] Modal shown, filling data now:', data);

            if (data.title && titleInput) titleInput.value = data.title;
            if (data.prompt && promptTextarea) promptTextarea.value = data.prompt;
            if (data.model && modalModelFilterValue) {
                modalModelFilterValue.value = data.model;
                if (modalModelFilterBtn) {
                    const modelItem = modalModelFilterMenu.querySelector(`.dropdown-item[data-value="${data.model}"]`);
                    modalModelFilterBtn.textContent = modelItem ? modelItem.textContent : data.model;
                }
                if (modalModelFilterMenu) {
                    modalModelFilterMenu.querySelectorAll('.dropdown-item').forEach(item => {
                        item.classList.toggle('active', item.dataset.value === data.model);
                    });
                }
            }
            if (window.tagifyInstance && data.tags && Array.isArray(data.tags)) {
                window.tagifyInstance.removeAllTags();
                window.tagifyInstance.addTags(data.tags);
            }

            if (data.imageFile instanceof File) {
                if (window.imageUtils && typeof window.imageUtils.handleFileSelect === 'function') {
                    await window.imageUtils.handleFileSelect(data.imageFile, 'main');
                } else {
                    console.error('imageUtils.handleFileSelect is not available');
                    window.selectedFile = data.imageFile; // Fallback
                    // Fallback display preview (simplified)
                    const previewImg = document.getElementById('previewImg');
                    if(previewImg) previewImg.src = URL.createObjectURL(data.imageFile);
                    document.getElementById('imagePreview').style.display = 'block';
                    document.getElementById('imageDropZone').style.display = 'none';

                }
            } else if (data.imageUrl) {
                try {
                    const response = await fetch(data.imageUrl);
                    const blob = await response.blob();
                    const file = new File([blob], 'example.png', { type: 'image/png' });
                    if (window.imageUtils && typeof window.imageUtils.handleFileSelect === 'function') {
                        await window.imageUtils.handleFileSelect(file, 'main');
                    } else {
                        console.error('imageUtils.handleFileSelect is not available for fetched image');
                        window.selectedFile = file; // Fallback
                        // Fallback display preview (simplified)
                        const previewImg = document.getElementById('previewImg');
                        if(previewImg) previewImg.src = URL.createObjectURL(file);
                        document.getElementById('imagePreview').style.display = 'block';
                        document.getElementById('imageDropZone').style.display = 'none';
                    }
                } catch (error) {
                    console.error('Error loading image after modal shown:', error);
                    const previewImg = document.getElementById('previewImg');
                    const imagePreview = document.getElementById('imagePreview');
                    const imageDropZone = document.getElementById('imageDropZone');
                    if (window.imageUtils && typeof window.imageUtils.removeImageFunc === 'function') {
                         window.imageUtils.removeImageFunc('selectedFile', imageInput, previewImg, imagePreview, imageDropZone);
                    }
                }
            }
            const titleElement = addExampleModalElement.querySelector('.modal-title');
            if (titleElement) titleElement.textContent = '添加提示词案例';

            // 移除监听器
            addExampleModalElement.removeEventListener('shown.bs.modal', fillModalAction);
        };
        
        addExampleModalElement.addEventListener('shown.bs.modal', fillModalAction, { once: true });
        modalInstance.show();

        // 设置FLUX第二张图片拖放区域事件
        if (fluxSecondImageDropZone && fluxSecondImageInput) {
            fluxSecondImageDropZone.addEventListener('click', () => fluxSecondImageInput.click());
            fluxSecondImageDropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                fluxSecondImageDropZone.classList.add('dragging');
            });
            fluxSecondImageDropZone.addEventListener('dragleave', () => {
                fluxSecondImageDropZone.classList.remove('dragging');
            });
            fluxSecondImageDropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                fluxSecondImageDropZone.classList.remove('dragging');
                if (e.dataTransfer.files.length > 0) {
                    handleFluxSecondImageSelect(e.dataTransfer.files[0]);
                }
            });
            
            fluxSecondImageInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFluxSecondImageSelect(e.target.files[0]);
                }
            });
            
            if (removeFluxSecondImageBtn) {
                removeFluxSecondImageBtn.addEventListener('click', () => {
                    fluxSecondImagePreview.style.display = 'none';
                    fluxSecondImageDropZone.style.display = 'block';
                    previewFluxSecondImg.src = '';
                    window.selectedFluxSecondFile = null;
                });
            }
        }
        
        // 设置FLUX结果图拖放区域事件
        if (fluxResultImageDropZone && fluxResultImageInput) {
            fluxResultImageDropZone.addEventListener('click', () => fluxResultImageInput.click());
            fluxResultImageDropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                fluxResultImageDropZone.classList.add('dragging');
            });
            fluxResultImageDropZone.addEventListener('dragleave', () => {
                fluxResultImageDropZone.classList.remove('dragging');
            });
            fluxResultImageDropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                fluxResultImageDropZone.classList.remove('dragging');
                if (e.dataTransfer.files.length > 0) {
                    handleFluxResultImageSelect(e.dataTransfer.files[0]);
                }
            });
            
            fluxResultImageInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFluxResultImageSelect(e.target.files[0]);
                }
            });
            
            if (removeFluxResultImageBtn) {
                removeFluxResultImageBtn.addEventListener('click', () => {
                    fluxResultImagePreview.style.display = 'none';
                    fluxResultImageDropZone.style.display = 'block';
                    previewFluxResultImg.src = '';
                    window.selectedFluxResultFile = null;
                });
            }
        }
    }

    // 添加handleFluxSecondImageSelect函数
    function handleFluxSecondImageSelect(file) {
        if (!file) return;
        const reader = new FileReader();
        reader.onload = (e) => {
            previewFluxSecondImg.src = e.target.result;
            fluxSecondImagePreview.style.display = 'block';
            fluxSecondImageDropZone.style.display = 'none';
            window.selectedFluxSecondFile = file;
        };
        reader.readAsDataURL(file);
    }

    // 添加handleFluxResultImageSelect函数
    function handleFluxResultImageSelect(file) {
        if (!file) return;
        const reader = new FileReader();
        reader.onload = (e) => {
            previewFluxResultImg.src = e.target.result;
            fluxResultImagePreview.style.display = 'block';
            fluxResultImageDropZone.style.display = 'none';
            window.selectedFluxResultFile = file;
        };
        reader.readAsDataURL(file);
    }

    /**
     * 填充案例编辑表单
     */
    async function populateExampleForm(example) {
        const exampleIdInput = document.getElementById('exampleId');
        const titleInput = document.getElementById('title');
        const promptTextarea = document.getElementById('prompt');
        const modalCategoryFilterValue = document.getElementById('modalCategoryFilterValue');
        const modalCategoryFilterBtn = document.getElementById('modalCategoryFilterBtn');
        const modalCategoryFilterMenu = document.getElementById('modalCategoryFilterMenu');
        const modalModelFilterValue = document.getElementById('modalModelFilterValue');
        const modalModelFilterBtn = document.getElementById('modalModelFilterBtn');
        const modalModelFilterMenu = document.getElementById('modalModelFilterMenu');
        const addExampleModalElement = document.getElementById('addExampleModal');

        // 图片相关DOM
        const imageInput = document.getElementById('image');
        const previewImg = document.getElementById('previewImg');
        const imagePreview = document.getElementById('imagePreview');
        const imageDropZone = document.getElementById('imageDropZone');
        const sourceImageInput = document.getElementById('source_image');
        const previewSourceImg = document.getElementById('previewSourceImg');
        const sourceImagePreview = document.getElementById('sourceImagePreview');
        const sourceImageDropZone = document.getElementById('sourceImageDropZone');
        const sourceImageSection = document.getElementById('sourceImageSection');
        const referenceImageInput = document.getElementById('reference_image');
        const previewReferenceImg = document.getElementById('previewReferenceImg');
        const referenceImagePreview = document.getElementById('referenceImagePreview');
        const referenceImageDropZone = document.getElementById('referenceImageDropZone');
        const referenceImageSection = document.getElementById('referenceImageSection');

        if (!example) {
            alert('未找到该案例数据');
            return;
        }

        console.log('[FormHandler] Populating form with example:', example);

        if (exampleIdInput) exampleIdInput.value = example.id;
        if (titleInput) titleInput.value = example.title;
        if (promptTextarea) promptTextarea.value = example.prompt;

        if (modalCategoryFilterValue && example.category) {
            modalCategoryFilterValue.value = example.category;
            if (modalCategoryFilterBtn && window.getCategoryNameFromMainJS) { //依赖main.js暴露的函数
                const categoryName = window.getCategoryNameFromMainJS(example.category) || example.category;
                modalCategoryFilterBtn.textContent = categoryName;
            }
            if (modalCategoryFilterMenu) {
                modalCategoryFilterMenu.querySelectorAll('.dropdown-item').forEach(item => {
                    item.classList.toggle('active', item.dataset.value === example.category);
                });
            }
        }

        if (modalModelFilterValue) {
            modalModelFilterValue.value = example.target_model || '';
            if (modalModelFilterBtn) {
                if (example.target_model) {
                    const modelItem = modalModelFilterMenu.querySelector(`.dropdown-item[data-value="${example.target_model}"]`);
                    modalModelFilterBtn.textContent = modelItem ? modelItem.textContent : example.target_model;
                } else {
                    modalModelFilterBtn.textContent = '请选择或留空';
                }
            }
            if (modalModelFilterMenu) {
                modalModelFilterMenu.querySelectorAll('.dropdown-item').forEach(item => {
                    item.classList.toggle('active', item.dataset.value === (example.target_model || ''));
                });
            }
        }

        if (window.tagifyInstance) {
            window.tagifyInstance.removeAllTags();
            if (example.tags && Array.isArray(example.tags) && example.tags.length > 0) {
                window.tagifyInstance.addTags(example.tags);
            }
        }

        // 清理本地已选文件和预览
        if (window.imageUtils && typeof window.imageUtils.removeImageFunc === 'function') {
            window.imageUtils.removeImageFunc('selectedFile', imageInput, previewImg, imagePreview, imageDropZone);
            window.imageUtils.removeImageFunc('selectedSourceFile', sourceImageInput, previewSourceImg, sourceImagePreview, sourceImageDropZone);
            window.imageUtils.removeImageFunc('selectedReferenceFile', referenceImageInput, previewReferenceImg, referenceImagePreview, referenceImageDropZone);
        }

        // 显示服务器上的图片预览
        const mainImageUrl = example.image_url || (example.image_file && window.imageBaseUrl ? window.imageBaseUrl + example.image_file : null);
        if (mainImageUrl && previewImg && imagePreview) {
            previewImg.src = mainImageUrl;
            imagePreview.style.display = 'block';
            if (imageDropZone) imageDropZone.style.display = 'none';
        } else {
            if (imageDropZone) imageDropZone.style.display = 'block';
            if (imagePreview) imagePreview.style.display = 'none';
            if (previewImg) previewImg.src = '';
        }

        const sourceImageUrl = example.source_image_url || (example.source_image_file && window.imageBaseUrl ? window.imageBaseUrl + example.source_image_file : null);
         if (sourceImageUrl && previewSourceImg && sourceImagePreview) {
            previewSourceImg.src = sourceImageUrl;
            sourceImagePreview.style.display = 'block';
            if(sourceImageDropZone) sourceImageDropZone.style.display = 'none';
            if (example.target_model === 'GPT4O-编辑' && sourceImageSection) sourceImageSection.style.display = 'block';
        } else {
            if(sourceImageDropZone) sourceImageDropZone.style.display = 'block';
            if(sourceImagePreview) sourceImagePreview.style.display = 'none';
            if(previewSourceImg) previewSourceImg.src = '';
            if (sourceImageSection && example.target_model !== 'GPT4O-编辑') sourceImageSection.style.display = 'none';
            else if (sourceImageSection && !sourceImageUrl) sourceImageSection.style.display = (example.target_model === 'GPT4O-编辑' ? 'block' : 'none'); // Ensure hidden if no img and not edit model
        }

        const refImageUrl = example.reference_image_url || (example.reference_image_file && window.imageBaseUrl ? window.imageBaseUrl + example.reference_image_file : null);
        if (refImageUrl && previewReferenceImg && referenceImagePreview) {
            previewReferenceImg.src = refImageUrl;
            referenceImagePreview.style.display = 'block';
            if(referenceImageDropZone) referenceImageDropZone.style.display = 'none';
            if (example.target_model === 'GPT4O-编辑' && referenceImageSection) referenceImageSection.style.display = 'block';
        } else {
            if(referenceImageDropZone) referenceImageDropZone.style.display = 'block';
            if(referenceImagePreview) referenceImagePreview.style.display = 'none';
            if(previewReferenceImg) previewReferenceImg.src = '';
            if (referenceImageSection && example.target_model !== 'GPT4O-编辑') referenceImageSection.style.display = 'none';
            else if (referenceImageSection && !refImageUrl) referenceImageSection.style.display = (example.target_model === 'GPT4O-编辑' ? 'block' : 'none');
        }
        
        // 确保在适当的模型下显示图片上传区域
        if (modalModelFilterValue && modalModelFilterValue.value === 'GPT4O-编辑') {
            if(sourceImageSection) sourceImageSection.style.display = 'block'; // 显示图1
            if(referenceImageSection) referenceImageSection.style.display = 'block'; // 显示图2
            if(fluxSecondImageSection) fluxSecondImageSection.style.display = 'none';
            if(fluxResultImageSection) fluxResultImageSection.style.display = 'none';
        } else if (modalModelFilterValue && modalModelFilterValue.value === 'FLUX-多图') {
            if(sourceImageSection) sourceImageSection.style.display = 'none'; // 隐藏源图片区域
            if(referenceImageSection) referenceImageSection.style.display = 'none'; // 隐藏参考图区域
            if(fluxSecondImageSection) fluxSecondImageSection.style.display = 'block'; // 显示FLUX第二张图片区域
            if(fluxResultImageSection) fluxResultImageSection.style.display = 'block'; // 显示FLUX结果图区域
        } else {
            if(sourceImageSection) sourceImageSection.style.display = 'none';
            if(referenceImageSection) referenceImageSection.style.display = 'none';
            if(fluxSecondImageSection) fluxSecondImageSection.style.display = 'none';
            if(fluxResultImageSection) fluxResultImageSection.style.display = 'none';
        }

        // 处理FLUX-多图的第二张图片
        const fluxSecondImageUrl = example.flux_second_image_url || (example.flux_second_image_file && window.imageBaseUrl ? window.imageBaseUrl + example.flux_second_image_file : null);
        if (fluxSecondImageUrl && previewFluxSecondImg && fluxSecondImagePreview) {
            previewFluxSecondImg.src = fluxSecondImageUrl;
            fluxSecondImagePreview.style.display = 'block';
            if(fluxSecondImageDropZone) fluxSecondImageDropZone.style.display = 'none';
            if (example.target_model === 'FLUX-多图' && fluxSecondImageSection) fluxSecondImageSection.style.display = 'block';
        } else {
            if(fluxSecondImageDropZone) fluxSecondImageDropZone.style.display = 'block';
            if(fluxSecondImagePreview) fluxSecondImagePreview.style.display = 'none';
            if(previewFluxSecondImg) previewFluxSecondImg.src = '';
            if (fluxSecondImageSection && example.target_model !== 'FLUX-多图') fluxSecondImageSection.style.display = 'none';
            else if (fluxSecondImageSection && !fluxSecondImageUrl) fluxSecondImageSection.style.display = (example.target_model === 'FLUX-多图' ? 'block' : 'none');
        }
        
        // 确保FLUX-多图模式下显示第二张图片上传区域
        if (modalModelFilterValue && modalModelFilterValue.value === 'FLUX-多图') {
            if(fluxSecondImageSection) fluxSecondImageSection.style.display = 'block';
        } else {
            if(fluxSecondImageSection) fluxSecondImageSection.style.display = 'none';
        }

        const titleElement = addExampleModalElement.querySelector('.modal-title');
        if (titleElement) titleElement.textContent = '编辑提示词案例';
        
        const modalInstance = bootstrap.Modal.getOrCreateInstance(addExampleModalElement);
        modalInstance.show();
    }

    /**
     * 实际执行上传的fetch请求
     */
    async function uploadExampleData(formData, isUpdate, exampleId) {
        let url = `${window.API_URL}/examples`; // 依赖 window.API_URL
        let method = 'POST';

        if (isUpdate && exampleId) {
            url = `${window.API_URL}/examples/${exampleId}`;
            method = 'PUT';
        } else if (isUpdate && !exampleId) {
            console.error('Update requested but no example ID provided.');
            throw new Error('更新请求缺少案例 ID');
        }

        console.log(`[FormHandler] Uploading example data. URL: ${url}, Method: ${method}`);
        const token = localStorage.getItem('token'); // 或者 window.getAuthHeaders().Authorization.split(' ')[1]
        
        const response = await fetch(url, {
            method: method,
            body: formData,
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                if (window.logoutMain) window.logoutMain(); // 依赖 main.js 暴露的 logout
                throw new Error('认证失败或权限不足');
            }
            const errorData = await response.json().catch(() => ({ error: `HTTP error ${response.status}` }));
            console.error('Upload failed:', errorData);
            throw new Error(errorData.error || errorData.message || '上传失败');
        }
        return await response.json();
    }
    window.uploadExampleData = uploadExampleData; // 也暴露给 ai-generate.js (如果它还在用旧的方式)

    /**
     * 保存提示词案例 (新增或更新)
     */
    async function saveExample() {
        const titleInput = document.getElementById('title');
        const modalCategoryFilterValue = document.getElementById('modalCategoryFilterValue');
        const promptTextarea = document.getElementById('prompt');
        const modalModelFilterValue = document.getElementById('modalModelFilterValue');
        const saveButton = document.getElementById('saveExample');
        const exampleIdInput = document.getElementById('exampleId');
        const addExampleModalElement = document.getElementById('addExampleModal');

        // 图片相关DOM (用于keep_existing_image判断)
        const previewImg = document.getElementById('previewImg');
        const previewSourceImg = document.getElementById('previewSourceImg');
        const previewReferenceImg = document.getElementById('previewReferenceImg');
        const previewFluxSecondImg = document.getElementById('previewFluxSecondImg');
        const previewFluxResultImg = document.getElementById('previewFluxResultImg');


        try {
            const categoryValue = modalCategoryFilterValue.value;
            const modelValue = modalModelFilterValue.value;

            if (!titleInput.value || !categoryValue || !promptTextarea.value) {
                alert('请填写标题、分类和提示词');
                return;
            }
            
            saveButton.disabled = true; 
            saveButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';

            const formData = new FormData();
            formData.append('title', titleInput.value);
            formData.append('category', categoryValue);
            formData.append('prompt', promptTextarea.value);
            formData.append('target_model', modelValue || '');
            
            if (window.tagifyInstance) {
                const tagValues = window.tagifyInstance.value.map(tag => tag.value);
                formData.append('tags', JSON.stringify(tagValues));
            }
            
            const exampleId = exampleIdInput.value;
            const isUpdate = exampleId !== '';
            
            const maxSizeInBytes = 100 * 1024; // 100KB

            // 定义图片压缩选项
            const options = {
                maxSizeMB: 0.097,
                maxWidthOrHeight: 1920,
                useWebWorker: true,
                fileType: 'image/jpeg' // 强制转为JPG
            };
            
            const editImageOptions = {
                maxSizeMB: 0.097,
                maxWidthOrHeight: 1024,
                useWebWorker: true,
                fileType: 'image/jpeg' // 强制转为JPG
            };

            const compressAndAppend = async (file, opts, fieldName) => {
                 if (file.size <= maxSizeInBytes) {
                    formData.append(fieldName, file, file.name);
                    return;
                }
                saveButton.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 压缩 ${file.name} 中...`;
                try {
                    if (typeof window.imageCompression !== 'function') throw new Error('图片压缩功能不可用');
                    const compressedFile = await window.imageCompression(file, opts);
                    const originalName = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;
                    formData.append(fieldName, compressedFile, `${originalName}.jpg`);
                } catch (compressError) {
                    console.warn(`图片压缩失败 for ${fieldName}, 使用原图片:`, compressError);
                    formData.append(fieldName, file, file.name);
                }
            };
            
            // 处理不同模型类型的图片上传
            if (modelValue === 'FLUX-多图') {
                if (window.selectedFluxSecondFile) {
                    await compressAndAppend(window.selectedFluxSecondFile, editImageOptions, 'flux_second_image');
                } else if (isUpdate && previewFluxSecondImg && previewFluxSecondImg.src && previewFluxSecondImg.src.startsWith('http')) {
                    formData.append('keep_existing_flux_second_image', 'true');
                }
                
                if (window.selectedFluxResultFile) {
                    await compressAndAppend(window.selectedFluxResultFile, editImageOptions, 'flux_result_image');
                } else if (isUpdate && previewFluxResultImg && previewFluxResultImg.src && previewFluxResultImg.src.startsWith('http')) {
                    formData.append('keep_existing_flux_result_image', 'true');
                }
                
                if (window.selectedFile) {
                    await compressAndAppend(window.selectedFile, options, 'image');
                } else if (isUpdate && previewImg && previewImg.src && previewImg.src.startsWith('http')) {
                    formData.append('keep_existing_image', 'true');
                }
            } else if (modelValue === 'GPT4O-编辑') {
                if (window.selectedFile) {
                    await compressAndAppend(window.selectedFile, options, 'image');
                } else if (isUpdate && previewImg && previewImg.src && previewImg.src.startsWith('http')) {
                    formData.append('keep_existing_image', 'true');
                }
                
                if (window.selectedSourceFile) {
                   await compressAndAppend(window.selectedSourceFile, editImageOptions, 'source_image');
                } else if (isUpdate && previewSourceImg && previewSourceImg.src && previewSourceImg.src.startsWith('http')) {
                    formData.append('keep_existing_source_image', 'true');
                }
                
                if (window.selectedReferenceFile) {
                    await compressAndAppend(window.selectedReferenceFile, editImageOptions, 'reference_image');
                } else if (isUpdate && previewReferenceImg && previewReferenceImg.src && previewReferenceImg.src.startsWith('http')) {
                    formData.append('keep_existing_reference_image', 'true');
                }
            } else {
                if (window.selectedFile) {
                    await compressAndAppend(window.selectedFile, options, 'image');
                } else if (isUpdate && previewImg && previewImg.src && previewImg.src.startsWith('http')) {
                    formData.append('keep_existing_image', 'true');
                }
            }
            
            saveButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
            const result = await uploadExampleData(formData, isUpdate, exampleId); // 调用本模块的 upload
            
            const addModalInstance = bootstrap.Modal.getInstance(addExampleModalElement);
            if (addModalInstance) addModalInstance.hide(); 
            
            if(window.resetPaginationAndLoad) window.resetPaginationAndLoad(); // 依赖 main.js 暴露的函数
            if(window.showToast) window.showToast(isUpdate ? '案例更新成功！' : '案例添加成功！', 'success'); // 依赖 main.js 暴露的函数
            
        } catch (error) {
            console.error('保存案例错误:', error);
            alert('保存失败: ' + (error.message || '请重试'));
        } finally {
            saveButton.disabled = false;
            saveButton.innerHTML = '保存';
            resetForm(); // 调用本模块的 resetForm，它内部会清理 selectedFile 等
        }
    }

    // 添加事件监听器，当选择不同模型时显示或隐藏相关上传区域
    document.getElementById('modalModelFilterMenu').addEventListener('click', function(event) {
        const clickedItem = event.target.closest('a.dropdown-item');
        if (clickedItem) {
            const selectedModelValue = clickedItem.getAttribute('data-value');
            
            // 根据不同模型类型显示不同的上传区域
            if (selectedModelValue === 'GPT4O-编辑') {
                // GPT4O-编辑模式显示源图片和参考图区域
                if (sourceImageSection) sourceImageSection.style.display = 'block';
                if (referenceImageSection) referenceImageSection.style.display = 'block';
                if (fluxSecondImageSection) fluxSecondImageSection.style.display = 'none';
                if (fluxResultImageSection) fluxResultImageSection.style.display = 'none';
            } else if (selectedModelValue === 'FLUX-多图') {
                // FLUX-多图模式显示FLUX相关上传区域，隐藏源图片和参考图区域
                if (sourceImageSection) sourceImageSection.style.display = 'none';
                if (referenceImageSection) referenceImageSection.style.display = 'none';
                if (fluxSecondImageSection) fluxSecondImageSection.style.display = 'block';
                if (fluxResultImageSection) fluxResultImageSection.style.display = 'block';
            } else {
                // 其他模型不显示特殊上传区域
                if (sourceImageSection) sourceImageSection.style.display = 'none';
                if (referenceImageSection) referenceImageSection.style.display = 'none';
                if (fluxSecondImageSection) fluxSecondImageSection.style.display = 'none';
                if (fluxResultImageSection) fluxResultImageSection.style.display = 'none';
            }
        }
    });

    // 暴露公共方法
    return {
        resetForm,
        prepareAndShowAddExampleModal,
        populateExampleForm,
        saveExample,
        handleFluxSecondImageSelect,
        handleFluxResultImageSelect
        // uploadExampleData // uploadExampleData 现在是内部辅助函数，但也被 ai-generate.js 调用，所以暂时保留在 window.uploadExampleData
    };
})();
