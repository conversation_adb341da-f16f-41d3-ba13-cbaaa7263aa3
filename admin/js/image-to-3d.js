// 图片转3D模型功能
document.addEventListener('DOMContentLoaded', () => {

    // --- 元素引用 ---
    const dropZone = document.getElementById('image3dDropZone');
    const fileInput = document.getElementById('image3dInput');
    const previewContainer = document.getElementById('image3dPreviewContainer');
    const previewImg = document.getElementById('image3dPreviewImg');
    const removeImageBtn = document.getElementById('removeImage3d');
    const startBtn = document.getElementById('startImage3dBtn');
    const image3dPane = document.getElementById('image-to-3d-pane');
    const image3dTab = document.getElementById('image-to-3d-tab');
    
    // 状态和进度区域
    const statusArea = document.getElementById('image3dStatusArea');
    const statusMessage = document.getElementById('image3dStatusMessage');
    const progressContainer = document.getElementById('image3dProgressContainer');
    const progressBar = document.getElementById('image3dProgressBar');
    const progressPercentage = document.getElementById('image3dProgressPercentage');
    const progressDetail = document.getElementById('image3dProgressDetail');
    const errorMessage = document.getElementById('image3dErrorMessage');
    const resultsPlaceholder = document.getElementById('image3dResultsPlaceholder');
    
    // 3D 模型相关
    const modelContainer = document.getElementById('image3dModelContainer');
    const modelViewer = document.getElementById('image3dModelViewer');
    const rotateBtn = document.getElementById('image3dRotateBtn');
    const resetBtn = document.getElementById('image3dResetBtn');
    let downloadLink = document.getElementById('image3dDownloadLink');
    
    // 历史记录相关
    const historyList = document.getElementById('image3dHistoryList');
    const historyPlaceholder = document.getElementById('image3dHistoryPlaceholder');
    const loadMoreContainer = document.getElementById('image3dHistoryLoadMoreContainer');
    const loadMoreBtn = document.getElementById('loadMoreImage3dHistoryBtn');
    
    // --- 状态变量 ---
    let currentFile = null;
    let pollIntervalId = null;
    let modelUrl = null;
    let featureCost = null; // 添加功能成本变量
    let animationFeatureCost = null; // 添加动画功能成本变量
    
    // Three.js 相关变量
    let scene, camera, renderer, controls, model;
    let autoRotate = false;
    let mixer = null;
    let clock = new THREE.Clock();
    
    // 历史记录分页
    let currentHistoryPage = 1;
    let totalHistoryPages = 1;
    let isLoadingHistory = false;

    // --- API 基础URL ---
    const API_BASE_URL = 'https://caca.yzycolour.top';
    const COMFYUI_BASE_URL = 'https://comfyui.yzycolour.top';

    // --- 获取认证令牌的函数 ---
    function getAuthToken() {
        return localStorage.getItem('token');
    }

    // --- 创建带认证的请求头 ---
    function getAuthHeaders() {
        const token = getAuthToken();
        return {
            'Authorization': `Bearer ${token}`
        };
    }

    // --- 新增：获取功能成本的函数 --- START ---
    async function fetchFeatureCost() {
        try {
            const token = getAuthToken();
            if (!token) {
                console.warn('未找到认证令牌，无法获取功能成本');
                return;
            }
            
            const response = await fetch(`${API_BASE_URL}/api/features/cost?key=image_to_3d`, {
                headers: getAuthHeaders()
            });
            
            if (!response.ok) {
                console.error('获取功能成本失败:', response.status);
                return;
            }
            
            const data = await response.json();
            if (data.success && data.cost !== undefined) {
                featureCost = data.cost;
                
                // 更新按钮文本
                updateButtonWithCost();
            }

            // 同时获取动画功能的成本
            const animResponse = await fetch(`${API_BASE_URL}/api/features/cost?key=image_to_3d_animation`, {
                headers: getAuthHeaders()
            });
            
            if (!animResponse.ok) {
                console.error('获取动画功能成本失败:', animResponse.status);
                return;
            }
            
            const animData = await animResponse.json();
            if (animData.success && animData.cost !== undefined) {
                animationFeatureCost = animData.cost;
            }
        } catch (error) {
            console.error('获取功能成本出错:', error);
        }
    }
    
    function updateButtonWithCost() {
        if (startBtn && featureCost !== null) {
            // 查找按钮内部专门用于显示成本信息的span，如果没有则创建一个
            let costMessageSpan = startBtn.querySelector('.dynamic-cost-message');
            if (!costMessageSpan) {
                costMessageSpan = document.createElement('span');
                costMessageSpan.className = 'dynamic-cost-message ms-1'; // ms-1 for a little left margin
                // We want to append it after the main text/icon, so let's find a common text span if it exists
                const btnTextSpan = startBtn.querySelector('.btn-text');
                if (btnTextSpan && btnTextSpan.nextSibling) {
                    btnTextSpan.parentNode.insertBefore(costMessageSpan, btnTextSpan.nextSibling);
                } else if (btnTextSpan) {
                    btnTextSpan.parentNode.appendChild(costMessageSpan);
                }
                else {
                     // Fallback: append directly to button if no .btn-text span found
                    startBtn.appendChild(costMessageSpan);
                }
            }
            
            const directDeductMessage = ' <span class="text-info" style="font-size: 0.8em;">(赠送积分无法使用该功能！)</span>';
            
            if (parseInt(featureCost) > 0) {
                costMessageSpan.innerHTML = `(${featureCost}积分)${directDeductMessage}`;
                costMessageSpan.classList.remove('text-success'); // Ensure no conflicting classes
            } else { // Cost is 0 or not positive
                costMessageSpan.innerHTML = `(免费)${directDeductMessage}`;
                costMessageSpan.classList.add('text-success');
            }
        }
    }
    // --- 新增：获取功能成本的函数 --- END ---

    // --- 初始加载 ---
    // 页面加载时获取功能成本
    fetchFeatureCost();

    // --- 新增：页面加载时检查是否有未完成的任务 --- START ---
    async function checkForPendingImage3DTask() {
        const pendingTaskId = localStorage.getItem('imageTo3d_pending_task_id');
        const pendingHistoryId = localStorage.getItem('imageTo3d_pending_task_history_id');

        if (pendingTaskId) {
            statusArea.style.display = 'block'; // 显示状态区域
            progressContainer.style.display = 'block';
            resultsPlaceholder.style.display = 'none';
            modelContainer.style.display = 'none';
            errorMessage.style.display = 'none';
            updateProgress(0, '正在恢复任务状态...', '请稍候，正在检查之前的任务进度。');

            try {
                const response = await fetch(`${API_BASE_URL}/api/image-to-3d/task-status/${pendingTaskId}`, {
                    headers: getAuthHeaders()
                });

                if (!response.ok) {
                    if (response.status === 404) {
                        showError('之前的任务未找到，可能已被删除或已过期。');
                        localStorage.removeItem('imageTo3d_pending_task_id');
                        localStorage.removeItem('imageTo3d_pending_task_history_id');
                        loadHistory(1, false); // 刷新历史列表
                    } else {
                        showError(`恢复任务状态失败: HTTP ${response.status}`);
                    }
                    // 不再轮询，按钮状态等应由 showError 或后续逻辑处理
                    if (startBtn) startBtn.disabled = false; // 允许用户重新开始
                    return;
                }

                const result = await response.json();
                if (result.success) {
                    const { status, progress, resultUrl, error: errorMsg } = result;

                    if (status === 'success' || status === 'completed') {
                        if (resultUrl) {
                            updateProgress(100, '任务已完成', '正在加载模型...');
                            modelUrl = resultUrl;
                            loadModel(resultUrl);                             
                        } else {
                            showError('任务已完成，但模型链接无效。');
                        }
                        localStorage.removeItem('imageTo3d_pending_task_id');
                        localStorage.removeItem('imageTo3d_pending_task_history_id');
                        if (startBtn) startBtn.disabled = false;
                        loadHistory(1, false); // 刷新历史列表
                    } else if (status === 'failed') {
                        showError(errorMsg || '之前的任务处理失败。');
                        localStorage.removeItem('imageTo3d_pending_task_id');
                        localStorage.removeItem('imageTo3d_pending_task_history_id');
                        if (startBtn) startBtn.disabled = false;
                        loadHistory(1, false); // 刷新历史列表
                    } else if (status === 'running' || status === 'queued' || status === 'pending') {
                        updateProgress(progress || 0, '正在恢复任务...', `状态: ${status}`);
                        pollTaskStatus(pendingTaskId); // 重新开始轮询
                        // 按钮在 pollTaskStatus 内部会被管理，初始时应禁用
                        if (startBtn) startBtn.disabled = true; 
                    } else {
                        showError(`未知的任务状态: ${status}`);
                        localStorage.removeItem('imageTo3d_pending_task_id');
                        localStorage.removeItem('imageTo3d_pending_task_history_id');
                        if (startBtn) startBtn.disabled = false;
                        loadHistory(1, false);
                    }
                } else {
                    showError(result.message || '恢复任务状态查询失败。');
                    localStorage.removeItem('imageTo3d_pending_task_id');
                    localStorage.removeItem('imageTo3d_pending_task_history_id');
                    if (startBtn) startBtn.disabled = false;
                }
            } catch (error) {
                console.error('检查待处理任务时出错:', error);
                showError('恢复任务状态时发生网络错误。');
                if (startBtn) startBtn.disabled = false;
            }
        } else {
            // 没有待处理任务，确保UI是初始状态
            if (startBtn && !currentFile) startBtn.disabled = true; 
            else if (startBtn) startBtn.disabled = false;
        }
    }
    // --- 新增：页面加载时检查是否有未完成的任务 --- END ---

    // 在 DOMContentLoaded 后也调用一次，确保首次加载时检查
    checkForPendingImage3DTask();

    // --- 事件监听初始化 ---
    
    // 文件选择和上传相关
    if (fileInput) {
        fileInput.addEventListener('change', (event) => {
            if (event.target.files && event.target.files[0]) {
                processFile(event.target.files[0]);
            }
        });
    }
    
    if (dropZone) {
        // 点击拖放区域触发文件选择
        dropZone.addEventListener('click', () => fileInput && fileInput.click());
        
        // 拖拽事件
        dropZone.addEventListener('dragover', handleDragOver);
        dropZone.addEventListener('dragleave', handleDragLeave);
        dropZone.addEventListener('drop', handleDrop);
    }
    
    // 粘贴事件
    if (image3dPane) {
        image3dPane.addEventListener('paste', handlePaste);
    }
    
    // 移除图片按钮
    if (removeImageBtn) {
        removeImageBtn.addEventListener('click', resetInput);
    }
    
    // 开始生成按钮
    if (startBtn) {
        startBtn.addEventListener('click', handleStartProcessing);
    }
    
    // 3D 模型控制按钮
    if (rotateBtn) {
        rotateBtn.addEventListener('click', toggleRotation);
    }
    
    if (resetBtn) {
        resetBtn.addEventListener('click', resetCamera);
    }
    
    // Tab 切换事件
    if (image3dTab) {
        image3dTab.addEventListener('shown.bs.tab', handleTabShown);
    }
    
    // 加载更多历史记录
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', () => {
            if (!isLoadingHistory && currentHistoryPage < totalHistoryPages) {
                loadHistory(currentHistoryPage + 1, true);
            }
        });
    }

    // --- 事件处理函数 ---
    
    function handleDragOver(event) {
        event.preventDefault();
        dropZone.classList.add('drag-over');
    }
    
    function handleDragLeave(event) {
        event.preventDefault();
        dropZone.classList.remove('drag-over');
    }
    
    function handleDrop(event) {
        event.preventDefault();
        dropZone.classList.remove('drag-over');
        const files = event.dataTransfer.files;
        if (files && files.length > 0) {
            processFile(files[0]);
        }
    }
    
    function handlePaste(event) {
        const items = (event.clipboardData || window.clipboardData).items;
        for (let item of items) {
            if (item.kind === 'file' && item.type.startsWith('image/')) {
                const file = item.getAsFile();
                processFile(file);
                event.preventDefault();
                break;
            }
        }
    }
    
    function handleTabShown() {
        // Tab 被激活时加载历史记录
        loadHistory(1, false);
        
        // 获取功能成本（如果尚未获取）
        if (featureCost === null || animationFeatureCost === null) {
            fetchFeatureCost();
        }
        // 当tab显示时，也检查是否有挂起的任务
        checkForPendingImage3DTask();
        
        // 新增：检查是否有从其他组件传递过来的图片URL
        if (window.pendingImageData && window.pendingImageData.targetPaneId === 'image-to-3d-pane') {
            
            // 异步加载图片
            const loadPendingImage = async () => {
                try {
                    // 重置当前状态
                    resetInput();
                    
                    // 获取图片文件
                    const imageUrl = window.pendingImageData.imageUrl;
                    const response = await fetch(imageUrl);
                    if (!response.ok) {
                        throw new Error(`获取图片失败: ${response.status}`);
                    }
                    
                    // 转换为Blob并创建File对象
                    const blob = await response.blob();
                    
                    // 尝试从URL中获取文件名
                    let filename = 'image.png';
                    const urlParts = imageUrl.split('/').pop().split('?')[0].split('.');
                    if (urlParts.length > 1) {
                        const ext = urlParts.pop().toLowerCase();
                        const name = urlParts.join('.');
                        filename = `${name}.${ext}`;
                    }
                    
                    const file = new File([blob], filename, { type: blob.type || 'image/png' });
                    
                    // 使用现有的processFile函数处理文件
                    processFile(file);
                    
                    // 清除pendingImageData，避免重复处理
                    window.pendingImageData = null;
                } catch (error) {
                    console.error('处理传入图片失败:', error);
                    showError(`无法处理传入的图片: ${error.message}`);
                }
            };
            
            // 执行异步加载
            loadPendingImage();
        }
    }
    
    function toggleRotation() {
        autoRotate = !autoRotate;
        rotateBtn.innerHTML = autoRotate ? 
            '<i class="bi bi-pause-circle me-1"></i>停止旋转' : 
            '<i class="bi bi-arrow-repeat me-1"></i>旋转';
    }
    
    function resetCamera() {
        if (controls) {
            controls.reset();
        }
    }

    // --- 核心功能函数 ---
    
    function processFile(file) {
        // 文件类型验证
        if (!file.type.startsWith('image/')) {
            showError('请选择图片文件。');
            return;
        }
        
        // 文件大小验证
        // if (file.size > 5 * 1024 * 1024) { // 5MB 限制
        //     showError('图片文件大小不能超过 5MB。');
        //     return;
        // }
        
        currentFile = file;
        
        // 显示预览
        const reader = new FileReader();
        reader.onload = (e) => {
            previewImg.src = e.target.result;
            previewContainer.style.display = 'block';
            dropZone.style.display = 'none'; // 隐藏拖放区域
            startBtn.disabled = false;
            resetResultState(); // 清除之前的结果状态
        };
        reader.readAsDataURL(currentFile);
    }
    
    function resetInput() {
        currentFile = null;
        fileInput.value = null;
        previewImg.src = '';
        previewContainer.style.display = 'none';
        dropZone.style.display = 'flex';
        startBtn.disabled = true;
        resetResultState();
    }
    
    function resetResultState() {
        // 重置状态显示
        statusArea.style.display = 'none';
        modelContainer.style.display = 'none';
        errorMessage.style.display = 'none';
        resultsPlaceholder.style.display = 'block';
        
        // 停止轮询
        if (pollIntervalId) {
            clearInterval(pollIntervalId);
            pollIntervalId = null;
        }

        // Clear download link listener
        const newDownloadLink = downloadLink.cloneNode(true);
        if(downloadLink.parentNode) {
            downloadLink.parentNode.replaceChild(newDownloadLink, downloadLink);
        }
        downloadLink = newDownloadLink;
        downloadLink.textContent = "下载模型文件"; // Restore text
        downloadLink.href = '#'; // Reset href
    }
    
    function showError(message) {
        statusArea.style.display = 'block';
        errorMessage.style.display = 'block';
        errorMessage.textContent = message;
        progressContainer.style.display = 'none';
        statusMessage.textContent = '处理失败';
    }
    
    function updateProgress(progress, message, detail) {
        // 更新进度条和状态
        statusArea.style.display = 'block';
        progressContainer.style.display = 'block';
        resultsPlaceholder.style.display = 'none';
        
        const percent = Math.max(0, Math.min(100, progress));
        progressBar.style.width = `${percent}%`;
        progressBar.textContent = `${percent}%`;
        progressBar.setAttribute('aria-valuenow', percent);
        progressPercentage.textContent = `${percent}%`;
        
        // 修改进度条样式为黑白灰色调
        progressBar.className = 'progress-bar bg-dark'; // 使用黑色背景
        
        // 添加光效动画
        if (!progressBar.querySelector('.glow-effect')) {
            const glowEffect = document.createElement('div');
            glowEffect.className = 'glow-effect';
            progressBar.appendChild(glowEffect);
            
            // 添加CSS
            const style = document.createElement('style');
            style.textContent = `
                .progress-bar {
                    position: relative;
                    overflow: hidden;
                }
                .glow-effect {
                    position: absolute;
                    top: 0;
                    left: -50%;
                    width: 50%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                    animation: glow-animation 1.5s infinite;
                }
                @keyframes glow-animation {
                    0% { left: -50%; }
                    100% { left: 150%; }
                }
            `;
            document.head.appendChild(style);
        }
        
        if (message) {
            statusMessage.textContent = message;
        }
        
        if (detail) {
            progressDetail.textContent = detail;
        } else {
            // 根据进度阶段更新详细信息
            if (percent < 20) {
                progressDetail.textContent = '正在分析您的图像...';
            } else if (percent < 50) {
                progressDetail.textContent = '正在生成 3D 模型基础几何...';
            } else if (percent < 80) {
                progressDetail.textContent = '正在添加纹理和细节...';
            } else {
                progressDetail.textContent = '即将完成，进行最终处理...';
            }
        }

        // 添加按钮动效
        if (percent === 100) {
            if (startBtn) {
                startBtn.classList.add('btn-success');
                startBtn.classList.remove('btn-primary');
                setTimeout(() => {
                    startBtn.classList.remove('btn-success');
                    startBtn.classList.add('btn-primary');
                }, 2000);
            }
        }
    }
    
    async function handleStartProcessing() {
        if (!currentFile) {
            showError('请先选择一个图片文件');
            return;
        }
        
        // 检查认证令牌
        const token = getAuthToken();
        if (!token) {
            showError('您尚未登录或登录已过期，请重新登录');
            return;
        }
        
        startBtn.disabled = true;
        // 显示加载动画
        startBtn.querySelector('.spinner-border').style.display = 'inline-block';
        startBtn.querySelector('.bi').style.display = 'none';
        
        // 重置结果区域
        resetResultState();
        updateProgress(0, '准备上传图片...');
        
        // 创建表单数据
        const formData = new FormData();
        formData.append('imageFile', currentFile);
        
        try {
            // 调用后端API生成3D模型
            const response = await fetch(`${API_BASE_URL}/api/image-to-3d/generate-3d`, {
                method: 'POST',
                headers: getAuthHeaders(),
                body: formData
            });
            
            // --- 新的错误处理和成功处理逻辑 --- START ---
            if (!response.ok) {
                let displayErrorMessage = `图片转3D请求失败，请稍后再试。 (状态码: ${response.status})`;
                try {
                    const errorData = await response.json();
                    // 后端此接口可能在 result.message 或直接在 errorData.error 中返回错误
                    if (errorData && (errorData.error || errorData.message)) {
                        displayErrorMessage = errorData.error || errorData.message;
                    }
                } catch (parseError) {
                    console.warn('Image-to-3D: Failed to parse error JSON response from non-ok fetch:', parseError);
                }
                showError(displayErrorMessage); // 使用已有的 showError 函数
                startBtn.disabled = false;
                if(startBtn.querySelector('.spinner-border')) startBtn.querySelector('.spinner-border').style.display = 'none';
                if(startBtn.querySelector('.bi')) startBtn.querySelector('.bi').style.display = 'inline-block';
                return; // 直接返回，不继续执行
            }

            // 如果 response.ok 为 true，则处理成功逻辑
            const result = await response.json();
            if (result.success && result.taskId) {
                updateProgress(5, '图片上传成功，任务已创建，开始处理...');
                // --- 保存任务ID到localStorage --- START ---
                localStorage.setItem('imageTo3d_pending_task_id', result.taskId);
                localStorage.setItem('imageTo3d_pending_task_history_id', result.historyId); // 也保存historyId，可能有用
                // --- 保存任务ID到localStorage --- END ---
                pollTaskStatus(result.taskId); // pollTaskStatus 应该会处理按钮状态的后续变化
            } else {
                // 即使 response.ok，但后端业务逻辑可能失败
                showError(result.message || '提交任务成功，但未能开始处理，请检查任务状态或联系支持。');
                startBtn.disabled = false;
                if(startBtn.querySelector('.spinner-border')) startBtn.querySelector('.spinner-border').style.display = 'none';
                if(startBtn.querySelector('.bi')) startBtn.querySelector('.bi').style.display = 'inline-block';
            }
            // --- 新的错误处理和成功处理逻辑 --- END ---

        } catch (error) {
            console.error('上传或处理请求时出错:', error);
            showError('无法连接到服务器或发生意外错误');
            // 恢复按钮状态
            startBtn.disabled = false;
            startBtn.querySelector('.spinner-border').style.display = 'none';
            startBtn.querySelector('.bi').style.display = 'inline-block';
        }
    }
    
    function pollTaskStatus(taskId) {
        
        // 保存当前任务ID为全局变量，以便缩略图生成时使用
        window.currentTaskId = taskId;
        
        pollIntervalId = setInterval(async () => {
            try {
                const response = await fetch(`${API_BASE_URL}/api/image-to-3d/task-status/${taskId}`, {
                    headers: getAuthHeaders()
                });
                
                if (!response.ok) {
                    // 请求失败处理
                    console.error(`轮询请求失败: HTTP ${response.status}`);
                    return;
                }
                
                const result = await response.json();
                
                if (result.success) {
                    const status = result.status;
                    const progress = result.progress;
                    const resultUrl = result.resultUrl;
                    const previewUrl = result.previewUrl;
                    const errorMsg = result.error;
                    
                    if ((status === "success" || status === "completed") && resultUrl) {
                        // 任务完成
                        updateProgress(100, '任务完成!', '模型生成成功，正在加载预览...');
                        // 恢复按钮状态
                        startBtn.disabled = false;
                        startBtn.querySelector('.spinner-border').style.display = 'none';
                        startBtn.querySelector('.bi').style.display = 'inline-block';
                        // 加载并显示 3D 模型
                        modelUrl = resultUrl;
                        loadModel(resultUrl);
                        clearInterval(pollIntervalId);
                        pollIntervalId = null;
                        // --- 任务完成后移除localStorage中的ID --- START ---
                        localStorage.removeItem('imageTo3d_pending_task_id');
                        localStorage.removeItem('imageTo3d_pending_task_history_id');
                        // --- 任务完成后移除localStorage中的ID --- END ---
                        loadHistory(1, false);
                        
                    } else if (status === 'failed') {
                        // 任务失败
                        showError(errorMsg || '未知错误');
                        // 恢复按钮状态
                        startBtn.disabled = false;
                        startBtn.querySelector('.spinner-border').style.display = 'none';
                        startBtn.querySelector('.bi').style.display = 'inline-block';
                        clearInterval(pollIntervalId);
                        pollIntervalId = null;
                        // --- 任务完成后移除localStorage中的ID --- START ---
                        localStorage.removeItem('imageTo3d_pending_task_id');
                        localStorage.removeItem('imageTo3d_pending_task_history_id');
                        // --- 任务完成后移除localStorage中的ID --- END ---
                        
                    } else if (status === 'running') {
                        // 任务进行中
                        updateProgress(progress, '模型生成中...');
                        
                    } else if (status === 'queued') {
                        // 任务排队中
                        updateProgress(0, '任务排队中...', '您的任务正在队列中等待处理，这可能需要一些时间');
                        
                    } else {
                        // 其他未知状态
                        updateProgress(progress, `未知状态: ${status}`);
                    }
                } else {
                    // 后端返回失败
                    showError(result.message || '查询任务状态失败');
                    // 恢复按钮状态
                    startBtn.disabled = false;
                    startBtn.querySelector('.spinner-border').style.display = 'none';
                    startBtn.querySelector('.bi').style.display = 'inline-block';
                    clearInterval(pollIntervalId);
                    pollIntervalId = null;
                }
            } catch (error) {
                console.error('轮询过程中发生错误:', error);
                // 网络错误等，暂时不停止轮询，让它重试
            }
        }, 3000); // 每 3 秒轮询一次
    }
    
    // --- 3D 模型相关函数 ---
    
    function initThreeJs() {
        // 创建场景
        scene = new THREE.Scene();
        scene.background = new THREE.Color(0x1a1a1a); // 深灰色背景
        
        // 添加网格地面 - 水平网格
        const gridHelper = new THREE.GridHelper(20, 20, 0x555555, 0x282828);
        scene.add(gridHelper);
        
        // 添加背景网格 - 垂直网格
        const verticalGrid = new THREE.GridHelper(20, 20, 0x555555, 0x282828);
        verticalGrid.rotation.x = Math.PI / 2;
        verticalGrid.position.z = -10;
        scene.add(verticalGrid);
        
        // 创建相机
        camera = new THREE.PerspectiveCamera(75, modelViewer.clientWidth / modelViewer.clientHeight, 0.1, 1000);
        camera.position.z = 5;
        
        // 创建渲染器
        renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            preserveDrawingBuffer: true // 允许我们捕获渲染画面
        });
        renderer.setSize(modelViewer.clientWidth, modelViewer.clientHeight);
        renderer.outputEncoding = THREE.sRGBEncoding;
        
        // 清空现有内容并添加渲染器
        while (modelViewer.firstChild) {
            modelViewer.removeChild(modelViewer.firstChild);
        }
        modelViewer.appendChild(renderer.domElement);
        
        // 添加环境光和定向光
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.7);
        scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(1, 1, 1);
        scene.add(directionalLight);
        
        // 添加来自背面的填充光
        const backLight = new THREE.DirectionalLight(0xffffff, 0.5);
        backLight.position.set(-1, 0.5, -1);
        scene.add(backLight);
        
        // 添加轨道控制
        controls = new THREE.OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.25;
        
        // 监听窗口大小变化
        window.addEventListener('resize', onWindowResize);
        
        // 开始动画循环
        animate();
    }
    
    function onWindowResize() {
        if (camera && renderer && modelViewer) {
            camera.aspect = modelViewer.clientWidth / modelViewer.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(modelViewer.clientWidth, modelViewer.clientHeight);
        }
    }
    
    function animate() {
        requestAnimationFrame(animate);

        if (autoRotate && model) {
            model.rotation.y += 0.01;
        }
        if (controls) controls.update();

        // --- 动画驱动 START ---
        if (mixer) mixer.update(clock.getDelta());
        // --- 动画驱动 END ---

        if (renderer && scene && camera) renderer.render(scene, camera);
    }
    
    function loadModel(url) {
        modelContainer.style.display = 'block';
        resultsPlaceholder.style.display = 'none';
        errorMessage.style.display = 'none'; // Hide error messages from previous operations

        // Ensure the status area for messages is visible and reset for model loading
        if (statusArea) statusArea.style.display = 'block';
        if (progressContainer) {
            progressContainer.style.display = 'block';
            // Clear/reset elements related to overall task progress
            if (progressBar) {
                progressBar.style.width = '0%';
                progressBar.textContent = '';
                progressBar.setAttribute('aria-valuenow', 0);
            }
            if (progressPercentage) progressPercentage.textContent = '';
        }
        if (statusMessage) statusMessage.textContent = '模型预览'; // Set a relevant status message
        if (progressDetail) progressDetail.textContent = '准备加载模型...'; // Initial placeholder

        if (!url) {
            console.error("模型URL无效，无法加载。");
            if (progressDetail) progressDetail.textContent = '模型URL无效，无法加载。';
            modelViewer.innerHTML = `
                <div style="padding: 20px; text-align: center; color: white;">
                    <p>模型文件加载失败</p>
                    <p>URL无效或文件未成功上传到服务器。</p>
                </div>
            `;
            if(downloadLink) downloadLink.style.display = 'none';
            if(rotateBtn) rotateBtn.style.display = 'none';
            if(resetBtn) resetBtn.style.display = 'none';
            return;
        } else {
            if(downloadLink) downloadLink.style.display = 'inline-block';
            if(rotateBtn) rotateBtn.style.display = 'inline-block';
            if(resetBtn) resetBtn.style.display = 'inline-block';
        }

        if (!scene) initThreeJs();
        if (model) {
            scene.remove(model);
            // Dispose of old model resources if necessary (geometry, material, textures)
            model.traverse(object => {
                if (object.isMesh) {
                    if (object.geometry) object.geometry.dispose();
                    if (object.material) {
                        if (Array.isArray(object.material)) {
                            object.material.forEach(material => material.dispose());
                        } else {
                            object.material.dispose();
                        }
                    }
                }
            });
            model = null;
        }
        
        const loader = new THREE.GLTFLoader();
        if (progressDetail) progressDetail.textContent = '正在加载 3D 模型...';
        
        loader.load(
            url, 
            (gltf) => {
                model = gltf.scene;
                const box = new THREE.Box3().setFromObject(model);
                const size = box.getSize(new THREE.Vector3()).length();
                const center = box.getCenter(new THREE.Vector3());
                model.position.sub(center); // Center the model
                scene.add(model);
                
                // Adjust camera
                if (camera && controls) {
                    camera.position.set(0, size * 0.5, size * 1.5); // Adjust position based on model size
                    camera.lookAt(0, 0, 0); // Ensure camera looks at the origin (where model is centered)
                    controls.target.set(0,0,0); // Ensure controls target the origin
                    controls.update();
                }
                
                renderer.render(scene, camera);
                if (progressDetail) progressDetail.textContent = '3D 模型加载完成';
                
                // Optionally hide progress message after a delay
                setTimeout(() => {
                    if (progressDetail && progressDetail.textContent === '3D 模型加载完成') {
                        progressDetail.textContent = ''; // Clear the loading message
                    }
                    if (statusMessage && statusMessage.textContent === '模型预览' && model) {
                        statusMessage.textContent = '模型已加载'; // Update status
                    }
                }, 2500);
                
                // Generate and save thumbnail (existing logic)
                setTimeout(() => {
                    try {
                        // 确保渲染器已经渲染了当前场景
                        renderer.render(scene, camera);
                        
                        // 获取画布数据
                        const dataUrl = renderer.domElement.toDataURL('image/png');
                        
                        if (window.currentTaskId) {
                            
                            // 上传缩略图到服务器
                            fetch(`${API_BASE_URL}/api/image-to-3d/save-thumbnail`, {
                                method: 'POST',
                                headers: {
                                    ...getAuthHeaders(),
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    taskId: window.currentTaskId,
                                    thumbnailData: dataUrl
                                })
                            })
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error(`HTTP错误: ${response.status}`);
                                }
                                return response.json();
                            })
                            .then(data => {
                                // 刷新历史记录以显示新的缩略图
                                loadHistory(1, false);
                            })
                            .catch(error => {
                                console.error('保存缩略图失败:', error);
                            });
                        } else {
                            console.warn('无法确定当前任务ID，无法保存缩略图');
                        }
                    } catch (e) {
                        console.error('创建缩略图失败:', e);
                    }
                }, 500); // 给渲染器一些时间来完成渲染

                // --- 动画支持 START ---
                if (mixer) mixer.stopAllAction(); // 清理上一个动画
                mixer = null;
                if (gltf.animations && gltf.animations.length > 0) {
                    mixer = new THREE.AnimationMixer(model);
                    gltf.animations.forEach((clip) => {
                        mixer.clipAction(clip).play();
                    });
                }
                // --- 动画支持 END ---
            },
            (xhr) => {
                // 模型加载进度
                const percent = Math.round((xhr.loaded / xhr.total) * 100);
                if (progressDetail) progressDetail.textContent = `3D 模型加载中... ${percent}%`;
            },
            (error) => {
                console.error('模型加载失败:', error);
                if (progressDetail) progressDetail.textContent = '模型加载失败，请尝试直接下载查看';
                if (statusMessage) statusMessage.textContent = '加载失败';
                modelViewer.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: white;">
                        <p>3D 模型加载失败</p>
                        <p>您可以点击下方链接下载并使用其他软件查看</p>
                    </div>
                `;
            }
        );
        
        // --- Setup Download Link --- START ---
        // Clear previous listener and set new one
        const newDownloadLink = downloadLink.cloneNode(true);
        if(downloadLink.parentNode){
            downloadLink.parentNode.replaceChild(newDownloadLink, downloadLink);
        }
        downloadLink = newDownloadLink;
        downloadLink.innerHTML = '<i class="bi bi-download"></i> 下载模型文件'; // 简化文本
        downloadLink.style.minWidth = '140px'; // 确保按钮宽度足够
        downloadLink.href = url; // 直接设置为模型URL
        downloadLink.target = "_blank"; // 在新窗口打开
        downloadLink.setAttribute('download', ''); // 尝试触发下载

        // 如果URL是ComfyUI或内部URL，使用我们的下载函数
        if (url.includes(COMFYUI_BASE_URL) || url.includes(API_BASE_URL)) {
            downloadLink.href = '#'; // 防止默认导航
            downloadLink.addEventListener('click', (event) => {
                event.preventDefault();
                // 使用下载函数处理
                initiateModelDownload(url, downloadLink, 'model.glb'); 
            });
        }
        // --- Setup Download Link --- END ---

        // Show expiration message (no changes needed here)
        let expirationMessageElement = document.getElementById('modelLinkExpirationMessage');
        if (expirationMessageElement) {
            expirationMessageElement.style.display = 'none'; // 隐藏此消息，因为ComfyUI链接是永久的
        }

        // --- 创建更多操作下拉菜单 ---
        let moreActionsDropdown = document.getElementById('moreModelActionsDropdown');
        if (!moreActionsDropdown) {
            // 创建下拉菜单容器
            const dropdownDiv = document.createElement('div');
            dropdownDiv.className = 'dropdown d-inline-block ms-2';
            
            // 创建下拉菜单按钮
            moreActionsDropdown = document.createElement('button');
            moreActionsDropdown.id = 'moreModelActionsDropdown';
            moreActionsDropdown.className = 'btn btn-outline-secondary dropdown-toggle';
            moreActionsDropdown.style.minWidth = '120px'; // 确保按钮有足够宽度显示文字
            moreActionsDropdown.type = 'button';
            moreActionsDropdown.setAttribute('data-bs-toggle', 'dropdown');
            moreActionsDropdown.setAttribute('aria-expanded', 'false');
            moreActionsDropdown.innerHTML = '<i class="bi bi-three-dots"></i> 更多操作';
            
            // 创建下拉菜单内容
            const dropdownMenu = document.createElement('ul');
            dropdownMenu.className = 'dropdown-menu dropdown-menu-dark dropdown-menu-end'; // 靠右对齐
            dropdownMenu.style.backgroundColor = '#2c2c2c'; // 深色背景
            dropdownMenu.style.border = '1px solid #444'; // 更细微的边框
            dropdownMenu.style.borderRadius = '6px'; // 圆角
            dropdownMenu.style.boxShadow = '0 3px 8px rgba(0,0,0,0.4)'; // 轻微阴影
            dropdownMenu.style.zIndex = '1050'; // 确保显示在最上层
            dropdownMenu.style.minWidth = moreActionsDropdown.style.minWidth; // 设置下拉菜单的最小宽度与按钮一致
            
            // 添加到DOM
            dropdownDiv.appendChild(moreActionsDropdown);
            dropdownDiv.appendChild(dropdownMenu);
            
            // 插入到下载按钮后面
            if (downloadLink && downloadLink.parentNode) {
                downloadLink.parentNode.insertBefore(dropdownDiv, downloadLink.nextSibling);
            }
        } else {
            // 获取现有下拉菜单
            const dropdownMenu = moreActionsDropdown.nextElementSibling;
            if (dropdownMenu) {
                dropdownMenu.innerHTML = ''; // 清空现有菜单项
            }
        }
        
        // 获取下拉菜单内容容器
        const dropdownMenu = moreActionsDropdown.parentElement.querySelector('.dropdown-menu');
        
        // 检查URL是否来自动画模型
        const isAnimatedModel = url.includes('anim_task') || url.includes('tripo_retarget') || 
                              (window.currentModel && window.currentModel.isAnimated) ||
                              url.includes('animated') || url.includes('animation');
        
        // 根据模型类型添加不同的菜单项
        if (isAnimatedModel) {
            // 如果是动画模型，添加"下载动画模型"菜单项
            const downloadAnimItem = document.createElement('li');
            const downloadAnimLink = document.createElement('a');
            downloadAnimLink.className = 'dropdown-item';
            downloadAnimLink.href = '#';
            downloadAnimLink.innerHTML = '<i class="bi bi-download"></i> 下载动画模型';
            downloadAnimLink.onclick = (event) => {
                event.preventDefault();
                initiateModelDownload(url, downloadAnimLink, 'animated_model.glb');
            };
            styleDropdownItem(downloadAnimLink); // 应用统一样式
            downloadAnimItem.appendChild(downloadAnimLink);
            dropdownMenu.appendChild(downloadAnimItem);
            
            // 设置标记，记住这是一个动画模型
            window.currentModel = { isAnimated: true, url: url };
        } else {
            // 如果是普通模型，添加"制作动画"菜单项
            const makeAnimItem = document.createElement('li');
            const makeAnimLink = document.createElement('a');
            makeAnimLink.className = 'dropdown-item';
            makeAnimLink.href = '#';
            makeAnimLink.innerHTML = '<i class="bi bi-film"></i> 制作动画';
            makeAnimLink.onclick = (event) => {
                event.preventDefault();
                if (!window.currentTaskId) {
                    showImage3DError('无法获取原始模型任务ID，无法开始动画制作。');
                    return;
                }
                handleStartAnimationProcess(window.currentTaskId);
            };
            styleDropdownItem(makeAnimLink); // 应用统一样式
            makeAnimItem.appendChild(makeAnimLink);
            dropdownMenu.appendChild(makeAnimItem);
            
            // 更新标记
            window.currentModel = { isAnimated: false, url: url };
        }
        
        // 添加分隔线
        const divider = document.createElement('li');
        divider.innerHTML = '<hr class="dropdown-divider" style="border-color: #555;">';
        dropdownMenu.appendChild(divider);
        
        // 无论什么类型的模型，都添加"旋转模型"菜单项
        const rotateItem = document.createElement('li');
        const rotateLink = document.createElement('a');
        rotateLink.className = 'dropdown-item';
        rotateLink.href = '#';
        rotateLink.innerHTML = autoRotate ? 
            '<i class="bi bi-pause-circle"></i> 停止旋转' : 
            '<i class="bi bi-arrow-repeat"></i> 旋转模型';
        rotateLink.onclick = (event) => {
            event.preventDefault();
            toggleRotation();
            rotateLink.innerHTML = autoRotate ? 
                '<i class="bi bi-pause-circle"></i> 停止旋转' : 
                '<i class="bi bi-arrow-repeat"></i> 旋转模型';
        };
        styleDropdownItem(rotateLink); // 应用统一样式
        rotateItem.appendChild(rotateLink);
        dropdownMenu.appendChild(rotateItem);
        
        // 添加"重置视角"菜单项
        const resetItem = document.createElement('li');
        const resetLink = document.createElement('a');
        resetLink.className = 'dropdown-item';
        resetLink.href = '#';
        resetLink.innerHTML = '<i class="bi bi-arrows-fullscreen"></i> 重置视角';
        resetLink.onclick = (event) => {
            event.preventDefault();
            resetCamera();
        };
        styleDropdownItem(resetLink); // 应用统一样式
        resetItem.appendChild(resetLink);
        dropdownMenu.appendChild(resetItem);
        
        // 隐藏之前的单独按钮
        if (rotateBtn) rotateBtn.style.display = 'none';
        if (resetBtn) resetBtn.style.display = 'none';
        
        // 删除旧的按钮以避免重复
        const oldMakeAnimBtn = document.getElementById('makeAnimationBtn');
        if (oldMakeAnimBtn) oldMakeAnimBtn.remove();
        
        const oldDownloadAnimBtn = document.getElementById('downloadAnimationBtn');
        if (oldDownloadAnimBtn) oldDownloadAnimBtn.remove();
    }
    
    // --- 历史记录功能 ---
    
    async function loadHistory(page = 1, append = false) {
        try {
            isLoadingHistory = true;
            
            if (!append) {
                historyList.innerHTML = '';
                historyPlaceholder.textContent = '加载历史记录中...';
                historyPlaceholder.style.display = 'block';
            }
            
            loadMoreContainer.style.display = 'none';
            
            
            // 调用API加载历史记录
            const response = await fetch(`${API_BASE_URL}/api/image-to-3d/history?page=${page}`, {
                headers: getAuthHeaders()
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}`);
            }
            
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || '加载历史记录失败');
            }
            
            // 更新分页数据
            currentHistoryPage = page;
            totalHistoryPages = data.totalPages || 1;
            
            // 处理数据
            if (data.history && data.history.length > 0) {
                
                if (!append) {
                    historyList.innerHTML = '';
                }
                
                historyPlaceholder.style.display = 'none';
                
                // 渲染历史记录项
                data.history.forEach(item => {
                    const historyItem = createHistoryItem(item);
                    historyList.appendChild(historyItem);
                });
                
                // 显示加载更多按钮
                if (currentHistoryPage < totalHistoryPages) {
                    loadMoreContainer.style.display = 'block';
                } else {
                    loadMoreContainer.style.display = 'none';
                }
            } else {
                if (!append) {
                    // 只有在非追加模式下才显示无记录
                    historyPlaceholder.textContent = '暂无历史记录';
                    historyPlaceholder.style.display = 'block';
                }
                loadMoreContainer.style.display = 'none';
            }
        } catch (error) {
            console.error('加载历史记录失败:', error);
            if (!append) {
                historyPlaceholder.textContent = '加载历史记录失败';
                historyPlaceholder.style.display = 'block';
            }
        } finally {
            isLoadingHistory = false;
        }
    }
    
    // 创建一个内联的默认缩略图
    const DEFAULT_THUMBNAIL_BASE64 = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMjQgMjQiIGZpbGw9Im5vbmUiIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1jdWJlIj48cGF0aCBkPSJNMTggOS41djcuNWEyLjUgMi41IDAgMCAxLTIuNSAyLjVINy41YTIuNSAyLjUgMCAwIDEtMi41LTIuNVY5LjUiLz48cGF0aCBkPSJNNi41IDE2LjVWOS41YTIuNSAyLjUgMCAwIDEgMi41LTIuNWg2LjkyOGEyLjUgMi41IDAgMCAxIDIuMDcyIDEuMDcyTDYgMTZoMHoiLz48bGluZSB4MT0iNiIgeTE9IjE2IiB4Mj0iMTcuNSIgeTI9IjE2Ii8+PC9zdmc+';

    // 修改缩略图加载过程，使用内联默认图片
    function createHistoryItem(item) {
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item-3d d-flex';
        // historyItem.dataset.taskId = item.tripo_task_id || item.id; // 用于可能的DOM选择
        
        const thumbnailContainer = document.createElement('div');
        thumbnailContainer.className = 'history-thumbnail me-3';
        thumbnailContainer.style.cssText = 'width: 100px; height: 100px; flex-shrink: 0;';
        
        const thumbnail = document.createElement('img');
        thumbnail.src = item.thumbnail_url || item.preview_url || DEFAULT_THUMBNAIL_BASE64;
        thumbnail.alt = '3D模型缩略图';
        thumbnail.className = 'rounded img-fluid';
        thumbnail.style.cssText = 'object-fit: cover; width: 100%; height: 100%; background-color: #343a40;';
        thumbnail.onerror = function() { this.src = DEFAULT_THUMBNAIL_BASE64; };
        thumbnailContainer.appendChild(thumbnail);
        
        // --- Main Content Container (to the right of thumbnail) ---
        const mainContentContainer = document.createElement('div');
        mainContentContainer.className = 'flex-grow-1 d-flex flex-column justify-content-between';

        // --- Top Row: Title, ID, Date (left) and Status Badge (right) ---
        const topRowDiv = document.createElement('div');
        topRowDiv.className = 'd-flex justify-content-between align-items-start mb-2';

        const topLeftInfoDiv = document.createElement('div');
        topLeftInfoDiv.style.overflow = 'hidden';
        topLeftInfoDiv.style.minWidth = '0';

        const nameElement = document.createElement('h6');
        nameElement.className = 'mb-1 text-truncate text-light d-block'; 
        const originalName = item.name || '未命名模型';
        nameElement.textContent = originalName.length > 30 ? originalName.substring(0, 30) + '...' : originalName;
        nameElement.title = originalName; // Title always shows the full name
        topLeftInfoDiv.appendChild(nameElement);

        if (item.tripo_task_id) {
            const modelIdElement = document.createElement('small');
            modelIdElement.className = 'text-muted d-block mb-1 text-truncate'; 
            const originalTaskId = `任务ID: ${item.tripo_task_id}`;
            modelIdElement.textContent = originalTaskId.length > 30 ? originalTaskId.substring(0, 30) + '...' : originalTaskId;
            modelIdElement.title = originalTaskId; // Title always shows the full ID string
            topLeftInfoDiv.appendChild(modelIdElement);
        }

        const dateElement = document.createElement('small');
        dateElement.className = 'text-muted d-block';
        dateElement.textContent = item.created_at ? new Date(item.created_at).toLocaleString() : '日期未知';
        topLeftInfoDiv.appendChild(dateElement);
        
        topRowDiv.appendChild(topLeftInfoDiv);

        // Status Badge
        const statusBadge = document.createElement('span');
        statusBadge.className = 'badge glassmorphism'; // 添加 glassmorphism 类
        const status = item.status ? item.status.toLowerCase() : 'unknown';

        if (status === 'completed' || status === 'success') {
            statusBadge.classList.add('bg-success');
            statusBadge.textContent = '成功';
        } else if (status === 'failed' || status === 'cancelled') {
            statusBadge.classList.add('bg-danger');
            statusBadge.textContent = '失败';
        } else if (['pending', 'processing', 'queued', 'running'].includes(status)) {
            statusBadge.classList.add('bg-info');
            statusBadge.innerHTML = '处理中 <span class="spinner-border spinner-border-sm ms-1" role="status" aria-hidden="true"></span>';
        } else {
            statusBadge.classList.add('bg-secondary');
            statusBadge.textContent = '未知';
        }
        topRowDiv.appendChild(statusBadge);
        mainContentContainer.appendChild(topRowDiv);

        // --- Bottom Row: Action Buttons ---
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'mt-auto d-flex flex-wrap gap-2'; // Added d-flex, flex-wrap, and gap-2 for button spacing and wrapping

        const modelUrl = item.model_url;
        if ((status === 'completed' || status === 'success') && modelUrl) {
            // VIEW/LOAD Button
            const viewButton = document.createElement('button');
            viewButton.className = 'btn btn-sm btn-outline-primary'; // Primary outline style for view
            viewButton.innerHTML = '<i class="bi bi-eye"></i> 查看';
            viewButton.style.minWidth = '80px'; // 确保按钮有最小宽度
            viewButton.onclick = () => {
                // Reset the main input/processing UI state
                resetInput(); 

                // 保存当前查看的模型任务ID到全局变量
                window.currentTaskId = item.tripo_task_id;

                // Load the model from history
                loadModel(modelUrl);
                
                // Scroll to the model viewer area
                const modelViewerElement = document.getElementById('image3dModelViewer');
                if (modelViewerElement) {
                    modelViewerElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            };
            actionsDiv.appendChild(viewButton);

            // 创建更多操作下拉菜单
            const dropdownDiv = document.createElement('div');
            dropdownDiv.className = 'dropdown d-inline-block ms-2';
            dropdownDiv.style.position = 'relative'; // 确保定位
            
            const moreActionsBtn = document.createElement('button');
            moreActionsBtn.className = 'btn btn-sm btn-outline-secondary dropdown-toggle';
            moreActionsBtn.style.minWidth = '120px'; // 确保按钮有足够宽度显示文字
            moreActionsBtn.setAttribute('data-bs-toggle', 'dropdown');
            moreActionsBtn.setAttribute('aria-expanded', 'false');
            moreActionsBtn.setAttribute('data-bs-auto-close', 'true'); // 确保点击后自动关闭
            moreActionsBtn.innerHTML = '<i class="bi bi-three-dots"></i> 更多操作';
            
            const dropdownMenu = document.createElement('ul');
            dropdownMenu.className = 'dropdown-menu dropdown-menu-dark dropdown-menu-end'; // 靠右对齐
            dropdownMenu.style.backgroundColor = '#2c2c2c'; // 深色背景
            dropdownMenu.style.border = '1px solid #444'; // 更细微的边框
            dropdownMenu.style.borderRadius = '6px'; // 圆角
            dropdownMenu.style.boxShadow = '0 3px 8px rgba(0,0,0,0.4)'; // 轻微阴影
            dropdownMenu.style.zIndex = '1050'; // 确保显示在最上层
            dropdownMenu.style.minWidth = moreActionsBtn.style.minWidth; // 设置下拉菜单的最小宽度与按钮一致
            
            // 添加下载原始模型选项
            const downloadItem = document.createElement('li');
            const downloadLink = document.createElement('a');
            downloadLink.className = 'dropdown-item';
            downloadLink.href = '#';
            downloadLink.innerHTML = '<i class="bi bi-download"></i> 下载模型';
            downloadLink.onclick = (event) => {
                event.preventDefault();
                let defaultFilename = 'model.glb';
                if (item.name) {
                    defaultFilename = `${item.name.replace(/[^a-zA-Z0-9_\-]+/g, '_')}_${item.tripo_task_id || 'model'}.glb`;
                } else if (item.tripo_task_id) {
                    defaultFilename = `task_${item.tripo_task_id}_model.glb`;
                }
                initiateModelDownload(modelUrl, downloadLink, defaultFilename);
            };
            styleDropdownItem(downloadLink); // 应用统一样式
            downloadItem.appendChild(downloadLink);
            dropdownMenu.appendChild(downloadItem);
            
            // 如果没有动画模型，添加制作动画选项
            if (!item.animated_model_url || item.animation_status !== 'completed') {
                const animationItem = document.createElement('li');
                const animationLink = document.createElement('a');
                animationLink.className = 'dropdown-item';
                animationLink.href = '#';
                animationLink.innerHTML = '<i class="bi bi-film"></i> 制作动画';
                animationLink.onclick = (event) => {
                    event.preventDefault();
                    if (!item.tripo_task_id) {
                        showImage3DError('无法获取原始模型任务ID，无法开始动画制作。');
                        return;
                    }
                    handleStartAnimationProcess(item.tripo_task_id);
                };
                styleDropdownItem(animationLink); // 应用统一样式
                animationItem.appendChild(animationLink);
                dropdownMenu.appendChild(animationItem);
            }
            
            // 如果有动画模型且状态完成，添加查看和下载动画选项
            if (item.animated_model_url && item.animation_status === 'completed') {
                // 添加分隔线
                const divider = document.createElement('li');
                divider.innerHTML = '<hr class="dropdown-divider" style="border-color: #555;">';
                dropdownMenu.appendChild(divider);
                
                const viewAnimItem = document.createElement('li');
                const viewAnimLink = document.createElement('a');
                viewAnimLink.className = 'dropdown-item';
                viewAnimLink.href = '#';
                viewAnimLink.innerHTML = '<i class="bi bi-film"></i> 查看动画';
                viewAnimLink.onclick = (event) => {
                    event.preventDefault();
                    resetInput();
                    loadModel(item.animated_model_url);
                    const modelViewerElement = document.getElementById('image3dModelViewer');
                    if (modelViewerElement) {
                        modelViewerElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                };
                styleDropdownItem(viewAnimLink); // 应用统一样式
                viewAnimItem.appendChild(viewAnimLink);
                dropdownMenu.appendChild(viewAnimItem);
                
                const downloadAnimItem = document.createElement('li');
                const downloadAnimLink = document.createElement('a');
                downloadAnimLink.className = 'dropdown-item';
                downloadAnimLink.href = '#';
                downloadAnimLink.innerHTML = '<i class="bi bi-download"></i> 下载动画模型';
                downloadAnimLink.onclick = (event) => {
                    event.preventDefault();
                    let defaultFilename = 'animated_model.glb';
                    if (item.name) {
                        defaultFilename = `${item.name.replace(/[^a-zA-Z0-9_\-]+/g, '_')}_animated.glb`;
                    }
                    initiateModelDownload(item.animated_model_url, downloadAnimLink, defaultFilename);
                };
                styleDropdownItem(downloadAnimLink); // 应用统一样式
                downloadAnimItem.appendChild(downloadAnimLink);
                dropdownMenu.appendChild(downloadAnimItem);
            }
            
            // 组装并添加下拉菜单
            dropdownDiv.appendChild(moreActionsBtn);
            dropdownDiv.appendChild(dropdownMenu);
            actionsDiv.appendChild(dropdownDiv);
        }

        if(actionsDiv.hasChildNodes()){ // Only append actionsDiv if it has buttons
            mainContentContainer.appendChild(actionsDiv);
        }

        historyItem.appendChild(thumbnailContainer);
        historyItem.appendChild(mainContentContainer);
        
        return historyItem;
    }

    // --- 可复用的文件下载函数 (Adapting from vectorize) --- START ---
    async function initiateModelDownload(modelUrl, clickedElement, suggestedFilename = 'model.glb') {
        const originalContent = clickedElement.innerHTML;
        clickedElement.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> 下载中...';
            clickedElement.disabled = true;

        try {
            // For GLB files from history, modelUrl should be directly fetchable
            const response = await fetch(modelUrl); 
            if (!response.ok) {
                 // Try to get more specific error from response body if available
                let errorText = response.statusText;
                try {
                    const errorJson = await response.json();
                    if (errorJson && errorJson.message) {
                        errorText = errorJson.message;
                    } else if (typeof errorJson.error === 'string') {
                        errorText = errorJson.error;
                    }
                } catch (e) { /* ignore parsing error */ }
                throw new Error(`下载失败: ${response.status} ${errorText}`);
            }
            const blob = await response.blob();
            const blobUrl = window.URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = blobUrl;
            a.download = suggestedFilename;
            
            document.body.appendChild(a);
            a.click();
            
            setTimeout(() => {
                document.body.removeChild(a);
                window.URL.revokeObjectURL(blobUrl);
                clickedElement.innerHTML = originalContent;
                clickedElement.disabled = false;
            }, 100);

        } catch (error) {
            console.error('模型下载失败:', error);
            clickedElement.innerHTML = '<i class="bi bi-exclamation-triangle me-1"></i> 下载失败';
            // Restore original button text after a delay
            setTimeout(() => {
                clickedElement.innerHTML = originalContent;
                clickedElement.disabled = false;
            }, 2000); // Show error for 2 seconds
        }
    }
    // --- 可复用的文件下载函数 --- END ---

    // 添加显示动画选择弹窗的函数
    function showAnimationTypeModal(originalModelTaskId) {
        // 创建模态对话框
        const costInfo = animationFeatureCost ? `<span class="text-warning ms-2">(${animationFeatureCost}积分)</span>` : '';
        const modalHtml = `
        <div class="modal fade" id="animationTypeModal" tabindex="-1" aria-labelledby="animationTypeModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content bg-dark text-light" style="background: rgba(18, 18, 20, 0.9) !important; backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1);">
                    <div class="modal-header" style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                        <h5 class="modal-title" id="animationTypeModalLabel">选择动画类型${costInfo}</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info mb-3" role="alert">
                            <i class="bi bi-info-circle"></i> 制作动画将消耗${animationFeatureCost || '一定数量的'}积分，赠送积分无法使用该功能。
                        </div>
                        <div class="row row-cols-2 g-3">
                            <div class="col">
                                <button class="btn btn-outline-primary w-100 anim-type-btn glassmorphism-btn" data-anim-type="walk">
                                    <i class="bi bi-person me-1"></i> 走路
                                </button>
                            </div>
                            <div class="col">
                                <button class="btn btn-outline-primary w-100 anim-type-btn glassmorphism-btn" data-anim-type="run">
                                    <i class="bi bi-fast-forward-fill me-1"></i> 跑步
                                </button>
                            </div>
                            <div class="col">
                                <button class="btn btn-outline-primary w-100 anim-type-btn glassmorphism-btn" data-anim-type="idle">
                                    <i class="bi bi-person-fill me-1"></i> 待机
                                </button>
                            </div>
                            <div class="col">
                                <button class="btn btn-outline-primary w-100 anim-type-btn glassmorphism-btn" data-anim-type="jump">
                                    <i class="bi bi-arrow-up-circle me-1"></i> 跳跃
                                </button>
                            </div>
                            <div class="col">
                                <button class="btn btn-outline-primary w-100 anim-type-btn glassmorphism-btn" data-anim-type="climb">
                                    <i class="bi bi-ladder me-1"></i> 攀爬
                                </button>
                            </div>
                            <div class="col">
                                <button class="btn btn-outline-primary w-100 anim-type-btn glassmorphism-btn" data-anim-type="slash">
                                    <i class="bi bi-slash-circle me-1"></i> 挥砍
                                </button>
                            </div>
                            <div class="col">
                                <button class="btn btn-outline-primary w-100 anim-type-btn glassmorphism-btn" data-anim-type="shoot">
                                    <i class="bi bi-bullseye me-1"></i> 射击
                                </button>
                            </div>
                            <div class="col">
                                <button class="btn btn-outline-primary w-100 anim-type-btn glassmorphism-btn" data-anim-type="turn">
                                    <i class="bi bi-arrow-clockwise me-1"></i> 转身
                                </button>
                            </div>
                            <div class="col">
                                <button class="btn btn-outline-primary w-100 anim-type-btn glassmorphism-btn" data-anim-type="hurt">
                                    <i class="bi bi-bandaid me-1"></i> 受伤
                                </button>
                            </div>
                            <div class="col">
                                <button class="btn btn-outline-primary w-100 anim-type-btn glassmorphism-btn" data-anim-type="fall">
                                    <i class="bi bi-arrow-down-circle me-1"></i> 跌落
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer" style="border-top: 1px solid rgba(255, 255, 255, 0.1);">
                        <button type="button" class="btn glassmorphism-btn" data-bs-dismiss="modal" style="background: rgba(30, 30, 32, 0.8) !important; color: #e0e0e0;">取消</button>
                    </div>
                </div>
            </div>
        </div>
        `;
        
        // 删除可能存在的之前的模态框
        const existingModal = document.getElementById('animationTypeModal');
        if (existingModal) {
            existingModal.remove();
        }
        
        // 添加模态框到DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // 获取模态框引用
        const modal = new bootstrap.Modal(document.getElementById('animationTypeModal'));
        
        // 为动画类型按钮添加点击事件监听器
        document.querySelectorAll('.anim-type-btn').forEach(button => {
            button.addEventListener('click', () => {
                const animationType = button.getAttribute('data-anim-type');
                modal.hide(); // 隐藏模态框
                // 调用制作动画函数，传入选择的动画类型
                startAnimationProcess(originalModelTaskId, animationType);
            });
        });
        
        // 显示模态框
        modal.show();
    }

    // 将原来的handleStartAnimationProcess修改为两个函数
    function handleStartAnimationProcess(originalModelTaskId) {
        if (!originalModelTaskId) {
            showImage3DError('无法获取原始模型任务ID，无法开始动画制作。');
            return;
        }
        
        // 显示动画类型选择弹窗
        showAnimationTypeModal(originalModelTaskId);
    }

    // 新增函数，实际处理动画制作请求
    async function startAnimationProcess(originalModelTaskId, animationType) {
        if (!originalModelTaskId) {
            showImage3DError('缺少原始模型任务ID，无法开始动画制作');
            return;
        }

        // 显示初始状态
        showImage3DStatus(`正在启动"${getAnimationTypeName(animationType)}"动画制作流程...`);
        updateProgress(0, `准备${getAnimationTypeName(animationType)}动画...`, '正在初始化动画处理任务');
        
        // 禁用开始按钮
        if (startBtn) {
            startBtn.disabled = true;
            if (startBtn.querySelector('.spinner-border')) 
                startBtn.querySelector('.spinner-border').style.display = 'inline-block';
            if (startBtn.querySelector('.bi')) 
                startBtn.querySelector('.bi').style.display = 'none';
        }

        try {
            // 发送动画处理请求
            const response = await fetch(`${API_BASE_URL}/api/image-to-3d/complete-animation-process`, {
                method: 'POST',
                headers: {
                    ...getAuthHeaders(),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    originalModelTaskId,
                    animationType
                })
            });

            let result;
            try {
                result = await response.json();
            } catch (e) {
                result = { success: false, message: '解析响应失败' };
            }

            if (!response.ok) {
                if (response.status === 402) {
                    // 积分不足
                    const errorMsg = result.error || '账户积分不足，请充值后再试';
                    showImage3DError(`无法制作动画: ${errorMsg}`);
                } else {
                    showImage3DError(result.message || result.error || `动画制作失败 (${response.status})`);
                }
                // 恢复按钮状态
                if (startBtn) {
                    startBtn.disabled = false;
                    if (startBtn.querySelector('.spinner-border')) 
                        startBtn.querySelector('.spinner-border').style.display = 'none';
                    if (startBtn.querySelector('.bi')) 
                        startBtn.querySelector('.bi').style.display = 'inline-block';
                }
                return;
            }

            if (!result.success) {
                showImage3DError(result.message || result.error || '动画制作失败');
                // 恢复按钮状态
                if (startBtn) {
                    startBtn.disabled = false;
                    if (startBtn.querySelector('.spinner-border')) 
                        startBtn.querySelector('.spinner-border').style.display = 'none';
                    if (startBtn.querySelector('.bi')) 
                        startBtn.querySelector('.bi').style.display = 'inline-block';
                }
                return;
            }

            // 任务启动成功，开始轮询
            updateProgress(5, `${getAnimationTypeName(animationType)}处理中`, '任务已提交，开始处理...');
            
            // 如果返回了historyId，开始轮询历史记录获取进度
            if (result.historyId) {
                pollAnimationProgress(result.historyId, animationType);
            } else {
                // 如果直接返回了结果...
            }
        } catch (error) {
            console.error('动画制作请求失败:', error);
            showImage3DError('动画制作请求失败: ' + (error.message || '未知错误'));
            // 恢复按钮状态
            if (startBtn) {
                startBtn.disabled = false;
                if (startBtn.querySelector('.spinner-border')) 
                    startBtn.querySelector('.spinner-border').style.display = 'none';
                if (startBtn.querySelector('.bi')) 
                    startBtn.querySelector('.bi').style.display = 'inline-block';
            }
        }
    }

    // 添加轮询动画进度的函数
    function pollAnimationProgress(historyId, animationType) {
        
        const progressPollId = setInterval(async () => {
            try {
                // 使用新API端点查询进度
                const response = await fetch(`${API_BASE_URL}/api/image-to-3d/animation-progress/${historyId}`, {
                    headers: getAuthHeaders()
                });
                
                if (!response.ok) {
                    console.error(`轮询进度请求失败: HTTP ${response.status}`);
                    return;
                }
                
                const result = await response.json();
                
                if (result.success) {
                    const progress = result.progress || 0;
                    const message = result.message || '';
                    const status = result.status;
                    
                    // 更新界面进度
                    updateProgress(progress, `${getAnimationTypeName(animationType)}动画处理中`, message);
                    
                    // 如果完成或失败，停止轮询
                    if (status === 'completed') {
                        clearInterval(progressPollId);
                        
                        // 获取动画模型URL并显示
            if (result.animatedModelUrl) {
                loadModel(result.animatedModelUrl);
                        } else {
                            // 通过另一个API获取模型URL
                            const historyResponse = await fetch(`${API_BASE_URL}/api/image-to-3d/history/${historyId}`, {
                                headers: getAuthHeaders()
                            });
                            
                            if (historyResponse.ok) {
                                const historyData = await historyResponse.json();
                                if (historyData.success && historyData.record && historyData.record.animated_model_url) {
                                    loadModel(historyData.record.animated_model_url);
                                } else {
                                    showImage3DError('动画制作完成，但无法获取模型URL');
                                }
                            }
                        }
                        
                        // 刷新历史列表
                        loadHistory(1, false);
                        
                        // 恢复按钮状态
                        if (startBtn) {
                            startBtn.disabled = false;
                            if (startBtn.querySelector('.spinner-border')) 
                                startBtn.querySelector('.spinner-border').style.display = 'none';
                            if (startBtn.querySelector('.bi')) 
                                startBtn.querySelector('.bi').style.display = 'inline-block';
                        }
                    } else if (status === 'failed') {
                        clearInterval(progressPollId);
                        showImage3DError('动画制作失败: ' + message);
                        
                        // 恢复按钮状态
                        if (startBtn) {
                            startBtn.disabled = false;
                            if (startBtn.querySelector('.spinner-border')) 
                                startBtn.querySelector('.spinner-border').style.display = 'none';
                            if (startBtn.querySelector('.bi')) 
                                startBtn.querySelector('.bi').style.display = 'inline-block';
                        }
                    }
                }
        } catch (error) {
                console.error('轮询动画进度出错:', error);
            }
        }, 3000); // 每3秒轮询一次
    }

    // 新增：下载并显示动画模型
    async function downloadAndShowAnimatedModel(tripoModelUrl, taskId) {
        try {
            // 使用ComfyUI下载URL接口
            const modelFilePrefix = `anim_task_${taskId}_model_`;
            const comfyApiEndpoint = `${API_BASE_URL}/api/image-to-3d/download-url`;
            
            updateProgress(100, '动画制作完成', '正在下载动画模型...');
            
            const downloadResponse = await fetch(comfyApiEndpoint, {
            method: 'POST',
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            },
                body: JSON.stringify({
                    url: tripoModelUrl,
                    filename_prefix: modelFilePrefix
                })
            });
            
            const downloadResult = await downloadResponse.json();
            
            if (downloadResult.success) {
                // 加载模型
                loadModel(downloadResult.view_url);
                
                // 恢复按钮状态
                if (startBtn) {
                    startBtn.disabled = false;
                    if (startBtn.querySelector('.spinner-border')) 
                        startBtn.querySelector('.spinner-border').style.display = 'none';
                    if (startBtn.querySelector('.bi')) 
                        startBtn.querySelector('.bi').style.display = 'inline-block';
                }
                    } else {
                showImage3DError('动画模型下载失败: ' + (downloadResult.message || '未知错误'));
                
                // 恢复按钮状态
                if (startBtn) {
                    startBtn.disabled = false;
                    if (startBtn.querySelector('.spinner-border')) 
                        startBtn.querySelector('.spinner-border').style.display = 'none';
                    if (startBtn.querySelector('.bi')) 
                        startBtn.querySelector('.bi').style.display = 'inline-block';
                }
                    }
                } catch (error) {
            console.error('下载动画模型失败:', error);
            showImage3DError('下载动画模型失败: ' + error.message);
            
            // 恢复按钮状态
            if (startBtn) {
                startBtn.disabled = false;
                if (startBtn.querySelector('.spinner-border')) 
                    startBtn.querySelector('.spinner-border').style.display = 'none';
                if (startBtn.querySelector('.bi')) 
                    startBtn.querySelector('.bi').style.display = 'inline-block';
            }
        }
    }

    // 辅助函数：获取动画类型的中文名称
    function getAnimationTypeName(type) {
        const animationTypes = {
            'walk': '走路',
            'run': '跑步',
            'idle': '待机',
            'climb': '攀爬',
            'jump': '跳跃',
            'slash': '挥砍',
            'shoot': '射击',
            'hurt': '受伤',
            'fall': '跌落',
            'turn': '转身'
        };
        return animationTypes[type] || type;
    }

    // UI反馈函数（可根据你现有的UI实现）
    function showImage3DStatus(msg) {
        const statusArea = document.getElementById('image3dStatusArea');
        const statusMessage = document.getElementById('image3dStatusMessage');
        if (statusArea) statusArea.style.display = 'block';
        if (statusMessage) statusMessage.textContent = msg;
    }
    function showImage3DError(msg) {
        showImage3DStatus('错误: ' + msg);
        const errorMessage = document.getElementById('image3dErrorMessage');
        if (errorMessage) {
            errorMessage.style.display = 'block';
            errorMessage.textContent = msg;
        }
    }
    function showImage3DSuccess(msg) {
        showImage3DStatus('完成: ' + msg);
    }

    // 添加一个统一设置下拉菜单项样式的函数
    function styleDropdownItem(element) {
        element.style.color = '#e0e0e0'; // 浅色文字
        element.style.padding = '8px 16px'; // 增加一点内边距
        element.style.fontSize = '14px'; // 字体大小
        element.style.transition = 'background-color 0.2s'; // 添加过渡效果
        
        // 添加悬停效果
        element.addEventListener('mouseover', () => {
            element.style.backgroundColor = '#3a3a3a'; // 稍亮的背景色
            element.style.color = '#ffffff'; // 更亮的文字颜色
        });
        
        element.addEventListener('mouseout', () => {
            element.style.backgroundColor = ''; // 恢复原来的背景色
            element.style.color = '#e0e0e0'; // 恢复原来的文字颜色
        });
        
        return element;
    }

    // 毛玻璃效果函数
    function applyGlassmorphismStyles() {
        
        // 为状态徽章添加毛玻璃效果
        document.querySelectorAll('.badge').forEach(badge => {
            badge.classList.add('glassmorphism');
        });
        
        // 为模态框中的按钮添加毛玻璃效果
        document.querySelectorAll('.anim-type-btn').forEach(button => {
            button.classList.add('glassmorphism-btn');
        });
    }
    
    // 页面加载时应用毛玻璃效果
    applyGlassmorphismStyles();
    
    // 为动态添加的内容绑定MutationObserver，以便在DOM更新时应用样式
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.addedNodes.length) {
                applyGlassmorphismStyles();
            }
        });
    });
    
    // 监视历史列表和状态区域的变化
    if (historyList) {
        observer.observe(historyList, { childList: true, subtree: true });
    }
    
    if (statusArea) {
        observer.observe(statusArea, { childList: true, subtree: true });
    }
}); 