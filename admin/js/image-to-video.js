document.addEventListener('DOMContentLoaded', () => {
    const i2vTab = document.getElementById('image-to-video-tab');
    if (!i2vTab) return; // Only run if the tab exists

    // --- 首尾帧模式相关元素 ---
    const modeToggle = document.getElementById('i2vModeToggle');
    const standardModeContainer = document.getElementById('i2vStandardModeContainer');
    const kf2vModeContainer = document.getElementById('i2vKf2vModeContainer');
    let isKf2vMode = false; // 默认使用标准图生视频模式
    
    // --- 标准图生视频元素 ---
    const imageDropZone = document.getElementById('i2vImageDropZone');
    const imageInput = document.getElementById('i2vImageInput');
    const previewImg = document.getElementById('i2vPreviewImg');
    const previewContainer = document.getElementById('i2vPreviewContainer');
    const removeImageBtn = document.getElementById('removeI2vImage');

    // --- 首尾帧图生视频元素 ---
    const firstFrameDropZone = document.getElementById('i2vFirstFrameDropZone');
    const firstFrameInput = document.getElementById('i2vFirstFrameInput');
    const firstFramePreviewImg = document.getElementById('i2vFirstFramePreviewImg');
    const firstFramePreviewContainer = document.getElementById('i2vFirstFramePreviewContainer');
    const removeFirstFrameBtn = document.getElementById('removeI2vFirstFrame');
    
    const lastFrameDropZone = document.getElementById('i2vLastFrameDropZone');
    const lastFrameInput = document.getElementById('i2vLastFrameInput');
    const lastFramePreviewImg = document.getElementById('i2vLastFramePreviewImg');
    const lastFramePreviewContainer = document.getElementById('i2vLastFramePreviewContainer');
    const removeLastFrameBtn = document.getElementById('removeI2vLastFrame');
    
    // --- 通用元素 ---
    const promptInput = document.getElementById('i2vPromptInput');
    const modelSelect = document.getElementById('i2vModelSelect');
    const resolutionSelect = document.getElementById('i2vResolutionSelect');
    const startGenerationBtn = document.getElementById('startI2vGenerationBtn');
    const startGenerationBtnSpinner = startGenerationBtn ? startGenerationBtn.querySelector('span.spinner-border') : null;
    const startGenerationBtnText = document.getElementById('startI2vGenerationBtnText');
    const buttonCostDisplay = document.getElementById('i2vButtonCostDisplay');
    
    // --- 状态显示元素 ---
    const loadingIndicator = document.getElementById('i2vLoadingIndicator');
    const loadingMessage = document.getElementById('i2vLoadingMessage');
    const errorAlert = document.getElementById('i2vErrorAlert');
    const resultContainer = document.getElementById('i2vResultContainer');
    const resultVideo = document.getElementById('i2vResultVideo');
    let downloadLink = document.getElementById('i2vDownloadLink');
    const resultsPlaceholder = document.getElementById('i2vResultsPlaceholder');
    
    // --- 历史记录元素 ---
    const historyTabButton = document.getElementById('i2vHistoryTabButton') || document.getElementById('image-to-video-tab');
    const historyList = document.getElementById('i2vHistoryList');
    const historyLoading = document.getElementById('i2vHistoryLoading');
    const historyError = document.getElementById('i2vHistoryError');
    const historyEmpty = document.getElementById('i2vHistoryEmpty');
    const historyPaginationNav = document.getElementById('i2vHistoryPaginationNav');
    const historyPagination = document.getElementById('i2vHistoryPagination');
    let currentHistoryPage = 1; // 由主模块管理当前历史页码

    // 检查关键元素是否存在
    if (!imageDropZone) console.error('找不到元素: i2vImageDropZone');
    if (!firstFrameDropZone) console.error('找不到元素: i2vFirstFrameDropZone');
    if (!lastFrameDropZone) console.error('找不到元素: i2vLastFrameDropZone');
    if (!modeToggle) console.error('找不到元素: i2vModeToggle');
    if (!standardModeContainer) console.error('找不到元素: i2vStandardModeContainer');
    if (!kf2vModeContainer) console.error('找不到元素: i2vKf2vModeContainer');

    // --- 初始样式修复 ---
    function fixDropZoneStyles() {
        
        // 不再需要额外添加drop-zone类和内容，因为HTML已经改为使用image-drop-zone类和结构
        if (imageDropZone && !imageDropZone.querySelector('.bi-cloud-arrow-up')) {
            imageDropZone.innerHTML = `
                <i class="bi bi-cloud-arrow-up"></i>
                <p>将图片拖拽到此处，或点击选择文件</p>
                <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
            `;
        }
        
        if (firstFrameDropZone && !firstFrameDropZone.querySelector('.bi-cloud-arrow-up')) {
            firstFrameDropZone.innerHTML = `
                <i class="bi bi-cloud-arrow-up"></i>
                <p>将首帧图片拖拽到此处，或点击选择图片</p>
                <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
            `;
        }
        
        if (lastFrameDropZone && !lastFrameDropZone.querySelector('.bi-cloud-arrow-up')) {
            lastFrameDropZone.innerHTML = `
                <i class="bi bi-cloud-arrow-up"></i>
                <p>将尾帧图片拖拽到此处，或点击选择图片</p>
                <small class="text-muted d-block mt-2">支持粘贴上传 (Ctrl+V)</small>
            `;
        }
    }
    
    // 首次加载时应用样式修复
    fixDropZoneStyles();
    
    // --- 切换首尾帧模式/标准模式
    i2vTab.addEventListener('shown.bs.tab', () => {
        fixDropZoneStyles();
        if (modeToggle) {
            toggleMode(); // 重新应用模式切换效果
        }
        
        // 当标签页激活时检查是否有进行中的任务
        checkForPendingTasks();
    });
    
    // 新增: 页面加载完成后检查是否有进行中的任务
    checkForPendingTasks();

    // --- 状态变量 ---
    let currentFile = null; // 标准模式下的当前图片文件
    let currentFirstFrame = null; // 首尾帧模式下的首帧图片
    let currentLastFrame = null; // 首尾帧模式下的尾帧图片
    let currentTaskId = null;
    let pollingInterval = null;
    const POLLING_INTERVAL_MS = 8000;
    // 标记任务是否已提交并在后台处理中
    let taskSubmitted = false;

    // --- 模型配置 ---
    const modelFeatureKeys = {
        "wanx2.1-i2v-turbo": "image_to_video_turbo",
        "wanx2.1-i2v-plus": "image_to_video_plus",
        "wanx2.1-kf2v-plus": "kf2v_plus" // 添加首尾帧模型对应的功能键
    };

    const modelResolutions = {
        "wanx2.1-i2v-turbo": ["720P", "480P"],
        "wanx2.1-i2v-plus": ["720P"],
        "wanx2.1-kf2v-plus": ["720P"] // 添加首尾帧模型支持的分辨率
    };
    
    // --- 模式切换函数 ---
    function toggleMode() {
        if(!modeToggle) return;
        
        isKf2vMode = modeToggle.checked;
        
        if(standardModeContainer) {
            standardModeContainer.style.display = isKf2vMode ? 'none' : 'block';
        }
        
        if(kf2vModeContainer) {
            kf2vModeContainer.style.display = isKf2vMode ? 'block' : 'none';
        }
        
        // 首尾帧模式下强制使用wanx2.1-kf2v-plus模型
        if (isKf2vMode && modelSelect) {
            // 保存之前的值，以便切换回标准模式时恢复
            if (!window.prevModelValue) {
                window.prevModelValue = modelSelect.value;
            }
            // 强制设置为首尾帧专用模型
            modelSelect.value = 'wanx2.1-kf2v-plus';
            modelSelect.disabled = true; // 禁用选择，强制使用首尾帧模型
            
            // 手动触发模型更改事件
            updateResolutionOptions();
        } else if (!isKf2vMode && modelSelect) {
            // 恢复之前的模型选择
            if (window.prevModelValue) {
                modelSelect.value = window.prevModelValue;
            }
            modelSelect.disabled = false; // 启用模型选择
            
            // 手动触发模型更改事件
            updateResolutionOptions();
        }
        
        // 强制刷新拖放区域显示逻辑
        if (isKf2vMode) {
            // 首帧上传区域始终显示（除非已上传）
            if (firstFrameDropZone) {
                firstFrameDropZone.style.display = currentFirstFrame ? 'none' : '';
            }
            // 首帧预览区域
            if (firstFramePreviewContainer) {
                firstFramePreviewContainer.style.display = currentFirstFrame ? 'block' : 'none';
            }
            // 尾帧上传区域始终显示（除非已上传）
            if (lastFrameDropZone) {
                lastFrameDropZone.style.display = currentLastFrame ? 'none' : '';
            }
            // 尾帧预览区域
            if (lastFramePreviewContainer) {
                lastFramePreviewContainer.style.display = currentLastFrame ? 'block' : 'none';
            }
        } else {
            // 标准模式下确保元素可见
            if (imageDropZone) {
                imageDropZone.style.display = currentFile ? 'none' : '';
            }
            if (previewContainer) {
                previewContainer.style.display = currentFile ? 'block' : 'none';
            }
        }
        
        // 更新开始按钮状态
        checkStartButtonState();
    }
    
    // --- 检查启动按钮状态 ---
    function checkStartButtonState() {
        if(!startGenerationBtn) return;
        
        if(isKf2vMode) {
            // 首尾帧模式：需要两张图片都上传
            startGenerationBtn.disabled = !(currentFirstFrame && currentLastFrame);
        } else {
            // 标准模式：需要一张图片
            startGenerationBtn.disabled = !currentFile;
        }
    }

    async function fetchAndDisplayModelCost() {
        if (!modelSelect || !buttonCostDisplay || !startGenerationBtnText) return;

        const selectedModel = modelSelect.value;
        const featureKey = modelFeatureKeys[selectedModel];

        if (!featureKey) {
            buttonCostDisplay.textContent = '';
            buttonCostDisplay.style.display = 'none';
            startGenerationBtnText.textContent = '开始生成视频';
            return;
        }

        buttonCostDisplay.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
        buttonCostDisplay.className = 'ms-1';
        buttonCostDisplay.style.display = 'inline';
        startGenerationBtnText.textContent = '生成';

        try {
            const apiBaseUrl = 'https://caca.yzycolour.top/api';
            const response = await fetch(`${apiBaseUrl}/features/cost?key=${featureKey}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });
            if (!response.ok) {
                const errorData = await response.json().catch(() => null);
                const message = errorData?.message || `获取成本失败 (${response.status})`;
                throw new Error(message);
            }
            const data = await response.json();
            const directDeductMessage = ' <span class="text-info" style="font-size: 0.8em;">(赠送积分无法使用该功能！)</span>';
            
            buttonCostDisplay.className = 'ms-1 align-middle'; // Set a base class, ensure vertical alignment
            buttonCostDisplay.style.display = 'inline-block'; // Helps with alignment

            if (data.success && typeof data.cost === 'number') {
                if (data.cost > 0) {
                    buttonCostDisplay.innerHTML = `(${data.cost}积分)${directDeductMessage}`;
                    // Optional: add back text-warning if desired, or rely on text-info from the message span
                    // buttonCostDisplay.classList.add('text-warning'); 
                } else {
                    // This case is less likely for these specific features now
                    buttonCostDisplay.innerHTML = `(免费)${directDeductMessage}`;
                    buttonCostDisplay.classList.add('text-success');
                }
                // startGenerationBtnText.textContent = '生成'; // This is already set before the try block
            } else {
                throw new Error(data.message || '成本数据格式无效');
            }
        } catch (error) {
            console.error(`获取模型 ${selectedModel} (${featureKey}) 成本失败:`, error);
            const directDeductMessageOnError = ' <span class="text-info" style="font-size: 0.8em;">(不使用免费额度，直接扣账户积分)</span>';
            buttonCostDisplay.innerHTML = `(成本?)${directDeductMessageOnError}`;
            buttonCostDisplay.className = 'ms-1 align-middle text-danger'; // Ensure className is set for error
             buttonCostDisplay.style.display = 'inline-block';
            // startGenerationBtnText.textContent = '开始生成视频'; // This is already set before the try block
        }
    }

    function updateResolutionOptions() {
        const selectedModel = modelSelect.value;
        const resolutions = modelResolutions[selectedModel] || [];
        resolutionSelect.innerHTML = '';
        resolutions.forEach(res => {
            const option = document.createElement('option');
            option.value = res;
            option.textContent = res;
            resolutionSelect.appendChild(option);
        });
        if (resolutions.includes("720P")) {
            resolutionSelect.value = "720P";
        } else if (resolutions.length > 0) {
            resolutionSelect.value = resolutions[0];
        }
        fetchAndDisplayModelCost();
    }

    function handleFileSelect(file) {
        if (!file) return;
        
        if (file.size > 5 * 1024 * 1024) {
            showError("文件大小不能超过 5MB。");
            return;
        }
        
        if (!['image/png', 'image/jpeg', 'image/webp'].includes(file.type)) {
            showError("不支持的文件格式。请上传 PNG, JPG, 或 WEBP 格式的图片。");
            return;
        }
        
        currentFile = file;
        
        const reader = new FileReader();
        reader.onload = (e) => {
            if (previewImg) previewImg.src = e.target.result;
            if (previewContainer) previewContainer.style.display = 'block';
            if (imageDropZone) imageDropZone.style.display = 'none';
            checkStartButtonState();
            hideError();
        };
        reader.readAsDataURL(file);
    }

    function removeFile() {
        currentFile = null;
        if (previewImg) previewImg.src = '';
        if (previewContainer) previewContainer.style.display = 'none';
        if (imageDropZone) imageDropZone.style.display = '';
        if (imageInput) imageInput.value = '';
        checkStartButtonState();
    }

    function showLoading(message) {
        if (loadingIndicator) loadingIndicator.style.display = 'block';
        if (loadingMessage) loadingMessage.textContent = message || '处理中，请稍候...';
        if (resultsPlaceholder) resultsPlaceholder.style.display = 'none';
        if (resultContainer) resultContainer.style.display = 'none';
        if (errorAlert) errorAlert.style.display = 'none';
        if (startGenerationBtnSpinner) startGenerationBtnSpinner.style.display = 'inline-block';
        if (startGenerationBtn) startGenerationBtn.disabled = true;
    }

    function hideLoading() {
        if (loadingIndicator) loadingIndicator.style.display = 'none';
        if (startGenerationBtnSpinner) startGenerationBtnSpinner.style.display = 'none';
        if (startGenerationBtn) startGenerationBtn.disabled = !currentFile;
    }

    function showError(message) {
        if (errorAlert) {
            errorAlert.textContent = message;
            errorAlert.style.display = 'block';
        }
        if (resultsPlaceholder) resultsPlaceholder.style.display = 'block';
        hideLoading();
    }
    
    function hideError() {
        if (errorAlert) errorAlert.style.display = 'none';
    }

    async function startGeneration() {
        if (!isKf2vMode && !currentFile) {
            showError("请先上传一张图片。");
            return;
        }
        
        if (isKf2vMode && (!currentFirstFrame || !currentLastFrame)) {
            showError("请先上传首帧和尾帧图片。");
            return;
        }
        
        if (!promptInput.value.trim()) {
            showError("请输入视频的提示词。");
            promptInput.focus();
            return;
        }
        
        // 首尾帧模式下必须使用wanx2.1-kf2v-plus模型
        if (isKf2vMode && modelSelect.value !== 'wanx2.1-kf2v-plus') {
            showError("首尾帧模式只支持 wanx2.1-kf2v-plus 模型。");
            // 强制切换为正确的模型
            modelSelect.value = 'wanx2.1-kf2v-plus';
            updateResolutionOptions();
            return;
        }
        
        showLoading('正在提交任务...');
        hideError();
        if (resultContainer) resultContainer.style.display = 'none';
        if (resultsPlaceholder) resultsPlaceholder.style.display = 'block';
        
        // 禁用按钮并显示加载中图标
        if (startGenerationBtn) {
            startGenerationBtn.disabled = true;
            if (startGenerationBtnSpinner) startGenerationBtnSpinner.style.display = 'inline-block';
        }

        const formData = new FormData();
        
        if (isKf2vMode) {
            // 首尾帧模式
            formData.append('first_frame', currentFirstFrame);
            formData.append('last_frame', currentLastFrame);
            formData.append('prompt', promptInput.value.trim());
            formData.append('model', modelSelect.value);
            formData.append('resolution', resolutionSelect.value);
            formData.append('mode', 'kf2v'); // 标记为首尾帧模式
        } else {
            // 标准图生视频模式
            formData.append('image', currentFile);
            formData.append('prompt', promptInput.value.trim());
            formData.append('model', modelSelect.value);
            formData.append('resolution', resolutionSelect.value);
            formData.append('mode', 'i2v'); // 标记为标准模式
        }

        try {
            const apiBaseUrl = 'https://caca.yzycolour.top/api';
            const endpoint = isKf2vMode ? `${apiBaseUrl}/image-to-video/submit-kf2v` : `${apiBaseUrl}/image-to-video/submit`;
            
            const response = await fetch(endpoint, {
                method: 'POST',
                body: formData,
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });
            
            if (!response.ok) {
                // const errorData = await response.json().catch(() => ({ message: '提交任务失败，请检查网络或联系管理员。' }));
                // throw new Error(errorData.message || `HTTP error! status: ${response.status}`); // <-- 旧的抛错方式

                // --- 新的直接错误处理 --- START ---
                let displayErrorMessage = `图生视频任务提交失败，请稍后再试。 (状态码: ${response.status})`;
                try {
                    const errorData = await response.json();
                    if (errorData && errorData.error) {
                        displayErrorMessage = errorData.error;
                        if (response.status === 402) {
                            // 可以在此为402附加特定信息，如果后端error字段不够详细
                            // displayErrorMessage += ' (请检查您的账户余额)';
                        }
                    }
                } catch (parseError) {
                    console.warn('Failed to parse error JSON response from non-ok fetch:', parseError);
                }
                showError(displayErrorMessage);
                hideLoading();
                taskSubmitted = false; 
                if (startGenerationBtn) startGenerationBtn.disabled = false;
                if (startGenerationBtnSpinner) startGenerationBtnSpinner.style.display = 'none';
                return; // 直接返回，不继续执行
                // --- 新的直接错误处理 --- END ---
            }
            
            const result = await response.json();
            if (result.output && result.output.task_id) {
                currentTaskId = result.output.task_id;
                taskSubmitted = true; // 标记任务已提交
                
                // 保存任务ID到localStorage，以便页面刷新后恢复
                localStorage.setItem('i2v_pending_task_id', currentTaskId);
                
                // 添加提示信息，告知用户即使关闭页面任务也会继续执行
                const taskMessage = `任务已提交 (ID: ${currentTaskId.substring(0,8)}...)，正在生成视频... (可能需要3-10分钟)\n\n即使您关闭页面，任务也会在后台继续处理，您可以稍后返回查看结果。`;
                showLoading(taskMessage);
                
                // 启动轮询任务状态
                pollTaskStatus(currentTaskId);
                
                // 刷新历史记录
                if (typeof loadI2VHistory === 'function') { // 检查历史记录函数是否存在
                    currentHistoryPage = await loadI2VHistory(1); 
                }
                
                // 添加超时保护: 如果12分钟后UI仍然锁定，强制解锁
                setTimeout(() => {
                    if (taskSubmitted && currentTaskId === result.output.task_id) {
                        // 清除localStorage中的任务ID
                        localStorage.removeItem('i2v_pending_task_id');
                        // 尝试最后一次获取任务状态
                        checkTaskUIState(currentTaskId).then(() => {
                            // 无论结果如何，重置UI状态
                            taskSubmitted = false;
                            hideLoading();
                            if (startGenerationBtn) startGenerationBtn.disabled = false;
                            if (startGenerationBtnSpinner) startGenerationBtnSpinner.style.display = 'none';
                            if (pollingInterval) {
                                clearInterval(pollingInterval);
                                pollingInterval = null;
                            }
                            showError("任务处理时间过长，已自动恢复界面。您可以通过历史记录查看任务结果。");
                        });
                    }
                }, 12 * 60 * 1000); // 12分钟超时
            } else {
                throw new Error(result.message || '未能获取任务ID，请重试。');
            }
        } catch (error) {
             console.error('Error starting video generation (non-fetch or code error):', error);
             // showError(`图生视频任务提交失败: ${error.message}`); // 旧的错误提示

            // --- catch块现在处理其他错误 --- START ---
            let displayErrorMessage = `图生视频任务发生意外错误。`; 
            if (error.message) {
                displayErrorMessage = `图生视频任务出错: ${error.message}`;
            }
            showError(displayErrorMessage);
            // --- catch块现在处理其他错误 --- END ---

             hideLoading();
             taskSubmitted = false; // 重置任务状态
             // 错误时恢复按钮状态
             if (startGenerationBtn) startGenerationBtn.disabled = false;
             if (startGenerationBtnSpinner) startGenerationBtnSpinner.style.display = 'none';
        }
    }

    async function pollTaskStatus(taskId) {
        if (pollingInterval) {
            clearInterval(pollingInterval);
        }
        
        let retryCount = 0;
        const MAX_RETRIES = 3;
        let isTaskCompleted = false;
        
        pollingInterval = setInterval(async () => {
            try {
                const apiBaseUrl = 'https://caca.yzycolour.top/api';
                const response = await fetch(`${apiBaseUrl}/image-to-video/status/${taskId}`, {
                     headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                if (!response.ok) {
                    if (response.status === 404 || response.status === 400) {
                        clearInterval(pollingInterval);
                        pollingInterval = null;
                        showError(`查询任务状态失败 (任务ID: ${taskId.substring(0,8)})。 服务端返回: ${response.status}.`);
                        hideLoading();
                        // --- 失败时恢复按钮 ---
                        if (startGenerationBtn) startGenerationBtn.disabled = false;
                        if (startGenerationBtnSpinner) startGenerationBtnSpinner.style.display = 'none';
                    } else {
                         console.warn(`查询任务状态时遇到临时错误: ${response.status}`);
                    }
                    return; 
                }
                const result = await response.json();
                if (result.output && result.output.task_status) {
                    const status = result.output.task_status.toUpperCase();
                    if(loadingMessage) {
                        // 更新状态提示，保留关于后台处理的信息
                        loadingMessage.innerHTML = `任务状态: ${status} (ID: ${taskId.substring(0,8)})... (可能需要3-10分钟)<br><small class="text-muted">即使关闭页面，任务也会继续在后台处理</small>`;
                    }

                    if (status === 'SUCCEEDED') {
                        clearInterval(pollingInterval);
                        pollingInterval = null;
                        taskSubmitted = false; // 任务已完成，重置状态
                        isTaskCompleted = true;
                        // 清除localStorage中保存的任务ID
                        localStorage.removeItem('i2v_pending_task_id');
                        if (result.output.video_url) {
                            displayVideo(result.output.video_url);
                        }
                        hideLoading();
                        // --- 成功时恢复按钮 ---
                        if (startGenerationBtn) startGenerationBtn.disabled = false;
                        if (startGenerationBtnSpinner) startGenerationBtnSpinner.style.display = 'none';
                        if (typeof loadI2VHistory === 'function') {
                           currentHistoryPage = await loadI2VHistory(currentHistoryPage || 1);
                        }
                    } else if (status === 'FAILED' || status === 'UNKNOWN_ERROR') {
                        clearInterval(pollingInterval);
                        pollingInterval = null;
                        taskSubmitted = false; // 任务已完成（失败），重置状态
                        isTaskCompleted = true;
                        // 清除localStorage中保存的任务ID
                        localStorage.removeItem('i2v_pending_task_id');
                        const errorMessage = result.output.message || '视频生成失败，请检查输入或稍后再试。';
                        showError(errorMessage);
                        hideLoading();
                        // --- 失败时恢复按钮 ---
                        if (startGenerationBtn) startGenerationBtn.disabled = false;
                        if (startGenerationBtnSpinner) startGenerationBtnSpinner.style.display = 'none';
                        if (typeof loadI2VHistory === 'function') {
                            currentHistoryPage = await loadI2VHistory(currentHistoryPage || 1);
                        }
                    } 
                } else if (result.message) {
                     clearInterval(pollingInterval);
                     pollingInterval = null;
                     taskSubmitted = false; // 发生错误，重置状态
                     showError(`查询任务状态时出错: ${result.message}`);
                     hideLoading();
                     // --- 失败时恢复按钮 ---
                     if (startGenerationBtn) startGenerationBtn.disabled = false;
                     if (startGenerationBtnSpinner) startGenerationBtnSpinner.style.display = 'none';
                }
            } catch (error) {
                console.error('Error polling task status:', error);
                retryCount++;
                
                // 如果连续失败次数超过阈值，尝试使用备用API检查状态
                if (retryCount >= MAX_RETRIES) {
                    checkTaskUIState(taskId);
                }
            }
        }, POLLING_INTERVAL_MS);
        
        // 添加额外保护: 每30秒检查一次UI状态
        const uiCheckInterval = setInterval(() => {
            if (isTaskCompleted) {
                clearInterval(uiCheckInterval);
                return;
            }
            checkTaskUIState(taskId);
        }, 30000);
    }
    
    // 新增: 检查任务UI状态并强制更新页面状态
    async function checkTaskUIState(taskId) {
        try {
            const apiBaseUrl = 'https://caca.yzycolour.top/api';
            const response = await fetch(`${apiBaseUrl}/image-to-video/task-ui-state/${taskId}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });
            
            if (!response.ok) {
                console.warn(`获取任务UI状态失败: ${response.status}`);
                return;
            }
            
            const uiState = await response.json();
            
            // 如果应该结束loading状态
            if (uiState.should_end_loading) {
                
                // 清除localStorage中的任务ID
                localStorage.removeItem('i2v_pending_task_id');
                
                // 如果任务成功且有视频URL
                if (uiState.status === 'SUCCEEDED' && uiState.video_url) {
                    displayVideo(uiState.video_url);
                } else if (uiState.error_message) {
                    // 如果任务失败且有错误信息
                    showError(uiState.error_message);
                }
                
                // 清除所有轮询计时器
                if (pollingInterval) {
                    clearInterval(pollingInterval);
                    pollingInterval = null;
                }
                
                // 重置任务状态
                taskSubmitted = false;
                
                // 隐藏loading
                hideLoading();
                
                // 恢复按钮状态
                if (startGenerationBtn) startGenerationBtn.disabled = false;
                if (startGenerationBtnSpinner) startGenerationBtnSpinner.style.display = 'none';
                
                // 刷新历史记录
                if (typeof loadI2VHistory === 'function') {
                    currentHistoryPage = await loadI2VHistory(currentHistoryPage || 1);
                }
            }
        } catch (error) {
            console.error('检查任务UI状态失败:', error);
        }
    }

    function displayVideo(videoUrl) {
        if (resultsPlaceholder) resultsPlaceholder.style.display = 'none';
        if (errorAlert) errorAlert.style.display = 'none';
        if (resultVideo) {
            resultVideo.src = videoUrl;
            resultVideo.load();
            resultVideo.oncanplay = () => {
                if(resultContainer) resultContainer.style.display = 'block';
            };
            resultVideo.onerror = () => {
                showError('加载视频失败，请检查链接或网络。');
                if(resultContainer) resultContainer.style.display = 'none';
                if(resultsPlaceholder) resultsPlaceholder.style.display = 'block';
            };
        }
        if (downloadLink) {
            // 不再直接设置href属性，而是使用事件监听器触发程序化下载
            downloadLink.href = '#'; // 使用无操作链接
            // 移除旧的事件监听器（如果有）
            const newDownloadLink = downloadLink.cloneNode(true);
            if (downloadLink.parentNode) {
                downloadLink.parentNode.replaceChild(newDownloadLink, downloadLink);
            }
            downloadLink = newDownloadLink;
            
            // 从URL中提取建议的文件名
            let suggestedFilename = 'video.mp4';
            try {
                const urlObj = new URL(videoUrl);
                const filenameParam = urlObj.searchParams.get('filename');
                if (filenameParam) {
                    suggestedFilename = filenameParam;
                }
            } catch (e) {
                console.warn('无法从URL解析文件名:', e);
            }
            
            // 添加新的事件监听器
            downloadLink.addEventListener('click', function(e) {
                e.preventDefault();
                initiateVideoDownload(videoUrl, this, suggestedFilename);
            });
        }
        if (loadingIndicator) loadingIndicator.style.display = 'none';
        // --- 修复：任务完成后恢复按钮状态 ---
        if (startGenerationBtn) startGenerationBtn.disabled = false;
        if (startGenerationBtnSpinner) startGenerationBtnSpinner.style.display = 'none';
    }
    
    // 处理首帧图片上传
    function handleFirstFrameSelect(file) {
        if (!file) return;
        
        if (file.size > 5 * 1024 * 1024) {
            showError("文件大小不能超过 5MB。");
            return;
        }
        
        if (!['image/png', 'image/jpeg', 'image/webp'].includes(file.type)) {
            showError("不支持的文件格式。请上传 PNG, JPG, 或 WEBP 格式的图片。");
            return;
        }
        
        currentFirstFrame = file;
        
        const reader = new FileReader();
        reader.onload = (e) => {
            if (firstFramePreviewImg) firstFramePreviewImg.src = e.target.result;
            if (firstFramePreviewContainer) firstFramePreviewContainer.style.display = 'block';
            if (firstFrameDropZone) firstFrameDropZone.style.display = 'none';
            checkStartButtonState();
            hideError();
        };
        reader.readAsDataURL(file);
    }
    
    // 处理尾帧图片上传
    function handleLastFrameSelect(file) {
        if (!file) return;
        
        if (file.size > 5 * 1024 * 1024) {
            showError("文件大小不能超过 5MB。");
            return;
        }
        
        if (!['image/png', 'image/jpeg', 'image/webp'].includes(file.type)) {
            showError("不支持的文件格式。请上传 PNG, JPG, 或 WEBP 格式的图片。");
            return;
        }
        
        currentLastFrame = file;
        
        const reader = new FileReader();
        reader.onload = (e) => {
            if (lastFramePreviewImg) lastFramePreviewImg.src = e.target.result;
            if (lastFramePreviewContainer) lastFramePreviewContainer.style.display = 'block';
            if (lastFrameDropZone) lastFrameDropZone.style.display = 'none';
            checkStartButtonState();
            hideError();
        };
        reader.readAsDataURL(file);
    }
    
    // 移除首帧图片
    function removeFirstFrame() {
        currentFirstFrame = null;
        if (firstFramePreviewImg) firstFramePreviewImg.src = '';
        if (firstFramePreviewContainer) firstFramePreviewContainer.style.display = 'none';
        if (firstFrameDropZone) firstFrameDropZone.style.display = '';
        if (firstFrameInput) firstFrameInput.value = '';
        checkStartButtonState();
    }
    
    // 移除尾帧图片
    function removeLastFrame() {
        currentLastFrame = null;
        if (lastFramePreviewImg) lastFramePreviewImg.src = '';
        if (lastFramePreviewContainer) lastFramePreviewContainer.style.display = 'none';
        if (lastFrameDropZone) lastFrameDropZone.style.display = '';
        if (lastFrameInput) lastFrameInput.value = '';
        checkStartButtonState();
    }
    
    // --- 事件绑定 ---
    if (modeToggle) {
        modeToggle.addEventListener('change', toggleMode);
        // 初始化界面
        toggleMode();
    }
    
    if (modelSelect) {
        updateResolutionOptions();
        modelSelect.addEventListener('change', updateResolutionOptions);
    }
    
    if (startGenerationBtn) {
        startGenerationBtn.addEventListener('click', startGeneration);
    }
    
    // 标准模式事件绑定
    if (removeImageBtn) {
        removeImageBtn.addEventListener('click', removeFile);
    }
    
    // 首尾帧模式事件绑定
    if (removeFirstFrameBtn) {
        removeFirstFrameBtn.addEventListener('click', removeFirstFrame);
    }
    
    if (removeLastFrameBtn) {
        removeLastFrameBtn.addEventListener('click', removeLastFrame);
    }
    
    // 设置标准模式下的拖放区域
    function setupImageDropZone(dropZoneId, inputId, previewImgId, previewContainerId, handleFileCallback, maxSizeMB = 5) {
        const dropZone = document.getElementById(dropZoneId);
        const inputElement = document.getElementById(inputId);
        const previewImg = document.getElementById(previewImgId);
        const previewContainer = document.getElementById(previewContainerId);
        
        if (!dropZone || !inputElement || !previewImg || !previewContainer) {
            console.error(`setupImageDropZone: 一个或多个元素未找到 [${dropZoneId}, ${inputId}, ${previewImgId}, ${previewContainerId}]`);
            return; // 如果任何必要元素不存在，则退出
        }
        
        // 修复：确保visually-hidden而不是drop-zone-input
        if (inputElement.classList.contains('drop-zone-input')) {
            inputElement.classList.remove('drop-zone-input');
            inputElement.classList.add('visually-hidden');
        }
        
        // 点击上传区域时触发文件选择
        const clickHandler = () => inputElement.click();
        dropZone.addEventListener('click', clickHandler);
        
        // 文件选择变更处理
        inputElement.addEventListener('change', function() {
            if (this.files && this.files.length) {
                const file = this.files[0];
                if (file.size > maxSizeMB * 1024 * 1024) {
                    showError(`文件大小不能超过 ${maxSizeMB}MB。`);
                    return;
                }
                handleFileCallback(file);
            }
        });
        
        // 拖放处理
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragging');
        });
        
        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragging');
        });
        
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragging');
            
            if (e.dataTransfer.files && e.dataTransfer.files.length) {
                const file = e.dataTransfer.files[0];
                
                if (file.size > maxSizeMB * 1024 * 1024) {
                    showError(`文件大小不能超过 ${maxSizeMB}MB。`);
                    return;
                }
                
                handleFileCallback(file);
            }
        });
        
        // 页面范围的粘贴处理
        document.addEventListener('paste', (e) => {
            // 检查是否在正确的标签页
            if (!i2vTab.classList.contains('active')) return;
            
            // 检查该上传框是否显示中
            if (dropZone.offsetParent === null) return; // 元素不可见
            
            // 检查是否有图片数据
            if (e.clipboardData && e.clipboardData.items) {
                const items = e.clipboardData.items;
                
                for (let i = 0; i < items.length; i++) {
                    if (items[i].type.indexOf('image') !== -1) {
                        const file = items[i].getAsFile();
                        
                        if (file.size > maxSizeMB * 1024 * 1024) {
                            showError(`文件大小不能超过 ${maxSizeMB}MB。`);
                            return;
                        }
                        
                        handleFileCallback(file);
                        break;
                    }
                }
            }
        });
    }

    setupImageDropZone(
        'i2vImageDropZone', 
        'i2vImageInput', 
        'i2vPreviewImg', 
        'i2vPreviewContainer',
        handleFileSelect,
        5
    );
    
    // 设置首尾帧模式下的拖放区域
    setupImageDropZone(
        'i2vFirstFrameDropZone', 
        'i2vFirstFrameInput', 
        'i2vFirstFramePreviewImg', 
        'i2vFirstFramePreviewContainer',
        handleFirstFrameSelect,
        5
    );
    
    setupImageDropZone(
        'i2vLastFrameDropZone', 
        'i2vLastFrameInput', 
        'i2vLastFramePreviewImg', 
        'i2vLastFramePreviewContainer',
        handleLastFrameSelect,
        5
    );

    // 初始化UI状态
    if (startGenerationBtn) startGenerationBtn.disabled = true;
    if (previewContainer) previewContainer.style.display = 'none';
    if (firstFramePreviewContainer) firstFramePreviewContainer.style.display = 'none';
    if (lastFramePreviewContainer) lastFramePreviewContainer.style.display = 'none';
    if (lastFrameDropZone) lastFrameDropZone.style.display = 'none'; // 初始隐藏尾帧上传区域
    if (loadingIndicator) loadingIndicator.style.display = 'none';
    if (errorAlert) errorAlert.style.display = 'none';
    if (resultContainer) resultContainer.style.display = 'none';
    if (resultsPlaceholder) resultsPlaceholder.style.display = 'block';

    // --- 初始化历史记录加载 ---
    if (historyTabButton) {
        historyTabButton.addEventListener('shown.bs.tab', async function () {
            if (typeof loadI2VHistory === 'function') {
                currentHistoryPage = await loadI2VHistory(1);
            }
        });
        if (historyTabButton.classList.contains('active')) {
            if (typeof loadI2VHistory === 'function') {
                 // 使用 IIFE 确保 await 生效
                (async () => {
                    currentHistoryPage = await loadI2VHistory(1);
                })();
            }
        }
    }
    // --- 历史记录UI初始状态 (已移至 history.js 或在 loadI2VHistory 中处理) ---

    // 页面加载后额外初始化
    window.addEventListener('load', () => {
        fixDropZoneStyles();
        if (modeToggle) toggleMode();
    });

    // 新增: 检查是否有进行中的任务
    async function checkForPendingTasks() {
        try {
            // 先从localStorage中检查是否有保存的任务ID
            const savedTaskId = localStorage.getItem('i2v_pending_task_id');
            if (!savedTaskId) return;
            
            
            // 检查任务状态
            const apiBaseUrl = 'https://caca.yzycolour.top/api';
            const response = await fetch(`${apiBaseUrl}/image-to-video/task-ui-state/${savedTaskId}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });
            
            if (!response.ok) {
                console.warn('检查保存的任务状态失败:', response.status);
                localStorage.removeItem('i2v_pending_task_id'); // 清除无效的任务ID
                return;
            }
            
            const uiState = await response.json();
            
            // 如果任务已完成或失败，清除记录
            if (uiState.should_end_loading) {
                localStorage.removeItem('i2v_pending_task_id');
                
                // 如果任务成功且有视频，显示结果
                if (uiState.status === 'SUCCEEDED' && uiState.video_url) {
                    displayVideo(uiState.video_url);
                } else if (uiState.error_message) {
                    showError(`先前的任务未成功完成: ${uiState.error_message}`);
                }
                
                return;
            }
            
            // 如果任务仍在进行中，恢复状态
            currentTaskId = savedTaskId;
            taskSubmitted = true;
            
            // 显示加载状态
            const taskMessage = `检测到进行中的任务 (ID: ${currentTaskId.substring(0,8)}...)，正在恢复状态监控...`;
            showLoading(taskMessage);
            
            // 禁用生成按钮
            if (startGenerationBtn) startGenerationBtn.disabled = true;
            if (startGenerationBtnSpinner) startGenerationBtnSpinner.style.display = 'inline-block';
            
            // 开始轮询任务状态
            pollTaskStatus(currentTaskId);
            
        } catch (error) {
            console.error('检查进行中任务失败:', error);
            localStorage.removeItem('i2v_pending_task_id'); // 出错时清除记录
        }
    }
});

// 添加通用的视频下载函数
function initiateVideoDownload(videoUrl, elementClicked, suggestedFilename = 'video.mp4') {
    const originalContent = elementClicked.innerHTML;
    elementClicked.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> 下载中...';
    elementClicked.disabled = true;

    fetch(videoUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error(`下载失败 (${response.status})`);
            }
            return response.blob();
        })
        .then(blob => {
            const blobUrl = window.URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = blobUrl;
            a.download = suggestedFilename;
            
            document.body.appendChild(a);
            a.click();
            
            setTimeout(() => {
                document.body.removeChild(a);
                window.URL.revokeObjectURL(blobUrl);
                elementClicked.innerHTML = originalContent;
                elementClicked.disabled = false;
            }, 100);
        })
        .catch(error => {
            console.error('视频下载失败:', error);
            elementClicked.innerHTML = '<i class="bi bi-exclamation-triangle me-1"></i> 下载失败';
            setTimeout(() => {
                elementClicked.innerHTML = originalContent;
                elementClicked.disabled = false;
            }, 2000);
        });
}

// 毛玻璃效果函数
function applyGlassmorphismStyles() {
    
    // 为状态徽章添加毛玻璃效果
    document.querySelectorAll('.badge').forEach(badge => {
        badge.classList.add('glassmorphism');
    });
    
    // 给历史记录项添加毛玻璃效果类
    document.querySelectorAll('#i2vHistoryList > div').forEach(item => {
        item.classList.add('i2v-history-item');
    });
    
    // 给按钮添加毛玻璃效果
    document.querySelectorAll('.play-i2v-video-btn, .download-i2v-video-btn').forEach(button => {
        button.classList.add('glassmorphism-btn');
    });
}

// 页面加载时应用毛玻璃效果
applyGlassmorphismStyles();

// 为动态添加的内容绑定MutationObserver，以便在DOM更新时应用样式
const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
        if (mutation.addedNodes.length) {
            applyGlassmorphismStyles();
        }
    });
});

// 等页面完全加载后设置MutationObserver
window.addEventListener('load', function() {
    // 获取历史列表元素
    const historyList = document.getElementById('i2vHistoryList');

    // 监视历史列表的变化
    if (historyList) {
        observer.observe(historyList, { childList: true, subtree: true });
    } else {
        console.warn('未找到历史列表元素 (#i2vHistoryList)，MutationObserver 未启动');
    }
}); 