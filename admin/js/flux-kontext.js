/**
 * Flux Kontext Pro 图片处理功能
 * 支持上传图片或纯文本生成图片
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化变量
    let fluxImageBase64 = null;
    let fluxImageFile = null;
    let fluxImage2Base64 = null; // 新增：第二张图片base64
    let fluxImage2File = null; // 新增：第二张图片文件
    let fluxExpandImageBase64 = null;
    let fluxExpandImageFile = null;
    let fluxProcessingTaskId = null;
    let fluxPollingInterval = null;
    let fluxQueuePollingInterval = null; // 新增：用于轮询队列状态的计时器
    let currentPage = 1;
    const pageSize = 6; // 每页显示的历史记录数量
    let currentFluxMode = 'normal'; // 当前模式：normal 或 expand
    let currentZoom = 1; // 当前缩放比例
    let offsetX = 0;     // 画布X轴偏移
    let offsetY = 0;     // 画布Y轴偏移
    let isSpaceDown = false; // 空格键是否按下
    let currentUserId = localStorage.getItem('userId'); // 获取用户ID
    
    // API基础URL
    const API_BASE_URL = 'https://caca.yzycolour.top';
    
    // 获取认证令牌
    function getAuthToken() {
        return localStorage.getItem('token') || '';
    }
    
    // 新增：将文件转为Base64的辅助函数
    function toBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }
    
    // 创建带认证的请求头
    function createAuthHeaders() {
        const token = getAuthToken();
        return {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
    }
    
    // DOM元素
    const fluxModeSelect = document.getElementById('fluxModeSelect');
    const fluxNormalMode = document.getElementById('fluxNormalMode');
    const fluxExpandMode = document.getElementById('fluxExpandMode');
    
    // 普通模式DOM元素
    const fluxImageDropZone = document.getElementById('fluxImageDropZone');
    const fluxImageInput = document.getElementById('fluxImageInput');
    const fluxPreviewContainer = document.getElementById('fluxPreviewContainer');
    const fluxPreviewImg = document.getElementById('fluxPreviewImg');
    const removeFluxImageBtn = document.getElementById('removeFluxImage');
    
    // 新增：第二张图片的DOM元素
    const fluxImage2DropZone = document.getElementById('fluxImage2DropZone');
    const fluxImage2Input = document.getElementById('fluxImage2Input');
    const fluxPreview2Container = document.getElementById('fluxPreview2Container');
    const fluxPreview2Img = document.getElementById('fluxPreview2Img');
    const removeFluxImage2Btn = document.getElementById('removeFluxImage2');
    
    const fluxPromptInput = document.getElementById('fluxPromptInput');
    const fluxAspectRatioSelect = document.getElementById('fluxAspectRatioSelect');
    const fluxSeedInput = document.getElementById('fluxSeedInput');
    const fluxOutputFormatSelect = document.getElementById('fluxOutputFormatSelect');
    const fluxSafetyToleranceInput = document.getElementById('fluxSafetyToleranceInput');
    const fluxPromptUpsamplingCheck = document.getElementById('fluxPromptUpsamplingCheck');
    
    // 扩图模式DOM元素
    const fluxExpandImageDropZone = document.getElementById('fluxExpandImageDropZone');
    const fluxExpandImageInput = document.getElementById('fluxExpandImageInput');
    const fluxExpandPreviewContainer = document.getElementById('fluxExpandPreviewContainer');
    const fluxExpandPreviewImg = document.getElementById('fluxExpandPreviewImg');
    const removeFluxExpandImageBtn = document.getElementById('removeFluxExpandImage');
    const fluxExpandPromptInput = document.getElementById('fluxExpandPromptInput');
    const fluxExpandTopInput = document.getElementById('fluxExpandTopInput');
    const fluxExpandBottomInput = document.getElementById('fluxExpandBottomInput');
    const fluxExpandLeftInput = document.getElementById('fluxExpandLeftInput');
    const fluxExpandRightInput = document.getElementById('fluxExpandRightInput');
    const fluxExpandStepsInput = document.getElementById('fluxExpandStepsInput');
    const fluxExpandSeedInput = document.getElementById('fluxExpandSeedInput');
    const fluxExpandGuidanceInput = document.getElementById('fluxExpandGuidanceInput');
    const fluxExpandOutputFormatSelect = document.getElementById('fluxExpandOutputFormatSelect');
    const fluxExpandSafetyToleranceInput = document.getElementById('fluxExpandSafetyToleranceInput');
    const fluxExpandPromptUpsamplingCheck = document.getElementById('fluxExpandPromptUpsamplingCheck');
    
    // 通用DOM元素
    const startFluxProcessingBtn = document.getElementById('startFluxProcessingBtn');
    const fluxResultLoading = document.getElementById('fluxResultLoading');
    const fluxResultContainer = document.getElementById('fluxResultContainer');
    const fluxResultImage = document.getElementById('fluxResultImage');
    const fluxResultLink = document.getElementById('fluxResultLink');
    const fluxErrorAlert = document.getElementById('fluxErrorAlert');
    const fluxResultsPlaceholder = document.getElementById('fluxResultsPlaceholder');
    const fluxResultPromptContainer = document.getElementById('fluxResultPromptContainer');
    const fluxResultPrompt = document.getElementById('fluxResultPrompt');
    const fluxHistoryList = document.getElementById('fluxHistoryList');
    const fluxHistoryPlaceholder = document.getElementById('fluxHistoryPlaceholder');
    const fluxHistoryLoadMoreContainer = document.getElementById('fluxHistoryLoadMoreContainer');
    const loadMoreFluxHistoryBtn = document.getElementById('loadMoreFluxHistoryBtn');

    // 新增DOM元素引用
    const openFluxExpandBtn = document.getElementById('openFluxExpandBtn');
    const fluxExpandModal = new bootstrap.Modal(document.getElementById('fluxExpandModal'));
    const fluxExpandEditorImg = document.getElementById('fluxExpandEditorImg');
    const fluxExpandEditorContainer = document.getElementById('fluxExpandEditorContainer');
    const startFluxExpandBtn = document.getElementById('startFluxExpandBtn');
    
    // 扩图区域参数
    let expandParams = {
        top: 0,
        bottom: 0,
        left: 0,
        right: 0
    };
    
    // 定义一个标志，标记是否正在进行比例设置操作
    let isSettingRatio = false;
    
    // New dropdown elements for normal mode
    const fluxAspectRatioBtn = document.getElementById('fluxAspectRatioBtn');
    const fluxAspectRatioMenu = document.getElementById('fluxAspectRatioMenu');
    const fluxAspectRatioValue = document.getElementById('fluxAspectRatioValue');
    const fluxOutputFormatBtn = document.getElementById('fluxOutputFormatBtn');
    const fluxOutputFormatMenu = document.getElementById('fluxOutputFormatMenu');
    const fluxOutputFormatValue = document.getElementById('fluxOutputFormatValue');

    let currentFluxImageFile = null;
    let currentFluxExpandImageFile = null;
    
    // 新增工作流选择相关元素
    let fluxWorkflowSelectBtn, fluxWorkflowSelectMenu, fluxWorkflowSelectValue;
    let fluxImage2Column, fluxProParams;

    // 初始化
    initFluxKontext();

    /**
     * 初始化Flux Kontext功能
     */
    function initFluxKontext() {
        // 绑定事件
        setupImageUpload();
        setupImage2Upload(); // 新增：设置第二张图片上传功能
        setupExpandFeature();
        setupProcessingButton();
        setupHistoryLoading();
        
        // 加载历史记录
        loadFluxHistory(1);
        
        // 获取功能所需积分
        getFluxCreditCost();
        
        // 确保普通模式显示，扩图模式隐藏
        fluxNormalMode.style.display = 'block';
        fluxExpandMode.style.display = 'none';
        
        // 初始化案例展示
        setupExamplesModal();

        // Initialize new custom dropdowns
        if (window.initializeCustomDropdown) {
            window.initializeCustomDropdown(fluxAspectRatioBtn, fluxAspectRatioMenu, fluxAspectRatioValue);
            window.initializeCustomDropdown(fluxOutputFormatBtn, fluxOutputFormatMenu, fluxOutputFormatValue);
        } else {
            // 降级处理：如果全局函数不可用，使用内联函数
            const dropdownItems = fluxAspectRatioMenu.querySelectorAll('.dropdown-item');
            dropdownItems.forEach(item => {
                item.addEventListener('click', function(event) {
                    event.preventDefault();
                    const selectedValue = this.dataset.value;
                    const selectedText = this.textContent;
                    
                    fluxAspectRatioValue.value = selectedValue;
                    fluxAspectRatioBtn.textContent = selectedText;

                    // Update active state
                    fluxAspectRatioMenu.querySelector('.dropdown-item.active')?.classList.remove('active');
                    this.classList.add('active');
                });
            });
            
            // 同样处理输出格式下拉菜单
            const formatItems = fluxOutputFormatMenu.querySelectorAll('.dropdown-item');
            formatItems.forEach(item => {
                item.addEventListener('click', function(event) {
                    event.preventDefault();
                    const selectedValue = this.dataset.value;
                    const selectedText = this.textContent;
                    
                    fluxOutputFormatValue.value = selectedValue;
                    fluxOutputFormatBtn.textContent = selectedText;

                    // Update active state
                    fluxOutputFormatMenu.querySelector('.dropdown-item.active')?.classList.remove('active');
                    this.classList.add('active');
                });
            });
        }

        // Initial cost update
        getFluxCreditCost().then(cost => {
            // ... existing code ...
        });

        // 新增工作流选择相关元素
        fluxWorkflowSelectBtn = document.getElementById('fluxWorkflowSelectBtn');
        fluxWorkflowSelectMenu = document.getElementById('fluxWorkflowSelectMenu');
        fluxWorkflowSelectValue = document.getElementById('fluxWorkflowSelectValue');
        fluxImage2Column = document.getElementById('fluxImage2Column');
        fluxProParams = document.getElementById('fluxProParams');

        setupWorkflowSelector(); // 初始化工作流选择器
    }

    function setupWorkflowSelector() {
        if (!fluxWorkflowSelectMenu) return;

        fluxWorkflowSelectMenu.addEventListener('click', (e) => {
            if (e.target.tagName === 'A') {
                e.preventDefault();
                const selectedValue = e.target.dataset.value;
                const selectedText = e.target.textContent;

                fluxWorkflowSelectValue.value = selectedValue;
                fluxWorkflowSelectBtn.textContent = selectedText;

                fluxWorkflowSelectMenu.querySelectorAll('a').forEach(a => a.classList.remove('active'));
                e.target.classList.add('active');

                if (selectedValue === 'comfy_flux_kontext') {
                    fluxImage2Column.style.display = 'none';
                    if (fluxProParams) fluxProParams.style.display = 'none';
                    if (openFluxExpandBtn) openFluxExpandBtn.style.display = 'none'; // 隐藏扩图按钮
                    removeFluxImage2();
                    
                    // 获取 comfy_flux_kontext 对应的积分消耗
                    getFluxCreditCost('comfy_flux_kontext');
                } else {
                    fluxImage2Column.style.display = 'block';
                    if (fluxProParams) fluxProParams.style.display = 'block';
                    if (openFluxExpandBtn) openFluxExpandBtn.style.display = 'block'; // 显示扩图按钮
                    
                    // 获取 flux_kontext_pro 对应的积分消耗
                    getFluxCreditCost('flux_kontext_pro');
                }
            }
        });
    }
    
    /**
     * 设置图片上传功能
     */
    function setupImageUpload() {
        // 点击上传区域触发文件选择
        fluxImageDropZone.addEventListener('click', () => {
            fluxImageInput.click();
        });

        // 文件选择变化事件
        fluxImageInput.addEventListener('change', handleFluxImageSelect);

        // 拖放事件
        fluxImageDropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            fluxImageDropZone.classList.add('dragging');
        });

        fluxImageDropZone.addEventListener('dragleave', () => {
            fluxImageDropZone.classList.remove('dragging');
        });

        fluxImageDropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            fluxImageDropZone.classList.remove('dragging');
            if (e.dataTransfer.files.length > 0) {
                handleFluxImageFile(e.dataTransfer.files[0]);
            }
        });

        // 粘贴事件 - 只为第一张图片处理粘贴
        document.addEventListener('paste', (e) => {
            // 只有当Flux标签页处于活动状态时才处理粘贴事件
            if (document.getElementById('flux-kontext-tab').classList.contains('active')) {
                const items = e.clipboardData.items;
                for (let i = 0; i < items.length; i++) {
                    if (items[i].type.indexOf('image') !== -1) {
                        const file = items[i].getAsFile();
                        handleFluxImageFile(file);
                        break;
                    }
                }
            }
        });

        // 移除图片按钮
        removeFluxImageBtn.addEventListener('click', () => {
            removeFluxImage();
        });
    }
    
    /**
     * 设置第二张图片上传功能
     */
    function setupImage2Upload() {
        if (!fluxImage2DropZone || !fluxImage2Input) {
            console.warn('第二张图片上传元素未找到');
            return;
        }
        
        // 点击上传区域触发文件选择
        fluxImage2DropZone.addEventListener('click', () => {
            fluxImage2Input.click();
        });

        // 文件选择变化事件
        fluxImage2Input.addEventListener('change', handleFluxImage2Select);

        // 拖放事件
        fluxImage2DropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            fluxImage2DropZone.classList.add('dragging');
        });

        fluxImage2DropZone.addEventListener('dragleave', () => {
            fluxImage2DropZone.classList.remove('dragging');
        });

        fluxImage2DropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            fluxImage2DropZone.classList.remove('dragging');
            if (e.dataTransfer.files.length > 0) {
                handleFluxImage2File(e.dataTransfer.files[0]);
            }
        });

        // 移除图片按钮
        if (removeFluxImage2Btn) {
            removeFluxImage2Btn.addEventListener('click', () => {
                removeFluxImage2();
            });
        }
    }
    
    /**
     * 设置扩图功能
     */
    function setupExpandFeature() {
        // 打开扩图弹窗按钮
        openFluxExpandBtn.addEventListener('click', () => {
            // 将当前图片复制到编辑器中
            fluxExpandEditorImg.src = fluxPreviewImg.src;
            
            // 将普通模式的图片数据传递给扩图模式
            fluxExpandImageBase64 = fluxImageBase64;
            fluxExpandImageFile = fluxImageFile;
            
            // 重置扩展参数
            expandParams = {
                top: 0,
                bottom: 0,
                left: 0,
                right: 0
            };
            
            // 显示弹窗
            fluxExpandModal.show();
            
            // 弹窗完全显示后执行初始化
            fluxExpandModal._element.addEventListener('shown.bs.modal', function onShown() {
                // 初始化扩图编辑器
                initExpandEditor();
                
                // 移除事件监听器，避免多次触发
                fluxExpandModal._element.removeEventListener('shown.bs.modal', onShown);
            });
        });
        
        // 开始扩图按钮
        startFluxExpandBtn.addEventListener('click', async () => {
            // 关闭弹窗
            fluxExpandModal.hide();
            
            // 执行扩图处理
            await handleExpandProcessing();
        });
        
        // 设置缩放按钮
        document.getElementById('zoomInBtn').addEventListener('click', () => {
            zoomCanvas(1.2);
        });
        
        document.getElementById('zoomOutBtn').addEventListener('click', () => {
            zoomCanvas(0.8);
        });
        
        document.getElementById('zoomFitBtn').addEventListener('click', () => {
            resetCanvasView();
        });
        
        // 设置输入框与拖拽的联动 - 全新实现
        setupExpandInputs();
        
        // 添加鼠标滚轮缩放功能 - 降低灵敏度
        const viewport = document.querySelector('.editor-viewport');
        viewport.addEventListener('wheel', function(e) {
            if (e.ctrlKey) {
                e.preventDefault();
                
                // 降低灵敏度 - 根据滚动量计算更小的缩放因子
                // 原来的缩放因子是固定的1.1或0.9，现在根据滚动量动态计算
                const scrollIntensity = 0.0005; // 灵敏度系数，越小越不灵敏
                const delta = -e.deltaY * scrollIntensity;
                
                // 计算缩放因子，确保每次缩放变化更小
                // 使用指数函数使缩放更平滑
                const zoomFactor = Math.exp(delta);
                
                // 获取鼠标相对于视口的位置
                const rect = viewport.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;
                
                // 缩放并保持鼠标位置不变
                zoomCanvasAtPoint(zoomFactor, mouseX, mouseY);
            }
        }, { passive: false });
        
        // 添加空格键检测
        document.addEventListener('keydown', function(e) {
            if (e.code === 'Space' && !isSpaceDown) {
                isSpaceDown = true;
                const viewport = document.querySelector('.editor-viewport');
                viewport.style.cursor = 'grab';
            }
        });
        
        document.addEventListener('keyup', function(e) {
            if (e.code === 'Space') {
                isSpaceDown = false;
                const viewport = document.querySelector('.editor-viewport');
                viewport.style.cursor = 'default';
            }
        });
        
        // 添加拖动画布功能
        setupCanvasDrag();
        
        // 设置比例选择按钮
        setupRatioButtons();
        
        // 添加窗口大小变化监听
        window.addEventListener('resize', debounce(function() {
            if (fluxExpandModal._element.classList.contains('show')) {
                fitImageToViewport();
                updateExpandAreas();
            }
        }, 200));
    }
    
    /**
     * 设置扩图输入框
     */
    function setupExpandInputs() {
        // 从弹窗中获取输入框元素
        const modal = document.getElementById('fluxExpandModal');
        const topInput = modal.querySelector('#expandTopInput');
        const bottomInput = modal.querySelector('#expandBottomInput');
        const leftInput = modal.querySelector('#expandLeftInput');
        const rightInput = modal.querySelector('#expandRightInput');
        
        // 创建一个防抖函数处理输入框更新
        const handleExpandInputChange = debounce(function(e) {
            const inputId = e.target.id;
            const value = parseInt(e.target.value) || 0;
            
            // 根据输入框ID更新对应参数
            switch(inputId) {
                case 'expandTopInput':
                    expandParams.top = value;
                    break;
                case 'expandBottomInput':
                    expandParams.bottom = value;
                    break;
                case 'expandLeftInput':
                    expandParams.left = value;
                    break;
                case 'expandRightInput':
                    expandParams.right = value;
                    break;
            }
            
            // 如果不是正在设置比例，则清除比例选择
            if (!isSettingRatio) {
                clearRatioSelection();
            }
            
            // 更新扩展区域和目标尺寸
            updateExpandAreas();
            updateTargetDimensionsFromParams();
        }, 100);
        
        // 绑定事件监听器
        topInput.addEventListener('input', handleExpandInputChange);
        bottomInput.addEventListener('input', handleExpandInputChange);
        leftInput.addEventListener('input', handleExpandInputChange);
        rightInput.addEventListener('input', handleExpandInputChange);
    }
    
    /**
     * 重置画布视图
     */
    function resetCanvasView() {
        currentZoom = 1;
        offsetX = 0;
        offsetY = 0;
        centerCanvas();
        updateCanvasTransform();
    }
    
    /**
     * 居中画布
     */
    function centerCanvas() {
        // 不需要额外操作，CSS已经设置为居中
        offsetX = 0;
        offsetY = 0;
    }
    
    /**
     * 在指定点缩放画布 - 优化缩放算法
     */
    function zoomCanvasAtPoint(factor, x, y) {
        // 计算缩放前鼠标在画布中的位置
        const beforeX = (x - offsetX) / currentZoom;
        const beforeY = (y - offsetY) / currentZoom;
        
        // 应用缩放 - 添加平滑缩放
        const newZoom = currentZoom * factor;
        
        // 动态调整缩放限制，确保不会缩得太小或太大
        const minZoom = 0.1;
        const maxZoom = 10; // 添加一个合理的上限，防止过度缩放导致性能问题
        
        // 应用缩放限制
        currentZoom = Math.min(Math.max(newZoom, minZoom), maxZoom);
        
        // 计算缩放后鼠标应该在的位置
        const afterX = beforeX * currentZoom;
        const afterY = beforeY * currentZoom;
        
        // 调整偏移量，使鼠标位置保持不变
        offsetX += (afterX - (x - offsetX));
        offsetY += (afterY - (y - offsetY));
        
        // 更新画布变换
        updateCanvasTransform();
        
        // 更新缩放百分比显示
        updateZoomDisplay();
    }
    
    /**
     * 缩放画布
     */
    function zoomCanvas(factor) {
        const viewport = document.querySelector('.editor-viewport');
        const rect = viewport.getBoundingClientRect();
        
        // 以视口中心为缩放点
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        
        zoomCanvasAtPoint(factor, centerX, centerY);
    }
    
    /**
     * 设置画布拖动功能
     */
    function setupCanvasDrag() {
        const viewport = document.querySelector('.editor-viewport');
        const canvas = document.getElementById('zoomableCanvas');
        let isDragging = false;
        let startX, startY;
        
        viewport.addEventListener('mousedown', function(e) {
            // 只有当空格键被按下时才能拖动画布
            if (isSpaceDown) {
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                viewport.classList.add('dragging');
                viewport.style.cursor = 'grabbing';
                e.preventDefault();
            }
        });
        
        document.addEventListener('mousemove', function(e) {
            if (isDragging) {
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                
                offsetX += deltaX;
                offsetY += deltaY;
                
                updateCanvasTransform();
                
                startX = e.clientX;
                startY = e.clientY;
            }
        });
        
        document.addEventListener('mouseup', function() {
            if (isDragging) {
                isDragging = false;
                viewport.classList.remove('dragging');
                viewport.style.cursor = isSpaceDown ? 'grab' : 'default';
            }
        });
        
        // 双击重置位置
        viewport.addEventListener('dblclick', function() {
            resetCanvasView();
        });
        
        // 确保当鼠标离开窗口或弹窗关闭时重置状态
        document.addEventListener('mouseleave', function() {
            isDragging = false;
            viewport.classList.remove('dragging');
        });
        
        // 当弹窗关闭时重置空格键状态
        fluxExpandModal._element.addEventListener('hidden.bs.modal', function() {
            isSpaceDown = false;
        });
    }
    
    /**
     * 更新画布变换
     */
    function updateCanvasTransform() {
        const canvas = document.getElementById('zoomableCanvas');
        canvas.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${currentZoom})`;
    }
    
    /**
     * 更新缩放百分比显示
     */
    function updateZoomDisplay() {
        // 如果有缩放百分比显示元素，可以在这里更新
        const zoomDisplay = document.getElementById('zoomPercentage');
        if (zoomDisplay) {
            zoomDisplay.textContent = `${Math.round(currentZoom * 100)}%`;
        }
    }
    
    /**
     * 设置比例选择按钮
     */
    function setupRatioButtons() {
        const ratioButtons = document.querySelectorAll('.ratio-btn');
        
        ratioButtons.forEach(button => {
            button.addEventListener('click', function() {
                // 移除所有按钮的active类
                ratioButtons.forEach(btn => btn.classList.remove('active'));
                
                // 添加当前按钮的active类
                this.classList.add('active');
                
                // 获取选择的比例
                const ratio = this.dataset.ratio;
                
                // 应用选择的比例
                applyRatio(ratio);
            });
        });
    }
    
    /**
     * 应用选择的比例
     */
    function applyRatio(ratio) {
        const img = fluxExpandEditorImg;
        if (!img.complete || !img.naturalWidth) return;
        
        // 设置标志，避免在更新输入框时清除比例选择
        isSettingRatio = true;
        
        const imgWidth = img.naturalWidth;
        const imgHeight = img.naturalHeight;
        
        // 从弹窗中获取输入框元素
        const modal = document.getElementById('fluxExpandModal');
        const topInput = modal.querySelector('#expandTopInput');
        const bottomInput = modal.querySelector('#expandBottomInput');
        const leftInput = modal.querySelector('#expandLeftInput');
        const rightInput = modal.querySelector('#expandRightInput');
        
        let newParams = { top: 0, bottom: 0, left: 0, right: 0 };

        // 如果选择原始比例，则重置所有扩展参数
        if (ratio !== 'original') {
            // 解析比例值
            const [widthRatio, heightRatio] = ratio.split(':').map(Number);
            const targetRatio = widthRatio / heightRatio;
            const currentRatio = imgWidth / imgHeight;
            
            // 根据比例差异计算需要扩展的像素
            if (targetRatio > currentRatio) {
                // 需要在左右两侧扩展
                const finalWidth = imgHeight * targetRatio;
                const totalWidthToAdd = finalWidth - imgWidth;
                newParams.left = Math.round(totalWidthToAdd / 2);
                newParams.right = Math.round(totalWidthToAdd / 2);
            } else if (targetRatio < currentRatio) {
                // 需要在上下两侧扩展
                const finalHeight = imgWidth / targetRatio;
                const totalHeightToAdd = finalHeight - imgHeight;
                newParams.top = Math.round(totalHeightToAdd / 2);
                newParams.bottom = Math.round(totalHeightToAdd / 2);
            }
        }
        
        // 更新全局的expandParams
        expandParams = newParams;

        // 直接更新输入框DOM值，确保UI更新
        topInput.value = expandParams.top;
        bottomInput.value = expandParams.bottom;
        leftInput.value = expandParams.left;
        rightInput.value = expandParams.right;
        
        // 触发input事件，确保UI和相关状态同步
        [topInput, bottomInput, leftInput, rightInput].forEach(input => {
            const event = new Event('input', { bubbles: true });
            input.dispatchEvent(event);
        });
        
        // 恢复标志
        isSettingRatio = false;
        
        // 打印详细的调试信息
        console.log('已应用比例:', ratio, '计算出的扩展参数:', {...expandParams});
    }
    
    /**
     * 初始化扩图编辑器
     */
    function initExpandEditor() {
        // 获取图片和控制元素
        const img = fluxExpandEditorImg;
        
        // 直接更新输入框DOM值
        document.getElementById('expandTopInput').value = expandParams.top;
        document.getElementById('expandBottomInput').value = expandParams.bottom;
        document.getElementById('expandLeftInput').value = expandParams.left;
        document.getElementById('expandRightInput').value = expandParams.right;
        
        // 等待图片加载完成
        img.onload = function() {
            // 自适应图片大小
            fitImageToViewport();
            
            // 设置边缘控制点拖拽功能
            setupHandleDrag(document.getElementById('topHandle'), 'top');
            setupHandleDrag(document.getElementById('bottomHandle'), 'bottom');
            setupHandleDrag(document.getElementById('leftHandle'), 'left');
            setupHandleDrag(document.getElementById('rightHandle'), 'right');
            
            // 设置角落控制点拖拽功能
            setupCornerHandleDrag(document.getElementById('topLeftHandle'), 'top-left');
            setupCornerHandleDrag(document.getElementById('topRightHandle'), 'top-right');
            setupCornerHandleDrag(document.getElementById('bottomLeftHandle'), 'bottom-left');
            setupCornerHandleDrag(document.getElementById('bottomRightHandle'), 'bottom-right');
            
            // 初始化扩展区域
            updateExpandAreas();
            
            // 初始化目标尺寸显示
            if (img.complete) {
                const finalWidth = img.naturalWidth + expandParams.left + expandParams.right;
                const finalHeight = img.naturalHeight + expandParams.top + expandParams.bottom;
                updateTargetDimensions(finalWidth, finalHeight);
            }
        };
        
        // 如果图片已经加载完成，直接设置
        if (img.complete) {
            // 自适应图片大小
            fitImageToViewport();
            
            // 设置边缘控制点拖拽功能
            setupHandleDrag(document.getElementById('topHandle'), 'top');
            setupHandleDrag(document.getElementById('bottomHandle'), 'bottom');
            setupHandleDrag(document.getElementById('leftHandle'), 'left');
            setupHandleDrag(document.getElementById('rightHandle'), 'right');
            
            // 设置角落控制点拖拽功能
            setupCornerHandleDrag(document.getElementById('topLeftHandle'), 'top-left');
            setupCornerHandleDrag(document.getElementById('topRightHandle'), 'top-right');
            setupCornerHandleDrag(document.getElementById('bottomLeftHandle'), 'bottom-left');
            setupCornerHandleDrag(document.getElementById('bottomRightHandle'), 'bottom-right');
            
            // 初始化扩展区域
            updateExpandAreas();
            
            // 初始化目标尺寸显示
            const finalWidth = img.naturalWidth + expandParams.left + expandParams.right;
            const finalHeight = img.naturalHeight + expandParams.top + expandParams.bottom;
            updateTargetDimensions(finalWidth, finalHeight);
        }
        
        // 重置比例按钮状态
        resetRatioButtons();
    }
    
    /**
     * 自适应图片大小，确保留出足够的拖拽空间
     */
    function fitImageToViewport() {
        const img = fluxExpandEditorImg;
        const viewport = document.querySelector('.editor-viewport');
        const canvas = document.getElementById('zoomableCanvas');
        
        if (!img || !viewport) return;
        
        // 获取图片原始尺寸
        const imgWidth = img.naturalWidth;
        const imgHeight = img.naturalHeight;
        
        // 获取视口尺寸（减去一定的边距，确保有拖拽空间）
        const viewportWidth = viewport.clientWidth - 100; // 左右各留50px
        const viewportHeight = viewport.clientHeight - 100; // 上下各留50px
        
        // 计算缩放比例
        const widthRatio = viewportWidth / imgWidth;
        const heightRatio = viewportHeight / imgHeight;
        
        // 使用较小的比例，确保图片完全在视口内
        const scale = Math.min(widthRatio, heightRatio, 1); // 不超过原始大小
        
        // 设置图片大小
        img.style.maxWidth = 'none'; // 覆盖默认的max-width
        img.style.maxHeight = 'none'; // 覆盖默认的max-height
        img.style.width = `${imgWidth * scale}px`;
        img.style.height = `${imgHeight * scale}px`;
        
        // 重置缩放和位置
        currentZoom = 1;
        offsetX = 0;
        offsetY = 0;
        updateCanvasTransform();
        
        // 确保画布居中
        centerCanvas();
    }
    
    /**
     * 重置比例按钮状态
     */
    function resetRatioButtons() {
        const ratioButtons = document.querySelectorAll('.ratio-btn');
        ratioButtons.forEach(btn => btn.classList.remove('active'));
        
        // 默认选中原始比例按钮
        const originalButton = document.querySelector('.ratio-btn[data-ratio="original"]');
        if (originalButton) {
            originalButton.classList.add('active');
        }
    }
    
    /**
     * 设置控制点拖拽功能
     */
    function setupHandleDrag(handle, position) {
        let startX, startY;
        let startValue;
        
        // 鼠标事件
        handle.addEventListener('mousedown', function(e) {
            e.stopPropagation(); // 阻止事件冒泡，避免触发画布拖动
            startDrag(e);
        });
        
        // 触摸事件
        handle.addEventListener('touchstart', function(e) {
            e.stopPropagation(); // 阻止事件冒泡
            const touch = e.touches[0];
            startDragCommon(touch.clientX, touch.clientY);
            
            document.addEventListener('touchmove', handleTouchMove);
            document.addEventListener('touchend', stopTouchDrag);
            
            e.preventDefault();
        });
        
        function startDrag(e) {
            startDragCommon(e.clientX, e.clientY);
            
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', stopDrag);
            
            e.preventDefault();
        }
        
        function startDragCommon(clientX, clientY) {
            startX = clientX;
            startY = clientY;
            
            // 获取当前值
            startValue = expandParams[position];
        }
        
        function handleMouseMove(e) {
            handleDragCommon(e.clientX, e.clientY);
        }
        
        function handleTouchMove(e) {
            const touch = e.touches[0];
            handleDragCommon(touch.clientX, touch.clientY);
            e.preventDefault();
        }
        
        function handleDragCommon(clientX, clientY) {
            const img = fluxExpandEditorImg;
            const imgRect = img.getBoundingClientRect();
            // 考虑缩放因素计算像素比例
            const pixelRatio = img.naturalWidth / (imgRect.width);
            let delta;
            
            if (position === 'top' || position === 'bottom') {
                delta = (clientY - startY) * (position === 'top' ? -1 : 1) / currentZoom;
            } else {
                delta = (clientX - startX) * (position === 'left' ? -1 : 1) / currentZoom;
            }
            
            // 计算新值
            const newValue = Math.max(0, startValue + Math.round(delta * pixelRatio));
            expandParams[position] = newValue;
            
            // 直接更新对应的输入框 - 从弹窗中获取
            const modal = document.getElementById('fluxExpandModal');
            const inputId = `expand${position.charAt(0).toUpperCase() + position.slice(1)}Input`;
            const inputElement = modal.querySelector(`#${inputId}`);
            inputElement.value = newValue;
            
            // 触发input事件，确保更新相关状态
            const event = new Event('input', { bubbles: true });
            inputElement.dispatchEvent(event);
            
            // 更新扩展区域
            updateExpandAreas();
            
            // 清除比例选择
            clearRatioSelection();
            
            // 更新目标尺寸显示
            updateTargetDimensionsFromParams();
            
            // 更新起始位置
            startX = clientX;
            startY = clientY;
            startValue = newValue;
        }
        
        function stopDrag() {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', stopDrag);
        }
        
        function stopTouchDrag() {
            document.removeEventListener('touchmove', handleTouchMove);
            document.removeEventListener('touchend', stopTouchDrag);
        }
    }
    
    /**
     * 设置角落控制点拖拽功能
     */
    function setupCornerHandleDrag(handle, position) {
        let startX, startY;
        let startTopValue, startBottomValue, startLeftValue, startRightValue;
        
        // 鼠标事件
        handle.addEventListener('mousedown', function(e) {
            e.stopPropagation(); // 阻止事件冒泡
            startDrag(e);
        });
        
        // 触摸事件
        handle.addEventListener('touchstart', function(e) {
            e.stopPropagation(); // 阻止事件冒泡
            const touch = e.touches[0];
            startDragCommon(touch.clientX, touch.clientY);
            
            document.addEventListener('touchmove', handleTouchMove);
            document.addEventListener('touchend', stopTouchDrag);
            
            e.preventDefault();
        });
        
        function startDrag(e) {
            startDragCommon(e.clientX, e.clientY);
            
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', stopDrag);
            
            e.preventDefault();
        }
        
        function startDragCommon(clientX, clientY) {
            startX = clientX;
            startY = clientY;
            
            // 获取当前值
            startTopValue = expandParams.top;
            startBottomValue = expandParams.bottom;
            startLeftValue = expandParams.left;
            startRightValue = expandParams.right;
        }
        
        function handleMouseMove(e) {
            handleDragCommon(e.clientX, e.clientY);
        }
        
        function handleTouchMove(e) {
            const touch = e.touches[0];
            handleDragCommon(touch.clientX, touch.clientY);
            e.preventDefault();
        }
        
        function handleDragCommon(clientX, clientY) {
            const img = fluxExpandEditorImg;
            const imgRect = img.getBoundingClientRect();
            // 考虑缩放因素计算像素比例
            const pixelRatio = img.naturalWidth / imgRect.width;
            
            const deltaX = (clientX - startX) / currentZoom;
            const deltaY = (clientY - startY) / currentZoom;
            
            // 根据角落位置更新相应的扩展参数
            switch(position) {
                case 'top-left':
                    expandParams.top = Math.max(0, startTopValue - Math.round(deltaY * pixelRatio));
                    expandParams.left = Math.max(0, startLeftValue - Math.round(deltaX * pixelRatio));
                    break;
                case 'top-right':
                    expandParams.top = Math.max(0, startTopValue - Math.round(deltaY * pixelRatio));
                    expandParams.right = Math.max(0, startRightValue + Math.round(deltaX * pixelRatio));
                    break;
                case 'bottom-left':
                    expandParams.bottom = Math.max(0, startBottomValue + Math.round(deltaY * pixelRatio));
                    expandParams.left = Math.max(0, startLeftValue - Math.round(deltaX * pixelRatio));
                    break;
                case 'bottom-right':
                    expandParams.bottom = Math.max(0, startBottomValue + Math.round(deltaY * pixelRatio));
                    expandParams.right = Math.max(0, startRightValue + Math.round(deltaX * pixelRatio));
                    break;
            }
            
            // 直接更新输入框的值并触发事件 - 确保从弹窗中获取元素
            const modal = document.getElementById('fluxExpandModal');
            const topInput = modal.querySelector('#expandTopInput');
            const bottomInput = modal.querySelector('#expandBottomInput');
            const leftInput = modal.querySelector('#expandLeftInput');
            const rightInput = modal.querySelector('#expandRightInput');
            
            topInput.value = expandParams.top;
            bottomInput.value = expandParams.bottom;
            leftInput.value = expandParams.left;
            rightInput.value = expandParams.right;
            
            // 触发input事件
            [topInput, bottomInput, leftInput, rightInput].forEach(input => {
                const event = new Event('input', { bubbles: true });
                input.dispatchEvent(event);
            });
            
            // 更新扩展区域
            updateExpandAreas();
            
            // 清除比例选择
            clearRatioSelection();
            
            // 更新目标尺寸显示
            updateTargetDimensionsFromParams();

            // 更新起始位置，为下一次移动做准备
            startX = clientX;
            startY = clientY;
            startTopValue = expandParams.top;
            startBottomValue = expandParams.bottom;
            startLeftValue = expandParams.left;
            startRightValue = expandParams.right;
        }
        
        function stopDrag() {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', stopDrag);
        }
        
        function stopTouchDrag() {
            document.removeEventListener('touchmove', handleTouchMove);
            document.removeEventListener('touchend', stopTouchDrag);
        }
    }
    
    /**
     * 更新输入框的值
     */
    function updateInputValues() {
        // 确保获取到输入框元素
        const topInput = document.getElementById('expandTopInput');
        const bottomInput = document.getElementById('expandBottomInput');
        const leftInput = document.getElementById('expandLeftInput');
        const rightInput = document.getElementById('expandRightInput');
        
        // 直接设置值，不触发任何事件
        if (topInput) topInput.value = expandParams.top;
        if (bottomInput) bottomInput.value = expandParams.bottom;
        if (leftInput) leftInput.value = expandParams.left;
        if (rightInput) rightInput.value = expandParams.right;
        
        // 打印调试信息
        console.log('更新输入框值:', {...expandParams});
    }
    
    /**
     * 更新扩展区域
     */
    function updateExpandAreas() {
        const img = fluxExpandEditorImg;
        const imgRect = img.getBoundingClientRect();
        // 不需要乘以currentZoom，因为getBoundingClientRect已经考虑了缩放
        const pixelRatio = img.naturalWidth / imgRect.width;
        
        // 更新扩展区域大小 - 特别指定在弹窗内查找元素
        const modal = document.getElementById('fluxExpandModal');
        
        const topArea = modal.querySelector('#topExpandArea');
        if (topArea) {
            topArea.style.height = `${expandParams.top / pixelRatio}px`;
        }
        
        const bottomArea = modal.querySelector('#bottomExpandArea');
        if (bottomArea) {
            bottomArea.style.height = `${expandParams.bottom / pixelRatio}px`;
        }
        
        const leftArea = modal.querySelector('#leftExpandArea');
        if (leftArea) {
            leftArea.style.width = `${expandParams.left / pixelRatio}px`;
        }
        
        const rightArea = modal.querySelector('#rightExpandArea');
        if (rightArea) {
            rightArea.style.width = `${expandParams.right / pixelRatio}px`;
        }
    }

    /**
     * 处理选择的图片文件
     */
    function handleFluxImageSelect(e) {
        const file = e.target.files[0];
        if (file) {
            handleFluxImageFile(file);
        }
        // 清空input的值，确保下次选择相同文件仍能触发change事件
        e.target.value = '';
    }
    
    /**
     * 处理选择的第二张图片文件
     */
    function handleFluxImage2Select(e) {
        if (e.target.files.length > 0) {
            handleFluxImage2File(e.target.files[0]);
        }
        // 清空input的值，确保下次选择相同文件仍能触发change事件
        e.target.value = '';
    }
    
    /**
     * 处理选择的扩图图片文件
     */
    function handleFluxExpandImageSelect(e) {
        if (e.target.files.length > 0) {
            handleFluxExpandImageFile(e.target.files[0]);
        }
        // 清空input的值，确保下次选择相同文件仍能触发change事件
        e.target.value = '';
    }

    /**
     * 处理图片文件
     */
    async function handleFluxImageFile(file) {
        if (!file.type.startsWith('image/')) {
            showFluxError('请上传图片文件');
            return;
        }

        currentFluxImageFile = file; // <--- 修正：直接赋值原始File对象
        fluxImageFile = file; // 新增，保持一致性
        
        try {
            fluxImageBase64 = await toBase64(file);
            fluxPreviewImg.src = fluxImageBase64;
            fluxPreviewContainer.style.display = 'block';
            fluxImageDropZone.classList.add('d-none');
            
            // 检查图像尺寸
            const img = new Image();
            img.onload = function() {
                if (openFluxExpandBtn) {
                   openFluxExpandBtn.style.display = 'block';
                }
            };
            img.src = fluxImageBase64;
            
        } catch (error) {
            showFluxError('图片预览失败: ' + error.message);
            removeFluxImage();
        }
    }
    
    /**
     * 处理第二张图片文件
     */
    async function handleFluxImage2File(file) {
        if (!file.type.startsWith('image/')) {
            showFluxError('请上传图片文件');
            return;
        }
        
        fluxImage2File = file; // <--- 修正：直接赋值原始File对象

        try {
            fluxImage2Base64 = await toBase64(file);
            fluxPreview2Img.src = fluxImage2Base64;
            fluxPreview2Container.style.display = 'block';
            fluxImage2DropZone.classList.add('d-none');
        } catch (error) {
            showFluxError('图片2预览失败: ' + error.message);
            removeFluxImage2();
        }
    }
    
    /**
     * 处理扩图图片文件
     */
    async function handleFluxExpandImageFile(file) {
        if (!file.type.startsWith('image/')) {
            showFluxError('请上传图片文件');
            return;
        }
        
        currentFluxExpandImageFile = file; // <--- 修正：直接赋值原始File对象

        try {
            fluxExpandImageBase64 = await toBase64(file);
            fluxExpandPreviewImg.src = fluxExpandImageBase64;
            fluxExpandPreviewContainer.style.display = 'block';
            fluxExpandImageDropZone.classList.add('d-none');
        } catch (error) {
            showFluxError('扩图图片预览失败: ' + error.message);
            // 这里可能需要一个 removeFluxExpandImage 函数
        }
    }

    /**
     * 压缩图片
     */
    async function compressImage(file) {
        try {
            // 使用browser-image-compression库压缩图片
            const options = {
                maxSizeMB: 1,
                maxWidthOrHeight: 1920,
                useWebWorker: true
            };
            
            return await imageCompression(file, options);
        } catch (error) {
            console.error('压缩图片时出错:', error);
            return file; // 如果压缩失败，返回原始文件
        }
    }

    /**
     * 移除图片
     */
    function removeFluxImage() {
        fluxImageBase64 = null;
        fluxImageFile = null;
        currentFluxImageFile = null; // 新增
        fluxPreviewContainer.style.display = 'none';
        fluxPreviewImg.src = '';
        
        // 重新显示上传区域
        fluxImageDropZone.classList.remove('d-none');
    }
    
    /**
     * 移除第二张图片
     */
    function removeFluxImage2() {
        fluxImage2Base64 = null;
        fluxImage2File = null;
        fluxPreview2Container.style.display = 'none';
        fluxPreview2Img.src = '';
        
        // 重新显示上传区域
        fluxImage2DropZone.classList.remove('d-none');
    }

    /**
     * 设置处理按钮事件
     */
    function setupProcessingButton() {
        if (!startFluxProcessingBtn) return;
        
        startFluxProcessingBtn.addEventListener('click', handleFluxProcessing);
    }
    
    async function handleFluxProcessing() {
        const selectedWorkflow = fluxWorkflowSelectValue.value;
        showFluxError(''); // Clear previous errors

        if (selectedWorkflow === 'comfy_flux_kontext') {
            await handleComfyProcessing();
        } else {
            await handleNormalProcessing();
        }
    }

    async function handleComfyProcessing() {
        if (!currentFluxImageFile) {
            showFluxError('请上传一张图片。');
            return;
        }
        if (!fluxPromptInput.value.trim()) {
            showFluxError('提示词不能为空。');
            return;
        }

        // 使用 FormData 来发送数据
        const formData = new FormData();
        formData.append('prompt', fluxPromptInput.value.trim());
        formData.append('workflow', 'comfy_flux_kontext');
        formData.append('image1', currentFluxImageFile, currentFluxImageFile.name);

        resetFluxProcessingState();
        fluxResultLoading.style.display = 'block';
        startFluxProcessingBtn.disabled = true;

        try {
            // 注意：当使用 FormData 时，不要在 headers 中手动设置 Content-Type
            // 浏览器会自动设置，并包含正确的 boundary
            const response = await fetch(`${API_BASE_URL}/api/flux/comfy-process`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${getAuthToken()}` // 修正：添加 'Bearer ' 前缀
                },
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ message: '处理失败，无法解析错误信息' }));
                throw new Error(errorData.message || '处理失败');
            }

            const result = await response.json();
            if (result.taskId) {
                startPollingTaskStatus(result.taskId, result.queuePosition);
            } else {
                throw new Error('未能获取到任务ID');
            }

        } catch (error) {
            showFluxError(error.message);
            resetFluxProcessingState();
        }
    }

    /**
     * 处理普通模式下的图片生成请求
     */
    async function handleNormalProcessing() {
        // 检查提示词
        if (!fluxPromptInput.value.trim()) {
            showFluxError('提示词不能为空。');
            return;
        }

        // 检查图片，至少要有一张
        if (!fluxImageBase64) {
            showFluxError('智能编辑模式下，图片1不能为空。');
            return;
        }
        
        resetFluxProcessingState();
        fluxResultLoading.style.display = 'block'; // 显示加载动画
        startFluxProcessingBtn.disabled = true; // 禁用按钮

        try {
            const originalPrompt = fluxPromptInput.value.trim();
            let translatedPrompt = originalPrompt;

            // 检查是否需要翻译
            if (originalPrompt && /[ \u4e00-\u9fa5]/.test(originalPrompt)) {
                fluxResultLoading.innerHTML = `
                    <div class="loading-dots">
                        <span></span><span></span><span></span>
                    </div>
                    <p class="text-center">智能编辑模式需要英文提示词，正在翻译...</p>
                `;
                if (window.imageUtils && typeof window.imageUtils.translateTextZhipuFrontend === 'function') {
                    try {
                        translatedPrompt = await window.imageUtils.translateTextZhipuFrontend(originalPrompt);
                        console.log('Flux智能编辑提示词翻译结果:', translatedPrompt);
                    } catch (translationError) {
                        console.error('翻译提示词时出错:', translationError);
                        // 翻译失败，将继续使用原始提示词，并在控制台发出警告
                    }
                } else {
                    console.warn('翻译函数 window.imageUtils.translateTextZhipuFrontend 不可用，将使用原始提示词');
                }
            }

            // 更新加载提示
            fluxResultLoading.innerHTML = `
                <div class="loading-dots">
                    <span></span><span></span><span></span>
                </div>
                <p class="text-center">正在准备处理...</p>
            `;

            const safetyTolerance = parseInt(fluxSafetyToleranceInput.value);
            const promptUpsampling = fluxPromptUpsamplingCheck.checked;
            
            // 准备发送到后端的数据. 这次不发送 mode 和 workflow
            const requestData = {
                prompt: translatedPrompt,
                original_prompt: originalPrompt,
                aspect_ratio: fluxAspectRatioValue.value,
                output_format: fluxOutputFormatValue.value,
                seed: fluxSeedInput.value ? parseInt(fluxSeedInput.value) : undefined,
                safety_tolerance: safetyTolerance,
                prompt_upsampling: promptUpsampling,
                user_id: currentUserId
            };
            
            // 处理图片合并
            if (fluxImageBase64 && fluxImage2Base64) {
                fluxResultLoading.innerHTML = `
                    <div class="loading-dots">
                        <span></span><span></span><span></span>
                    </div>
                    <p class="text-center">正在合并图片，请稍候...</p>
                `;
                try {
                    const mergedImageBase64 = await mergeImagesWithCanvas(fluxImageBase64, fluxImage2Base64);
                    requestData.input_image = mergedImageBase64; // mergeImagesWithCanvas should return without prefix
                } catch (mergeError) {
                    throw new Error(`合并图片失败: ${mergeError.message}`);
                }
            } else if (fluxImageBase64) {
                requestData.input_image = fluxImageBase64.split(',')[1];
            }
            
            fluxResultLoading.innerHTML = `
                <div class="loading-dots">
                    <span></span><span></span><span></span>
                </div>
                <p class="text-center">正在处理图片，请稍候...</p>
            `;

            const response = await fetch(`${API_BASE_URL}/api/flux/process`, {
                method: 'POST',
                headers: createAuthHeaders(),
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ message: '处理失败，无法解析错误信息' }));
                throw new Error(errorData.message || '处理失败');
            }

            const result = await response.json();
            if (result.taskId) {
                startPollingTaskStatus(result.taskId, result.queuePosition); // 开始轮询任务状态
            } else {
                throw new Error('未能获取到任务ID');
            }
        } catch (error) {
            showFluxError(error.message);
            resetFluxProcessingState();
        }
    }

    /**
     * 处理扩图模式下的图片生成请求
     */
    async function handleExpandProcessing() {
        if (startFluxProcessingBtn.disabled) return;

        startFluxProcessingBtn.disabled = true;
        startFluxProcessingBtn.querySelector('.spinner-border').style.display = 'inline-block';
        fluxResultsPlaceholder.style.display = 'none';
        fluxResultContainer.style.display = 'none';
        fluxErrorAlert.style.display = 'none';
        fluxResultLoading.style.display = 'block';
        fluxResultLoading.innerHTML = `
            <div class="loading-dots">
                <span></span><span></span><span></span>
            </div>
            <p class="text-center">正在准备扩图...</p>
        `;

        try {
            const originalPrompt = fluxExpandPromptInput.value.trim();
            let translatedPrompt = originalPrompt;
            
            if (originalPrompt && /[ \u4e00-\u9fa5]/.test(originalPrompt)) {
                try {
                    fluxResultLoading.innerHTML = `
                        <div class="loading-dots">
                            <span></span><span></span><span></span>
                        </div>
                        <p class="text-center">正在翻译扩图提示词...</p>
                    `;
                    if (window.imageUtils && typeof window.imageUtils.translateTextZhipuFrontend === 'function') {
                        translatedPrompt = await window.imageUtils.translateTextZhipuFrontend(originalPrompt);
                        console.log('扩图提示词翻译结果:', translatedPrompt);
                    } else {
                        console.warn('翻译函数不可用，将使用原始提示词');
                    }
                } catch (translationError) {
                    console.error('翻译提示词出错:', translationError);
                } finally {
                     fluxResultLoading.innerHTML = `
                        <div class="loading-dots">
                            <span></span><span></span><span></span>
                        </div>
                        <p class="text-center">正在处理扩图，请稍候...</p>
                    `;
                }
            }

            let currentExpandSafetyTolerance = parseInt(fluxExpandSafetyToleranceInput.value);
            currentExpandSafetyTolerance = Math.min(currentExpandSafetyTolerance, 2);

            const requestData = {
                mode: 'expand',
                image: fluxExpandImageBase64.split(',')[1], // 修正：使用 fluxExpandImageBase64
                top: expandParams.top,
                bottom: expandParams.bottom,
                left: expandParams.left,
                right: expandParams.right,
                prompt: translatedPrompt, 
                original_prompt: originalPrompt, 
                steps: parseInt(fluxExpandStepsInput.value),
                guidance: parseFloat(fluxExpandGuidanceInput.value),
                output_format: fluxExpandOutputFormatSelect.value,
                safety_tolerance: currentExpandSafetyTolerance, // 使用调整后的值
                prompt_upsampling: fluxExpandPromptUpsamplingCheck.checked
            };

            // 添加可选的扩图种子
            if (fluxExpandSeedInput.value) {
                requestData.seed = parseInt(fluxExpandSeedInput.value);
            }

            const response = await fetch(`${API_BASE_URL}/api/flux/process`, {
                method: 'POST',
                headers: createAuthHeaders(),
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                let specificErrorMessage = `HTTP错误: ${response.status}`;
                if (errorData && errorData.detail && errorData.detail[0] && errorData.detail[0].msg) {
                    specificErrorMessage = errorData.detail[0].msg;
                } else if (errorData && errorData.error) {
                    specificErrorMessage = errorData.error;
                }
                throw new Error(specificErrorMessage);
            }

            const responseData = await response.json();
            
            if (responseData && responseData.taskId) {
                fluxProcessingTaskId = responseData.taskId;
                startPollingTaskStatus(fluxProcessingTaskId, responseData.queuePosition);
            } else {
                throw new Error('无效的响应数据');
            }
        } catch (error) {
            console.error('开始处理扩图时出错:', error);
            showFluxError(`处理扩图请求失败: ${error.message || '未知错误'}`);
            resetFluxProcessingState();
        }
    }

    /**
     * 开始轮询任务状态
     */
    function startPollingTaskStatus(taskId, initialQueuePosition = null) {
        // 清理旧的计时器
        if (fluxPollingInterval) clearInterval(fluxPollingInterval);
        if (fluxQueuePollingInterval) clearInterval(fluxQueuePollingInterval);

        fluxProcessingTaskId = taskId; // 确保我们正在跟踪正确的任务

        // 根据初始队列位置显示提示信息
        updateQueueMessage(initialQueuePosition);

        // 为ComfyUI任务启动一个单独的队列状态轮询器
        if (initialQueuePosition) { // 仅当是ComfyUI任务时才启动
            fluxQueuePollingInterval = setInterval(async () => {
                if (!fluxProcessingTaskId) {
                    clearInterval(fluxQueuePollingInterval);
                    return;
                }
                try {
                    const response = await fetch(`${API_BASE_URL}/api/flux/comfy-queue-status`, { headers: createAuthHeaders() });
                    if (response.ok) {
                        const queueData = await response.json();
                        const queueRemaining = queueData?.exec_info?.queue_remaining ?? 0;
                        if (queueRemaining > 0) {
                            fluxResultLoading.innerHTML = `
                                <div class="loading-dots"><span></span><span></span><span></span></div>
                                <p class="text-center">服务器繁忙，当前队列中还有 ${queueRemaining} 个任务在等待处理。</p>`;
                        }
                    }
                } catch (error) {
                    console.warn('轮询队列状态失败:', error);
                }
            }, 3000); // 每3秒查询一次
        }

        // 主任务状态轮询
        fluxPollingInterval = setInterval(async () => {
            try {
                const response = await fetch(`${API_BASE_URL}/api/flux/task/${taskId}`, {
                    headers: createAuthHeaders()
                });

                if (!response.ok) {
                    const errorData = await response.text();
                    showFluxError(`获取任务状态失败: HTTP ${response.status} - ${errorData || '无法连接服务器'}`);
                    resetFluxProcessingState(); // 包含清理计时器的逻辑
                    return;
                }

                const task = await response.json();
                
                if (!task || typeof task.status === 'undefined') {
                    console.warn('任务状态返回格式异常:', task);
                    return;
                }

                const status = task.status.toLowerCase();
                
                if (status === 'succeeded') {
                    handleFluxSuccess(task);
                    resetFluxProcessingState();
                } else if (status === 'failed' || status === 'request moderated') {
                    let detailedError = task.error || '处理失败';
                    if (typeof task.error === 'object' && task.error !== null) {
                        detailedError = task.error.message || JSON.stringify(task.error);
                    }
                    showFluxError(`处理失败: ${detailedError}`);
                    resetFluxProcessingState();
                } else if (status === 'processing') {
                    // 修改：不要立即停止队列轮询，只有在有实际进度信息时才认为任务真正开始处理
                    const hasProgressInfo = task.progress && 
                                          (task.progress.percentage !== undefined || 
                                           task.progress.current_step !== undefined);
                    
                    // 只有当有具体进度信息时，才认为任务真正开始处理
                    if (hasProgressInfo) {
                        // 任务有进度信息，真正开始处理了，此时可以停止队列轮询
                        if (fluxQueuePollingInterval) {
                            clearInterval(fluxQueuePollingInterval);
                            fluxQueuePollingInterval = null;
                        }
                        
                        let progressMessage = "正在处理图片";
                        if (task.progress.percentage) {
                            progressMessage += `，完成度：${task.progress.percentage}%`;
                        }
                        if (task.progress.current_step && task.progress.total_steps) {
                            progressMessage += `（步骤 ${task.progress.current_step}/${task.progress.total_steps}）`;
                        }
                        
                    fluxResultLoading.innerHTML = `
                            <div class="loading-dots"><span></span><span></span><span></span></div>
                            <p class="text-center">${progressMessage}，请稍候...</p>`;
                }
                    // 如果没有进度信息，保持队列轮询，等待队列轮询器更新队列信息
                }
                // 对于 'pending' 状态，我们让队列轮询器继续显示队列信息
            } catch (error) {
                console.error('轮询任务状态时出错:', error);
                showFluxError('获取任务状态时出错: ' + (error.message || '无法连接服务器'));
                resetFluxProcessingState();
            }
        }, 2000); 

        // 单独轮询队列状态
        if (initialQueuePosition) { // 仅当是ComfyUI任务时才启动
            fluxQueuePollingInterval = setInterval(async () => {
                if (!fluxProcessingTaskId) {
                    clearInterval(fluxQueuePollingInterval);
                    return;
                }
                try {
                    const response = await fetch(`${API_BASE_URL}/api/flux/comfy-queue-status`, { headers: createAuthHeaders() });
                    if (response.ok) {
                        const queueData = await response.json();
                        const queueRemaining = queueData?.exec_info?.queue_remaining ?? 0;
                        
                        // 获取当前任务状态
                        const taskResponse = await fetch(`${API_BASE_URL}/api/flux/task/${taskId}`, {
                            headers: createAuthHeaders()
                        });
                        
                        if (taskResponse.ok) {
                            const taskData = await taskResponse.json();
                            const status = taskData?.status?.toLowerCase();
                            
                            // 根据任务状态和队列信息组合显示消息
                            if (status === 'processing') {
                                // 任务状态为processing，但可能仍在队列中等待
                                const hasProgressInfo = taskData.progress && 
                                                      (taskData.progress.percentage !== undefined || 
                                                       taskData.progress.current_step !== undefined);
                                
                                if (!hasProgressInfo && queueRemaining > 0) {
                                    // 没有进度信息，但队列中还有任务，可能仍在等待
                                    fluxResultLoading.innerHTML = `
                                        <div class="loading-dots"><span></span><span></span><span></span></div>
                                        <p class="text-center">您的任务已进入处理队列</p>
                                        <p class="text-center">当前队列中还有 ${queueRemaining} 个任务在等待处理。</p>`;
                                } else if (!hasProgressInfo) {
                                    // 没有进度信息，队列为空，可能即将开始处理
                                    fluxResultLoading.innerHTML = `
                                        <div class="loading-dots"><span></span><span></span><span></span></div>
                                        <p class="text-center">您的任务准备开始处理，请稍候...</p>`;
                                }
                                // 如果有进度信息，会由主轮询器更新显示
                            } else if (status === 'pending' && queueRemaining > 0) {
                                // 任务状态为pending，显示队列信息
                                fluxResultLoading.innerHTML = `
                                    <div class="loading-dots"><span></span><span></span><span></span></div>
                                    <p class="text-center">服务器繁忙，当前队列中还有 ${queueRemaining} 个任务在等待处理。</p>`;
                            }
                        }
                    }
                } catch (error) {
                    console.warn('轮询队列状态失败:', error);
                }
            }, 3000); // 每3秒查询一次
        }
    }

    /**
     * 处理Flux成功结果
     */
    function handleFluxSuccess(task) {
        // 停止加载动画
        fluxResultLoading.style.display = 'none';
        
        // 显示结果图片
        // 适配新的字段名：output_image_url（新）或 image_urls（旧）
        const imageUrl = task.output_image_url || (task.image_urls && task.image_urls.length > 0 ? task.image_urls[0] : null);
        
        if (imageUrl) {
            fluxResultImage.src = imageUrl;
            fluxResultImage.classList.add('cursor-pointer'); // 添加可点击样式
            fluxResultLink.href = imageUrl;
            fluxResultContainer.style.display = 'block';
            
            // 添加点击图片直接打开大图功能
            fluxResultImage.onclick = () => {
                window.open(imageUrl, '_blank');
            };
            
            // 显示提示词信息（如果有）
            if (task.prompt) {
                // 如果有原始中文提示词和翻译后的英文提示词，则同时显示
                if (task.original_prompt && task.original_prompt !== task.prompt) {
                    fluxResultPrompt.innerHTML = `
                        <div class="mb-2">
                            <strong>原始提示词 (中文):</strong>
                            <p>${task.original_prompt}</p>
                        </div>
                        <div>
                            <strong>翻译提示词 (英文):</strong>
                            <p>${task.prompt}</p>
                        </div>
                    `;
                } else {
                    fluxResultPrompt.innerHTML = `<strong>提示词:</strong> ${task.prompt}`;
                }
                fluxResultPromptContainer.style.display = 'block';
            } else {
                fluxResultPromptContainer.style.display = 'none';
            }
            
            // 更新历史记录
            loadFluxHistory(1);
            resetFluxProcessingState();
        } else {
            showFluxError('处理成功但未返回图片URL');
            resetFluxProcessingState();
        }
    }

    /**
     * 重置Flux处理状态
     */
    function resetFluxProcessingState() {
        startFluxProcessingBtn.disabled = false;
        startFluxProcessingBtn.querySelector('.spinner-border').style.display = 'none';
        fluxResultLoading.style.display = 'none';
        
        // 清理所有计时器
        if (fluxPollingInterval) clearInterval(fluxPollingInterval);
        if (fluxQueuePollingInterval) clearInterval(fluxQueuePollingInterval);
        fluxPollingInterval = null;
        fluxQueuePollingInterval = null;
        fluxProcessingTaskId = null;
        
        // 如果按钮文本不包含积分信息，重新获取积分成本
        getFluxCreditCost();
    }

    /**
     * 显示Flux错误消息
     */
    function showFluxError(message) {
        fluxResultLoading.style.display = 'none';
        fluxResultContainer.style.display = 'none';
        fluxResultsPlaceholder.style.display = 'none'; // 确保占位符也隐藏
        fluxErrorAlert.innerHTML = message; // 使用 innerHTML 以便可以包含错误详情
        
        // 只有在有消息时才显示警告框
        if (message && message.trim() !== '') {
            fluxErrorAlert.style.display = 'block';
        } else {
            fluxErrorAlert.style.display = 'none';
        }
    }

    /**
     * 设置历史记录加载
     */
    function setupHistoryLoading() {
        // 加载更多按钮
        loadMoreFluxHistoryBtn.addEventListener('click', () => {
            currentPage++;
            loadFluxHistory(currentPage, true);
        });
    }

    /**
     * 加载Flux历史记录
     */
    async function loadFluxHistory(page, append = false) {
        try {
            // 显示加载状态
            if (!append) {
                fluxHistoryList.innerHTML = '<p class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> 加载历史记录中...</p>';
            }
            
            // 发送请求到后端API
            const response = await fetch(`${API_BASE_URL}/api/flux/history?page=${page}&pageSize=${pageSize}`, {
                headers: createAuthHeaders()
            });
            
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            const data = await response.json();
            
            // 渲染历史记录
            renderFluxHistory(data, append);
            
            // 更新加载更多按钮状态
            fluxHistoryLoadMoreContainer.style.display = 
                data.hasMore ? 'block' : 'none';
        } catch (error) {
            if (!append) {
                fluxHistoryList.innerHTML = '<p class="text-muted text-center">加载历史记录失败</p>';
            }
            console.error('加载历史记录失败:', error);
        }
    }

    /**
     * 渲染Flux历史记录
     */
    function renderFluxHistory(data, append) {
        // 如果没有数据且不是追加模式，显示空状态
        if (data.items.length === 0 && !append) {
            fluxHistoryList.innerHTML = '<p class="text-muted text-center w-100">暂无Flux处理历史记录</p>';
            return;
        }
        
        // 如果是第一页且不追加，清空列表
        if (data.page === 1 && !append) {
            fluxHistoryList.innerHTML = '';
        }
        
        // 添加历史项
        data.items.forEach(item => {
            const historyItem = createFluxHistoryItem(item);
            fluxHistoryList.appendChild(historyItem);
        });
    }

    /**
     * 创建Flux历史记录项
     */
    function createFluxHistoryItem(item) {
        // 创建历史记录项元素
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';
        historyItem.id = `history-item-${item.id}`; // 为历史项添加ID
        
        // 设置历史记录项内容
        historyItem.innerHTML = `
            <div class="history-images">
                <img src="${item.output_image_url}" alt="Flux处理结果" class="img-fluid rounded cursor-pointer">
            </div>
            <div class="history-item-content">
                <p class="history-item-title">${item.prompt || '无提示词'}</p>
                <span class="history-item-date">${formatDate(item.created_at)}</span>
                <span class="badge bg-secondary" id="history-item-${item.id}-status">${item.status}</span>
            </div>
            <div class="history-actions">
                <button class="btn btn-sm btn-outline-danger delete-history-btn" data-id="${item.id}">
                    <i class="bi bi-trash"></i> 删除
                </button>
            </div>
        `;
        
        // 绑定事件
        const resultImage = historyItem.querySelector('.history-images img');
        resultImage.addEventListener('click', () => {
            window.open(item.output_image_url, '_blank');
        });
        
        const deleteBtn = historyItem.querySelector('.delete-history-btn');
        deleteBtn.addEventListener('click', () => {
            deleteFluxHistory(item.id);
        });
        
        return historyItem;
    }

    /**
     * 格式化日期
     */
    function formatDate(dateString) {
        if (!dateString) return '未知时间';
        
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * 删除Flux历史记录
     */
    async function deleteFluxHistory(id) {
        if (!confirm('确定要删除这条历史记录吗？')) {
            return;
        }
        
        try {
            // 发送请求到后端API
            const response = await fetch(`${API_BASE_URL}/api/flux/task/${id}`, {
                method: 'DELETE',
                headers: createAuthHeaders()
            });
            
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            // 重新加载历史记录
            loadFluxHistory(1);
        } catch (error) {
            console.error('删除历史记录失败:', error);
            alert('删除历史记录失败，请重试');
        }
    }

    /**
     * 获取Flux处理所需积分
     */
    async function getFluxCreditCost(featureKey = null) {
        try {
            // 如果没有传入 featureKey，则从选择器获取当前选择的工作流
            if (featureKey === null) {
            const selectedWorkflow = fluxWorkflowSelectValue ? fluxWorkflowSelectValue.value : 'flux_kontext_pro';
                featureKey = selectedWorkflow;
            }
            
            const response = await fetch(`${API_BASE_URL}/api/features/cost?key=${featureKey}`, {
                headers: createAuthHeaders()
            });
            
            if (!response.ok) {
                console.error('获取积分成本失败:', response.status);
                return null;
            }
            
            const data = await response.json();
            if (data && typeof data.cost !== 'undefined') {
                // 更新按钮显示积分成本
                updateProcessingButtonWithCost(data.cost);
                return data.cost;
            }
            return null;
        } catch (error) {
            console.error('获取积分成本出错:', error);
            return null;
        }
    }
    
    /**
     * 更新处理按钮显示积分成本
     */
    function updateProcessingButtonWithCost(cost) {
        if (!startFluxProcessingBtn) return;
        
        // 防御性检查确保cost是有效值
        const costText = (cost !== null && cost !== undefined) ? `(${cost}积分)` : '';
        
        // 修改按钮内容，将积分显示为括号内文本
        startFluxProcessingBtn.innerHTML = `
            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true" style="display: none;"></span>
            <i class="bi bi-palette me-1"></i>开始处理 ${costText}
        `;
    }

    /**
     * 清除比例选择
     */
    function clearRatioSelection() {
        const ratioButtons = document.querySelectorAll('.ratio-btn');
        ratioButtons.forEach(btn => btn.classList.remove('active'));
    }

    /**
     * 防抖函数
     */
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this, args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    /**
     * 更新目标尺寸显示
     */
    function updateTargetDimensions(width, height) {
        const modal = document.getElementById('fluxExpandModal');
        const targetDimensionsElem = modal.querySelector('#targetDimensions');
        
        if (targetDimensionsElem) {
            targetDimensionsElem.textContent = `${Math.round(width)} × ${Math.round(height)} 像素`;
            targetDimensionsElem.style.display = 'block';
        } else {
            // 如果元素不存在，则在弹窗内创建一个
            const dimensionsContainer = modal.querySelector('.ratio-selector').parentNode;
            if (dimensionsContainer) {
                const dimensionsElem = document.createElement('div');
                dimensionsElem.id = 'targetDimensions';
                dimensionsElem.className = 'mt-2 text-center small text-light';
                dimensionsElem.textContent = `${Math.round(width)} × ${Math.round(height)} 像素`;
                dimensionsContainer.appendChild(dimensionsElem);
            }
        }
    }

    /**
     * 从当前参数更新目标尺寸显示
     */
    function updateTargetDimensionsFromParams() {
        const img = fluxExpandEditorImg;
        if (!img || !img.complete) return;
        
        const finalWidth = img.naturalWidth + expandParams.left + expandParams.right;
        const finalHeight = img.naturalHeight + expandParams.top + expandParams.bottom;
        updateTargetDimensions(finalWidth, finalHeight);
    }

    /**
     * 设置案例展示模态窗口
     */
    function setupExamplesModal() {
        // 通过AJAX加载模态窗口HTML
        fetch('includes/flux-examples-modal.html')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.text();
            })
            .then(html => {
                // 将加载的HTML插入到容器中
                document.getElementById('fluxExamplesModalContainer').innerHTML = html;
                
                // 获取按钮和模态窗口元素
                const showFluxExamplesBtn = document.getElementById('showFluxExamplesBtn');
                const fluxExamplesModal = new bootstrap.Modal(document.getElementById('fluxExamplesModal'));
                
                // 绑定按钮事件
                if (showFluxExamplesBtn) {
                    showFluxExamplesBtn.addEventListener('click', () => {
                        fluxExamplesModal.show();
                        console.log('打开Flux案例展示');
                    });
                }
                
                // 预加载案例图片
                // preloadExampleImages();
            })
            .catch(error => {
                console.error('加载案例模态窗口失败:', error);
            });
        
        // 预加载案例图片函数
        // function preloadExampleImages() {
        //     const imageUrls = [
        //         'img/examples/flux-example1-before.jpg',
        //         'img/examples/flux-example1-after.jpg',
        //         'img/examples/flux-example1b-before.jpg',
        //         'img/examples/flux-example1b-after.jpg',
        //         'img/examples/flux-example2-before.jpg',
        //         'img/examples/flux-example2-after.jpg',
        //         'img/examples/flux-example2b-before.jpg',
        //         'img/examples/flux-example2b-after.jpg',
        //         'img/examples/flux-example3-before.jpg',
        //         'img/examples/flux-example3-after.jpg',
        //         'img/examples/flux-example3b-before.jpg',
        //         'img/examples/flux-example3b-after.jpg'
        //     ];
            
        //     imageUrls.forEach(url => {
        //         const img = new Image();
        //         img.src = url;
        //     });
        // }
    }

    /**
     * 使用Canvas将两张图片合并为一张
     * @param {string} image1Base64 第一张图片的base64
     * @param {string} image2Base64 第二张图片的base64
     * @returns {Promise<string>} 合并后图片的base64（不含前缀）
     */
    function mergeImagesWithCanvas(image1Base64, image2Base64) {
        return new Promise((resolve, reject) => {
            const img1 = new Image();
            const img2 = new Image();
            
            // 第一张图片加载完成
            img1.onload = function() {
                // 第二张图片加载完成
                img2.onload = function() {
                    try {
                        // 创建Canvas
                        const canvas = document.createElement('canvas');
                        // 设置Canvas大小 - 水平拼接
                        canvas.width = img1.width + img2.width;
                        canvas.height = Math.max(img1.height, img2.height);
                        
                        // 获取绘图上下文
                        const ctx = canvas.getContext('2d');
                        // 填充黑色背景，确保透明区域有底色
                        ctx.fillStyle = '#000000';
                        ctx.fillRect(0, 0, canvas.width, canvas.height);
                        
                        // 绘制第一张图片（左侧）
                        ctx.drawImage(img1, 0, 0);
                        // 绘制第二张图片（右侧）
                        ctx.drawImage(img2, img1.width, 0);
                        
                        // 转换为base64并返回不含前缀的部分
                        const mergedBase64 = canvas.toDataURL('image/png').split(',')[1];
                        console.log('图片合并成功，合并后大小:', Math.round(mergedBase64.length / 1024), 'KB');
                        resolve(mergedBase64);
                    } catch (error) {
                        console.error('合并图片出错:', error);
                        reject(error);
                    }
                };
                
                // 设置第二张图片加载错误处理
                img2.onerror = function() {
                    console.error('第二张图片加载失败');
                    reject(new Error('第二张图片加载失败'));
                };
                
                // 设置第二张图片源
                img2.src = image2Base64;
            };
            
            // 设置第一张图片加载错误处理
            img1.onerror = function() {
                console.error('第一张图片加载失败');
                reject(new Error('第一张图片加载失败'));
            };
            
            // 设置第一张图片源
            img1.src = image1Base64;
        });
    }

    // 全局暴露变量和函数，用于外部访问
    window.fluxKontext = {
        setImage1: function(file) {
            if (!file) return false;
            fluxImageFile = file;
            const reader = new FileReader();
            reader.onload = (e) => {
                fluxImageBase64 = e.target.result;
                fluxPreviewImg.src = fluxImageBase64;
                fluxPreviewContainer.style.display = 'block';
                fluxImageDropZone.style.display = 'none';
            };
            reader.readAsDataURL(file);
            return true;
        },
        setImage2: function(file) {
            if (!file) return false;
            fluxImage2File = file;
            const reader = new FileReader();
            reader.onload = (e) => {
                fluxImage2Base64 = e.target.result;
                fluxPreview2Img.src = fluxImage2Base64;
                fluxPreview2Container.style.display = 'block';
                fluxImage2DropZone.style.display = 'none';
            };
            reader.readAsDataURL(file);
            return true;
        }
    };

    /**
     * 更新队列信息显示
     */
    function updateQueueMessage(position) {
        if (position && fluxResultLoading.style.display === 'block') {
            if (position > 1) {
                fluxResultLoading.innerHTML = `
                    <div class="loading-dots"><span></span><span></span><span></span></div>
                    <p class="text-center">服务器繁忙，您前面还有 ${position - 1} 个任务。</p>
                    <p class="text-center">您的任务排在第 ${position} 位，请稍候...</p>`;
            } else {
                fluxResultLoading.innerHTML = `
                    <div class="loading-dots"><span></span><span></span><span></span></div>
                    <p class="text-center">您的任务已提交，即将开始处理...</p>`;
            }
        }
    }
}); 