/**
 * my-examples.js
 * 
 * Handles logic for the "My Examples" tab, allowing users to view,
 * edit, and delete the prompt examples they have created.
 */

// --- Global variables for My Examples tab ---
let myExamplesCurrentPage = 1;
const myExamplesLimit = 12; // Or adjust as needed
let myExamplesTotalPages = 1;
let myExamplesIsLoading = false;
let myExamplesHasMore = true; // Added to track if more pages exist

// For Liked Examples
let likedExamplesCurrentPage = 1;
const likedExamplesLimit = 12; // Can be same or different
let likedExamplesHasMore = true;
let likedExamplesIsLoading = false;

// Update Selectors to target the new modal
const myExamplesModalElement = document.getElementById('myExamplesModal');
const myExamplesModalBody = myExamplesModalElement?.querySelector('.modal-body'); // Get modal body for scrolling
const myExamplesContainer = myExamplesModalElement?.querySelector('.my-examples-container'); // Use modal as base
const myExamplesLoadingIndicator = myExamplesModalElement?.querySelector('.loading');
const myExamplesEmptyState = myExamplesModalElement?.querySelector('.my-empty-state');

// Selectors for Liked Examples Modal
const likedExamplesModalElement = document.getElementById('likedExamplesModal');
const likedExamplesModalBody = likedExamplesModalElement?.querySelector('.modal-body');
const likedExamplesContainer = likedExamplesModalElement?.querySelector('.liked-examples-container');
const likedExamplesLoadingIndicator = likedExamplesModalElement?.querySelector('.loading'); // Initial loading
const likedExamplesEmptyState = likedExamplesModalElement?.querySelector('.liked-empty-state');

// Add selectors for sort filters
const myExamplesSortFilter = document.getElementById('myExamplesSortFilter');
const likedExamplesSortFilter = document.getElementById('likedExamplesSortFilter');

// --- Initialization ---
document.addEventListener('DOMContentLoaded', () => {
    // Remove the Tab listener
    /*
    const myExamplesTab = document.getElementById('my-examples-tab');
    if (myExamplesTab) {
        myExamplesTab.addEventListener('shown.bs.tab', event => {
            console.log('My Examples tab shown');
            resetMyExamplesView();
            loadMyExamples(1);
        });
    }
    */
    
    // Add listener for the new modal
    if (myExamplesModalElement) {
        myExamplesModalElement.addEventListener('shown.bs.modal', event => {
            console.log('My Examples modal shown');
            resetMyExamplesView();
            loadMyExamples(1);
        });
        // Add scroll listener to modal body
        if (myExamplesModalBody) {
            myExamplesModalBody.addEventListener('scroll', handleMyExamplesScroll);
        }
    } else {
        console.warn('My Examples Modal element (#myExamplesModal) not found.');
    }

    // Listener for Liked Examples modal shown event
    if (likedExamplesModalElement) {
        likedExamplesModalElement.addEventListener('shown.bs.modal', event => {
            console.log('Liked Examples modal shown');
            resetLikedExamplesView();
            loadLikedExamples(1);
        });
         // Add scroll listener to modal body
         if (likedExamplesModalBody) {
            likedExamplesModalBody.addEventListener('scroll', handleLikedExamplesScroll);
        }
    } else {
        console.warn('Liked Examples Modal element (#likedExamplesModal) not found.');
    }

    // Event listener for dynamic content clicks within My Examples
    if (myExamplesContainer) {
        myExamplesContainer.addEventListener('click', (event) => {
             handleCardButtonClick(event, 'my'); // Pass type 'my'
        });
    }
    
    // Event listener for dynamic content clicks within Liked Examples
    if (likedExamplesContainer) {
        likedExamplesContainer.addEventListener('click', (event) => {
            handleCardButtonClick(event, 'liked'); // Pass type 'liked'
        });
    }

    // --- NEW: Add listeners for sort filters --- 
    if (myExamplesSortFilter) {
        myExamplesSortFilter.addEventListener('change', handleSortChangeMyExamples);
    }
    if (likedExamplesSortFilter) {
        likedExamplesSortFilter.addEventListener('change', handleSortChangeLikedExamples);
    }
    // --- End NEW --- 
});

// --- Unified Card Button Click Handler ---
function handleCardButtonClick(event, context) { // context can be 'my' or 'liked'
    const target = event.target;
    const exampleCard = target.closest('.example-card');
    if (!exampleCard) return;
    const exampleId = exampleCard.dataset.id;

    // Handle Edit button click (Only possible from 'my' context usually)
    if (target.closest('.edit-example-btn')) {
        console.log(`Edit button clicked for ${context} example: ${exampleId}`);
        const modalToClose = context === 'my' ? myExamplesModalElement : likedExamplesModalElement;
        const currentModalInstance = bootstrap.Modal.getInstance(modalToClose);
        if (currentModalInstance) {
            currentModalInstance.hide();
        }
        if (typeof editExample === 'function') {
            setTimeout(() => editExample(exampleId), 200);
        } else {
            console.error('editExample function not found.');
            alert('编辑功能暂时无法使用。');
        }
    }

    // Handle Delete button click
    if (target.closest('.delete-example-btn')) {
        console.log(`Delete button clicked for ${context} example: ${exampleId}`);
        if (typeof showDeleteConfirm === 'function') {
            showDeleteConfirm(exampleId, () => {
                console.log(`Reloading ${context} examples after deletion confirmation.`);
                const modalToCheck = context === 'my' ? myExamplesModalElement : likedExamplesModalElement;
                const modalInstance = bootstrap.Modal.getInstance(modalToCheck);
                if (modalInstance && modalInstance._isShown) {
                    if (context === 'my') {
                        resetMyExamplesView();
                        loadMyExamples(1);
                    } else {
                        resetLikedExamplesView();
                        loadLikedExamples(1);
                    }
                } else {
                    console.log(`${context} examples modal closed, not reloading.`);
                }
            });
        } else {
            console.error('showDeleteConfirm function not found.');
            alert('删除功能暂时无法使用。');
        }
    }

    // Handle Like button click
    if (target.closest('.like-btn')) {
        console.log(`Like button clicked for ${context} example: ${exampleId}`);
        if (typeof toggleLike === 'function') {
            toggleLike(exampleId, target.closest('.like-btn'));
            // Optional: If in liked list, maybe remove card immediately on unlike?
            // Or just let the icon state change.
        } else {
            console.error('toggleLike function not found.');
            alert('点赞功能暂时无法使用。');
        }
    }

    // Handle Share button click
    if (target.closest('.share-example-btn')) {
        console.log(`Share button clicked for ${context} example: ${exampleId}`);
        if (typeof showShareModal === 'function') {
            showShareModal(exampleId);
        } else {
            console.error('showShareModal function not found.');
            alert('分享功能暂时无法使用。');
        }
    }

    // Handle Copy Prompt button click
    if (target.closest('.copy-prompt-btn')) {
        console.log(`Copy prompt button clicked for ${context} example: ${exampleId}`);
        if (typeof copyPrompt === 'function') {
            copyPrompt(exampleId, target.closest('.copy-prompt-btn'));
        } else {
            console.error('copyPrompt function not found.');
            alert('复制功能暂时无法使用。');
        }
    }
}

// --- Helper Functions (may need adjustments or imports from main.js) ---

// Get Auth Headers (Ensure this is available, e.g., from main.js or duplicated)
// function getAuthHeaders() { ... }

// Render Examples (Need a specific version for "My Examples") -> Align with renderExamples in main.js
function renderMyExamples(examplesToRender, append = false) {
    if (!myExamplesContainer) return;

    if (!append) {
        myExamplesContainer.innerHTML = ''; // Clear previous examples if not appending
    }

    if (examplesToRender.length === 0 && !append) {
        myExamplesEmptyState.style.display = 'block';
    } else {
        myExamplesEmptyState.style.display = 'none';
    }

    const fragment = document.createDocumentFragment(); // Use fragment for better performance

    examplesToRender.forEach(example => {
        // --- Start: Logic copied and adapted from main.js/renderExamples --- 
        const card = document.createElement('div');
        card.classList.add('example-card'); // Use the exact same base class
        card.dataset.id = example.id; // Use data-id, not data-example-id

        // console.log('Rendering my example:', example);

        const categoryName = typeof getCategoryName === 'function' ? getCategoryName(example.category) : example.category;
        const categoryColorInfo = typeof getCategoryColor === 'function' ? getCategoryColor(example.category) : { backgroundColor: '#6c757d', borderColor: '#adb5bd' };
        
        // Use imageBaseUrl directly (ensure it's defined in main.js scope)
        const imageUrl = example.image_file && typeof imageBaseUrl !== 'undefined' ? imageBaseUrl + example.image_file : ''; 
        const defaultAvatar = 'images/default-avatar.png';

        // Tags HTML (use .tag class like in main.js)
        const tagsHtml = Array.isArray(example.tags) 
            ? example.tags.map(tag => `<span class="tag">${tag}</span>`).join('')
            : ''; // No fallback text needed if empty
        
        // Author HTML
        const author = example.author;
        let authorHtml = '';
        if (author) {
            const authorName = author.nickname || author.username || '匿名';
            const authorAvatarUrl = author.avatar_url && typeof imageBaseUrl !== 'undefined' ? imageBaseUrl + author.avatar_url : defaultAvatar;
            authorHtml = `
                <div class="author-info">
                    <img src="${authorAvatarUrl}" alt="${authorName}" class="author-avatar" loading="lazy" onerror="this.onerror=null; this.src='${defaultAvatar}';">
                    <span>${authorName}</span>
                </div>
            `;
        }

        // Model HTML (use .model-badge class like in main.js)
        const modelName = example.target_model || null;
        let modelHtml = '';
        if (modelName) {
            // Use the same badge structure as in main.js renderExamples
            modelHtml = `<span class="model-badge">${modelName}</span>`; 
        }

        // Like button state
        const likeButtonClass = example.is_liked_by_current_user ? 'liked' : '';
        const likeIconClass = example.is_liked_by_current_user ? 'bi-heart-fill' : 'bi-heart';
        const likesCount = example.likes_count || 0;
        const likeTitle = example.is_liked_by_current_user ? '取消点赞' : '点赞';

        // The Core HTML Structure - Copied directly from renderExamples in main.js and adapted slightly
        card.innerHTML = `
            ${imageUrl ? `<img src="${imageUrl}" class="example-img" alt="${example.title || '案例图片'}" loading="lazy" onerror="this.onerror=null; this.src='images/placeholder.png';">` : '<div class="example-img-placeholder"></div>'}
            <span class="category-badge" style="background-color: ${categoryColorInfo.backgroundColor}; border-color: ${categoryColorInfo.borderColor};">${categoryName}</span>
            ${modelHtml} <!-- Model badge inserted here -->
            <div class="card-body">
                <h5>${example.title || '无标题'}</h5>
                <p class="prompt-preview">${example.prompt ? example.prompt.substring(0, 100) + (example.prompt.length > 100 ? '...' : '') : '无提示词'}</p>
                <div class="tags-container">
                    ${tagsHtml}
                </div>
                <div class="actions">
                    <button class="btn btn-icon btn-outline-secondary like-btn ${likeButtonClass}" 
                            title="${likeTitle}">
                        <i class="bi ${likeIconClass}"></i>
                        <span class="like-count ms-1">${likesCount}</span>
                    </button>
                    <button class="btn btn-icon btn-outline-secondary copy-prompt-btn" title="复制提示词">
                        <i class="bi bi-clipboard"></i>
                    </button>
                    <button class="btn btn-icon btn-outline-secondary share-example-btn" title="分享">
                        <i class="bi bi-share"></i>
                    </button>
                    <!-- Edit/Delete dropdown - Always show for "My Examples" -->
                    <div class="dropdown d-inline-block action-dropdown"> 
                      <button class="btn btn-icon btn-outline-secondary" type="button" data-bs-toggle="dropdown" aria-expanded="false" title="更多操作">
                        <i class="bi bi-three-dots-vertical"></i>
                      </button>
                      <ul class="dropdown-menu dropdown-menu-dark dropdown-menu-end">
                        <li><button class="dropdown-item edit-example-btn" type="button"><i class="bi bi-pencil-square me-2"></i>编辑</button></li>
                        <li><button class="dropdown-item delete-example-btn text-danger" type="button"><i class="bi bi-trash me-2"></i>删除</button></li>
                      </ul>
                    </div>
                </div>
            </div>
            ${authorHtml} 
        `;
        // --- End: Logic copied and adapted from main.js/renderExamples --- 

        fragment.appendChild(card); // Append the card directly, not wrapped in .col
    });

    // Append the fragment to the container
    myExamplesContainer.appendChild(fragment);
}

// Show/Hide Loading Indicator
function showMyExamplesLoading(show) {
    if (myExamplesLoadingIndicator) {
        myExamplesLoadingIndicator.style.display = show ? 'flex' : 'none';
    }
}

// Reset view state
function resetMyExamplesView() {
    myExamplesCurrentPage = 1;
    myExamplesHasMore = true; // Reset hasMore flag
    if (myExamplesContainer) myExamplesContainer.innerHTML = '';
    if (myExamplesEmptyState) myExamplesEmptyState.style.display = 'none';
    // Remove pagination reset as it's gone
    // if (myExamplesPaginationNav) myExamplesPaginationNav.style.display = 'none';
    // if (myExamplesPaginationUl) myExamplesPaginationUl.innerHTML = '';
    // Reset scroll position
    if (myExamplesModalBody) {
        myExamplesModalBody.scrollTop = 0;
    }
}

// --- Infinite Scroll Handler ---
function handleMyExamplesScroll() {
    if (!myExamplesModalBody || !myExamplesContainer) return;

    // Calculate if scroll is near the bottom
    const scrollHeight = myExamplesModalBody.scrollHeight;
    const scrollTop = myExamplesModalBody.scrollTop;
    const clientHeight = myExamplesModalBody.clientHeight;
    const threshold = 200; // Pixels from bottom to trigger load

    if (scrollHeight - scrollTop - clientHeight < threshold) {
        // Check if not currently loading and if more pages exist
        if (!myExamplesIsLoading && myExamplesHasMore) {
            console.log('Scroll near bottom, loading next page...');
            loadMyExamples(myExamplesCurrentPage + 1, true); // Load next page and append
        }
    }
}

// --- API Call ---
async function loadMyExamples(page = 1, append = false) {
    if (myExamplesIsLoading) return;
    myExamplesIsLoading = true;
    const currentSort = myExamplesSortFilter?.value || 'date_desc'; // Get current sort value

    // Show initial loading indicator only if not appending and page is 1
    if (!append && page === 1 && myExamplesLoadingIndicator) {
        myExamplesLoadingIndicator.style.display = 'block';
    }
    if (append && myExamplesContainer) {
        showMyExamplesLoadMoreIndicator(true); // Show 'loading more' only when appending
    }
    if(myExamplesEmptyState) myExamplesEmptyState.style.display = 'none'; // Hide empty state

    try {
        const headers = getAuthHeaders(); // Assuming getAuthHeaders is available globally
        if (!headers) throw new Error('用户未认证');

        // Add sort parameter to the API call
        const response = await fetch(`${API_URL}/users/me/examples?page=${page}&limit=${myExamplesLimit}&sort=${currentSort}`, {
            headers: headers
        });

        if (!response.ok) {
            if (response.status === 401) {
                // Handle unauthorized access (e.g., redirect to login)
                console.error('用户未授权访问我的案例');
                // Optionally redirect or show message
            } else {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
        } else {
            const data = await response.json();
            console.log('My examples data:', data);
            // Use the generic render function
            renderExamplesList(myExamplesContainer, data.examples || [], append, true); 
            
            myExamplesTotalPages = data.pagination.totalPages;
            myExamplesHasMore = page < myExamplesTotalPages;
            myExamplesCurrentPage = page;
        }

    } catch (error) {
        console.error('加载我的案例失败:', error);
        if (myExamplesContainer && !append) {
            myExamplesContainer.innerHTML = '<div class="alert alert-danger">加载失败，请稍后重试。</div>';
        }
    } finally {
        myExamplesIsLoading = false;
        if (myExamplesLoadingIndicator) myExamplesLoadingIndicator.style.display = 'none';
        if (myExamplesContainer) showMyExamplesLoadMoreIndicator(false); // Always hide load more indicator after finishing
        // Show empty state only if it's the first page, not appending, and no examples were rendered
        if (page === 1 && !append && myExamplesContainer && myExamplesContainer.children.length === 0 && myExamplesEmptyState) {
             myExamplesEmptyState.style.display = 'block';
        }
    }
}

// Show/Hide Loading Indicator for loading more items (appended at the end)
function showMyExamplesLoadMoreIndicator(show) {
    showLoadMoreIndicator(myExamplesContainer, show);
}

// --- Pagination ---
/*
function updateMyExamplesPagination(pagination) { ... }
*/ 

// --- Infinite Scroll Handlers ---
function handleLikedExamplesScroll() {
    if (!likedExamplesModalBody || !likedExamplesContainer) return;
    const scrollHeight = likedExamplesModalBody.scrollHeight;
    const scrollTop = likedExamplesModalBody.scrollTop;
    const clientHeight = likedExamplesModalBody.clientHeight;
    const threshold = 200;
    if (scrollHeight - scrollTop - clientHeight < threshold) {
        if (!likedExamplesIsLoading && likedExamplesHasMore) {
            console.log('Liked Examples: Scroll near bottom, loading next page...');
            loadLikedExamples(likedExamplesCurrentPage + 1, true);
        }
    }
}

// --- Rendering Functions ---

// Render Liked Examples - Edit/Delete depends on ownership/admin
function renderLikedExamples(examplesToRender, append = false) {
    renderExamplesList(likedExamplesContainer, examplesToRender, append, false); // isOwnerList = false
}

// Generic Render Function
function renderExamplesList(container, examplesToRender, append, isOwnerList) {
    if (!container) return;
    const emptyState = container.querySelector('.empty-state'); // Find empty state within container

    if (!append) {
        container.innerHTML = ''; // Clear previous examples
    }

    if (examplesToRender.length === 0 && !append) {
        if (emptyState) emptyState.style.display = 'block';
    } else {
        if (emptyState) emptyState.style.display = 'none';
    }

    const fragment = document.createDocumentFragment();

    examplesToRender.forEach(example => {
        const card = document.createElement('div');
        card.classList.add('example-card');
        card.dataset.id = example.id;

        const categoryName = typeof getCategoryName === 'function' ? getCategoryName(example.category) : example.category;
        const categoryColorInfo = typeof getCategoryColor === 'function' ? getCategoryColor(example.category) : { backgroundColor: '#6c757d', borderColor: '#adb5bd' };
        const imageUrl = example.image_file && typeof imageBaseUrl !== 'undefined' ? imageBaseUrl + example.image_file : '';
        const defaultAvatar = 'images/default-avatar.png';
        const tagsHtml = Array.isArray(example.tags) ? example.tags.map(tag => `<span class="tag">${tag}</span>`).join('') : '';
        
        let authorHtml = '';
        if (example.author) {
            const authorName = example.author.nickname || example.author.username || '匿名';
            const authorAvatarUrl = example.author.avatar_url && typeof imageBaseUrl !== 'undefined' ? imageBaseUrl + example.author.avatar_url : defaultAvatar;
            authorHtml = `
                <div class="author-info">
                    <img src="${authorAvatarUrl}" alt="${authorName}" class="author-avatar" loading="lazy" onerror="this.onerror=null; this.src='${defaultAvatar}';">
                    <span>${authorName}</span>
                </div>
            `;
        }
        
        const modelName = example.target_model || null;
        let modelHtml = modelName ? `<span class="model-badge">${modelName}</span>` : '';
        
        const likeButtonClass = example.is_liked_by_current_user ? 'liked' : '';
        const likeIconClass = example.is_liked_by_current_user ? 'bi-heart-fill' : 'bi-heart';
        const likesCount = example.likes_count || 0;
        const likeTitle = example.is_liked_by_current_user ? '取消点赞' : '点赞';
        
        // Determine if Edit/Delete should be shown
        let editDeleteDropdownHtml = '';
        const canModify = isOwnerList || (currentUser && (example.author?.id === currentUser.id || currentUser.role === 'admin'));
        if (canModify) {
            editDeleteDropdownHtml = `
                <div class="dropdown d-inline-block action-dropdown"> 
                  <button class="btn btn-icon btn-outline-secondary" type="button" data-bs-toggle="dropdown" aria-expanded="false" title="更多操作">
                    <i class="bi bi-three-dots-vertical"></i>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-dark dropdown-menu-end">
                    <li><button class="dropdown-item edit-example-btn" type="button"><i class="bi bi-pencil-square me-2"></i>编辑</button></li>
                    <li><button class="dropdown-item delete-example-btn text-danger" type="button"><i class="bi bi-trash me-2"></i>删除</button></li>
                  </ul>
                </div>
            `;
        }

        card.innerHTML = `
            ${imageUrl ? `<img src="${imageUrl}" class="example-img" alt="${example.title || '案例图片'}" loading="lazy" onerror="this.onerror=null; this.src='images/placeholder.png';">` : '<div class="example-img-placeholder"></div>'}
            <span class="category-badge" style="background-color: ${categoryColorInfo.backgroundColor}; border-color: ${categoryColorInfo.borderColor};">${categoryName}</span>
            ${modelHtml}
            <div class="card-body">
                <h5>${example.title || '无标题'}</h5>
                <p class="prompt-preview">${example.prompt ? example.prompt.substring(0, 100) + (example.prompt.length > 100 ? '...' : '') : '无提示词'}</p>
                <div class="tags-container">
                    ${tagsHtml}
                </div>
                <div class="actions">
                    <button class="btn btn-icon btn-outline-secondary like-btn ${likeButtonClass}" title="${likeTitle}">
                        <i class="bi ${likeIconClass}"></i>
                        <span class="like-count ms-1">${likesCount}</span>
                    </button>
                    <button class="btn btn-icon btn-outline-secondary copy-prompt-btn" title="复制提示词">
                        <i class="bi bi-clipboard"></i>
                    </button>
                    <button class="btn btn-icon btn-outline-secondary share-example-btn" title="分享">
                        <i class="bi bi-share"></i>
                    </button>
                    ${editDeleteDropdownHtml} <!-- Insert Edit/Delete dropdown here -->
                </div>
            </div>
            ${authorHtml}
        `;
        fragment.appendChild(card);
    });

    container.appendChild(fragment);
}

// --- Loading Indicator Functions ---
function showLikedExamplesInitialLoading(show) {
    if (likedExamplesLoadingIndicator) likedExamplesLoadingIndicator.style.display = show ? 'flex' : 'none';
}
function showLikedExamplesLoadMoreIndicator(show) {
    showLoadMoreIndicator(likedExamplesContainer, show);
}

// Generic function to show/hide load more indicator
function showLoadMoreIndicator(container, show) {
    if (!container) return;
    let indicator = container.querySelector('.loading-more-indicator');
    if (show) {
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'loading-more-indicator loading';
            indicator.style.gridColumn = '1 / -1';
            indicator.innerHTML = `
                <div class="spinner-border spinner-border-sm" role="status"></div>
                <span>加载更多...</span>
            `;
            container.appendChild(indicator);
        }
    } else {
        if (indicator) indicator.remove();
    }
}

// --- Reset View Functions ---
function resetLikedExamplesView() {
    likedExamplesCurrentPage = 1;
    likedExamplesHasMore = true;
    if (likedExamplesContainer) likedExamplesContainer.innerHTML = '';
    if (likedExamplesEmptyState) likedExamplesEmptyState.style.display = 'none';
    // Reset scroll position
    if (likedExamplesModalBody) {
        likedExamplesModalBody.scrollTop = 0;
    }
}

// --- API Calls ---
// Load Liked Examples
async function loadLikedExamples(page = 1, append = false) {
     await loadExamplesGeneric('/users/me/likes', page, append, likedExamplesIsLoading, likedExamplesHasMore, likedExamplesCurrentPage,
                              showLikedExamplesInitialLoading, showLikedExamplesLoadMoreIndicator, renderLikedExamples,
                              likedExamplesContainer, likedExamplesEmptyState, (loading) => likedExamplesIsLoading = loading, 
                              (hasMore) => likedExamplesHasMore = hasMore, (cp) => likedExamplesCurrentPage = cp);
}

// Generic API Call Logic
async function loadExamplesGeneric(endpointPath, page, append, isLoadingFlag, hasMoreFlag, currentPageVar,
                                   showInitialLoadingFunc, showLoadMoreFunc, renderFunc,
                                   containerElement, emptyStateElement, setLoadingFunc, 
                                   setHasMoreFunc, setCurrentPageFunc, sortValue) {
    
    // Use the isLoadingFlag passed by reference (via setter function)
    if (isLoadingFlag) return;
    setLoadingFunc(true);
    
    if (!append) {
        showInitialLoadingFunc(true);
    } else {
        showLoadMoreFunc(true);
    }

    if (typeof getAuthHeaders !== 'function' || typeof API_URL === 'undefined') {
        console.error('Dependencies missing (getAuthHeaders or API_URL).');
        if(containerElement && !append) containerElement.innerHTML = '<div class="alert alert-danger">加载失败，请刷新重试。</div>';
        showInitialLoadingFunc(false);
        showLoadMoreFunc(false);
        setLoadingFunc(false);
        return;
    }

    const limit = endpointPath.includes('/likes') ? likedExamplesLimit : myExamplesLimit; // Use appropriate limit
    // Include sort parameter in the URL
    const url = `${API_URL}${endpointPath}?page=${page}&limit=${limit}&sort=${sortValue || 'date_desc'}`;

    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: getAuthHeaders(),
        });

        if (response.status === 401 || response.status === 403) {
            console.log('Unauthorized or Forbidden, redirecting to login.');
            if (typeof logout === 'function') { logout(); } else { window.location.href = 'login.html'; }
            return;
        }

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log(`Data received for ${endpointPath} page ${page}:`, data);

        if (page === 1 && !append) {
            containerElement.innerHTML = ''; 
            // Set appropriate empty state text (could be passed as param if needed)
            if (emptyStateElement) {
                 emptyStateElement.innerHTML = endpointPath.includes('/likes') 
                    ? '<i class="bi bi-heartbreak"></i> <span>您还没有点赞任何案例。</span>' 
                    : '<i class="bi bi-journal-x"></i> <span>您还没有创建任何案例。</span>';
            }
        }

        if (!data.examples || data.examples.length === 0) {
            setHasMoreFunc(false);
            if (page === 1) {
                 if (emptyStateElement) emptyStateElement.style.display = 'block';
            }
        } else {
            renderFunc(data.examples, append); // Call the specific render function
            setCurrentPageFunc(data.pagination.currentPage);
            setHasMoreFunc(data.pagination.currentPage < data.pagination.totalPages);
             if (emptyStateElement) emptyStateElement.style.display = 'none';
        }

    } catch (error) {
        console.error(`Error loading examples from ${endpointPath}:`, error);
        if(containerElement && !append) containerElement.innerHTML = '<div class="alert alert-danger">加载案例失败，请稍后重试。</div>';
        if (emptyStateElement) emptyStateElement.style.display = 'none';
    } finally {
        setLoadingFunc(false);
        showInitialLoadingFunc(false);
        showLoadMoreFunc(false);
    }
} 

// --- NEW: Sort Change Handlers --- 
function handleSortChangeMyExamples() {
    console.log('My Examples sort changed to:', myExamplesSortFilter.value);
    resetMyExamplesView();
    loadMyExamples(1); // Load first page with new sort
}

function handleSortChangeLikedExamples() {
    console.log('Liked Examples sort changed to:', likedExamplesSortFilter.value);
    resetLikedExamplesView();
    loadLikedExamples(1); // Load first page with new sort
}
// --- End NEW --- 