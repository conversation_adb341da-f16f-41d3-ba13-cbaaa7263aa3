document.addEventListener('DOMContentLoaded', () => {

    const creditsBalanceElement = document.getElementById('credits-balance');
    const tooltipWrapperElement = document.getElementById('user-credits-tooltip-wrapper'); // 获取 tooltip 的包裹元素
    const creditTagClickable = document.getElementById('credit-tag-clickable'); // 获取可点击的积分标签
    let creditTooltip = null; // 用于存储 tooltip 实例

    if (!creditsBalanceElement || !tooltipWrapperElement) {
        console.error('[Credits] 无法找到用于显示积分或 tooltip 的 HTML 元素。');
        return;
    }

    // 添加积分标签点击事件
    if (creditTagClickable) {
        creditTagClickable.addEventListener('click', () => {
            window.location.href = 'purchase.html';
        });
    }

    // 初始化 Tooltip (如果元素存在)
    if (tooltipWrapperElement) {
        creditTooltip = new bootstrap.Tooltip(tooltipWrapperElement, {
            title: '加载中...', // 初始标题
            trigger: 'hover', // 触发方式
            placement: 'bottom' // 显示位置
        });
    }

    // 显示账户激活提示
    function showActivationNotice(message) {
        // 检查是否已存在提示框，防止重复显示
        const existingAlert = document.querySelector('#activation-alert');
        if (existingAlert) {
            existingAlert.remove();
        }

        // 创建提示框
        const alertDiv = document.createElement('div');
        alertDiv.id = 'activation-alert';
        alertDiv.className = 'alert alert-warning alert-dismissible fade show mt-2';
        alertDiv.role = 'alert';
        alertDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
        `;

        // 插入到页面中合适的位置
        const container = document.querySelector('.container');
        if (container && container.firstChild) {
            container.insertBefore(alertDiv, container.firstChild);
        } else {
            document.body.insertBefore(alertDiv, document.body.firstChild);
        }
    }

    window.fetchCredits = async () => {

        let token = null;
        try {
            token = localStorage.getItem('token');
        } catch (e) {
            console.error('[Credits] Error accessing localStorage:', e);
            creditsBalanceElement.textContent = '错误';
            return;
        }

        if (!token) {
            console.error('[Credits] 未找到认证 Token，无法获取积分信息。');
            creditsBalanceElement.textContent = 'N/A';
            return;
        }


        try {
            const response = await fetch('https://caca.yzycolour.top/api/credits/balance', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });


            if (!response.ok) {
                const errorText = await response.text(); // 获取错误文本
                console.error(`[Credits] 获取积分信息失败。状态码: ${response.status}, 响应: ${errorText}`);
                throw new Error(`获取积分信息失败: ${response.status}`);
            }

            const data = await response.json();

            // 处理激活状态消息
            if (!data.is_activated && data.activation_message) {
                const customMessage = '您的账户尚未激活，请查收邮箱并点击激活链接，激活后即可获得每日免费积分！';
                showActivationNotice(customMessage);
            }

            let cumulativeCredits = 0;
            let remainingDailyFreeCredits = 0;
            let dailyFreeCreditsAmount = 20; // 默认值，以防后端没返回
            let displayTotalCreditsText = '错误';
            let tooltipText = '无法加载积分信息';

            // 安全地获取数据
            if (typeof data.cumulativeCredits === 'number') {
                cumulativeCredits = data.cumulativeCredits;
            }
            if (typeof data.remainingDailyFreeCredits === 'number') {
                remainingDailyFreeCredits = data.remainingDailyFreeCredits;
            }
            if (typeof data.dailyFreeCreditsAmount === 'number') {
                dailyFreeCreditsAmount = data.dailyFreeCreditsAmount;
            }

            // 计算总显示积分
            if (typeof data.cumulativeCredits === 'number' && typeof data.remainingDailyFreeCredits === 'number') {
                 displayTotalCreditsText = cumulativeCredits + remainingDailyFreeCredits;
            } else {
                 // 如果任一积分获取失败，显示累积积分（如果存在）或错误
                 displayTotalCreditsText = typeof data.cumulativeCredits === 'number' ? cumulativeCredits : '错误'; 
                 tooltipText = '积分信息加载不完整'; // 同时更新 tooltip 提示
            }
            
            // 更新 Tooltip 内容
            if (creditTooltip) {
                if (!data.is_activated) {
                    // 未激活用户显示激活提醒
                    tooltipText = '账户激活后可获得每日免费积分';
                } else if (remainingDailyFreeCredits > 0) {
                    tooltipText = `其中 ${remainingDailyFreeCredits} 积分将在 24 小时内过期`;
                } else {
                    tooltipText = `今日免费额度已用完 (共 ${dailyFreeCreditsAmount} 积分)`;
                }
                // 如果之前因为数据不完整设置了错误提示，这里会覆盖掉，是期望行为
            }

            // 更新页面上显示的积分（现在显示总和）
            creditsBalanceElement.textContent = displayTotalCreditsText;

            // 更新 Tooltip 的实际内容
            if (creditTooltip) {
                creditTooltip.setContent({ '.tooltip-inner': tooltipText });
            }

            /*
            const dailyFreeCreditsElement = document.getElementById('daily-free-credits-display'); // 假设 HTML 中有这个元素
            // ... (旧代码)
            */

        } catch (error) {
            console.error('[Credits] 获取或处理积分信息时出错:', error);
            creditsBalanceElement.textContent = '加载失败';
            if (creditTooltip) {
                creditTooltip.setContent({ '.tooltip-inner': '加载失败' });
            }
        }
    };

    // 页面加载时立即获取一次积分
    window.fetchCredits();
}); 