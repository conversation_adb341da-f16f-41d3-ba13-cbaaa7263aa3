document.addEventListener('DOMContentLoaded', () => {
    const adminManagementTabLink = document.getElementById('adminManagementTabLink');
    const adminTagsSection = document.getElementById('admin-tags-section');
    const adminTagsTableBody = document.getElementById('adminTagsTableBody');
    const adminAddTagBtn = document.getElementById('adminAddTagBtn');
    const editTagModalElement = document.getElementById('editTagModal');
    const editTagModal = new bootstrap.Modal(editTagModalElement);
    const tagForm = document.getElementById('tagForm');
    const tagIdInput = document.getElementById('tagId');
    const tagNameInput = document.getElementById('tagName');
    const saveTagBtn = document.getElementById('saveTagBtn');

    const adminCategoriesTableBody = document.getElementById('adminCategoriesTableBody');
    const adminAddCategoryBtn = document.getElementById('adminAddCategoryBtn');
    const editCategoryModalElement = document.getElementById('editCategoryModal');
    const editCategoryModal = new bootstrap.Modal(editCategoryModalElement);
    const categoryForm = document.getElementById('categoryForm');
    const categoryIdInput = document.getElementById('categoryId');
    const categoryNameInput = document.getElementById('categoryName');
    const categorySlugInput = document.getElementById('categorySlug');
    const categoryDescriptionInput = document.getElementById('categoryDescription');
    const saveCategoryBtn = document.getElementById('saveCategory');

    const adminUsersTableBody = document.getElementById('adminUsersTableBody');
    const adminUserSearchInput = document.getElementById('adminUserSearchInput');
    const adminUserSearchBtn = document.getElementById('adminUserSearchBtn');
    const adminUsersPagination = document.getElementById('adminUsersPagination');
    const editUserModalElement = document.getElementById('editUserModal');
    const editUserModal = new bootstrap.Modal(editUserModalElement);
    const userForm = document.getElementById('userForm');
    const editUserIdInput = document.getElementById('editUserId');
    const editUsernameDisplay = document.getElementById('editUsernameDisplay');
    const editEmailDisplay = document.getElementById('editEmailDisplay');
    const editNicknameInput = document.getElementById('editNickname');
    const editRoleSelect = document.getElementById('editRole');
    const saveUserBtn = document.getElementById('saveUserBtn');

    const adminDeleteConfirmModalElement = document.getElementById('adminDeleteConfirmModal');
    const adminDeleteConfirmModal = new bootstrap.Modal(adminDeleteConfirmModalElement);
    const adminDeleteConfirmModalLabel = document.getElementById('adminDeleteConfirmModalLabel');
    const adminDeleteConfirmModalBody = document.getElementById('adminDeleteConfirmModalBody');
    const adminConfirmDeleteBtn = document.getElementById('adminConfirmDeleteBtn');

    const userCreditSearchBtn = document.getElementById('userCreditSearchBtn');
    const userCreditsPagination = document.getElementById('user-credits-pagination');

    // Feature Costs Elements
    const featureCostsTabLink = document.querySelector('a[href="#admin-feature-costs-section"]');
    const featureCostsListDiv = document.getElementById('featureCostsList');
    const featureCostsLoading = document.getElementById('feature-costs-loading');
    const featureCostsError = document.getElementById('feature-costs-error');
    const saveFeatureCostsBtn = document.getElementById('saveFeatureCostsBtn');
    const featureCostsForm = document.getElementById('featureCostsForm');

    let currentAdminDeleteContext = null; // To store { type: 'tag'/'user'/'category', id: ..., name: ... }
    let currentAdminPage = 'tags'; // Track current admin page (tags, categories, users, credits, feature-costs)
    let currentAdminUserPage = 1; // Track current user list page

    // --- State Variables ---
    let currentUserPage = 1;
    let currentUserSearch = '';
    let totalUserPages = 1;

    // --- API Helper ---
    async function fetchAdminAPI(url, options = {}) {
        const token = localStorage.getItem('token');
        const defaultHeaders = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        };
        options.headers = { ...defaultHeaders, ...options.headers };

        try {
            const response = await fetch(getApiUrl(url), options);
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ error: '请求失败，无法解析响应' }));
                console.error(`API Error (${response.status}):`, errorData);
                showToast(`操作失败: ${errorData.error || response.statusText}`, 'error');
                throw new Error(errorData.error || `Request failed with status ${response.status}`);
            }
            // Handle cases where response might be empty (e.g., DELETE)
            if (response.status === 204 || response.headers.get('content-length') === '0') {
                return null; 
            }
            return await response.json();
        } catch (error) {
            console.error('Fetch Admin API Error:', error);
            // Don't show generic network error if a specific one was already shown
            if (!error.message.startsWith('Request failed')) {
                 showToast(`网络或服务器错误: ${error.message}`, 'error');
            }
            throw error; // Re-throw to be caught by calling function if needed
        }
    }

    // --- Utility Functions (Show Toast - assuming it exists in main.js or common scope) ---
    // function showToast(message, type = 'info') { ... } // Placeholder

    // --- Check Admin Role and Show Tab ---
    function checkAdminRoleAndSetup() {
        const userData = JSON.parse(localStorage.getItem('user'));
        if (userData && userData.role === 'admin') {
            adminManagementTabLink.style.display = 'block';

            // Load initial data for the default active tab (Tags)
            loadTags();

            // Add event listeners for admin navigation pills if needed
            const adminNavLinks = document.querySelectorAll('.admin-nav a[data-bs-toggle="pill"]');
            adminNavLinks.forEach(link => {
                link.addEventListener('shown.bs.tab', event => {
                    const targetId = event.target.getAttribute('href');
                    if (targetId === '#admin-tags-section') {
                        loadTags();
                    } else if (targetId === '#admin-categories-section') {
                        loadCategories();
                    } else if (targetId === '#admin-users-section') {
                        loadUsers(); // Now implemented
                    }
                });
            });

        } else {
            adminManagementTabLink.style.display = 'none';
        }
    }

    // --- Tag Management ---
    async function loadTags() {
        adminTagsTableBody.innerHTML = `<tr><td colspan="3" class="text-center"><div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">加载中...</span></div> 加载中...</td></tr>`;
        try {
            const tags = await fetchAdminAPI('/api/tags');
            renderTagsTable(tags);
        } catch (error) {
            adminTagsTableBody.innerHTML = `<tr><td colspan="3" class="text-center text-danger">加载标签失败</td></tr>`;
        }
    }

    function renderTagsTable(tags) {
        if (!tags || tags.length === 0) {
            adminTagsTableBody.innerHTML = `<tr><td colspan="3" class="text-center">暂无标签</td></tr>`;
            return;
        }
        adminTagsTableBody.innerHTML = tags.map(tag => `
            <tr>
                <td>${tag.id}</td>
                <td>${escapeHTML(tag.name)}</td>
                <td>
                    <button class="btn btn-outline-primary btn-sm edit-tag-btn" data-id="${tag.id}" data-name="${escapeHTML(tag.name)}">
                        <i class="bi bi-pencil-fill"></i> 编辑
                    </button>
                    <button class="btn btn-outline-danger btn-sm delete-tag-btn" data-id="${tag.id}" data-name="${escapeHTML(tag.name)}">
                        <i class="bi bi-trash-fill"></i> 删除
                    </button>
                </td>
            </tr>
        `).join('');
    }

    // Event Listener for Add Tag Button
    adminAddTagBtn.addEventListener('click', () => {
        tagForm.reset();
        tagIdInput.value = ''; // Ensure ID is cleared for adding
        editTagModalElement.querySelector('.modal-title').textContent = '添加标签';
        editTagModal.show();
    });

    // Event Listener for Edit/Delete Buttons (using delegation)
    adminTagsTableBody.addEventListener('click', (event) => {
        const target = event.target.closest('button');
        if (!target) return;

        const tagId = target.dataset.id;
        const tagName = target.dataset.name;

        if (target.classList.contains('edit-tag-btn')) {
            tagForm.reset();
            tagIdInput.value = tagId;
            tagNameInput.value = tagName; // Populate name
            editTagModalElement.querySelector('.modal-title').textContent = '编辑标签';
            editTagModal.show();
        } else if (target.classList.contains('delete-tag-btn')) {
            currentAdminDeleteContext = { type: 'tag', id: tagId, name: tagName };
            adminDeleteConfirmModalLabel.textContent = '确认删除标签';
            adminDeleteConfirmModalBody.innerHTML = `确定要删除标签 "<strong>${escapeHTML(tagName)}</strong>" 吗？<p class="text-warning mt-2">注意：如果此标签已被案例使用，将无法删除。</p>`;
            adminDeleteConfirmModal.show();
        }
    });

    // Event Listener for Save Tag Button (in modal)
    saveTagBtn.addEventListener('click', async () => {
        if (!tagForm.checkValidity()) {
            tagForm.reportValidity();
            return;
        }

        const tagData = { name: tagNameInput.value.trim() };
        const id = tagIdInput.value;
        const method = id ? 'PUT' : 'POST';
        const url = id ? `/api/tags/${id}` : '/api/tags';

        saveTagBtn.disabled = true;
        saveTagBtn.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...`;

        try {
            await fetchAdminAPI(url, {
                method: method,
                body: JSON.stringify(tagData)
            });
            showToast(`标签已成功${id ? '更新' : '创建'}`, 'success');
            editTagModal.hide();
            loadTags(); // Refresh the list
        } catch (error) {
            // Error toast is shown by fetchAdminAPI
        } finally {
             saveTagBtn.disabled = false;
             saveTagBtn.innerHTML = '保存';
        }
    });

    // --- Category Management ---
    async function loadCategories() {
        adminCategoriesTableBody.innerHTML = `<tr><td colspan="5" class="text-center"><div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">加载中...</span></div> 加载中...</td></tr>`;
        try {
            const categories = await fetchAdminAPI('/api/categories');
            renderCategoriesTable(categories);
        } catch (error) {
            adminCategoriesTableBody.innerHTML = `<tr><td colspan="5" class="text-center text-danger">加载分类失败</td></tr>`;
        }
    }

    function renderCategoriesTable(categories) {
        if (!categories || categories.length === 0) {
            adminCategoriesTableBody.innerHTML = `<tr><td colspan="5" class="text-center">暂无分类</td></tr>`;
            return;
        }
        adminCategoriesTableBody.innerHTML = categories.map(cat => `
            <tr>
                <td>${cat.id}</td>
                <td>${escapeHTML(cat.name)}</td>
                <td><code>${escapeHTML(cat.slug)}</code></td>
                <td>${escapeHTML(cat.description) || '-'}</td>
                <td>
                    <button class="btn btn-outline-primary btn-sm edit-category-btn" data-id="${cat.id}" data-name="${escapeHTML(cat.name)}" data-slug="${escapeHTML(cat.slug)}" data-description="${escapeHTML(cat.description || '')}">
                        <i class="bi bi-pencil-fill"></i> 编辑
                    </button>
                    <button class="btn btn-outline-danger btn-sm delete-category-btn" data-id="${cat.id}" data-name="${escapeHTML(cat.name)}">
                        <i class="bi bi-trash-fill"></i> 删除
                    </button>
                </td>
            </tr>
        `).join('');
    }

    // Event Listener for Add Category Button
    adminAddCategoryBtn.addEventListener('click', () => {
        categoryForm.reset();
        categoryIdInput.value = ''; // Ensure ID is cleared
        editCategoryModalElement.querySelector('.modal-title').textContent = '添加分类';
        editCategoryModal.show();
    });

    // Event Listener for Edit/Delete Category Buttons (using delegation)
    adminCategoriesTableBody.addEventListener('click', (event) => {
        const target = event.target.closest('button');
        if (!target) return;

        const catId = target.dataset.id;
        const catName = target.dataset.name;

        if (target.classList.contains('edit-category-btn')) {
            categoryForm.reset();
            categoryIdInput.value = catId;
            categoryNameInput.value = catName;
            categorySlugInput.value = target.dataset.slug;
            categoryDescriptionInput.value = target.dataset.description;
            editCategoryModalElement.querySelector('.modal-title').textContent = '编辑分类';
            editCategoryModal.show();
        } else if (target.classList.contains('delete-category-btn')) {
            currentAdminDeleteContext = { type: 'category', id: catId, name: catName };
            adminDeleteConfirmModalLabel.textContent = '确认删除分类';
            adminDeleteConfirmModalBody.innerHTML = `确定要删除分类 "<strong>${escapeHTML(catName)}</strong>" 吗？<p class="text-danger mt-2">注意：如果此分类下有案例，将无法删除。</p>`;
            adminDeleteConfirmModal.show();
        }
    });

    // Event Listener for Save Category Button (in modal)
    saveCategoryBtn.addEventListener('click', async () => {
        if (!categoryForm.checkValidity()) {
            categoryForm.reportValidity(); return;
        }

        const categoryData = {
            name: categoryNameInput.value.trim(),
            slug: categorySlugInput.value.trim(),
            description: categoryDescriptionInput.value.trim()
        };
        const id = categoryIdInput.value;
        const method = id ? 'PUT' : 'POST';
        const url = id ? `/api/categories/${id}` : '/api/categories';

        saveCategoryBtn.disabled = true;
        saveCategoryBtn.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...`;

        try {
            await fetchAdminAPI(url, {
                method: method,
                body: JSON.stringify(categoryData)
            });
            showToast(`分类已成功${id ? '更新' : '创建'}`, 'success');
            editCategoryModal.hide();
            loadCategories(); // Refresh the list
        } catch (error) { /* Handled by fetchAdminAPI */ } finally {
            saveCategoryBtn.disabled = false; saveCategoryBtn.innerHTML = '保存';
        }
    });

    // --- User Management ---
    async function loadUsers(page = 1, search = '') {
        currentUserPage = page;
        currentUserSearch = search;
        adminUsersTableBody.innerHTML = `<tr><td colspan="7" class="text-center"><div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">加载中...</span></div> 加载中...</td></tr>`;
        adminUsersPagination.innerHTML = ''; // Clear pagination

        try {
            const url = `/api/admin/users?page=${page}&limit=15&search=${encodeURIComponent(search)}`; // Limit 15 per page
            const data = await fetchAdminAPI(url);
            renderUsersTable(data.users);
            totalUserPages = data.pagination.totalPages; // Store total pages
            renderPaginationControls(data.pagination.currentPage, data.pagination.totalPages, '#adminUsersPagination', loadUsers);
        } catch (error) {
            adminUsersTableBody.innerHTML = `<tr><td colspan="7" class="text-center text-danger">加载用户失败</td></tr>`;
        }
    }

    function renderUsersTable(users) {
        if (!users || users.length === 0) {
            adminUsersTableBody.innerHTML = `<tr><td colspan="7" class="text-center">未找到用户</td></tr>`;
            return;
        }
        // Get current logged-in user ID to disable delete for self
        const loggedInUserId = JSON.parse(localStorage.getItem('user'))?.id;

        adminUsersTableBody.innerHTML = users.map(user => {
            const createdAt = user.created_at ? new Date(user.created_at).toLocaleString('zh-CN') : '-';
            const isSelf = user.id === loggedInUserId;
            const deleteButtonDisabled = isSelf ? 'disabled' : '';
            const deleteButtonTitle = isSelf ? '不能删除自己' : '删除用户';
            const roleBadge = user.role === 'admin' 
                ? '<span class="badge bg-danger">Admin</span>' 
                : '<span class="badge bg-secondary">User</span>';
                
            return `
            <tr>
                <td>${user.id}</td>
                <td>${escapeHTML(user.username)}</td>
                <td title="${escapeHTML(user.email)}">${escapeHTML(user.email)}</td>
                <td>${escapeHTML(user.nickname) || '-'}</td>
                <td>${roleBadge}</td>
                <td>${createdAt}</td>
                <td>
                    <button class="btn btn-outline-primary btn-sm edit-user-btn" 
                            data-id="${user.id}" 
                            data-username="${escapeHTML(user.username)}" 
                            data-email="${escapeHTML(user.email)}" 
                            data-nickname="${escapeHTML(user.nickname || '')}" 
                            data-role="${user.role}">
                        <i class="bi bi-pencil-fill"></i> 编辑
                    </button>
                    <button class="btn btn-outline-danger btn-sm delete-user-btn" 
                            data-id="${user.id}" 
                            data-username="${escapeHTML(user.username)}" 
                            ${deleteButtonDisabled}
                            title="${deleteButtonTitle}">
                        <i class="bi bi-trash-fill"></i> 删除
                    </button>
                </td>
            </tr>
        `}).join('');
    }

    // Generic Pagination Renderer
    function renderPaginationControls(currentPage, totalPages, targetElementSelector, loadFunction) {
        const paginationElement = document.querySelector(targetElementSelector);
        if (!paginationElement) return;
        paginationElement.innerHTML = ''; // Clear existing

        if (totalPages <= 1) return; // No pagination needed

        const createPageItem = (pageNumber, text = pageNumber, isActive = false, isDisabled = false) => {
            const li = document.createElement('li');
            li.className = `page-item ${isActive ? 'active' : ''} ${isDisabled ? 'disabled' : ''}`;
            const a = document.createElement('a');
            a.className = 'page-link';
            a.href = '#';
            a.textContent = text;
            if (!isDisabled) {
                a.dataset.page = pageNumber;
                a.addEventListener('click', (e) => {
                    e.preventDefault();
                    // Call the provided load function with page and current search state
                    if (loadFunction === loadUsers) {
                        loadFunction(pageNumber, currentUserSearch);
                    } else {
                        loadFunction(pageNumber); // Assume other load functions only need page
                    }
                });
            }
            li.appendChild(a);
            return li;
        };

        // Previous Button
        paginationElement.appendChild(createPageItem(currentPage - 1, '«', false, currentPage === 1));

        // Page Numbers (simplified version: show first, last, current +/- 2, and ellipses)
        const maxPagesToShow = 5; // Number of numeric links around current page
        let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
        let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

        // Adjust startPage if endPage hits the limit
        startPage = Math.max(1, endPage - maxPagesToShow + 1);

        if (startPage > 1) {
            paginationElement.appendChild(createPageItem(1));
            if (startPage > 2) {
                paginationElement.appendChild(createPageItem(0, '...', false, true)); // Ellipsis
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationElement.appendChild(createPageItem(i, i, i === currentPage));
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationElement.appendChild(createPageItem(0, '...', false, true)); // Ellipsis
            }
            paginationElement.appendChild(createPageItem(totalPages));
        }

        // Next Button
        paginationElement.appendChild(createPageItem(currentPage + 1, '»', false, currentPage === totalPages));
        
        // 添加页码输入和跳转功能
        const pageJumpContainer = document.createElement('li');
        pageJumpContainer.className = 'page-item ms-2 d-flex align-items-center';
        
        // 生成唯一ID，以避免多个分页控件时的ID冲突
        const uniqueId = targetElementSelector.replace('#', '');
        const inputId = `pageJumpInput_${uniqueId}`;
        const btnId = `pageJumpBtn_${uniqueId}`;
        
        pageJumpContainer.innerHTML = `
            <div class="input-group input-group-sm" style="width: 110px;">
                <input type="number" class="form-control" id="${inputId}" 
                    min="1" max="${totalPages}" placeholder="${currentPage}/${totalPages}" 
                    style="text-align: center; border-radius: 4px 0 0 4px; height: 31px;">
                <button class="btn btn-outline-secondary" type="button" id="${btnId}" 
                    style="border-radius: 0 4px 4px 0; height: 31px;">
                    跳转
                </button>
            </div>
        `;
        paginationElement.appendChild(pageJumpContainer);
        
        // 添加跳转功能的事件监听
        setTimeout(() => {
            const pageJumpInput = document.getElementById(inputId);
            const pageJumpBtn = document.getElementById(btnId);
            
            if (pageJumpInput && pageJumpBtn) {
                // 设置焦点时清除placeholder
                pageJumpInput.addEventListener('focus', () => {
                    pageJumpInput.placeholder = '';
                });
                
                // 失去焦点时恢复placeholder
                pageJumpInput.addEventListener('blur', () => {
                    if (!pageJumpInput.value) {
                        pageJumpInput.placeholder = `${currentPage}/${totalPages}`;
                    }
                });
                
                // 输入框回车跳转
                pageJumpInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        jumpToPage();
                    }
                });
                
                // 按钮点击跳转
                pageJumpBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    jumpToPage();
                });
                
                // 跳转逻辑函数
                function jumpToPage() {
                    const pageNum = parseInt(pageJumpInput.value, 10);
                    if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages) {
                        if (loadFunction === loadUsers) {
                            loadFunction(pageNum, currentUserSearch);
                        } else {
                            loadFunction(pageNum);
                        }
                        pageJumpInput.value = ''; // 清空输入
                    } else {
                        // 输入无效，闪烁提示
                        pageJumpInput.classList.add('is-invalid');
                        setTimeout(() => {
                            pageJumpInput.classList.remove('is-invalid');
                        }, 1000);
                    }
                }
            }
        }, 100);
    }

    // Event Listener for User Search
    adminUserSearchBtn.addEventListener('click', () => {
        loadUsers(1, adminUserSearchInput.value.trim());
    });
    adminUserSearchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            loadUsers(1, adminUserSearchInput.value.trim());
        }
    });

    // Event Listener for Edit/Delete User Buttons (using delegation)
    adminUsersTableBody.addEventListener('click', (event) => {
        const target = event.target.closest('button');
        if (!target) return;

        const userId = target.dataset.id;
        const username = target.dataset.username;

        if (target.classList.contains('edit-user-btn')) {
            userForm.reset();
            editUserIdInput.value = userId;
            editUsernameDisplay.value = username;
            editEmailDisplay.value = target.dataset.email;
            editNicknameInput.value = target.dataset.nickname;
            editRoleSelect.value = target.dataset.role;
            editUserModal.show();
        } else if (target.classList.contains('delete-user-btn')) {
             if (target.disabled) return; // Double check if button is disabled
             currentAdminDeleteContext = { type: 'user', id: userId, name: username };
             adminDeleteConfirmModalLabel.textContent = '确认删除用户';
             adminDeleteConfirmModalBody.innerHTML = `确定要删除用户 "<strong>${escapeHTML(username)}</strong>" 吗？<p class="text-danger mt-2">此操作会移除该用户的所有关联数据（如点赞）并将其创建的案例作者设为 NULL，无法撤销！</p>`;
             adminDeleteConfirmModal.show();
        }
    });

    // Event Listener for Save User Button (in modal)
    saveUserBtn.addEventListener('click', async () => {
        if (!userForm.checkValidity()) {
             userForm.reportValidity(); return;
        }

        const userId = editUserIdInput.value;
        const userData = {
            nickname: editNicknameInput.value.trim(),
            role: editRoleSelect.value
        };

        saveUserBtn.disabled = true;
        saveUserBtn.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...`;

        try {
            await fetchAdminAPI(`/api/admin/users/${userId}`, {
                method: 'PUT',
                body: JSON.stringify(userData)
            });
            showToast('用户信息已更新', 'success');
            editUserModal.hide();
            loadUsers(currentUserPage, currentUserSearch); // Refresh current page/search
        } catch (error) { /* Handled by fetchAdminAPI */ } finally {
             saveUserBtn.disabled = false; saveUserBtn.innerHTML = '保存';
        }
    });

    // Event Listener for Confirm Delete Button (in confirmation modal)
    adminConfirmDeleteBtn.addEventListener('click', async () => {
        if (!currentAdminDeleteContext) return;

        const { type, id, name } = currentAdminDeleteContext;
        let url = '';
        let successMessage = '';
        let refreshFunction = null;

        if (type === 'tag') {
            url = `/api/tags/${id}`;
            successMessage = `标签 "${escapeHTML(name)}" 已删除`;
            refreshFunction = loadTags;
        } else if (type === 'category') {
            url = `/api/categories/${id}`;
            successMessage = `分类 "${escapeHTML(name)}" 已删除`;
            refreshFunction = loadCategories;
        } else if (type === 'user') {
            url = `/api/admin/users/${id}`;
            successMessage = `用户 "${escapeHTML(name)}" 已删除`;
            refreshFunction = () => loadUsers(currentUserPage, currentUserSearch); // Refresh current view
        } else {
            console.error('未知的删除类型:', type);
            return;
        }

        adminConfirmDeleteBtn.disabled = true;
        adminConfirmDeleteBtn.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 删除中...`;

        try {
            await fetchAdminAPI(url, { method: 'DELETE' });
            showToast(successMessage, 'success');
            adminDeleteConfirmModal.hide();

            if (refreshFunction) {
                refreshFunction(); // Call the appropriate refresh function
            }
            currentAdminDeleteContext = null;
        } catch (error) {
             console.error(`删除 ${type} (ID: ${id}, Name: ${name}) 失败:`, error.message);
             // Error toast handled by fetchAdminAPI
        } finally {
            adminConfirmDeleteBtn.disabled = false;
            adminConfirmDeleteBtn.innerHTML = '删除';
        }
    });

    // --- Feature Costs Management --- START ---

    async function loadFeatureCosts() {
        currentAdminPage = 'feature-costs';
        showLoadingState('feature-costs', true);
        featureCostsError.style.display = 'none';
        featureCostsListDiv.innerHTML = ''; // Clear previous entries
        saveFeatureCostsBtn.disabled = true;

        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${API_URL}/admin/feature-costs`, {
                headers: { 'Authorization': `Bearer ${token}` }
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || `HTTP Error ${response.status}`);
            }

            const costs = await response.json();
            renderFeatureCosts(costs);
            saveFeatureCostsBtn.disabled = false; // Enable save button after loading

        } catch (error) {
            console.error('Error loading feature costs:', error);
            featureCostsError.textContent = `加载成本设置失败: ${error.message}`;
            featureCostsError.style.display = 'block';
        } finally {
            showLoadingState('feature-costs', false);
        }
    }

    function renderFeatureCosts(costs) {
        featureCostsListDiv.innerHTML = ''; // Clear again just in case
        if (!costs || costs.length === 0) {
            featureCostsListDiv.innerHTML = '<p class="text-muted">未找到功能成本配置。</p>';
            return;
        }

        costs.forEach(item => {
            const div = document.createElement('div');
            div.className = 'row mb-3 align-items-center gx-2';
            
            const isChecked = item.can_use_free_credits === true || item.can_use_free_credits === 1;

            div.innerHTML = `
                <div class="col-sm-4">
                    <label for="cost-${item.feature_key}" class="col-form-label">
                        ${escapeHTML(item.description || item.feature_key)} <code class="small">(${item.feature_key})</code>
                    </label>
                </div>
                <div class="col-sm-3">
                    <input type="number" class="form-control form-control-sm feature-cost-input" 
                           id="cost-${item.feature_key}" 
                           data-feature-key="${item.feature_key}" 
                           value="${item.cost}" 
                           min="0" 
                           required>
                </div>
                <div class="col-sm-3">
                    <div class="form-check form-switch mt-1">
                        <input class="form-check-input feature-can-use-free-credits-input" type="checkbox" role="switch" 
                               id="free-${item.feature_key}" 
                               data-feature-key="${item.feature_key}" 
                               ${isChecked ? 'checked' : ''}>
                        <label class="form-check-label small" for="free-${item.feature_key}">可用免费额度</label>
                    </div>
                </div>
                <div class="col-sm-2">
                    <small class="text-muted d-block mt-1">积分</small>
                </div>
            `;
            featureCostsListDiv.appendChild(div);
        });
    }

    async function saveFeatureCosts() {
        const costItems = featureCostsListDiv.querySelectorAll('.row[data-feature-key], div[data-feature-key]');
        const costInputs = featureCostsListDiv.querySelectorAll('.feature-cost-input');
        const updates = [];
        let hasInvalidInput = false;

        costInputs.forEach(input => {
            const key = input.dataset.featureKey;
            const costValue = input.value.trim();
            const costInt = parseInt(costValue, 10);

            const canUseFreeCreditsCheckbox = featureCostsListDiv.querySelector(`.feature-can-use-free-credits-input[data-feature-key="${key}"]`);
            const canUseFreeCredits = canUseFreeCreditsCheckbox ? canUseFreeCreditsCheckbox.checked : false;

            if (costValue === '' || isNaN(costInt) || costInt < 0) {
                input.classList.add('is-invalid');
                hasInvalidInput = true;
            } else {
                input.classList.remove('is-invalid');
                updates.push({ 
                    feature_key: key, 
                    cost: costInt,
                    can_use_free_credits: canUseFreeCredits
                });
            }
        });

        if (hasInvalidInput) {
            showToast('输入无效，请检查成本值（必须为非负整数）。', 'error');
            return;
        }

        if (updates.length === 0) {
            showToast('没有可保存的更改。', 'info');
            return;
        }

        // Disable button and show spinner
        saveFeatureCostsBtn.disabled = true;
        const spinner = saveFeatureCostsBtn.querySelector('.spinner-border');
        if (spinner) spinner.style.display = 'inline-block';

        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${API_URL}/admin/feature-costs`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(updates)
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || `HTTP Error ${response.status}`);
            }

            showToast(result.message || '功能成本已更新', 'success');
            // Optionally reload costs after successful save
            // loadFeatureCosts(); 

        } catch (error) {
            console.error('Error saving feature costs:', error);
            showToast(`保存失败: ${error.message}`, 'error');
        } finally {
            // Re-enable button and hide spinner
            saveFeatureCostsBtn.disabled = false;
            if (spinner) spinner.style.display = 'none';
        }
    }

    // Event listener for the feature costs tab
    if (featureCostsTabLink) {
        featureCostsTabLink.addEventListener('shown.bs.tab', loadFeatureCosts);
    }

    // Event listener for the save button
    if (saveFeatureCostsBtn) {
        saveFeatureCostsBtn.addEventListener('click', saveFeatureCosts);
    }

    // --- Feature Costs Management --- END ---


    // Helper function to show loading states for different sections
    function showLoadingState(section, isLoading) {
        const loadingElements = {
            tags: adminTagsTableBody.querySelector('td'), // Assuming loading text is in a cell
            categories: adminCategoriesTableBody.querySelector('td'),
            users: adminUsersTableBody.querySelector('td'),
            credits: document.getElementById('user-credits-loading'),
            'feature-costs': featureCostsLoading
        };
        const errorElements = { // Added error elements
             tags: null, // Add if you have specific error divs
             categories: null,
             users: null,
             credits: document.getElementById('user-credits-error'),
             'feature-costs': featureCostsError
        }
        const contentElements = { // Added content elements
            tags: adminTagsTableBody,
            categories: adminCategoriesTableBody,
            users: adminUsersTableBody,
            credits: document.getElementById('user-credits-table-body').parentNode, // Table container
            'feature-costs': featureCostsListDiv
        }

        const loadingEl = loadingElements[section];
        const errorEl = errorElements[section];
        const contentEl = contentElements[section];

        if (loadingEl) {
            if (isLoading) {
                loadingEl.style.display = section === 'credits' || section === 'feature-costs' ? 'block' : 'table-cell'; // Adjust display based on element type
                if(contentEl) contentEl.style.display = 'none'; // Hide content when loading
                if(errorEl) errorEl.style.display = 'none'; // Hide error when loading
            } else {
                loadingEl.style.display = 'none';
                if(contentEl) contentEl.style.display = section === 'credits' || section === 'feature-costs' ? 'block' : 'table-row-group'; // Show content after loading
            }
        }
    }

    // --- Initialization ---
    // Check admin role after ensuring the rest of the page (like main.js user fetch) might have run
    // Using a small delay, but a more robust solution might involve custom events or promises
    // if main.js handles user/token loading.
    // For now, assuming token/user might be ready:
     checkAdminRoleAndSetup();

    // Helper to escape HTML (important for rendering user-generated content like tag names)
    function escapeHTML(str) {
        if (!str) return '';
        const div = document.createElement('div');
        div.appendChild(document.createTextNode(str));
        return div.innerHTML;
    }

    // Helper to get full API URL (assuming getApiUrl exists globally or in main.js)
    // If not, define it here:
    function getApiUrl(path) {
        // Use environment variable or a default for development
        const baseUrl = window.config?.apiUrl || 'https://caca.yzycolour.top'; 
        return `${baseUrl}${path}`;
    }
    // Ensure window.showToast exists or define it
    window.showToast = window.showToast || function(message, type = 'info') {
        // Basic alert fallback if no proper toast system is in place
        alert(`[${type.toUpperCase()}] ${message}`);
    };

}); 