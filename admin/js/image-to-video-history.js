const HISTORY_ITEMS_PER_PAGE = 5; // 定义在全局作用域或 loadI2VHistory 可访问的作用域

// --- 历史记录函数 ---
async function loadI2VHistory(page = 1, append = false) {
    const historyListContainer = document.getElementById('i2vHistoryList');
    const historyLoadingIndicator = document.getElementById('i2vHistoryLoading');
    const historyErrorAlert = document.getElementById('i2vHistoryError');
    const historyEmptyMessage = document.getElementById('i2vHistoryEmpty');
    const historyPaginationNav = document.getElementById('i2vHistoryPaginationNav');
    const historyRefreshBtn = document.getElementById('i2vHistoryRefreshBtn') || createRefreshButton();
    // currentHistoryPage 变量现在应该在主模块中管理，或者通过参数传递，或者也移到这里统一管理
    // 为简单起见，暂时假设 currentHistoryPage 在调用此函数前已在主模块中更新
    // 或者，也可以将 currentHistoryPage 的声明移到这个文件，并让主模块调用这里的函数时更新它

    if (!historyListContainer || !historyLoadingIndicator || !historyErrorAlert || !historyEmptyMessage || !historyPaginationNav) {
        console.warn("图生视频历史记录相关DOM元素未完全找到，跳过加载历史。");
        return;
    }

    // 只有在非追加模式下才清空历史记录列表和显示加载指示器
    if (!append) {
        historyLoadingIndicator.style.display = 'block';
        historyErrorAlert.style.display = 'none';
        historyListContainer.innerHTML = ''; // 清空旧列表
        historyEmptyMessage.style.display = 'none';
    }
    historyPaginationNav.style.display = 'none';
    // currentHistoryPage = page; // 移到主模块或作为参数传入后在主模块更新

    try {
        // const apiBaseUrl = 'https://caca.yzycolour.top'; // 旧的设置
        const apiBaseUrl = 'https://caca.yzycolour.top/api'; // <--- 修改此行
        const response = await fetch(`${apiBaseUrl}/image-to-video/history?page=${page}&limit=${HISTORY_ITEMS_PER_PAGE}`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) {
            throw new Error(`获取历史记录失败，状态码: ${response.status}`);
        }
        const data = await response.json();

        // 只有在非追加模式下才隐藏加载指示器
        if (!append) {
            historyLoadingIndicator.style.display = 'none';
        }

        if (data.history && data.history.length > 0) {
            // 检查是否有任务正在处理中
            const hasRunningTasks = data.history.some(item => 
                item.status.toUpperCase() === 'RUNNING' || 
                item.status.toUpperCase() === 'PENDING' || 
                item.status.toUpperCase() === 'PROCESSING');
            
            // 如果有任务在处理中，显示刷新按钮
            if (historyRefreshBtn) {
                historyRefreshBtn.style.display = hasRunningTasks ? 'inline-block' : 'none';
            }
            
            // 渲染历史记录项
            data.history.forEach(item => {
                const listItem = createI2VHistoryListItem(item); // 重命名以避免与全局命名冲突
                historyListContainer.appendChild(listItem);
            });
            
            // 渲染加载更多按钮
            renderI2VPagination(data.pagination, loadI2VHistory); // 传递回调函数
            
            // 如果没有记录，显示没有记录的消息
            if (historyListContainer.childElementCount === 0 && !append) {
                historyEmptyMessage.style.display = 'block';
            } else {
                historyEmptyMessage.style.display = 'none';
            }
        } else {
            if (!append) {
                historyEmptyMessage.style.display = 'block';
            }
            if (historyRefreshBtn) {
                historyRefreshBtn.style.display = 'none';
            }
        }
        return data.pagination.currentPage; // 返回当前页码，方便主模块更新 currentHistoryPage
    } catch (error) {
        console.error('加载图生视频历史记录错误:', error);
        if (!append) {
            historyLoadingIndicator.style.display = 'none';
            historyErrorAlert.textContent = error.message || '加载历史记录时发生未知错误。';
            historyErrorAlert.style.display = 'block';
        }
        return page; // 出错时返回请求的页码
    }
}

// 创建刷新按钮
function createRefreshButton() {
    // 检查容器元素是否存在
    const container = document.getElementById('i2vHistoryListContainer');
    if (!container) return null;
    
    // 检查是否已存在刷新按钮
    let refreshBtn = document.getElementById('i2vHistoryRefreshBtn');
    if (refreshBtn) return refreshBtn;
    
    // 创建刷新按钮
    refreshBtn = document.createElement('button');
    refreshBtn.id = 'i2vHistoryRefreshBtn';
    refreshBtn.className = 'btn btn-sm btn-outline-primary mb-3';
    refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>刷新历史记录';
    refreshBtn.style.display = 'none'; // 默认隐藏
    
    // 直接添加到容器中，避免DOM关系错误
    container.appendChild(refreshBtn);
    
    // 添加点击事件
    refreshBtn.addEventListener('click', async () => {
        // 使用当前页码重新加载历史记录
        const currentPage = parseInt(document.querySelector('#i2vHistoryPagination .page-item.active .page-link')?.dataset.page) || 1;
        if (typeof loadI2VHistory === 'function') {
            await loadI2VHistory(currentPage);
        }
    });
    
    return refreshBtn;
}

function createI2VHistoryListItem(item) { // 重命名
    const div = document.createElement('div');
    div.className = 'd-flex flex-column align-items-start i2v-history-item'; // 移除mb-3 p-3 rounded-3，i2v-history-item样式将处理所有外观
    
    let statusBadge;
    let statusNote = '';
    
    switch (item.status.toUpperCase()) {
        case 'SUCCEEDED': 
            statusBadge = '<span class="badge bg-success glassmorphism">成功</span>'; 
            break;
        case 'FAILED': 
            statusBadge = '<span class="badge bg-danger glassmorphism">失败</span>'; 
            break;
        case 'PROCESSING':
        case 'RUNNING': 
            statusBadge = '<span class="badge bg-info glassmorphism">处理中</span>'; 
            statusNote = '<small class="text-muted d-block mt-1">任务正在后台处理中，状态会自动更新</small>';
            break;
        case 'PENDING': 
            statusBadge = '<span class="badge bg-warning text-dark glassmorphism">等待中</span>'; 
            statusNote = '<small class="text-muted d-block mt-1">任务正在后台等待处理，状态会自动更新</small>';
            break;
        default: 
            statusBadge = `<span class="badge bg-secondary glassmorphism">${item.status}</span>`;
    }

    const createdAt = new Date(item.created_at).toLocaleString('zh-CN', { hour12: false });
    const shortTaskId = item.aliyun_task_id.substring(0, 8) + '...';

    let resultControls = '';
    if (item.status.toUpperCase() === 'SUCCEEDED' && item.video_url) {
        resultControls = `
            <button class="btn btn-sm btn-outline-primary mt-1 me-2 play-i2v-video-btn glassmorphism-btn" data-video-url="${item.video_url}" title="播放视频">
                <i class="bi bi-play-circle"></i> 播放
            </button>
            <button class="btn btn-sm btn-outline-secondary mt-1 download-i2v-video-btn glassmorphism-btn" data-video-url="${item.video_url}" title="下载视频">
                <i class="bi bi-download"></i> 下载
            </button>`;
    } else if (item.status.toUpperCase() === 'FAILED' && item.error_message) {
        resultControls = `<p class="mb-0 mt-1"><small class="text-danger">错误: ${item.error_message}</small></p>`;
    } else if (item.status.toUpperCase() === 'RUNNING' || item.status.toUpperCase() === 'PROCESSING' || item.status.toUpperCase() === 'PENDING') {
        resultControls = statusNote;
    }

    // 判断是否为首尾帧模式
    const isKf2vMode = item.api_type === 'kf2v';
    
    let imagePreviewHtml = '';
    if (isKf2vMode) {
        // 首尾帧模式：显示两张图片的缩略图
        const firstFrameImg = item.original_image_comfyui_url 
            ? `<img src="${item.original_image_comfyui_url}" alt="首帧图片" class="rounded" style="width: 80px; height: 80px; object-fit: cover; border: 1px solid #444;">` 
            : '<div class="rounded bg-secondary d-flex align-items-center justify-content-center" style="width: 80px; height: 80px; border: 1px solid #444;"><i class="bi bi-image-alt fs-4"></i></div>';
            
        const lastFrameImg = item.last_frame_comfyui_url 
            ? `<img src="${item.last_frame_comfyui_url}" alt="尾帧图片" class="rounded" style="width: 80px; height: 80px; object-fit: cover; border: 1px solid #444;">` 
            : '<div class="rounded bg-secondary d-flex align-items-center justify-content-center" style="width: 80px; height: 80px; border: 1px solid #444;"><i class="bi bi-image-alt fs-4"></i></div>';
        
        imagePreviewHtml = `
            <div class="d-flex flex-column me-3">
                <div class="position-relative mb-1">
                    ${firstFrameImg}
                    <span class="position-absolute bottom-0 start-0 badge bg-dark text-light" style="font-size: 0.6rem;">首帧</span>
                </div>
                <div class="position-relative">
                    ${lastFrameImg}
                    <span class="position-absolute bottom-0 start-0 badge bg-dark text-light" style="font-size: 0.6rem;">尾帧</span>
                </div>
            </div>`;
    } else {
        // 传统模式：显示单张图片
        imagePreviewHtml = item.original_image_url 
            ? `<img src="${item.original_image_url}" alt="原始图片缩略图" class="me-3 rounded" style="width: 80px; height: 80px; object-fit: cover; border: 1px solid #444;">` 
            : '<div class="me-3 rounded bg-secondary d-flex align-items-center justify-content-center" style="width: 80px; height: 80px; border: 1px solid #444;"><i class="bi bi-image-alt fs-4"></i></div>';
    }

    div.innerHTML = `
        <div class="d-flex w-100">
            ${imagePreviewHtml}
            <div class="flex-grow-1">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1 text-truncate" title="任务ID: ${item.aliyun_task_id}">任务: ${shortTaskId}</h6>
                    <small class="text-muted">${createdAt}</small>
                </div>
                <p class="mb-1 prompt-text" title="${item.prompt}">提示: ${item.prompt || '-'}</p>
                <div class="d-flex justify-content-between align-items-center mt-1">
                    <small class="text-muted">模型: ${item.model_used} | 分辨率: ${item.resolution_used} ${isKf2vMode ? '| <span class="badge bg-info text-light">首尾帧</span>' : ''}</small>
                    ${statusBadge}
                </div>
                <div class="mt-2">
                    ${resultControls}
                </div>
            </div>
        </div>
    `;

    const playButton = div.querySelector('.play-i2v-video-btn');
    if (playButton) {
        playButton.addEventListener('click', (e) => {
            e.preventDefault();
            const resultVideoEl = document.getElementById('i2vResultVideo');
            const resultContainerEl = document.getElementById('i2vResultContainer');
            const resultsPlaceholderEl = document.getElementById('i2vResultsPlaceholder');
            const downloadLinkEl = document.getElementById('i2vDownloadLink');
            
            if (resultVideoEl && resultContainerEl && resultsPlaceholderEl && downloadLinkEl) {
                resultsPlaceholderEl.style.display = 'none';
                document.getElementById('i2vErrorAlert').style.display = 'none';
                document.getElementById('i2vLoadingIndicator').style.display = 'none';
                
                resultVideoEl.src = playButton.dataset.videoUrl;
                resultVideoEl.load();
                resultContainerEl.style.display = 'block';
                downloadLinkEl.href = playButton.dataset.videoUrl;
                resultVideoEl.scrollIntoView({ behavior: 'smooth', block: 'center' });
            } else {
                console.warn('图生视频主播放器DOM元素未找到');
            }
        });
    }

    // 为下载按钮添加事件监听器
    const downloadButton = div.querySelector('.download-i2v-video-btn');
    if (downloadButton) {
        downloadButton.addEventListener('click', (e) => {
            e.preventDefault();
            const videoUrl = downloadButton.dataset.videoUrl;
            
            // 从URL中提取建议的文件名
            let suggestedFilename = 'video.mp4';
            try {
                const urlObj = new URL(videoUrl);
                const filenameParam = urlObj.searchParams.get('filename');
                if (filenameParam) {
                    suggestedFilename = filenameParam;
                }
            } catch (e) {
                console.warn('无法从URL解析文件名:', e);
            }
            
            // 使用全局的 initiateVideoDownload 函数（定义在 image-to-video.js 中）
            if (typeof initiateVideoDownload === 'function') {
                initiateVideoDownload(videoUrl, downloadButton, suggestedFilename);
            } else {
                console.error('initiateVideoDownload 函数未定义，请确保 image-to-video.js 已正确加载');
                alert('下载功能暂时不可用，请稍后再试');
            }
        });
    }

    return div;
}

function renderI2VPagination(pagination, loadHistoryCallback) {
    // 检查需要的DOM元素
    const historyPaginationNav = document.getElementById('i2vHistoryPaginationNav');
    const loadMoreContainer = document.getElementById('i2vHistoryLoadMoreContainer');
    const loadMoreBtn = document.getElementById('loadMoreI2VHistoryBtn');
    
    if (!historyPaginationNav || !loadMoreContainer || !loadMoreBtn) {
        console.warn('加载更多按钮相关DOM元素未找到');
        return;
    }
    
    // 隐藏旧的分页导航
    historyPaginationNav.style.display = 'none';
    
    // 如果当前页小于总页数，显示加载更多按钮
    if (pagination.currentPage < pagination.totalPages) {
        loadMoreContainer.style.display = 'block';
        
        // 移除之前的事件监听器（如果有）
        loadMoreBtn.replaceWith(loadMoreBtn.cloneNode(true));
        
        // 重新获取按钮引用并添加事件监听器
        const refreshedLoadMoreBtn = document.getElementById('loadMoreI2VHistoryBtn');
        refreshedLoadMoreBtn.addEventListener('click', async () => {
            if (typeof loadHistoryCallback === 'function') {
                refreshedLoadMoreBtn.disabled = true;
                refreshedLoadMoreBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>加载中...';
                
                // 加载下一页并追加到现有列表
                await loadHistoryCallback(pagination.currentPage + 1, true);
                
                refreshedLoadMoreBtn.disabled = false;
                refreshedLoadMoreBtn.innerHTML = '加载更多';
            }
        });
    } else {
        // 没有更多页面可加载
        loadMoreContainer.style.display = 'none';
    }
}

// 添加一个定时刷新的函数，用于自动更新处理中的任务
function setupAutoRefresh() {
    // 检查是否已设置自动刷新
    if (window.i2vAutoRefreshInterval) {
        clearInterval(window.i2vAutoRefreshInterval);
    }
    
    // 设置每30秒自动刷新一次
    window.i2vAutoRefreshInterval = setInterval(async () => {
        // 只在历史标签页激活时刷新
        const historyTab = document.querySelector('.tab-pane.fade.show.active#image-to-video-history-pane');
        if (historyTab) {
            const currentPage = parseInt(document.querySelector('#i2vHistoryPagination .page-item.active .page-link')?.dataset.page) || 1;
            if (typeof loadI2VHistory === 'function') {
                await loadI2VHistory(currentPage);
            }
        }
    }, 30000); // 30秒刷新一次
}

// 在页面加载时启动自动刷新
document.addEventListener('DOMContentLoaded', () => {
    setupAutoRefresh();
});

// 初始化和事件绑定应在主模块 image-to-video.js 中处理，调用这里的函数
// 例如，在 image-to-video.js 的 DOMContentLoaded:
// const historyTabButton = document.getElementById('image-to-video-tab');
// if (historyTabButton) {
// historyTabButton.addEventListener('shown.bs.tab', async function () {
// currentHistoryPage = await loadI2VHistory(1);
// });
// if (historyTabButton.classList.contains('active')) {
// currentHistoryPage = await loadI2VHistory(1);
// }
// } 