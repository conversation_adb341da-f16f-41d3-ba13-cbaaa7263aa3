'use strict';

document.addEventListener('DOMContentLoaded', () => {

    const section = document.getElementById('user-credit-management-section');
    const tableBody = document.getElementById('user-credits-table-body');
    const loadingIndicator = document.getElementById('user-credits-loading');
    const errorIndicator = document.getElementById('user-credits-error');
    const navLink = document.querySelector('a[href="#user-credit-management-section"]');
    const paginationContainer = document.getElementById('user-credits-pagination');
    // 新增：获取搜索元素引用
    const searchInput = document.getElementById('userCreditSearchInput');
    const searchButton = document.getElementById('userCreditSearchBtn');


    if (!section || !tableBody || !loadingIndicator || !errorIndicator || !navLink || !paginationContainer || !searchInput || !searchButton) { // 添加搜索元素的检查
        console.error('[UserCredits] Required HTML elements not found. Cannot initialize user credit management.');
        return;
    }

    // 新增：分页和搜索状态变量
    let userCreditsCurrentPage = 1;
    let userCreditsTotalPages = 1;
    let currentUserCreditsSearchTerm = ''; // 存储当前搜索词

    // --- 函数：加载用户数据 (修改：接受页码和搜索词参数) ---
    async function loadUserCredits(page = 1, searchTerm = '') {

        try {
            showLoading(true);
            showError(false);
            tableBody.innerHTML = ''; // 清空旧数据
            paginationContainer.innerHTML = ''; // 清空旧分页

            const token = localStorage.getItem('token');
            if (!token) {
                console.error('[UserCredits Debug] No token found, stopping fetch.');
                showError(true, '无法加载数据：未找到认证令牌。');
                return;
            }

            const fetchHeaders = {
                'Authorization': `Bearer ${token}`
            };

            // 构建带分页和搜索参数的 URL
            const apiUrl = `https://caca.yzycolour.top/api/admin/users?page=${page}&search=${encodeURIComponent(searchTerm)}`;

            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: fetchHeaders
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                 if (response.status === 403) {
                    throw new Error(errorData.error || '权限不足，需要管理员权限');
                 } else {
                    throw new Error(errorData.error || `HTTP 错误: ${response.status}`);
                 }
            }

            const responseData = await response.json();

            if (!responseData || !Array.isArray(responseData.users) || !responseData.pagination) {
                console.error('[UserCredits] Invalid data structure received from server:', responseData);
                throw new Error('从服务器获取的数据格式不正确。');
            }

            currentUserCreditsSearchTerm = searchTerm; // 更新当前搜索状态
            userCreditsCurrentPage = responseData.pagination.currentPage;
            userCreditsTotalPages = responseData.pagination.totalPages;

            renderTable(responseData.users);
            renderPaginationControls();

        } catch (error) {
            console.error('[UserCredits] Error during loadUserCredits execution:', error);
            console.error('[UserCredits] Error Name:', error.name);
            console.error('[UserCredits] Error Message:', error.message);
            console.error('[UserCredits] Error Stack:', error.stack); 
            showError(true, `加载失败: ${error.message}`);
            paginationContainer.innerHTML = ''; 
        } finally {
            showLoading(false);
        }
    }

    // --- 函数：渲染表格 (修改：增加日志并确保 credits 显示) ---
    function renderTable(users) {
        tableBody.innerHTML = ''; // 确保清空

        if (!users || users.length === 0) {
            const message = currentUserCreditsSearchTerm ? `没有找到符合"${escapeHTML(currentUserCreditsSearchTerm)}"的用户。` : '没有找到用户数据。';
            tableBody.innerHTML = `<tr><td colspan="5" class="text-center text-muted">${message}</td></tr>`;
            return;
        }

        users.forEach((user, index) => {

            // 确保 credits 有一个默认值（例如 0）如果它是 null 或 undefined
            const displayCredits = (user.credits === null || user.credits === undefined) ? 0 : user.credits;
            const escapedCredits = escapeHTML(displayCredits); // 对可能为 0 的值进行转义

            const row = tableBody.insertRow();
            row.innerHTML = `
                <th scope="row">${escapeHTML(user.id)}</th>
                <td>${escapeHTML(user.username) || '(无用户名)'}</td>
                <td>${escapeHTML(user.email) || '(无邮箱)'}</td>
                <td><span class="current-credits">${escapedCredits}</span></td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="number" class="form-control new-credits-input" value="${escapedCredits}" min="0" aria-label="新积分" style="max-width: 80px;">
                        <button class="btn btn-outline-primary btn-sm save-credits-btn" type="button" data-user-id="${user.id}">
                            保存
                        </button>
                         <span class="spinner-border spinner-border-sm ms-2 d-none" role="status" aria-hidden="true"></span>
                    </div>
                </td>
            `;

            // 为保存按钮添加事件监听器
            const saveButton = row.querySelector('.save-credits-btn');
            const inputElement = row.querySelector('.new-credits-input');
            const buttonSpinner = row.querySelector('.spinner-border');
            if (saveButton && inputElement) {
                saveButton.addEventListener('click', () => {
                     updateCredits(user.id, inputElement, saveButton, buttonSpinner);
                });
            }
        });
    }

    // --- 新增：函数：渲染分页控件 ---
    function renderPaginationControls() {
        paginationContainer.innerHTML = ''; // 清空现有按钮

        if (userCreditsTotalPages <= 1) return; // 如果只有一页或没有页，则不显示分页

        const fragment = document.createDocumentFragment();

        // 上一页按钮
        const prevLi = document.createElement('li');
        prevLi.classList.add('page-item');
        if (userCreditsCurrentPage === 1) {
            prevLi.classList.add('disabled');
        }
        const prevLink = document.createElement('a');
        prevLink.classList.add('page-link');
        prevLink.href = '#';
        prevLink.textContent = '上一页';
        prevLink.addEventListener('click', (e) => {
            e.preventDefault();
            if (userCreditsCurrentPage > 1) {
                loadUserCredits(userCreditsCurrentPage - 1, currentUserCreditsSearchTerm); // 传递搜索词
            }
        });
        prevLi.appendChild(prevLink);
        fragment.appendChild(prevLi);

        // 页码显示 (简单版：只显示当前页/总页数)
        const pageInfoLi = document.createElement('li');
        pageInfoLi.classList.add('page-item', 'disabled');
        const pageInfoSpan = document.createElement('span');
        pageInfoSpan.classList.add('page-link');
        pageInfoSpan.textContent = `第 ${userCreditsCurrentPage} / ${userCreditsTotalPages} 页`;
        pageInfoLi.appendChild(pageInfoSpan);
        fragment.appendChild(pageInfoLi);

        // 下一页按钮
        const nextLi = document.createElement('li');
        nextLi.classList.add('page-item');
        if (userCreditsCurrentPage === userCreditsTotalPages) {
            nextLi.classList.add('disabled');
        }
        const nextLink = document.createElement('a');
        nextLink.classList.add('page-link');
        nextLink.href = '#';
        nextLink.textContent = '下一页';
        nextLink.addEventListener('click', (e) => {
            e.preventDefault();
            if (userCreditsCurrentPage < userCreditsTotalPages) {
                loadUserCredits(userCreditsCurrentPage + 1, currentUserCreditsSearchTerm); // 传递搜索词
            }
        });
        nextLi.appendChild(nextLink);
        fragment.appendChild(nextLi);
        
        // 添加页码输入和跳转功能
        const pageJumpContainer = document.createElement('li');
        pageJumpContainer.className = 'page-item ms-2 d-flex align-items-center';
        
        // 生成唯一ID，避免多个分页控件ID冲突
        const inputId = `pageJumpInput_userCredits`;
        const btnId = `pageJumpBtn_userCredits`;
        
        pageJumpContainer.innerHTML = `
            <div class="input-group input-group-sm" style="width: 110px;">
                <input type="number" class="form-control" id="${inputId}" 
                    min="1" max="${userCreditsTotalPages}" placeholder="${userCreditsCurrentPage}/${userCreditsTotalPages}" 
                    style="text-align: center; border-radius: 4px 0 0 4px; height: 31px;">
                <button class="btn btn-outline-secondary" type="button" id="${btnId}" 
                    style="border-radius: 0 4px 4px 0; height: 31px;">
                    跳转
                </button>
            </div>
        `;
        fragment.appendChild(pageJumpContainer);

        paginationContainer.appendChild(fragment);
        
        // 添加跳转功能的事件监听
        setTimeout(() => {
            const pageJumpInput = document.getElementById(inputId);
            const pageJumpBtn = document.getElementById(btnId);
            
            if (pageJumpInput && pageJumpBtn) {
                // 设置焦点时清除placeholder
                pageJumpInput.addEventListener('focus', () => {
                    pageJumpInput.placeholder = '';
                });
                
                // 失去焦点时恢复placeholder
                pageJumpInput.addEventListener('blur', () => {
                    if (!pageJumpInput.value) {
                        pageJumpInput.placeholder = `${userCreditsCurrentPage}/${userCreditsTotalPages}`;
                    }
                });
                
                // 输入框回车跳转
                pageJumpInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        jumpToPage();
                    }
                });
                
                // 按钮点击跳转
                pageJumpBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    jumpToPage();
                });
                
                // 跳转逻辑函数
                function jumpToPage() {
                    const pageNum = parseInt(pageJumpInput.value, 10);
                    if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= userCreditsTotalPages) {
                        loadUserCredits(pageNum, currentUserCreditsSearchTerm); // 保持搜索词
                        pageJumpInput.value = ''; // 清空输入
                    } else {
                        // 输入无效，闪烁提示
                        pageJumpInput.classList.add('is-invalid');
                        setTimeout(() => {
                            pageJumpInput.classList.remove('is-invalid');
                        }, 1000);
                    }
                }
            }
        }, 100);
    }

    // --- 函数：更新用户积分 (待实现) ---
    async function updateCredits(userId, inputElement, buttonElement, spinnerElement) {
        const newCreditsValue = inputElement.value.trim();
        
        // 简单的客户端验证
        if (newCreditsValue === '' || isNaN(parseInt(newCreditsValue)) || parseInt(newCreditsValue) < 0) {
            alert('请输入有效的非负积分数值。');
            inputElement.focus();
            return;
        }

        const newCredits = parseInt(newCreditsValue, 10);

        // 禁用按钮并显示加载状态
        buttonElement.disabled = true;
        if (spinnerElement) spinnerElement.classList.remove('d-none'); // 显示 spinner
        let originalButtonText = buttonElement.textContent;
        buttonElement.textContent = '保存中...'; 

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('未找到认证 Token');
            }

            const response = await fetch(`https://caca.yzycolour.top/api/admin/users/${userId}/credits`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({ newCredits: newCredits })
            });

            const result = await response.json(); // 尝试解析响应体

            if (!response.ok) {
                 throw new Error(result.error || `HTTP 错误: ${response.status}`);
            }

            // 更新成功后，直接修改页面上的值，而不是重新加载整个表格
            const creditsSpan = inputElement.closest('tr').querySelector('.current-credits');
            if(creditsSpan) creditsSpan.textContent = newCredits;
            // 也可以把输入框的值也更新一下，虽然用户可能不会再次修改
            inputElement.value = newCredits;
            alert('积分更新成功！');
            
        } catch (error) {
            console.error(`[UserCredits] Failed to update credits for user ${userId}:`, error);
            alert(`积分更新失败: ${error.message}`);
             // 出错时恢复输入框的值为更新前的值 (如果需要)
             // inputElement.value = inputElement.closest('tr').querySelector('.current-credits').textContent;
        } finally {
             // 恢复按钮状态
            buttonElement.disabled = false;
            if (spinnerElement) spinnerElement.classList.add('d-none'); // 隐藏 spinner
            buttonElement.textContent = originalButtonText;
        }
    }
    
    // --- 辅助函数：控制加载和错误显示 ---
    function showLoading(isLoading) {
        if (loadingIndicator) {
            loadingIndicator.style.display = isLoading ? 'block' : 'none';
        }
    }
    function showError(isError, message = '加载用户列表失败，请稍后重试。') {
        if (errorIndicator) {
            errorIndicator.textContent = message;
            errorIndicator.style.display = isError ? 'block' : 'none';
        }
    }
    function escapeHTML(str) {
        if (str === null || str === undefined) return '';
        const div = document.createElement('div');
        div.appendChild(document.createTextNode(String(str)));
        return div.innerHTML;
    }

    // --- 事件监听：点击导航链接加载数据 (修改：重置搜索词) ---
    if (navLink) {

        const loadInitialData = () => {
            currentUserCreditsSearchTerm = ''; // 重置搜索词
            searchInput.value = ''; // 清空输入框
            loadUserCredits(1, ''); // 加载第一页，无搜索词
        };

        // 使用 'shown.bs.pill' 确保在 Tab 内容显示后加载
        navLink.addEventListener('shown.bs.pill', loadInitialData);

        // 保留 click 监听器作为备用或补充，但主要依赖 shown.bs.pill
        navLink.addEventListener('click', loadInitialData);

    } else {
        console.error('[UserCredits] Navigation link for user credits not found.');
    }

    // --- 新增：搜索事件监听器 ---
    function triggerSearch() {
        const searchTerm = searchInput.value.trim();
        loadUserCredits(1, searchTerm); // 搜索总是从第一页开始
    }

    searchButton.addEventListener('click', triggerSearch);

    searchInput.addEventListener('keypress', (event) => {
        if (event.key === 'Enter') {
            event.preventDefault(); // 阻止可能的表单提交
            triggerSearch();
        }
    });

}); 