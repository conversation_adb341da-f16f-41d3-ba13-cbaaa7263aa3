<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件介绍 - 提示词工具箱</title>
    <!-- Local Bootstrap CSS -->
    <link href="libs/css/bootstrap.min.css" rel="stylesheet">
    <!-- Local Bootstrap Icons CSS -->
    <link href="libs/css/bootstrap-icons.min.css" rel="stylesheet">
    <link href="css/style.css?=1.45" rel="stylesheet">
    <style>
        /* Define purple color variables */
        :root {
            --bs-primary: #6f42c1; /* Bootstrap Purple */
            --bs-primary-rgb: 111, 66, 193;
            --bs-link-color: #8a63d2; /* Lighter purple for links */
            --bs-link-hover-color: #5a32a3; /* Darker purple for link hover */
        }

        body {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        .plugin-hero {
            text-align: center;
            padding: 4rem 1rem;
            background-color: rgba(var(--bs-emphasis-color-rgb), 0.05);
            border-radius: .5rem;
            margin-bottom: 2rem;
        }
        .plugin-hero h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        .plugin-hero p {
            font-size: 1.25rem;
            color: var(--bs-secondary-color);
        }
        .feature-icon {
            font-size: 2rem;
            color: var(--bs-primary); /* Uses the overridden primary color */
        }
        .step-indicator {
            background-color: var(--bs-primary); /* Uses the overridden primary color */
            color: var(--bs-light);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        /* Ensure button uses the overridden primary color */
        .btn-primary {
            --bs-btn-color: var(--bs-white);
            --bs-btn-bg: var(--bs-primary);
            --bs-btn-border-color: var(--bs-primary);
            --bs-btn-hover-color: var(--bs-white);
            --bs-btn-hover-bg: #5a32a3; /* Slightly darker purple for hover */
            --bs-btn-hover-border-color: #522e93;
            --bs-btn-focus-shadow-rgb: var(--bs-primary-rgb);
            --bs-btn-active-color: var(--bs-btn-hover-color);
            --bs-btn-active-bg: var(--bs-btn-hover-bg);
            --bs-btn-active-border-color: var(--bs-btn-hover-border-color);
        }

        /* Removed custom alert-info styles as it's now a card */

        /* Add spacing to list items in optimization tab */
        #optimization-tab-pane ol li {
            margin-bottom: 0.5rem; /* Adjust value as needed */
        }
        #optimization-tab-pane ol li:last-child {
            margin-bottom: 0; /* Remove margin from the last item */
        }

    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4 border-bottom pb-3">
            <h1 class="h4 mb-0">提示词工具箱 - 像素星云上传助手介绍</h1>
            <a href="index.html" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> 返回主页
            </a>
        </div>

        <div class="plugin-hero">
            <h1>像素星云上传助手</h1>
            <p class="lead">快速收藏网页上的图片，一键将其发送到你的提示词库。</p>
            <!-- Updated with the backend download link -->
            <!-- Pointing href to the absolute backend proxy URL -->
            <a href="https://caca.yzycolour.top/api/plugin/download" target="_blank" rel="noopener noreferrer" class="btn btn-primary btn-lg mt-3">
                <i class="bi bi-download me-2"></i> 立即安装插件
            </a>
            <!-- Added element for download statistics -->
            <p id="download-stats" class="mt-3 text-muted">正在获取下载信息...</p>
        </div>

        <div class="row g-4 mb-5">
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="feature-icon mb-3"><i class="bi bi-images"></i></div>
                        <h5 class="card-title">网页图片提取</h5>
                        <p class="card-text">自动检测并提取当前网页上的图片，方便快速选择收藏。</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="feature-icon mb-3"><i class="bi bi-magic"></i></div>
                        <h5 class="card-title">智能识别</h5>
                        <p class="card-text">尝试识别页面主要图片和相关信息（如提示词），简化添加流程。</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="feature-icon mb-3"><i class="bi bi-send-fill"></i></div>
                        <h5 class="card-title">一键发送平台</h5>
                        <p class="card-text">将选中的图片及识别出的信息一键发送到提示词管理平台。</p>
                    </div>
                </div>
            </div>
        </div>

        <div>

        </div>

        <!-- Tab Navigation for Usage, Optimization and Installation -->
        <ul class="nav nav-tabs justify-content-center mt-5 mb-4" id="pluginDetailsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="usage-tab" data-bs-toggle="tab" data-bs-target="#usage-tab-pane" type="button" role="tab" aria-controls="usage-tab-pane" aria-selected="true">
                    <i class="bi bi-card-list me-1"></i> 通用使用方法
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="optimization-tab" data-bs-toggle="tab" data-bs-target="#optimization-tab-pane" type="button" role="tab" aria-controls="optimization-tab-pane" aria-selected="false">
                    <i class="bi bi-gear-fill me-1"></i> 特定网站优化
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="installation-tab" data-bs-toggle="tab" data-bs-target="#installation-tab-pane" type="button" role="tab" aria-controls="installation-tab-pane" aria-selected="false">
                    <i class="bi bi-box-arrow-in-down me-1"></i> 安装教程
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="pluginDetailsTabContent">
            <!-- Usage Tab Pane -->
            <div class="tab-pane fade show active" id="usage-tab-pane" role="tabpanel" aria-labelledby="usage-tab" tabindex="0">
                <h3 class="text-center mb-3">通用使用方法</h3>
                <div class="row justify-content-center g-3">
                    <div class="col-md-8">
                        <div class="card mb-3">
                            <div class="card-body d-flex align-items-center">
                                <span class="step-indicator">1</span>
                                <div>安装插件到你的浏览器。</div>
                            </div>
                        </div>
                        <div class="card mb-3">
                            <div class="card-body d-flex align-items-center">
                                <span class="step-indicator">2</span>
                                <div>访问你想要收藏图片的网页。</div>
                            </div>
                        </div>
                        <div class="card mb-3">
                             <div class="card-body d-flex align-items-center">
                                <span class="step-indicator">3</span>
                                <div>点击浏览器工具栏中的插件图标。</div>
                            </div>
                        </div>
                         <div class="card mb-3">
                             <div class="card-body d-flex align-items-center">
                                <span class="step-indicator">4</span>
                                <div>在弹出的窗口中，插件会列出检测到的图片。选择你需要的图片，然后点击发送按钮，即可将其添加到平台。</div>
                                <!-- 建议在此处添加截图 -->
                                <!-- <img src="images/plugin-popup-screenshot.png" class="img-fluid mt-3 rounded mb-2" alt="插件弹出窗口截图"> -->
                            </div>
                        </div>
                    </div>
                 </div>
            </div>

            <!-- Optimization Tab Pane -->
            <div class="tab-pane fade" id="optimization-tab-pane" role="tabpanel" aria-labelledby="optimization-tab" tabindex="0">
                <h3 class="text-center mb-3">即梦AI (Jimeng)</h3>
                <div class="row justify-content-center g-3">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-body d-flex align-items-start">
                                <i class="bi bi-gear-fill flex-shrink-0 me-3 fs-4 text-primary"></i>
                                <div>
                                    <h6 class="card-title mb-1">即梦AI 作品详情页优化</h6>
                                    插件对该页面的优化可以更便捷地抓取图片和提示词：
                                    <ol class="mb-0 mt-2 ps-3">
                                        <li>访问 <b>即梦AI</b> 的作品详情页面</li>
                                        <li>页面加载完成后，在页面右侧或角落寻找并点击 <span class="badge bg-primary">✨ 添加到平台</span> 悬浮按钮。</li>
                                        <li>插件会自动抓取该作品的主图片和对应的提示词，并打开添加页面（或直接发送，取决于插件设置）。</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Installation Tab Pane -->
            <div class="tab-pane fade" id="installation-tab-pane" role="tabpanel" aria-labelledby="installation-tab" tabindex="0">
                 <h3 class="text-center mb-3">如何安装插件？</h3>
                 <div class="row justify-content-center g-3">
                    <div class="col-md-8">
                        <div class="card mb-3">
                            <div class="card-body d-flex align-items-center">
                                <span class="step-indicator">1</span>
                                <div><b>下载插件文件：</b> 点击上方的 "立即安装插件" 按钮（如果链接指向 ZIP 文件）或通过其他方式获取插件的 `.zip` 压缩包。</div>
                            </div>
                        </div>
                        <div class="card mb-3">
                            <div class="card-body d-flex align-items-center">
                                <span class="step-indicator">2</span>
                                <div><b>解压文件：</b> 将下载的 `.zip` 文件解压到一个你方便找到的文件夹中。解压后会得到一个包含插件所有文件的文件夹。</div>
                            </div>
                        </div>
                         <div class="card mb-3">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="step-indicator">3</span>
                                    <div><b>打开浏览器扩展页面：</b></div>
                                </div>
                                <ul class="list-unstyled ps-4">
                                    <li><b>Chrome:</b> 在地址栏输入 `chrome://extensions` 并回车。</li>
                                    <li><b>Edge:</b> 在地址栏输入 `edge://extensions` 并回车。</li>
                                    <li>(其他 Chromium 内核浏览器类似)</li>
                                </ul>
                            </div>
                        </div>
                         <div class="card mb-3">
                            <div class="card-body d-flex align-items-center">
                                <span class="step-indicator">4</span>
                                <div><b>启用开发者模式：</b> 在扩展页面右上角，找到并打开 "开发者模式" (Developer mode) 的开关。</div>
                            </div>
                        </div>
                         <div class="card mb-3">
                            <div class="card-body d-flex align-items-center">
                                <span class="step-indicator">5</span>
                                <div><b>加载已解压的扩展程序：</b> 点击页面左上方出现的 "加载已解压的扩展程序" (Load unpacked) 按钮。</div>
                            </div>
                        </div>
                         <div class="card mb-3">
                            <div class="card-body d-flex align-items-center">
                                <span class="step-indicator">6</span>
                                <div><b>选择插件文件夹：</b> 在弹出的文件选择窗口中，找到并选择你**步骤 2** 中解压出来的那个**文件夹**，然后确认。</div>
                            </div>
                        </div>
                         <div class="card mb-3 bg-success-subtle border-success">
                             <div class="card-body d-flex align-items-center">
                                 <i class="bi bi-check-circle-fill flex-shrink-0 me-3 fs-4 text-success"></i>
                                <div>安装成功！现在你应该能在浏览器工具栏看到插件的图标了。</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <footer class="text-center text-muted mt-5 pt-4 border-top">
            <p>&copy; 2024 提示词工具箱. All Rights Reserved.</p>
        </footer>

    </div>

    <!-- Local Bootstrap JS Bundle -->
    <script src="libs/js/bootstrap.bundle.min.js"></script>

    <!-- Added script for download statistics -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const statsElement = document.getElementById('download-stats');
            const apiBaseUrl = 'https://caca.yzycolour.top'; // Or use your specific backend URL if different

            fetch(`${apiBaseUrl}/api/plugin/stats`) // Assuming backend runs on the same host/port
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (statsElement && typeof data.downloadCount !== 'undefined') {
                        // You can customize the message here
                        statsElement.textContent = `已有 ${data.downloadCount} 人下载！快加入我们吧！🚀`;
                         // Optional: Add a subtle animation or style change
                         statsElement.style.opacity = '0';
                         statsElement.style.transition = 'opacity 0.5s ease-in-out';
                         setTimeout(() => { statsElement.style.opacity = '1'; }, 100); // Fade in effect
                    } else {
                        statsElement.textContent = '无法获取下载统计。'; // Fallback message
                    }
                })
                .catch(error => {
                    console.error('获取插件统计失败:', error);
                    if (statsElement) {
                        statsElement.textContent = '获取统计信息时出错。'; // Error message
                    }
                });
        });
    </script>
</body>
</html> 