# 像素星云AI助手 - Vue版本

这是基于原始Bootstrap + jQuery项目改造的Vue 3 + Tailwind CSS现代化版本。

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

## 🔐 测试登录

项目包含了测试登录功能，您可以使用以下账号进行测试：

- **用户名**: `admin`
- **密码**: `admin123`

## 🎨 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 类型安全的JavaScript
- **Tailwind CSS** - 原子化CSS框架
- **Pinia** - Vue状态管理
- **Vue Router** - 官方路由管理器
- **Vite** - 现代化构建工具

## ✨ 主要特性

### 已实现功能
- ✅ 用户认证系统（登录/登出）
- ✅ 案例管理（浏览、搜索、筛选）
- ✅ 提示词反推功能
- ✅ 响应式设计
- ✅ 暗色主题
- ✅ 组件化架构

### 开发中功能
- 🚧 AI图片生成
- 🚧 管理中心
- 🚧 其他AI工具功能
