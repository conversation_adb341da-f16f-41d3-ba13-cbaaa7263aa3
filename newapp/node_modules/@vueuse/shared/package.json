{"name": "@vueuse/shared", "type": "module", "version": "13.5.0", "author": "<PERSON> <https://github.com/antfu>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/vueuse/vueuse/tree/main/packages/shared#readme", "repository": {"type": "git", "url": "git+https://github.com/vueuse/vueuse.git", "directory": "packages/shared"}, "bugs": {"url": "https://github.com/vueuse/vueuse/issues"}, "keywords": ["vue", "vue-use", "utils"], "sideEffects": false, "exports": {".": "./index.mjs", "./*": "./*"}, "main": "./index.mjs", "module": "./index.mjs", "unpkg": "./index.iife.min.js", "jsdelivr": "./index.iife.min.js", "types": "./index.d.mts", "files": ["*.d.mts", "*.js", "*.mjs"], "peerDependencies": {"vue": "^3.5.0"}, "scripts": {"build": "rollup --config=rollup.config.ts --configPlugin=rollup-plugin-esbuild", "test:attw": "attw --pack --config-path ../../.attw.json ."}}