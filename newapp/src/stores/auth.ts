import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, ApiResponse } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isLoading = ref(false)

  // Getters
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const userDisplayName = computed(() => user.value?.nickname || user.value?.username || '')
  const userAvatar = computed(() => user.value?.avatar_url || '/images/default-avatar.png')

  // Actions
  const initAuth = () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    
    if (savedToken && savedUser) {
      try {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('Failed to parse saved user data:', error)
        clearAuth()
      }
    }
  }

  const login = async (identifier: string, password: string, rememberMe = false) => {
    isLoading.value = true
    try {
      const response = await fetch('https://caca.yzycolour.top/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ identifier, password }),
      })

      const data: ApiResponse<{ user: User; token: string }> = await response.json()

      if (data.success && data.data) {
        token.value = data.data.token
        user.value = data.data.user
        
        localStorage.setItem('token', data.data.token)
        localStorage.setItem('user', JSON.stringify(data.data.user))
        
        if (rememberMe) {
          localStorage.setItem('identifier', identifier)
          localStorage.setItem('rememberMe', 'true')
        } else {
          localStorage.removeItem('identifier')
          localStorage.removeItem('rememberMe')
        }
        
        return { success: true }
      } else {
        return { success: false, message: data.message || '登录失败' }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, message: '网络错误，请稍后重试' }
    } finally {
      isLoading.value = false
    }
  }

  const logout = () => {
    token.value = null
    user.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  const clearAuth = () => {
    logout()
  }

  const updateUser = (userData: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      localStorage.setItem('user', JSON.stringify(user.value))
    }
  }

  const getAuthHeaders = () => {
    return {
      'Authorization': `Bearer ${token.value}`,
      'Content-Type': 'application/json'
    }
  }

  return {
    // State
    user,
    token,
    isLoading,
    
    // Getters
    isAuthenticated,
    isAdmin,
    userDisplayName,
    userAvatar,
    
    // Actions
    initAuth,
    login,
    logout,
    clearAuth,
    updateUser,
    getAuthHeaders,
  }
})
