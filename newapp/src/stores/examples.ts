import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Example, Category, Tag, PaginatedResponse, ApiResponse } from '@/types'
import { useAuthStore } from './auth'

export const useExamplesStore = defineStore('examples', () => {
  // State
  const examples = ref<Example[]>([])
  const categories = ref<Category[]>([])
  const tags = ref<Tag[]>([])
  const isLoading = ref(false)
  const currentPage = ref(1)
  const totalPages = ref(1)
  const hasMore = ref(true)
  
  // Filters
  const searchQuery = ref('')
  const selectedCategory = ref('')
  const selectedModel = ref('')
  const sortBy = ref('date_desc')

  // Getters
  const filteredExamples = computed(() => {
    let filtered = examples.value

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(example => 
        example.title.toLowerCase().includes(query) ||
        example.prompt.toLowerCase().includes(query) ||
        example.tags.some(tag => tag.name.toLowerCase().includes(query))
      )
    }

    if (selectedCategory.value) {
      filtered = filtered.filter(example => 
        example.category?.slug === selectedCategory.value
      )
    }

    if (selectedModel.value) {
      filtered = filtered.filter(example => 
        example.model === selectedModel.value
      )
    }

    return filtered
  })

  // Actions
  const fetchExamples = async (page = 1, reset = false) => {
    if (isLoading.value) return

    isLoading.value = true
    try {
      const authStore = useAuthStore()
      const params = new URLSearchParams({
        page: page.toString(),
        per_page: '12',
        search: searchQuery.value,
        category: selectedCategory.value,
        model: selectedModel.value,
        sort: sortBy.value,
      })

      const response = await fetch(`https://caca.yzycolour.top/api/examples?${params}`, {
        headers: authStore.getAuthHeaders(),
      })

      const data: PaginatedResponse<Example> = await response.json()

      if (data.success) {
        if (reset || page === 1) {
          examples.value = data.data
        } else {
          examples.value.push(...data.data)
        }
        
        currentPage.value = data.pagination.current_page
        totalPages.value = data.pagination.total_pages
        hasMore.value = currentPage.value < totalPages.value
      }
    } catch (error) {
      console.error('Failed to fetch examples:', error)
    } finally {
      isLoading.value = false
    }
  }

  const fetchCategories = async () => {
    try {
      const authStore = useAuthStore()
      const response = await fetch('https://caca.yzycolour.top/api/categories', {
        headers: authStore.getAuthHeaders(),
      })

      const data: ApiResponse<Category[]> = await response.json()
      if (data.success && data.data) {
        categories.value = data.data
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error)
    }
  }

  const fetchTags = async () => {
    try {
      const authStore = useAuthStore()
      const response = await fetch('https://caca.yzycolour.top/api/tags', {
        headers: authStore.getAuthHeaders(),
      })

      const data: ApiResponse<Tag[]> = await response.json()
      if (data.success && data.data) {
        tags.value = data.data
      }
    } catch (error) {
      console.error('Failed to fetch tags:', error)
    }
  }

  const likeExample = async (exampleId: number) => {
    try {
      const authStore = useAuthStore()
      const response = await fetch(`https://caca.yzycolour.top/api/examples/${exampleId}/like`, {
        method: 'POST',
        headers: authStore.getAuthHeaders(),
      })

      const data: ApiResponse = await response.json()
      if (data.success) {
        const example = examples.value.find(e => e.id === exampleId)
        if (example) {
          example.is_liked = !example.is_liked
          example.likes_count += example.is_liked ? 1 : -1
        }
      }
    } catch (error) {
      console.error('Failed to like example:', error)
    }
  }

  const deleteExample = async (exampleId: number) => {
    try {
      const authStore = useAuthStore()
      const response = await fetch(`https://caca.yzycolour.top/api/examples/${exampleId}`, {
        method: 'DELETE',
        headers: authStore.getAuthHeaders(),
      })

      const data: ApiResponse = await response.json()
      if (data.success) {
        examples.value = examples.value.filter(e => e.id !== exampleId)
        return { success: true }
      } else {
        return { success: false, message: data.message }
      }
    } catch (error) {
      console.error('Failed to delete example:', error)
      return { success: false, message: '删除失败' }
    }
  }

  const setFilters = (filters: {
    search?: string
    category?: string
    model?: string
    sort?: string
  }) => {
    if (filters.search !== undefined) searchQuery.value = filters.search
    if (filters.category !== undefined) selectedCategory.value = filters.category
    if (filters.model !== undefined) selectedModel.value = filters.model
    if (filters.sort !== undefined) sortBy.value = filters.sort
  }

  const resetFilters = () => {
    searchQuery.value = ''
    selectedCategory.value = ''
    selectedModel.value = ''
    sortBy.value = 'date_desc'
  }

  const loadMore = () => {
    if (hasMore.value && !isLoading.value) {
      fetchExamples(currentPage.value + 1)
    }
  }

  return {
    // State
    examples,
    categories,
    tags,
    isLoading,
    currentPage,
    totalPages,
    hasMore,
    searchQuery,
    selectedCategory,
    selectedModel,
    sortBy,
    
    // Getters
    filteredExamples,
    
    // Actions
    fetchExamples,
    fetchCategories,
    fetchTags,
    likeExample,
    deleteExample,
    setFilters,
    resetFilters,
    loadMore,
  }
})
