import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useUIStore = defineStore('ui', () => {
  // State
  const activeTab = ref('management')
  const sidebarCollapsed = ref(false)
  const notifications = ref<any[]>([])
  const toasts = ref<any[]>([])
  const modals = ref<Record<string, boolean>>({})

  // Mouse effect
  const mousePosition = ref({ x: 0, y: 0 })
  const showMouseEffect = ref(false)

  // Actions
  const setActiveTab = (tab: string) => {
    activeTab.value = tab
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  const showModal = (modalId: string) => {
    modals.value[modalId] = true
  }

  const hideModal = (modalId: string) => {
    modals.value[modalId] = false
  }

  const isModalVisible = (modalId: string) => {
    return modals.value[modalId] || false
  }

  const showToast = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info', duration = 3000) => {
    const id = Date.now().toString()
    const toast = {
      id,
      message,
      type,
      duration,
    }
    
    toasts.value.push(toast)
    
    setTimeout(() => {
      hideToast(id)
    }, duration)
    
    return id
  }

  const hideToast = (id: string) => {
    const index = toasts.value.findIndex(toast => toast.id === id)
    if (index > -1) {
      toasts.value.splice(index, 1)
    }
  }

  const updateMousePosition = (x: number, y: number) => {
    mousePosition.value = { x, y }
    document.documentElement.style.setProperty('--mouse-x', `${x}px`)
    document.documentElement.style.setProperty('--mouse-y', `${y}px`)
  }

  const enableMouseEffect = () => {
    showMouseEffect.value = true
    const gridMask = document.querySelector('.grid-mask') as HTMLElement
    if (gridMask) {
      gridMask.style.opacity = '1'
    }
  }

  const disableMouseEffect = () => {
    showMouseEffect.value = false
    const gridMask = document.querySelector('.grid-mask') as HTMLElement
    if (gridMask) {
      gridMask.style.opacity = '0'
    }
  }

  const addNotification = (notification: any) => {
    notifications.value.unshift(notification)
  }

  const markNotificationAsRead = (id: number) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.is_read = true
    }
  }

  const clearNotifications = () => {
    notifications.value = []
  }

  return {
    // State
    activeTab,
    sidebarCollapsed,
    notifications,
    toasts,
    modals,
    mousePosition,
    showMouseEffect,
    
    // Actions
    setActiveTab,
    toggleSidebar,
    showModal,
    hideModal,
    isModalVisible,
    showToast,
    hideToast,
    updateMousePosition,
    enableMouseEffect,
    disableMouseEffect,
    addNotification,
    markNotificationAsRead,
    clearNotifications,
  }
})
