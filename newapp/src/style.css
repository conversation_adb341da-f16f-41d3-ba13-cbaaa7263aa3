@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --gradient-border-anim: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
  --gradient-border-static: linear-gradient(90deg, #6c757d, #adb5bd, #6c757d);
  --gradient-bg: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
  --checkerboard-color1: rgba(255, 255, 255, 0.03);
  --checkerboard-size-sm: 80px;
  --checkerboard-size-lg: 200px;
  --card-bg-color: rgba(20, 20, 20, 0.7);
  --modal-bg-color: rgba(30, 30, 30, 0.8);
  --blur-intensity: 15px;
}

@layer base {
  html, body {
    margin: 0;
    padding: 0;
    width: 100%;
    min-height: 100vh;
    background-color: #0a0a0a;
    color: #e9ecef;
    overflow-x: hidden;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    scroll-behavior: smooth;
  }

  body {
    position: relative;
    background-color: #0a0a0a !important;
    color: #e9ecef;
    overflow-y: overlay;
    padding-top: 20px;
  }

  /* Background grid patterns */
  body::before {
    content: '';
    position: fixed;
    inset: 0;
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.2) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.2) 1px, transparent 1px);
    background-size: var(--checkerboard-size-sm) var(--checkerboard-size-sm);
    z-index: 0;
    opacity: 0.3;
    pointer-events: none;
  }

  body::after {
    content: '';
    position: fixed;
    inset: 0;
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.15) 2px, transparent 2px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.15) 2px, transparent 2px);
    background-size: var(--checkerboard-size-lg) var(--checkerboard-size-lg);
    z-index: 0;
    opacity: 0.3;
    pointer-events: none;
  }
}

@layer components {
  .grid-mask {
    position: fixed;
    inset: 0;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    background: radial-gradient(
      600px circle at var(--mouse-x) var(--mouse-y),
      rgba(255, 255, 255, 0.08),
      transparent 40%
    );
  }

  .card-glass {
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    background: var(--card-bg-color);
  }

  .btn-primary {
    background-color: #2563eb;
    color: white;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
  }

  .btn-primary:hover {
    background-color: #1d4ed8;
  }

  .btn-secondary {
    background-color: #4b5563;
    color: white;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
  }

  .btn-secondary:hover {
    background-color: #374151;
  }

  .btn-outline {
    border: 1px solid #4b5563;
    color: #d1d5db;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    background: transparent;
    cursor: pointer;
  }

  .btn-outline:hover {
    border-color: #6b7280;
    color: white;
  }

  .loading-dots {
    display: flex;
    gap: 0.25rem;
  }

  .loading-dots span {
    width: 0.5rem;
    height: 0.5rem;
    background-color: #3b82f6;
    border-radius: 50%;
    animation: pulse 1.5s ease-in-out infinite;
    animation-delay: calc(var(--i) * 0.2s);
  }
}

@layer utilities {
  .fade-in {
    animation: fadeIn 0.5s 0.2s ease-in forwards;
    opacity: 0;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
}
