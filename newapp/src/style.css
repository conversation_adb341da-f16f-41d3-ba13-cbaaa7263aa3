@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --gradient-border-anim: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
  --gradient-border-static: linear-gradient(90deg, #6c757d, #adb5bd, #6c757d);
  --gradient-bg: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
  --checkerboard-color1: rgba(255, 255, 255, 0.03);
  --checkerboard-size-sm: 80px;
  --checkerboard-size-lg: 200px;
  --card-bg-color: rgba(20, 20, 20, 0.7);
  --modal-bg-color: rgba(30, 30, 30, 0.8);
  --blur-intensity: 15px;
}

@layer base {
  html, body {
    @apply m-0 p-0 w-full min-h-screen bg-gray-950 text-gray-100 overflow-x-hidden;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply relative;
    background-color: #0a0a0a !important;
    color: #e9ecef;
    overflow-y: overlay;
    padding-top: 20px;
  }

  /* Background grid patterns */
  body::before {
    content: '';
    position: fixed;
    inset: 0;
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.2) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.2) 1px, transparent 1px);
    background-size: var(--checkerboard-size-sm) var(--checkerboard-size-sm);
    z-index: 0;
    opacity: 0.3;
    pointer-events: none;
  }

  body::after {
    content: '';
    position: fixed;
    inset: 0;
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.15) 2px, transparent 2px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.15) 2px, transparent 2px);
    background-size: var(--checkerboard-size-lg) var(--checkerboard-size-lg);
    z-index: 0;
    opacity: 0.3;
    pointer-events: none;
  }
}

@layer components {
  .grid-mask {
    @apply fixed inset-0 pointer-events-none opacity-0 transition-opacity duration-300;
    z-index: -1;
    background: radial-gradient(
      600px circle at var(--mouse-x) var(--mouse-y),
      rgba(255, 255, 255, 0.08),
      transparent 40%
    );
  }

  .card-glass {
    @apply backdrop-blur-md border border-white/10 rounded-lg;
    background: var(--card-bg-color);
  }

  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-outline {
    @apply border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .loading-dots {
    @apply flex space-x-1;
  }

  .loading-dots span {
    @apply w-2 h-2 bg-blue-500 rounded-full animate-pulse;
    animation-delay: calc(var(--i) * 0.2s);
  }
}

@layer utilities {
  .fade-in {
    animation: fadeIn 0.5s 0.2s ease-in forwards;
    opacity: 0;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
}
