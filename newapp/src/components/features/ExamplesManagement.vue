<template>
  <div class="space-y-6">
    <!-- Filters Card -->
    <div class="card-glass p-6 rounded-lg">
      <h3 class="text-lg font-semibold text-white mb-4">筛选器</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Category Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">分类筛选</label>
          <select
            v-model="selectedCategory"
            @change="handleFilterChange"
            class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部分类</option>
            <option
              v-for="category in categories"
              :key="category.id"
              :value="category.slug"
            >
              {{ category.name }}
            </option>
          </select>
        </div>

        <!-- Model Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">模型筛选</label>
          <select
            v-model="selectedModel"
            @change="handleFilterChange"
            class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部模型</option>
            <option value="MJ">MJ</option>
            <option value="FLUX">FLUX</option>
            <option value="FLUX-多图">FLUX-多图</option>
            <option value="SDXL">SDXL</option>
            <option value="超级生图">超级生图</option>
            <option value="高级生图">高级生图</option>
            <option value="GPT4O">GPT4O</option>
            <option value="GPT4O-编辑">GPT4O-编辑</option>
            <option value="其他">其他</option>
          </select>
        </div>

        <!-- Sort Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">排序方式</label>
          <select
            v-model="sortBy"
            @change="handleFilterChange"
            class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="date_desc">最新发布</option>
            <option value="likes_desc">最多点赞</option>
          </select>
        </div>
      </div>

      <!-- Search -->
      <div class="mt-4">
        <label class="block text-sm font-medium text-gray-300 mb-2">搜索</label>
        <div class="relative">
          <input
            v-model="searchQuery"
            @input="handleSearchInput"
            type="text"
            placeholder="输入标题、提示词或标签进行搜索"
            class="w-full px-3 py-2 pr-10 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Examples Grid -->
    <div class="examples-container">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <span class="text-gray-400">加载案例中...</span>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else-if="false" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <span class="text-gray-400">没有找到符合条件的案例。</span>
      </div>

      <!-- Examples Grid -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <!-- Mock Example Cards -->
        <div
          v-for="i in 8"
          :key="i"
          class="card-glass rounded-lg overflow-hidden hover:scale-105 transition-transform duration-200"
        >
          <div class="aspect-square bg-gray-800 flex items-center justify-center">
            <span class="text-gray-400">示例图片 {{ i }}</span>
          </div>
          <div class="p-4">
            <h3 class="text-white font-medium mb-2">示例案例 {{ i }}</h3>
            <p class="text-gray-400 text-sm mb-3">这是一个示例案例的描述...</p>
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span>2小时前</span>
              <span>❤️ {{ i * 3 }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Load More -->
      <div class="text-center mt-8">
        <button
          @click="loadMore"
          :disabled="isLoading"
          class="btn-primary disabled:opacity-50"
        >
          <span v-if="isLoading" class="flex items-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            加载中...
          </span>
          <span v-else>加载更多</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Mock data for demonstration
const selectedCategory = ref('')
const selectedModel = ref('')
const sortBy = ref('date_desc')
const searchQuery = ref('')
const isLoading = ref(false)

const categories = ref([
  { id: 1, name: '人物', slug: 'people' },
  { id: 2, name: '风景', slug: 'landscape' },
  { id: 3, name: '动物', slug: 'animals' },
])

const handleFilterChange = () => {
  console.log('Filter changed')
}

const handleSearchInput = () => {
  console.log('Search input changed')
}

const loadMore = () => {
  console.log('Load more clicked')
}
</script>
