<template>
  <div class="space-y-6">
    <!-- Filters Card -->
    <div class="card-glass p-6 rounded-lg">
      <h3 class="text-lg font-semibold text-white mb-4">筛选器</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Category Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">分类筛选</label>
          <select
            v-model="examplesStore.selectedCategory"
            @change="handleFilterChange"
            class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部分类</option>
            <option
              v-for="category in examplesStore.categories"
              :key="category.id"
              :value="category.slug"
            >
              {{ category.name }}
            </option>
          </select>
        </div>

        <!-- Model Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">模型筛选</label>
          <select
            v-model="examplesStore.selectedModel"
            @change="handleFilterChange"
            class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部模型</option>
            <option value="MJ">MJ</option>
            <option value="FLUX">FLUX</option>
            <option value="FLUX-多图">FLUX-多图</option>
            <option value="SDXL">SDXL</option>
            <option value="超级生图">超级生图</option>
            <option value="高级生图">高级生图</option>
            <option value="GPT4O">GPT4O</option>
            <option value="GPT4O-编辑">GPT4O-编辑</option>
            <option value="其他">其他</option>
          </select>
        </div>

        <!-- Sort Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">排序方式</label>
          <select
            v-model="examplesStore.sortBy"
            @change="handleFilterChange"
            class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="date_desc">最新发布</option>
            <option value="likes_desc">最多点赞</option>
          </select>
        </div>
      </div>

      <!-- Search -->
      <div class="mt-4">
        <label class="block text-sm font-medium text-gray-300 mb-2">搜索</label>
        <div class="relative">
          <input
            v-model="examplesStore.searchQuery"
            @input="handleSearchInput"
            type="text"
            placeholder="输入标题、提示词或标签进行搜索"
            class="w-full px-3 py-2 pr-10 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Examples Grid -->
    <div class="examples-container">
      <!-- Loading State -->
      <div v-if="examplesStore.isLoading && examplesStore.examples.length === 0" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <span class="text-gray-400">加载案例中...</span>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else-if="examplesStore.filteredExamples.length === 0" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <span class="text-gray-400">没有找到符合条件的案例。</span>
      </div>

      <!-- Examples Grid -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <ExampleCard
          v-for="example in examplesStore.filteredExamples"
          :key="example.id"
          :example="example"
          @like="handleLike"
          @delete="handleDelete"
          @view="handleView"
        />
      </div>

      <!-- Load More -->
      <div v-if="examplesStore.hasMore" class="text-center mt-8">
        <button
          @click="loadMore"
          :disabled="examplesStore.isLoading"
          class="btn-primary disabled:opacity-50"
        >
          <span v-if="examplesStore.isLoading" class="flex items-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            加载中...
          </span>
          <span v-else>加载更多</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useExamplesStore } from '@/stores/examples'
import { useUIStore } from '@/stores/ui'
import { debounce } from '@/utils'
import ExampleCard from '../ui/ExampleCard.vue'

const examplesStore = useExamplesStore()
const uiStore = useUIStore()

const handleFilterChange = () => {
  examplesStore.fetchExamples(1, true)
}

const handleSearchInput = debounce(() => {
  examplesStore.fetchExamples(1, true)
}, 500)

const handleLike = async (exampleId: number) => {
  await examplesStore.likeExample(exampleId)
}

const handleDelete = async (exampleId: number) => {
  const result = await examplesStore.deleteExample(exampleId)
  if (result.success) {
    uiStore.showToast('案例删除成功', 'success')
  } else {
    uiStore.showToast(result.message || '删除失败', 'error')
  }
}

const handleView = (example: any) => {
  // TODO: Show example detail modal
  uiStore.showModal('exampleDetail')
}

const loadMore = () => {
  examplesStore.loadMore()
}

onMounted(async () => {
  await Promise.all([
    examplesStore.fetchCategories(),
    examplesStore.fetchTags(),
    examplesStore.fetchExamples(1, true)
  ])
})
</script>
