<template>
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Left Panel - Upload and Settings -->
    <div class="card-glass p-6 rounded-lg">
      <h3 class="text-lg font-semibold text-white mb-4">上传图片并选择模型</h3>
      
      <!-- Image Upload Area -->
      <div
        @drop="handleDrop"
        @dragover.prevent
        @dragenter.prevent
        class="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center hover:border-gray-500 transition-colors cursor-pointer"
        @click="triggerFileInput"
      >
        <div v-if="!selectedImage">
          <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          <p class="text-gray-400">将图片拖拽到此处，或点击选择文件</p>
          <small class="text-gray-500 block mt-2">支持粘贴上传 (Ctrl+V)</small>
        </div>
        
        <div v-else class="space-y-4">
          <img :src="selectedImage" alt="Preview" class="max-w-full max-h-64 mx-auto rounded" />
          <button
            @click.stop="removeImage"
            class="btn-outline text-sm"
          >
            移除图片
          </button>
        </div>
      </div>
      
      <input
        ref="fileInput"
        type="file"
        accept="image/*"
        class="hidden"
        @change="handleFileSelect"
      />

      <!-- Model Selection -->
      <div class="mt-6">
        <label class="block text-sm font-medium text-gray-300 mb-2">选择模型</label>
        <select
          v-model="selectedModel"
          class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">选择模型</option>
          <option value="gpt-4o">GPT-4o</option>
          <option value="claude">Claude</option>
          <option value="gemini">Gemini</option>
        </select>
      </div>

      <!-- Prompt Types -->
      <div class="mt-6">
        <label class="block text-sm font-medium text-gray-300 mb-3">选择生成的提示词类型:</label>
        <div class="space-y-2">
          <label class="flex items-center">
            <input
              v-model="promptTypes"
              type="checkbox"
              value="general"
              class="w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500"
            />
            <span class="ml-2 text-gray-300">通用详细描述</span>
          </label>
          <label class="flex items-center">
            <input
              v-model="promptTypes"
              type="checkbox"
              value="mj"
              class="w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500"
            />
            <span class="ml-2 text-gray-300">Midjourney 风格</span>
          </label>
          <label class="flex items-center">
            <input
              v-model="promptTypes"
              type="checkbox"
              value="sd"
              class="w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500"
            />
            <span class="ml-2 text-gray-300">Stable Diffusion 风格</span>
          </label>
        </div>
      </div>

      <!-- Generate Button -->
      <button
        @click="generatePrompt"
        :disabled="!canGenerate || isLoading"
        class="w-full mt-6 btn-primary py-3 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span v-if="isLoading" class="flex items-center justify-center">
          <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
          AI 正在分析图片...
        </span>
        <span v-else>
          <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          开始生成提示词
        </span>
      </button>
    </div>

    <!-- Right Panel - Results -->
    <div class="card-glass p-6 rounded-lg">
      <h3 class="text-lg font-semibold text-white mb-4">生成结果</h3>
      
      <div v-if="isLoading" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <span class="text-gray-400">AI 正在分析图片并生成提示词...</span>
        </div>
      </div>

      <div v-else-if="error" class="bg-red-900/50 border border-red-500/50 rounded-lg p-4 text-red-200">
        {{ error }}
      </div>

      <div v-else-if="results.length > 0" class="space-y-4">
        <div
          v-for="(result, index) in results"
          :key="index"
          class="bg-gray-800/50 rounded-lg p-4"
        >
          <div class="flex items-center justify-between mb-2">
            <h4 class="font-medium text-white">{{ result.type }}</h4>
            <button
              @click="copyToClipboard(result.content)"
              class="text-blue-400 hover:text-blue-300 text-sm"
            >
              复制
            </button>
          </div>
          <p class="text-gray-300 text-sm leading-relaxed">{{ result.content }}</p>
        </div>
      </div>

      <div v-else class="text-center py-12 text-gray-400">
        上传图片并点击生成按钮
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useUIStore } from '@/stores/ui'
import { copyToClipboard as copyText } from '@/utils'

const uiStore = useUIStore()

const fileInput = ref<HTMLInputElement>()
const selectedImage = ref<string>('')
const selectedModel = ref('')
const promptTypes = ref(['general', 'mj', 'sd'])
const isLoading = ref(false)
const error = ref('')
const results = ref<Array<{ type: string; content: string }>>([])

const canGenerate = computed(() => {
  return selectedImage.value && selectedModel.value && promptTypes.value.length > 0
})

const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    processFile(file)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  const file = event.dataTransfer?.files[0]
  if (file && file.type.startsWith('image/')) {
    processFile(file)
  }
}

const processFile = (file: File) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    selectedImage.value = e.target?.result as string
  }
  reader.readAsDataURL(file)
}

const removeImage = () => {
  selectedImage.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const generatePrompt = async () => {
  if (!canGenerate.value) return

  isLoading.value = true
  error.value = ''
  results.value = []

  try {
    // TODO: Implement actual API call
    await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate API call
    
    // Mock results
    results.value = [
      {
        type: '通用详细描述',
        content: '一张展示现代建筑设计的照片，建筑物采用玻璃幕墙结构，在蓝天白云的背景下显得格外醒目。建筑的几何线条简洁明快，体现了当代建筑的美学特征。'
      },
      {
        type: 'Midjourney 风格',
        content: 'modern glass building architecture, clean geometric lines, blue sky background, contemporary design, professional photography, architectural photography --ar 16:9 --v 6'
      },
      {
        type: 'Stable Diffusion 风格',
        content: 'modern glass building, architectural photography, clean lines, geometric design, blue sky, contemporary architecture, high quality, detailed, professional lighting'
      }
    ]
  } catch (err) {
    error.value = '生成失败，请稍后重试或检查图片。'
  } finally {
    isLoading.value = false
  }
}

const copyToClipboard = async (text: string) => {
  const success = await copyText(text)
  if (success) {
    uiStore.showToast('已复制到剪贴板', 'success')
  } else {
    uiStore.showToast('复制失败', 'error')
  }
}

// Handle paste events
const handlePaste = (event: ClipboardEvent) => {
  const items = event.clipboardData?.items
  if (items) {
    for (let i = 0; i < items.length; i++) {
      if (items[i].type.indexOf('image') !== -1) {
        const file = items[i].getAsFile()
        if (file) {
          processFile(file)
        }
        break
      }
    }
  }
}

onMounted(() => {
  document.addEventListener('paste', handlePaste)
})

onUnmounted(() => {
  document.removeEventListener('paste', handlePaste)
})
</script>
