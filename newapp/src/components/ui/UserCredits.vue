<template>
  <div
    class="relative"
    @mouseenter="showTooltip = true"
    @mouseleave="showTooltip = false"
  >
    <!-- Credits Display -->
    <div
      @click="handleCreditsClick"
      class="flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-800/50 border border-gray-600 hover:border-gray-500 transition-colors cursor-pointer"
    >
      <svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.51-1.31c-.562-.649-1.413-1.076-2.353-1.253V5z" clip-rule="evenodd"/>
      </svg>
      <span class="text-sm font-medium text-gray-200">
        {{ formattedCredits }}
      </span>
    </div>

    <!-- Tooltip -->
    <Transition
      name="tooltip"
      @enter="onTooltipEnter"
      @leave="onTooltipLeave"
    >
      <div
        v-if="showTooltip"
        class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs text-white bg-gray-900 rounded-lg shadow-lg border border-gray-700 whitespace-nowrap z-10"
      >
        <div class="text-center">
          <div class="font-medium">当前积分余额</div>
          <div class="text-gray-300 mt-1">点击购买积分</div>
        </div>
        <!-- Arrow -->
        <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useUIStore } from '@/stores/ui'

const authStore = useAuthStore()
const uiStore = useUIStore()

const showTooltip = ref(false)
const credits = ref<number | null>(null)
const isLoading = ref(true)

const formattedCredits = computed(() => {
  if (isLoading.value) return '...'
  if (credits.value === null) return '0'
  return credits.value.toLocaleString()
})

const handleCreditsClick = () => {
  // Navigate to purchase page or show purchase modal
  uiStore.showModal('purchase')
}

const fetchCredits = async () => {
  try {
    isLoading.value = true
    const response = await fetch('https://caca.yzycolour.top/api/user/credits', {
      headers: authStore.getAuthHeaders(),
    })
    
    const data = await response.json()
    if (data.success) {
      credits.value = data.credits || 0
      // Update user store with credits
      authStore.updateUser({ credits_balance: credits.value })
    }
  } catch (error) {
    console.error('Failed to fetch credits:', error)
    credits.value = 0
  } finally {
    isLoading.value = false
  }
}

const onTooltipEnter = (el: Element) => {
  const element = el as HTMLElement
  element.style.opacity = '0'
  element.style.transform = 'translateX(-50%) translateY(10px)'
  
  requestAnimationFrame(() => {
    element.style.transition = 'opacity 0.2s ease, transform 0.2s ease'
    element.style.opacity = '1'
    element.style.transform = 'translateX(-50%) translateY(0)'
  })
}

const onTooltipLeave = (el: Element) => {
  const element = el as HTMLElement
  element.style.transition = 'opacity 0.2s ease, transform 0.2s ease'
  element.style.opacity = '0'
  element.style.transform = 'translateX(-50%) translateY(10px)'
}

onMounted(() => {
  if (authStore.isAuthenticated) {
    fetchCredits()
  }
})
</script>

<style scoped>
.tooltip-enter-active,
.tooltip-leave-active {
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.tooltip-enter-from,
.tooltip-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(10px);
}
</style>
