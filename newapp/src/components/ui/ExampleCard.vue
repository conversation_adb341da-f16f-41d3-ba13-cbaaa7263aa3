<template>
  <div class="card-glass rounded-lg overflow-hidden hover:scale-105 transition-transform duration-200">
    <!-- Image -->
    <div class="relative aspect-square">
      <img
        :src="example.image_url"
        :alt="example.title"
        class="w-full h-full object-cover"
        @error="handleImageError"
      />
      
      <!-- Overlay -->
      <div class="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
        <div class="flex space-x-2">
          <button
            @click="$emit('view', example)"
            class="p-2 bg-white/20 rounded-full hover:bg-white/30 transition-colors"
            title="查看详情"
          >
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
            </svg>
          </button>
          
          <button
            @click="handleLike"
            :class="[
              'p-2 rounded-full transition-colors',
              example.is_liked 
                ? 'bg-red-500 hover:bg-red-600' 
                : 'bg-white/20 hover:bg-white/30'
            ]"
            title="点赞"
          >
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
            </svg>
          </button>
          
          <button
            v-if="canDelete"
            @click="handleDelete"
            class="p-2 bg-red-500/20 rounded-full hover:bg-red-500/30 transition-colors"
            title="删除"
          >
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Category Badge -->
      <div
        v-if="example.category"
        class="absolute top-2 left-2 px-2 py-1 rounded text-xs font-medium"
        :style="categoryStyle"
      >
        {{ example.category.name }}
      </div>

      <!-- Model Badge -->
      <div class="absolute top-2 right-2 px-2 py-1 bg-black/50 rounded text-xs font-medium text-white">
        {{ example.model }}
      </div>
    </div>

    <!-- Content -->
    <div class="p-4">
      <!-- Title -->
      <h3 class="text-white font-medium mb-2 line-clamp-2">
        {{ example.title }}
      </h3>

      <!-- Prompt -->
      <p class="text-gray-400 text-sm mb-3 line-clamp-3">
        {{ example.prompt }}
      </p>

      <!-- Tags -->
      <div v-if="example.tags && example.tags.length > 0" class="flex flex-wrap gap-1 mb-3">
        <span
          v-for="tag in example.tags.slice(0, 3)"
          :key="tag.id"
          class="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded"
        >
          {{ tag.name }}
        </span>
        <span
          v-if="example.tags.length > 3"
          class="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded"
        >
          +{{ example.tags.length - 3 }}
        </span>
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-between text-xs text-gray-500">
        <div class="flex items-center space-x-2">
          <span>{{ formatRelativeTime(example.created_at) }}</span>
          <span>•</span>
          <span>{{ example.width }}×{{ example.height }}</span>
        </div>
        
        <div class="flex items-center space-x-1">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
          </svg>
          <span>{{ example.likes_count || 0 }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { formatRelativeTime, getCategoryColor } from '@/utils'
import type { Example } from '@/types'

interface Props {
  example: Example
}

const props = defineProps<Props>()

const emit = defineEmits<{
  like: [id: number]
  delete: [id: number]
  view: [example: Example]
}>()

const authStore = useAuthStore()

const canDelete = computed(() => {
  return authStore.isAdmin || authStore.user?.id === props.example.user_id
})

const categoryStyle = computed(() => {
  const colors = getCategoryColor(props.example.category?.slug)
  return {
    backgroundColor: colors.backgroundColor,
    borderColor: colors.borderColor,
    border: `1px solid ${colors.borderColor}`,
    color: 'white'
  }
})

const handleLike = () => {
  emit('like', props.example.id)
}

const handleDelete = () => {
  if (confirm('确定要删除这个案例吗？')) {
    emit('delete', props.example.id)
  }
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/placeholder.jpg' // You can add a placeholder image
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
