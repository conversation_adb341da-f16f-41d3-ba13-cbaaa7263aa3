<template>
  <Teleport to="body">
    <Transition
      name="modal"
      @enter="onEnter"
      @leave="onLeave"
    >
      <div
        v-if="show"
        class="fixed inset-0 z-50 overflow-y-auto"
        @click="handleBackdropClick"
      >
        <!-- Backdrop -->
        <div class="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity" />
        
        <!-- Modal Container -->
        <div class="flex min-h-full items-center justify-center p-4">
          <div
            ref="modalRef"
            :class="[
              'relative w-full rounded-lg shadow-xl transition-all',
              'card-glass border border-white/10',
              sizeClasses
            ]"
            @click.stop
          >
            <!-- Header -->
            <div v-if="title || $slots.header || closable" class="flex items-center justify-between p-6 border-b border-gray-700">
              <div class="flex items-center">
                <slot name="header">
                  <h3 class="text-lg font-semibold text-white">
                    {{ title }}
                  </h3>
                </slot>
              </div>
              
              <button
                v-if="closable"
                @click="$emit('close')"
                class="text-gray-400 hover:text-gray-200 transition-colors"
              >
                <span class="sr-only">关闭</span>
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <!-- Body -->
            <div class="p-6">
              <slot />
            </div>
            
            <!-- Footer -->
            <div v-if="$slots.footer" class="flex items-center justify-end space-x-3 p-6 border-t border-gray-700">
              <slot name="footer" />
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, ref, nextTick } from 'vue'

interface Props {
  show: boolean
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  closable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  closable: true,
})

const emit = defineEmits<{
  close: []
}>()

const modalRef = ref<HTMLElement>()

const sizeClasses = computed(() => {
  const sizes = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    '2xl': 'max-w-6xl',
  }
  return sizes[props.size]
})

const handleBackdropClick = (event: MouseEvent) => {
  if (props.closable && event.target === event.currentTarget) {
    emit('close')
  }
}

const onEnter = async () => {
  await nextTick()
  // Focus management
  const focusableElements = modalRef.value?.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  )
  if (focusableElements && focusableElements.length > 0) {
    (focusableElements[0] as HTMLElement).focus()
  }
}

const onLeave = () => {
  // Cleanup if needed
}

// Handle escape key
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.closable) {
    emit('close')
  }
}

// Add/remove event listeners
const addEventListeners = () => {
  document.addEventListener('keydown', handleKeydown)
  document.body.style.overflow = 'hidden'
}

const removeEventListeners = () => {
  document.removeEventListener('keydown', handleKeydown)
  document.body.style.overflow = ''
}

// Watch for show prop changes
import { watch } from 'vue'
watch(() => props.show, (newShow) => {
  if (newShow) {
    addEventListeners()
  } else {
    removeEventListeners()
  }
})
</script>

<style scoped>
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-active .relative,
.modal-leave-active .relative {
  transition: transform 0.3s ease;
}

.modal-enter-from .relative,
.modal-leave-to .relative {
  transform: scale(0.95) translateY(-20px);
}
</style>
