<template>
  <div class="relative" ref="dropdownRef">
    <!-- Notification Bell -->
    <button
      @click="toggleDropdown"
      class="relative p-2 text-gray-400 hover:text-gray-200 transition-colors"
      title="通知"
    >
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
      </svg>
      
      <!-- Badge -->
      <span
        v-if="unreadCount > 0"
        class="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full min-w-[1.25rem] h-5"
      >
        {{ unreadCount > 99 ? '99+' : unreadCount }}
      </span>
    </button>

    <!-- Dropdown Menu -->
    <Transition
      name="dropdown"
      @enter="onEnter"
      @leave="onLeave"
    >
      <div
        v-if="isOpen"
        class="absolute right-0 top-full mt-2 w-80 rounded-lg shadow-lg border border-gray-700 bg-gray-800/95 backdrop-blur-sm z-20 max-h-96 overflow-hidden"
      >
        <!-- Header -->
        <div class="px-4 py-3 border-b border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="text-sm font-medium text-white">通知</h3>
            <button
              v-if="unreadCount > 0"
              @click="markAllAsRead"
              class="text-xs text-blue-400 hover:text-blue-300 transition-colors"
            >
              全部标记为已读
            </button>
          </div>
        </div>

        <!-- Notification List -->
        <div class="max-h-64 overflow-y-auto">
          <div v-if="notifications.length === 0" class="px-4 py-6 text-center text-gray-400">
            <svg class="w-8 h-8 mx-auto mb-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
            </svg>
            <p class="text-sm">暂无通知</p>
          </div>

          <div v-else>
            <div
              v-for="notification in notifications"
              :key="notification.id"
              @click="handleNotificationClick(notification)"
              :class="[
                'px-4 py-3 border-b border-gray-700 last:border-b-0 cursor-pointer transition-colors',
                notification.is_read ? 'hover:bg-gray-700/50' : 'bg-blue-900/20 hover:bg-blue-900/30'
              ]"
            >
              <div class="flex items-start space-x-3">
                <!-- Icon -->
                <div :class="[
                  'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center',
                  getNotificationIconClass(notification.type)
                ]">
                  <component :is="getNotificationIcon(notification.type)" class="w-4 h-4" />
                </div>

                <!-- Content -->
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-white truncate">
                    {{ notification.title }}
                  </p>
                  <p class="text-xs text-gray-400 mt-1 line-clamp-2">
                    {{ notification.message }}
                  </p>
                  <p class="text-xs text-gray-500 mt-1">
                    {{ formatRelativeTime(notification.created_at) }}
                  </p>
                </div>

                <!-- Unread indicator -->
                <div
                  v-if="!notification.is_read"
                  class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="px-4 py-3 border-t border-gray-700">
          <button
            @click="viewAllNotifications"
            class="text-sm text-blue-400 hover:text-blue-300 transition-colors"
          >
            查看更新日志
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useUIStore } from '@/stores/ui'
import { formatRelativeTime } from '@/utils'

// Icons
const ChatIcon = 'div'
const BellIcon = 'div'
const InformationCircleIcon = 'div'

const uiStore = useUIStore()
const dropdownRef = ref<HTMLElement>()
const isOpen = ref(false)

const notifications = computed(() => uiStore.notifications)
const unreadCount = computed(() => 
  notifications.value.filter(n => !n.is_read).length
)

const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

const closeDropdown = () => {
  isOpen.value = false
}

const handleNotificationClick = (notification: any) => {
  if (!notification.is_read) {
    uiStore.markNotificationAsRead(notification.id)
  }
  
  // Handle specific notification actions
  if (notification.type === 'comment') {
    // Navigate to comments or show comments modal
    uiStore.showModal('comments')
  }
  
  closeDropdown()
}

const markAllAsRead = () => {
  notifications.value.forEach(notification => {
    if (!notification.is_read) {
      uiStore.markNotificationAsRead(notification.id)
    }
  })
}

const viewAllNotifications = () => {
  // Navigate to changelog
  window.open('/changelog/changelog.html', '_blank')
  closeDropdown()
}

const getNotificationIcon = (type: string) => {
  const icons = {
    comment: ChatIcon,
    system: BellIcon,
    update: InformationCircleIcon,
  }
  return icons[type as keyof typeof icons] || BellIcon
}

const getNotificationIconClass = (type: string) => {
  const classes = {
    comment: 'bg-blue-500/20 text-blue-400',
    system: 'bg-gray-500/20 text-gray-400',
    update: 'bg-green-500/20 text-green-400',
  }
  return classes[type as keyof typeof classes] || 'bg-gray-500/20 text-gray-400'
}

const handleClickOutside = (event: MouseEvent) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    closeDropdown()
  }
}

const onEnter = (el: Element) => {
  const element = el as HTMLElement
  element.style.opacity = '0'
  element.style.transform = 'translateY(-10px) scale(0.95)'
  
  requestAnimationFrame(() => {
    element.style.transition = 'opacity 0.2s ease, transform 0.2s ease'
    element.style.opacity = '1'
    element.style.transform = 'translateY(0) scale(1)'
  })
}

const onLeave = (el: Element) => {
  const element = el as HTMLElement
  element.style.transition = 'opacity 0.2s ease, transform 0.2s ease'
  element.style.opacity = '0'
  element.style.transform = 'translateY(-10px) scale(0.95)'
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.dropdown-enter-active,
.dropdown-leave-active {
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
