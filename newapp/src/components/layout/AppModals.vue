<template>
  <div>
    <!-- Profile Modal -->
    <BaseModal
      :show="uiStore.isModalVisible('profile')"
      @close="uiStore.hideModal('profile')"
      title="个人资料"
      size="md"
    >
      <ProfileModal />
    </BaseModal>

    <!-- Add Example Modal -->
    <BaseModal
      :show="uiStore.isModalVisible('addExample')"
      @close="uiStore.hideModal('addExample')"
      title="添加案例"
      size="lg"
    >
      <AddExampleModal />
    </BaseModal>

    <!-- Join Group Modal -->
    <BaseModal
      :show="uiStore.isModalVisible('joinGroup')"
      @close="uiStore.hideModal('joinGroup')"
      title="加入官方社群"
      size="sm"
    >
      <JoinGroupModal />
    </BaseModal>

    <!-- My Examples Modal -->
    <BaseModal
      :show="uiStore.isModalVisible('myExamples')"
      @close="uiStore.hideModal('myExamples')"
      title="我的案例"
      size="xl"
    >
      <MyExamplesModal />
    </BaseModal>

    <!-- Liked Examples Modal -->
    <BaseModal
      :show="uiStore.isModalVisible('likedExamples')"
      @close="uiStore.hideModal('likedExamples')"
      title="我点赞的案例"
      size="xl"
    >
      <LikedExamplesModal />
    </BaseModal>

    <!-- API Key Modal -->
    <BaseModal
      :show="uiStore.isModalVisible('apiKey')"
      @close="uiStore.hideModal('apiKey')"
      title="API Key 管理"
      size="lg"
    >
      <ApiKeyModal />
    </BaseModal>

    <!-- Example Detail Modal -->
    <BaseModal
      :show="uiStore.isModalVisible('exampleDetail')"
      @close="uiStore.hideModal('exampleDetail')"
      title="案例详情"
      size="xl"
    >
      <ExampleDetailModal />
    </BaseModal>

    <!-- Image Detail Modal -->
    <BaseModal
      :show="uiStore.isModalVisible('imageDetail')"
      @close="uiStore.hideModal('imageDetail')"
      title="图片详情"
      size="lg"
    >
      <ImageDetailModal />
    </BaseModal>

    <!-- Confirm Delete Modal -->
    <BaseModal
      :show="uiStore.isModalVisible('confirmDelete')"
      @close="uiStore.hideModal('confirmDelete')"
      title="确认删除"
      size="sm"
    >
      <ConfirmDeleteModal />
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { useUIStore } from '@/stores/ui'
import BaseModal from '../ui/BaseModal.vue'

// Import modal components (these will be created later)
import ProfileModal from '../features/ProfileModal.vue'
import AddExampleModal from '../features/AddExampleModal.vue'
import JoinGroupModal from '../features/JoinGroupModal.vue'
import MyExamplesModal from '../features/MyExamplesModal.vue'
import LikedExamplesModal from '../features/LikedExamplesModal.vue'
import ApiKeyModal from '../features/ApiKeyModal.vue'
import ExampleDetailModal from '../features/ExampleDetailModal.vue'
import ImageDetailModal from '../features/ImageDetailModal.vue'
import ConfirmDeleteModal from '../features/ConfirmDeleteModal.vue'

const uiStore = useUIStore()
</script>
