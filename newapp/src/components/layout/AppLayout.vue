<template>
  <div class="min-h-screen bg-gray-950 text-gray-100 relative">
    <!-- Background grid mask for mouse effect -->
    <div class="grid-mask"></div>
    
    <!-- Main container -->
    <div class="container mx-auto max-w-7xl px-4 py-5 relative z-10 fade-in">
      <!-- Header -->
      <AppHeader />
      
      <!-- Navigation Tabs -->
      <AppTabs />
      
      <!-- Main Content -->
      <main class="mt-6">
        <slot />
      </main>
    </div>
    
    <!-- Global Modals -->
    <AppModals />
    
    <!-- Toast Notifications -->
    <AppToasts />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useUIStore } from '@/stores/ui'
import AppHeader from './AppHeader.vue'
import AppTabs from './AppTabs.vue'
import AppModals from './AppModals.vue'
import AppToasts from './AppToasts.vue'

const uiStore = useUIStore()

// Mouse effect handling
const handleMouseMove = (event: MouseEvent) => {
  uiStore.updateMousePosition(event.clientX, event.clientY)
}

const handleMouseEnter = () => {
  uiStore.enableMouseEffect()
}

const handleMouseLeave = () => {
  uiStore.disableMouseEffect()
}

onMounted(() => {
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseenter', handleMouseEnter)
  document.addEventListener('mouseleave', handleMouseLeave)
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseenter', handleMouseEnter)
  document.removeEventListener('mouseleave', handleMouseLeave)
})
</script>

<style scoped>
.container {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  position: relative;
  z-index: 1;
  opacity: 0;
  animation: fadeIn 0.5s 0.2s ease-in forwards;
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(10px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}
</style>
