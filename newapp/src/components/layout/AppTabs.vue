<template>
  <nav class="mb-6">
    <div class="border-b border-gray-700">
      <div class="flex space-x-1 overflow-x-auto scrollbar-hide">
        <button
          v-for="tab in visibleTabs"
          :key="tab.id"
          @click="setActiveTab(tab.id)"
          :class="[
            'flex items-center px-4 py-3 text-sm font-medium rounded-t-lg transition-colors duration-200 whitespace-nowrap',
            activeTab === tab.id
              ? 'bg-blue-600 text-white border-b-2 border-blue-400'
              : 'text-gray-400 hover:text-gray-200 hover:bg-gray-800'
          ]"
        >
          <component :is="tab.icon" class="w-4 h-4 mr-2" />
          {{ tab.label }}
        </button>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUIStore } from '@/stores/ui'
import { useAuthStore } from '@/stores/auth'

// Icons (you can replace these with actual icon components)
const CollectionIcon = 'div'
const ImageIcon = 'div'
const StarsIcon = 'div'
const ShieldIcon = 'div'
const ExpandIcon = 'div'
const EraserIcon = 'div'
const VectorIcon = 'div'
const CubeIcon = 'div'
const FilmIcon = 'div'
const EditIcon = 'div'
const ChatIcon = 'div'
const ScissorsIcon = 'div'
const PaletteIcon = 'div'
const ShopIcon = 'div'
const LightningIcon = 'div'

const uiStore = useUIStore()
const authStore = useAuthStore()

const { activeTab } = uiStore

const allTabs = [
  {
    id: 'management',
    label: '案例管理',
    icon: CollectionIcon,
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'reverse-prompt',
    label: '提示词反推 (图生文)',
    icon: ImageIcon,
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'ai-generate',
    label: 'AI 图片生成',
    icon: StarsIcon,
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'admin-management',
    label: '管理中心',
    icon: ShieldIcon,
    requiresAuth: true,
    requiresAdmin: true,
  },
  {
    id: 'creative-upscale',
    label: '清晰放大',
    icon: ExpandIcon,
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'remove-background',
    label: '背景移除',
    icon: EraserIcon,
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'vectorize',
    label: '位图转 SVG',
    icon: VectorIcon,
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'image-to-3d',
    label: '图片转3D',
    icon: CubeIcon,
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'image-to-video',
    label: '图生视频',
    icon: FilmIcon,
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'gpt4o-image-edit',
    label: 'GPT-4o 图片编辑',
    icon: EditIcon,
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'ai-chat-assistant',
    label: 'AI 聊天助手',
    icon: ChatIcon,
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'remove-anything',
    label: '移除万物',
    icon: ScissorsIcon,
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'flux-kontext',
    label: 'Flux智能编辑图片',
    icon: PaletteIcon,
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'ecommerce',
    label: '电商专栏',
    icon: ShopIcon,
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'flash-light',
    label: 'AI补光&美颜',
    icon: LightningIcon,
    requiresAuth: true,
    requiresAdmin: false,
  },
]

const visibleTabs = computed(() => {
  return allTabs.filter(tab => {
    if (tab.requiresAuth && !authStore.isAuthenticated) return false
    if (tab.requiresAdmin && !authStore.isAdmin) return false
    return true
  })
})

const setActiveTab = (tabId: string) => {
  uiStore.setActiveTab(tabId)
}
</script>

<style scoped>
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
</style>
