<template>
  <Teleport to="body">
    <div class="fixed top-4 right-4 z-50 space-y-2">
      <TransitionGroup
        name="toast"
        tag="div"
        class="space-y-2"
      >
        <div
          v-for="toast in uiStore.toasts"
          :key="toast.id"
          :class="[
            'max-w-sm w-full shadow-lg rounded-lg pointer-events-auto overflow-hidden',
            'backdrop-blur-md border border-white/10',
            getToastClasses(toast.type)
          ]"
        >
          <div class="p-4">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <component :is="getToastIcon(toast.type)" class="h-5 w-5" />
              </div>
              <div class="ml-3 w-0 flex-1">
                <p class="text-sm font-medium text-white">
                  {{ toast.message }}
                </p>
              </div>
              <div class="ml-4 flex-shrink-0 flex">
                <button
                  @click="uiStore.hideToast(toast.id)"
                  class="inline-flex text-gray-400 hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white"
                >
                  <span class="sr-only">关闭</span>
                  <XMarkIcon class="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
          
          <!-- Progress bar -->
          <div
            v-if="toast.duration > 0"
            class="h-1 bg-white/20"
          >
            <div
              :class="[
                'h-full transition-all ease-linear',
                getProgressBarClass(toast.type)
              ]"
              :style="{
                width: '100%',
                animationDuration: `${toast.duration}ms`,
                animationName: 'toast-progress'
              }"
            />
          </div>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { useUIStore } from '@/stores/ui'

// Icons (replace with actual icon components)
const CheckCircleIcon = 'div'
const ExclamationCircleIcon = 'div'
const ExclamationTriangleIcon = 'div'
const InformationCircleIcon = 'div'
const XMarkIcon = 'div'

const uiStore = useUIStore()

const getToastClasses = (type: string) => {
  const classes = {
    success: 'bg-green-900/80 border-green-500/50',
    error: 'bg-red-900/80 border-red-500/50',
    warning: 'bg-yellow-900/80 border-yellow-500/50',
    info: 'bg-blue-900/80 border-blue-500/50',
  }
  return classes[type as keyof typeof classes] || classes.info
}

const getToastIcon = (type: string) => {
  const icons = {
    success: CheckCircleIcon,
    error: ExclamationCircleIcon,
    warning: ExclamationTriangleIcon,
    info: InformationCircleIcon,
  }
  return icons[type as keyof typeof icons] || icons.info
}

const getProgressBarClass = (type: string) => {
  const classes = {
    success: 'bg-green-500',
    error: 'bg-red-500',
    warning: 'bg-yellow-500',
    info: 'bg-blue-500',
  }
  return classes[type as keyof typeof classes] || classes.info
}
</script>

<style scoped>
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

@keyframes toast-progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
</style>
