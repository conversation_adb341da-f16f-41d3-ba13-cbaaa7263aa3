<template>
  <header class="flex justify-between items-center mb-10">
    <!-- Logo and Title -->
    <div class="flex items-center space-x-4">
      <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
        像素星云
      </h1>
    </div>

    <!-- Header Actions -->
    <div class="flex items-center space-x-4">
      <!-- User Credits -->
      <UserCredits />
      
      <!-- Notifications -->
      <NotificationDropdown />
      
      <!-- User Menu -->
      <UserMenu />
      
      <!-- Action Buttons -->
      <div class="flex items-center space-x-2">
        <!-- Join Group Button -->
        <button
          @click="showJoinGroupModal"
          class="btn-outline text-sm"
          title="加入官方社群获取帮助"
        >
          <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
          </svg>
        </button>
        
        <!-- AI Canvas Button -->
        <button
          @click="jumpToAICanvas"
          class="btn-primary text-sm"
        >
          AI画布
        </button>
        
        <!-- Add Example Button -->
        <button
          @click="showAddExampleModal"
          class="btn-primary text-sm"
        >
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
          </svg>
          添加案例
        </button>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { useUIStore } from '@/stores/ui'
import UserCredits from '../ui/UserCredits.vue'
import NotificationDropdown from '../ui/NotificationDropdown.vue'
import UserMenu from '../ui/UserMenu.vue'

const uiStore = useUIStore()

const showJoinGroupModal = () => {
  uiStore.showModal('joinGroup')
}

const showAddExampleModal = () => {
  uiStore.showModal('addExample')
}

const jumpToAICanvas = () => {
  // TODO: Implement AI Canvas navigation
  console.log('Jump to AI Canvas')
}
</script>
