// 用户相关类型
export interface User {
  id: number
  username: string
  nickname?: string
  email: string
  role: 'admin' | 'user'
  avatar_url?: string
  created_at: string
  credits_balance?: number
}

// 案例相关类型
export interface Example {
  id: number
  title: string
  prompt: string
  image_url: string
  model: string
  category_id?: number
  category?: Category
  tags: Tag[]
  user_id: number
  user?: User
  likes_count: number
  is_liked?: boolean
  created_at: string
  width?: number
  height?: number
}

// 分类类型
export interface Category {
  id: number
  name: string
  slug: string
  description?: string
}

// 标签类型
export interface Tag {
  id: number
  name: string
}

// AI生成相关类型
export interface AIGenerateRequest {
  prompt: string
  model: string
  width: number
  height: number
  aspect_ratio?: string
  num_images?: number
  base_image?: File
  mode?: string
}

export interface AIGenerateResponse {
  id: string
  prompt: string
  model: string
  image_url: string
  width: number
  height: number
  created_at: string
  status: 'pending' | 'completed' | 'failed'
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 分页响应类型
export interface PaginatedResponse<T> {
  success: boolean
  data: T[]
  pagination: {
    current_page: number
    total_pages: number
    total_items: number
    per_page: number
  }
}

// 功能成本类型
export interface FeatureCost {
  feature_key: string
  cost: number
  description?: string
}

// 通知类型
export interface Notification {
  id: number
  type: 'comment' | 'system' | 'update'
  title: string
  message: string
  is_read: boolean
  created_at: string
}

// 模态框类型
export interface ModalProps {
  show: boolean
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  closable?: boolean
}

// 下拉菜单选项类型
export interface DropdownOption {
  value: string
  label: string
  disabled?: boolean
}

// 表单验证规则类型
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  message: string
}

// 上传文件类型
export interface UploadFile {
  file: File
  preview?: string
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress?: number
}

// 路由元信息类型
export interface RouteMeta {
  title?: string
  requiresAuth?: boolean
  requiresAdmin?: boolean
  icon?: string
}
