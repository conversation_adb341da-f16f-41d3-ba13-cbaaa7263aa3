import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/HomeView.vue'),
    meta: {
      title: '像素星云AI助手',
      requiresAuth: true,
    },
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/LoginView.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
    },
  },
  {
    path: '/examples',
    name: 'Examples',
    component: () => import('@/views/ExamplesView.vue'),
    meta: {
      title: '案例管理',
      requiresAuth: true,
    },
  },
  {
    path: '/ai-generate',
    name: 'AIGenerate',
    component: () => import('@/views/AIGenerateView.vue'),
    meta: {
      title: 'AI图片生成',
      requiresAuth: true,
    },
  },
  {
    path: '/reverse-prompt',
    name: 'ReversePrompt',
    component: () => import('@/views/ReversePromptView.vue'),
    meta: {
      title: '提示词反推',
      requiresAuth: true,
    },
  },
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('@/views/AdminView.vue'),
    meta: {
      title: '管理中心',
      requiresAuth: true,
      requiresAdmin: true,
    },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFoundView.vue'),
    meta: {
      title: '页面未找到',
    },
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title as string
  }

  // 检查本地存储中的认证状态
  const token = localStorage.getItem('token')
  const userStr = localStorage.getItem('user')
  const isAuthenticated = !!(token && userStr)

  let user = null
  let isAdmin = false

  if (userStr) {
    try {
      user = JSON.parse(userStr)
      isAdmin = user?.role === 'admin'
    } catch (error) {
      console.error('Failed to parse user data:', error)
    }
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login')
    return
  }

  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin && !isAdmin) {
    next('/')
    return
  }

  // 如果已登录用户访问登录页，重定向到首页
  if (to.name === 'Login' && isAuthenticated) {
    next('/')
    return
  }

  next()
})

export default router
