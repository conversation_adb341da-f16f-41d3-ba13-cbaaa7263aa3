<template>
  <div class="min-h-screen bg-gray-950 flex items-center justify-center">
    <div class="text-center">
      <div class="mb-8">
        <h1 class="text-9xl font-bold text-gray-700">404</h1>
        <h2 class="text-2xl font-semibold text-gray-300 mb-4">页面未找到</h2>
        <p class="text-gray-400 mb-8">抱歉，您访问的页面不存在。</p>
      </div>
      
      <div class="space-x-4">
        <button
          @click="goBack"
          class="btn-secondary"
        >
          返回上页
        </button>
        <router-link
          to="/"
          class="btn-primary"
        >
          回到首页
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>
