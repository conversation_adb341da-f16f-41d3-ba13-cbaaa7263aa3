<template>
  <AppLayout>
    <!-- Tab Content Based on Active Tab -->
    <div class="tab-content">
      <!-- Examples Management Tab -->
      <div v-if="uiStore.activeTab === 'management'" class="tab-pane">
        <ExamplesManagement />
      </div>

      <!-- Reverse Prompt Tab -->
      <div v-else-if="uiStore.activeTab === 'reverse-prompt'" class="tab-pane">
        <ReversePrompt />
      </div>

      <!-- AI Generate Tab -->
      <div v-else-if="uiStore.activeTab === 'ai-generate'" class="tab-pane">
        <AIGenerate />
      </div>

      <!-- Admin Management Tab -->
      <div v-else-if="uiStore.activeTab === 'admin-management'" class="tab-pane">
        <AdminManagement />
      </div>

      <!-- Creative Upscale Tab -->
      <div v-else-if="uiStore.activeTab === 'creative-upscale'" class="tab-pane">
        <CreativeUpscale />
      </div>

      <!-- Remove Background Tab -->
      <div v-else-if="uiStore.activeTab === 'remove-background'" class="tab-pane">
        <RemoveBackground />
      </div>

      <!-- Vectorize Tab -->
      <div v-else-if="uiStore.activeTab === 'vectorize'" class="tab-pane">
        <VectorizeImage />
      </div>

      <!-- Image to 3D Tab -->
      <div v-else-if="uiStore.activeTab === 'image-to-3d'" class="tab-pane">
        <ImageTo3D />
      </div>

      <!-- Image to Video Tab -->
      <div v-else-if="uiStore.activeTab === 'image-to-video'" class="tab-pane">
        <ImageToVideo />
      </div>

      <!-- GPT-4o Image Edit Tab -->
      <div v-else-if="uiStore.activeTab === 'gpt4o-image-edit'" class="tab-pane">
        <GPT4oImageEdit />
      </div>

      <!-- AI Chat Assistant Tab -->
      <div v-else-if="uiStore.activeTab === 'ai-chat-assistant'" class="tab-pane">
        <AIChatAssistant />
      </div>

      <!-- Remove Anything Tab -->
      <div v-else-if="uiStore.activeTab === 'remove-anything'" class="tab-pane">
        <RemoveAnything />
      </div>

      <!-- Flux Kontext Tab -->
      <div v-else-if="uiStore.activeTab === 'flux-kontext'" class="tab-pane">
        <FluxKontext />
      </div>

      <!-- Ecommerce Tab -->
      <div v-else-if="uiStore.activeTab === 'ecommerce'" class="tab-pane">
        <EcommerceTools />
      </div>

      <!-- Flash Light Tab -->
      <div v-else-if="uiStore.activeTab === 'flash-light'" class="tab-pane">
        <FlashLight />
      </div>

      <!-- Default/Fallback -->
      <div v-else class="tab-pane">
        <ExamplesManagement />
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useUIStore } from '@/stores/ui'
import { useExamplesStore } from '@/stores/examples'
import AppLayout from '@/components/layout/AppLayout.vue'

// Feature components (these will be created later)
import ExamplesManagement from '@/components/features/ExamplesManagement.vue'
import ReversePrompt from '@/components/features/ReversePrompt.vue'
import AIGenerate from '@/components/features/AIGenerate.vue'
import AdminManagement from '@/components/features/AdminManagement.vue'
import CreativeUpscale from '@/components/features/CreativeUpscale.vue'
import RemoveBackground from '@/components/features/RemoveBackground.vue'
import VectorizeImage from '@/components/features/VectorizeImage.vue'
import ImageTo3D from '@/components/features/ImageTo3D.vue'
import ImageToVideo from '@/components/features/ImageToVideo.vue'
import GPT4oImageEdit from '@/components/features/GPT4oImageEdit.vue'
import AIChatAssistant from '@/components/features/AIChatAssistant.vue'
import RemoveAnything from '@/components/features/RemoveAnything.vue'
import FluxKontext from '@/components/features/FluxKontext.vue'
import EcommerceTools from '@/components/features/EcommerceTools.vue'
import FlashLight from '@/components/features/FlashLight.vue'

const uiStore = useUIStore()
const examplesStore = useExamplesStore()

onMounted(async () => {
  // Initialize data
  await Promise.all([
    examplesStore.fetchCategories(),
    examplesStore.fetchTags(),
    examplesStore.fetchExamples(1, true)
  ])
})
</script>

<style scoped>
.tab-pane {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
