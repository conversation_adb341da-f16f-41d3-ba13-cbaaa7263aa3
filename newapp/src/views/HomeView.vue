<template>
  <div class="min-h-screen bg-gray-950 text-gray-100 relative">
    <!-- Background grid mask for mouse effect -->
    <div class="grid-mask"></div>

    <!-- Main container -->
    <div class="container mx-auto max-w-7xl px-4 py-5 relative z-10 fade-in">
      <!-- Header -->
      <header class="flex justify-between items-center mb-10">
        <!-- Logo and Title -->
        <div class="flex items-center space-x-4">
          <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
            像素星云
          </h1>
        </div>

        <!-- Header Actions -->
        <div class="flex items-center space-x-4">
          <!-- User Info -->
          <div class="flex items-center space-x-2 text-gray-300">
            <span class="text-sm">欢迎，{{ authStore.userDisplayName }}</span>
          </div>

          <!-- Logout Button -->
          <button
            @click="handleLogout"
            class="btn-outline text-sm"
          >
            退出登录
          </button>
        </div>
      </header>

      <!-- Navigation Tabs -->
      <nav class="mb-6">
        <div class="border-b border-gray-700">
          <div class="flex space-x-1 overflow-x-auto scrollbar-hide">
            <button
              v-for="tab in visibleTabs"
              :key="tab.id"
              @click="setActiveTab(tab.id)"
              :class="[
                'flex items-center px-4 py-3 text-sm font-medium rounded-t-lg transition-colors duration-200 whitespace-nowrap',
                activeTab === tab.id
                  ? 'bg-blue-600 text-white border-b-2 border-blue-400'
                  : 'text-gray-400 hover:text-gray-200 hover:bg-gray-800'
              ]"
            >
              {{ tab.label }}
            </button>
          </div>
        </div>
      </nav>

      <!-- Tab Content Based on Active Tab -->
      <main class="mt-6">
        <div class="tab-content">
          <!-- Examples Management Tab -->
          <div v-if="activeTab === 'management'" class="tab-pane">
            <ExamplesManagement />
          </div>

          <!-- Reverse Prompt Tab -->
          <div v-else-if="activeTab === 'reverse-prompt'" class="tab-pane">
            <ReversePrompt />
          </div>

          <!-- Default/Fallback -->
          <div v-else class="tab-pane">
            <div class="text-center py-12">
              <h2 class="text-2xl font-semibold text-white mb-4">{{ getCurrentTabName() }}</h2>
              <p class="text-gray-400">功能开发中...</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useExamplesStore } from '@/stores/examples'

// Feature components
import ExamplesManagement from '@/components/features/ExamplesManagement.vue'
import ReversePrompt from '@/components/features/ReversePrompt.vue'

const router = useRouter()
const authStore = useAuthStore()
const examplesStore = useExamplesStore()

const activeTab = ref('management')

const allTabs = [
  {
    id: 'management',
    label: '案例管理',
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'reverse-prompt',
    label: '提示词反推 (图生文)',
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'ai-generate',
    label: 'AI 图片生成',
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'admin-management',
    label: '管理中心',
    requiresAuth: true,
    requiresAdmin: true,
  },
  {
    id: 'creative-upscale',
    label: '清晰放大',
    requiresAuth: true,
    requiresAdmin: false,
  },
  {
    id: 'remove-background',
    label: '背景移除',
    requiresAuth: true,
    requiresAdmin: false,
  },
]

const visibleTabs = computed(() => {
  return allTabs.filter(tab => {
    if (tab.requiresAuth && !authStore.isAuthenticated) return false
    if (tab.requiresAdmin && !authStore.isAdmin) return false
    return true
  })
})

const setActiveTab = (tabId: string) => {
  activeTab.value = tabId
}

const getCurrentTabName = () => {
  const tab = allTabs.find(t => t.id === activeTab.value)
  return tab ? tab.label : '未知功能'
}

const handleLogout = () => {
  authStore.logout()
  router.push('/login')
}

onMounted(async () => {
  // Initialize data for examples management
  try {
    await Promise.all([
      examplesStore.fetchCategories(),
      examplesStore.fetchTags(),
      examplesStore.fetchExamples(1, true)
    ])
  } catch (error) {
    console.error('Failed to initialize data:', error)
  }
})
</script>

<style scoped>
.container {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  position: relative;
  z-index: 1;
  opacity: 0;
  animation: fadeIn 0.5s 0.2s ease-in forwards;
}

.grid-mask {
  position: fixed;
  inset: 0;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  background: radial-gradient(
    600px circle at var(--mouse-x) var(--mouse-y),
    rgba(255, 255, 255, 0.08),
    transparent 40%
  );
}

.tab-pane {
  animation: fadeIn 0.3s ease-in-out;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
