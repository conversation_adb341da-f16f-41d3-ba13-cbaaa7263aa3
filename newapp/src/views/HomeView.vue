<template>
  <div class="min-h-screen bg-gray-950 text-gray-100 relative">
    <!-- Background grid patterns -->
    <div class="fixed inset-0 bg-gray-950"></div>
    <div class="fixed inset-0 grid-bg"></div>

    <!-- Main container -->
    <div class="container mx-auto max-w-7xl px-4 py-5 relative z-10">
      <!-- Header -->
      <header class="flex justify-between items-center mb-10">
        <!-- Logo and Title -->
        <div class="flex items-center space-x-4">
          <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
            像素星云AI助手
          </h1>
        </div>

        <!-- Header Actions -->
        <div class="flex items-center space-x-4">
          <!-- User Info -->
          <div class="flex items-center space-x-2 text-gray-300">
            <span class="text-sm">欢迎，{{ userDisplayName }}</span>
          </div>

          <!-- Logout Button -->
          <button
            @click="handleLogout"
            class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
          >
            退出登录
          </button>
        </div>
      </header>

      <!-- Welcome Message -->
      <div class="text-center py-12">
        <h2 class="text-4xl font-bold text-white mb-4">🎉 登录成功！</h2>
        <p class="text-xl text-gray-300 mb-8">欢迎使用像素星云AI助手</p>

        <!-- Debug Info -->
        <div class="bg-gray-800 rounded-lg p-6 max-w-md mx-auto text-left">
          <h3 class="text-lg font-semibold text-white mb-4">调试信息</h3>
          <div class="space-y-2 text-sm">
            <div class="text-gray-300">
              <span class="text-gray-500">用户名:</span> {{ userDisplayName }}
            </div>
            <div class="text-gray-300">
              <span class="text-gray-500">认证状态:</span> {{ isAuthenticated ? '已认证' : '未认证' }}
            </div>
            <div class="text-gray-300">
              <span class="text-gray-500">管理员:</span> {{ isAdmin ? '是' : '否' }}
            </div>
            <div class="text-gray-300">
              <span class="text-gray-500">Token:</span> {{ token ? '存在' : '不存在' }}
            </div>
          </div>
        </div>

        <!-- Navigation Links -->
        <div class="mt-8 space-x-4">
          <router-link
            to="/examples"
            class="inline-block px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            案例管理
          </router-link>
          <router-link
            to="/reverse-prompt"
            class="inline-block px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
          >
            提示词反推
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// Computed properties for debugging
const userDisplayName = computed(() => authStore.userDisplayName || '未知用户')
const isAuthenticated = computed(() => authStore.isAuthenticated)
const isAdmin = computed(() => authStore.isAdmin)
const token = computed(() => authStore.token)

const handleLogout = () => {
  console.log('Logging out...')
  authStore.logout()
  router.push('/login')
}

onMounted(() => {
  console.log('HomeView mounted')
  console.log('Auth state:', {
    isAuthenticated: authStore.isAuthenticated,
    user: authStore.user,
    token: authStore.token
  })

  // 初始化认证状态
  authStore.initAuth()
})
</script>

<style scoped>
.container {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  position: relative;
  z-index: 1;
  opacity: 0;
  animation: fadeIn 0.5s 0.2s ease-in forwards;
}

.grid-bg {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.2) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.2) 1px, transparent 1px),
    linear-gradient(rgba(255, 255, 255, 0.15) 2px, transparent 2px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.15) 2px, transparent 2px);
  background-size: 80px 80px, 80px 80px, 200px 200px, 200px 200px;
  opacity: 0.3;
  pointer-events: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
