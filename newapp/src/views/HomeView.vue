<template>
  <div class="home-page">
    <!-- Main container -->
    <el-container class="home-container">
      <!-- Header -->
      <el-header class="home-header">
        <div class="header-content">
          <!-- Logo and Title -->
          <div class="logo-section">
            <h1 class="app-title">
              像素星云AI助手
            </h1>
          </div>

          <!-- Header Actions -->
          <div class="header-actions">
            <!-- User Info -->
            <el-dropdown>
              <el-button type="primary" plain>
                <el-icon><User /></el-icon>
                {{ userDisplayName }}
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>个人资料</el-dropdown-item>
                  <el-dropdown-item>设置</el-dropdown-item>
                  <el-dropdown-item divided @click="handleLogout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>

      <!-- Main Content -->
      <el-main class="home-main">
        <!-- Welcome Section -->
        <div class="welcome-section">
          <el-row justify="center">
            <el-col :span="16">
              <el-card class="welcome-card">
                <div class="welcome-content">
                  <h2 class="welcome-title">🎉 登录成功！</h2>
                  <p class="welcome-subtitle">欢迎使用像素星云AI助手</p>

                  <!-- Status Info -->
                  <el-descriptions title="用户信息" :column="2" border>
                    <el-descriptions-item label="用户名">{{ userDisplayName }}</el-descriptions-item>
                    <el-descriptions-item label="认证状态">
                      <el-tag :type="isAuthenticated ? 'success' : 'danger'">
                        {{ isAuthenticated ? '已认证' : '未认证' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="管理员权限">
                      <el-tag :type="isAdmin ? 'warning' : 'info'">
                        {{ isAdmin ? '是' : '否' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="Token状态">
                      <el-tag :type="token ? 'success' : 'danger'">
                        {{ token ? '存在' : '不存在' }}
                      </el-tag>
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- Feature Navigation -->
        <div class="features-section">
          <el-row :gutter="20" justify="center">
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <div class="feature-content">
                  <el-icon class="feature-icon" size="48"><Collection /></el-icon>
                  <h3 class="feature-title">案例管理</h3>
                  <p class="feature-description">浏览和管理AI生成的图片案例</p>
                  <el-button type="primary" @click="$router.push('/examples')">
                    进入案例管理
                  </el-button>
                </div>
              </el-card>
            </el-col>

            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <div class="feature-content">
                  <el-icon class="feature-icon" size="48"><Picture /></el-icon>
                  <h3 class="feature-title">提示词反推</h3>
                  <p class="feature-description">从图片反推生成提示词</p>
                  <el-button type="success" @click="$router.push('/reverse-prompt')">
                    开始反推
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { User, SwitchButton, ArrowDown, Collection, Picture } from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()

// Computed properties for debugging
const userDisplayName = computed(() => authStore.userDisplayName || '未知用户')
const isAuthenticated = computed(() => authStore.isAuthenticated)
const isAdmin = computed(() => authStore.isAdmin)
const token = computed(() => authStore.token)

const handleLogout = () => {
  console.log('Logging out...')
  authStore.logout()
  router.push('/login')
}

onMounted(() => {
  console.log('HomeView mounted')
  console.log('Auth state:', {
    isAuthenticated: authStore.isAuthenticated,
    user: authStore.user,
    token: authStore.token
  })

  // 初始化认证状态
  authStore.initAuth()
})
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%);
}

.home-container {
  min-height: 100vh;
}

.home-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.app-title {
  font-size: 1.8rem;
  font-weight: bold;
  background: linear-gradient(135deg, #409eff, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.home-main {
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 40px;
}

.welcome-card {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #e4e7ed;
  margin-bottom: 1rem;
}

.welcome-subtitle {
  font-size: 1.2rem;
  color: #909399;
  margin-bottom: 2rem;
}

.features-section {
  margin-top: 40px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.feature-content {
  text-align: center;
  padding: 20px;
}

.feature-icon {
  color: #409eff;
  margin-bottom: 16px;
}

.feature-title {
  font-size: 1.3rem;
  font-weight: bold;
  color: #e4e7ed;
  margin-bottom: 12px;
}

.feature-description {
  color: #909399;
  margin-bottom: 20px;
  line-height: 1.6;
}

/* Element Plus dark theme customization */
:deep(.el-card) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-descriptions__label) {
  color: #e4e7ed !important;
}

:deep(.el-descriptions__content) {
  color: #e4e7ed !important;
}

:deep(.el-descriptions__title) {
  color: #e4e7ed !important;
}
</style>
