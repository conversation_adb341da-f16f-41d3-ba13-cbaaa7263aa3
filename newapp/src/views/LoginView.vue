<template>
  <div class="min-h-screen flex items-center justify-center p-5 relative">
    <!-- Background grid patterns -->
    <div class="fixed inset-0 bg-gray-950"></div>
    <div class="fixed inset-0 grid-bg"></div>

    <!-- Background Effects -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="spotlight top-left"></div>
      <div class="spotlight top-right"></div>
    </div>

    <!-- Login Container -->
    <div class="relative z-10 w-full max-w-md">
      <div class="login-container">
        <!-- Header -->
        <div class="text-center mb-8">
          <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent mb-2">
            像素星云AI助手
          </h1>
          <p class="text-gray-400" id="formTitle">{{ isRegisterMode ? '注册账号' : '登录账号' }}</p>
        </div>

        <!-- Alerts -->
        <div v-if="errorMessage" class="mb-4 p-3 bg-red-900/50 border border-red-500/50 rounded-lg text-red-200 text-sm">
          {{ errorMessage }}
        </div>
        <div v-if="successMessage" class="mb-4 p-3 bg-green-900/50 border border-green-500/50 rounded-lg text-green-200 text-sm">
          {{ successMessage }}
        </div>

        <!-- Form -->
        <form @submit.prevent="handleSubmit" class="space-y-4">
          <!-- Username/Email -->
          <div>
            <label for="identifier" class="block text-sm font-medium text-gray-300 mb-2">
              {{ isRegisterMode ? '用户名' : '用户名或邮箱' }}
            </label>
            <input
              id="identifier"
              v-model="formData.identifier"
              type="text"
              required
              class="form-input"
              :placeholder="isRegisterMode ? '请输入用户名' : '请输入用户名或邮箱'"
            />
          </div>

          <!-- Email (Register only) -->
          <div v-if="isRegisterMode">
            <label for="email" class="block text-sm font-medium text-gray-300 mb-2">邮箱</label>
            <input
              id="email"
              v-model="formData.email"
              type="email"
              required
              class="form-input"
              placeholder="请输入邮箱地址"
            />
          </div>

          <!-- Password -->
          <div class="relative">
            <label for="password" class="block text-sm font-medium text-gray-300 mb-2">密码</label>
            <input
              id="password"
              v-model="formData.password"
              :type="showPassword ? 'text' : 'password'"
              required
              class="form-input pr-10"
              placeholder="请输入密码"
            />
            <button
              type="button"
              @click="showPassword = !showPassword"
              class="absolute right-3 top-9 text-gray-400 hover:text-gray-200"
            >
              <svg v-if="showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
              </svg>
              <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
              </svg>
            </button>
          </div>

          <!-- Confirm Password (Register only) -->
          <div v-if="isRegisterMode" class="relative">
            <label for="confirmPassword" class="block text-sm font-medium text-gray-300 mb-2">确认密码</label>
            <input
              id="confirmPassword"
              v-model="formData.confirmPassword"
              :type="showConfirmPassword ? 'text' : 'password'"
              required
              class="form-input pr-10"
              placeholder="请再次输入密码"
            />
            <button
              type="button"
              @click="showConfirmPassword = !showConfirmPassword"
              class="absolute right-3 top-9 text-gray-400 hover:text-gray-200"
            >
              <svg v-if="showConfirmPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
              </svg>
              <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
              </svg>
            </button>
          </div>

          <!-- Remember Me & Forgot Password (Login only) -->
          <div v-if="!isRegisterMode" class="flex items-center justify-between">
            <label class="flex items-center">
              <input
                v-model="formData.rememberMe"
                type="checkbox"
                class="w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
              />
              <span class="ml-2 text-sm text-gray-300">记住我</span>
            </label>
            <button
              type="button"
              @click="showForgotPassword"
              class="text-sm text-blue-400 hover:text-blue-300"
            >
              忘记密码？
            </button>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            :disabled="isLoading"
            class="w-full btn-primary py-3 text-base font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="isLoading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isRegisterMode ? '注册中...' : '登录中...' }}
            </span>
            <span v-else>
              {{ isRegisterMode ? '注册' : '登录' }}
            </span>
          </button>

          <!-- Toggle Form -->
          <div class="text-center">
            <span class="text-gray-400 text-sm">
              {{ isRegisterMode ? '已有账号？' : '还没有账号？' }}
            </span>
            <button
              type="button"
              @click="toggleMode"
              class="ml-1 text-blue-400 hover:text-blue-300 text-sm"
            >
              {{ isRegisterMode ? '立即登录' : '立即注册' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const isRegisterMode = ref(false)
const isLoading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const errorMessage = ref('')
const successMessage = ref('')

const formData = reactive({
  identifier: '',
  email: '',
  password: '',
  confirmPassword: '',
  rememberMe: false,
})

const toggleMode = () => {
  isRegisterMode.value = !isRegisterMode.value
  errorMessage.value = ''
  successMessage.value = ''
  // Reset form
  Object.assign(formData, {
    identifier: '',
    email: '',
    password: '',
    confirmPassword: '',
    rememberMe: false,
  })
}

const showForgotPassword = () => {
  // TODO: Implement forgot password functionality
  console.log('Show forgot password modal')
}

const handleSubmit = async () => {
  errorMessage.value = ''
  successMessage.value = ''

  if (isRegisterMode.value) {
    // Register validation
    if (formData.password !== formData.confirmPassword) {
      errorMessage.value = '两次输入的密码不一致'
      return
    }
    if (formData.password.length < 6) {
      errorMessage.value = '密码长度至少6位'
      return
    }
    // TODO: Implement registration
    errorMessage.value = '注册功能暂未实现'
  } else {
    // Login
    if (!formData.identifier || !formData.password) {
      errorMessage.value = '请填写完整信息'
      return
    }

    isLoading.value = true
    try {
      const result = await authStore.login(
        formData.identifier,
        formData.password,
        formData.rememberMe
      )

      if (result.success) {
        successMessage.value = '登录成功，正在跳转...'
        console.log('Login successful, redirecting...')
        // 立即跳转，不使用延迟
        await router.push('/')
        console.log('Redirected to home page')
      } else {
        errorMessage.value = result.message || '登录失败'
        console.log('Login failed:', result.message)
      }
    } catch (error) {
      errorMessage.value = '网络错误，请稍后重试'
    } finally {
      isLoading.value = false
    }
  }
}

onMounted(() => {
  console.log('LoginView mounted')

  // Check if already logged in
  const token = localStorage.getItem('token')
  const user = localStorage.getItem('user')
  console.log('Existing auth state:', { token: !!token, user: !!user })

  if (token && user) {
    console.log('User already logged in, redirecting...')
    router.push('/')
    return
  }

  // Load saved login info
  const savedIdentifier = localStorage.getItem('identifier')
  const savedRememberMe = localStorage.getItem('rememberMe') === 'true'

  if (savedIdentifier && savedRememberMe) {
    formData.identifier = savedIdentifier
    formData.rememberMe = true
  }
})
</script>

<style scoped>
.grid-bg {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.2) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.2) 1px, transparent 1px),
    linear-gradient(rgba(255, 255, 255, 0.15) 2px, transparent 2px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.15) 2px, transparent 2px);
  background-size: 80px 80px, 80px 80px, 200px 200px, 200px 200px;
  opacity: 0.3;
  pointer-events: none;
}

.login-container {
  background: rgba(20, 20, 20, 0.8);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

.spotlight {
  position: absolute;
  width: 600px;
  height: 600px;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.3;
  pointer-events: none;
}

.spotlight.top-left {
  top: -300px;
  left: -300px;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
}

.spotlight.top-right {
  top: -300px;
  right: -300px;
  background: linear-gradient(225deg, #06b6d4, #3b82f6);
}

/* Form styles */
.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: rgba(31, 41, 55, 0.8);
  border: 1px solid rgba(75, 85, 99, 0.8);
  border-radius: 0.5rem;
  color: white;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
  color: rgba(156, 163, 175, 0.8);
}

.btn-primary {
  width: 100%;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-outline {
  padding: 0.5rem 1rem;
  background: transparent;
  color: #9ca3af;
  border: 1px solid rgba(75, 85, 99, 0.8);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-outline:hover {
  color: white;
  border-color: #6b7280;
}
</style>
